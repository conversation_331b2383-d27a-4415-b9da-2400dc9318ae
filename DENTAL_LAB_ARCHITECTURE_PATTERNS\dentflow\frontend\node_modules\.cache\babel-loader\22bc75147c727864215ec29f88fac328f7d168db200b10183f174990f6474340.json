{"ast": null, "code": "/**\n * Unified API Service Layer for DentFlow\n * Connects React frontend to Django backend with fallback to mock data\n */\n\nimport axios from 'axios';\n\n// API Configuration\nconst API_BASE_URL = process.env.REACT_APP_API_URL || 'http://localhost:8001/api/v1';\nclass UnifiedApiClient {\n  constructor() {\n    this.client = void 0;\n    this.isBackendAvailable = true;\n    this.client = axios.create({\n      baseURL: API_BASE_URL,\n      timeout: 10000,\n      headers: {\n        'Content-Type': 'application/json'\n      }\n    });\n\n    // Request interceptor for auth token\n    this.client.interceptors.request.use(config => {\n      const token = localStorage.getItem('dentflow_token');\n      if (token) {\n        config.headers.Authorization = `Bearer ${token}`;\n      }\n      return config;\n    }, error => Promise.reject(error));\n\n    // Response interceptor for error handling\n    this.client.interceptors.response.use(response => response, error => {\n      var _error$response;\n      if (((_error$response = error.response) === null || _error$response === void 0 ? void 0 : _error$response.status) === 401) {\n        localStorage.removeItem('dentflow_token');\n        window.location.href = '/login';\n      }\n      return Promise.reject(error);\n    });\n  }\n  async get(url, config) {\n    try {\n      const response = await this.client.get(url, config);\n      this.isBackendAvailable = true;\n      return response.data;\n    } catch (error) {\n      this.isBackendAvailable = false;\n      throw error;\n    }\n  }\n  async getBlob(url) {\n    try {\n      const response = await this.client.get(url, {\n        responseType: 'blob'\n      });\n      this.isBackendAvailable = true;\n      return response.data;\n    } catch (error) {\n      this.isBackendAvailable = false;\n      throw error;\n    }\n  }\n  async post(url, data) {\n    try {\n      const response = await this.client.post(url, data);\n      this.isBackendAvailable = true;\n      return response.data;\n    } catch (error) {\n      this.isBackendAvailable = false;\n      throw error;\n    }\n  }\n  async put(url, data) {\n    try {\n      const response = await this.client.put(url, data);\n      this.isBackendAvailable = true;\n      return response.data;\n    } catch (error) {\n      this.isBackendAvailable = false;\n      throw error;\n    }\n  }\n  async patch(url, data) {\n    try {\n      const response = await this.client.patch(url, data);\n      this.isBackendAvailable = true;\n      return response.data;\n    } catch (error) {\n      this.isBackendAvailable = false;\n      throw error;\n    }\n  }\n  async delete(url) {\n    try {\n      const response = await this.client.delete(url);\n      this.isBackendAvailable = true;\n      return response.data;\n    } catch (error) {\n      this.isBackendAvailable = false;\n      throw error;\n    }\n  }\n\n  // Authentication methods\n  async login(email, password) {\n    try {\n      const response = await this.client.post('/auth/login/', {\n        email,\n        password\n      });\n\n      // Store tokens for future requests\n      if (response.data.access) {\n        localStorage.setItem('dentflow_token', response.data.access);\n        localStorage.setItem('dentflow_refresh_token', response.data.refresh);\n        this.client.defaults.headers.common['Authorization'] = `Bearer ${response.data.access}`;\n      }\n      return response.data;\n    } catch (error) {\n      var _error$response2, _error$response3, _error$response3$data;\n      console.error('Login failed:', ((_error$response2 = error.response) === null || _error$response2 === void 0 ? void 0 : _error$response2.data) || error.message);\n      throw new Error(((_error$response3 = error.response) === null || _error$response3 === void 0 ? void 0 : (_error$response3$data = _error$response3.data) === null || _error$response3$data === void 0 ? void 0 : _error$response3$data.detail) || 'Login failed');\n    }\n  }\n  async logout() {\n    localStorage.removeItem('dentflow_token');\n    localStorage.removeItem('dentflow_refresh_token');\n    delete this.client.defaults.headers.common['Authorization'];\n  }\n  getBackendStatus() {\n    return {\n      available: this.isBackendAvailable,\n      mode: this.isBackendAvailable ? 'live' : 'mock'\n    };\n  }\n}\nexport const apiClient = new UnifiedApiClient();", "map": {"version": 3, "names": ["axios", "API_BASE_URL", "process", "env", "REACT_APP_API_URL", "UnifiedApiClient", "constructor", "client", "isBackendAvailable", "create", "baseURL", "timeout", "headers", "interceptors", "request", "use", "config", "token", "localStorage", "getItem", "Authorization", "error", "Promise", "reject", "response", "_error$response", "status", "removeItem", "window", "location", "href", "get", "url", "data", "getBlob", "responseType", "post", "put", "patch", "delete", "login", "email", "password", "access", "setItem", "refresh", "defaults", "common", "_error$response2", "_error$response3", "_error$response3$data", "console", "message", "Error", "detail", "logout", "getBackendStatus", "available", "mode", "apiClient"], "sources": ["C:/GPT4_PROJECTS/DENTAL_LAB_ARCHITECTURE_PATTERNS/dentflow/frontend/src/api/unifiedClient.ts"], "sourcesContent": ["/**\n * Unified API Service Layer for DentFlow\n * Connects React frontend to Django backend with fallback to mock data\n */\n\nimport axios, { AxiosInstance, AxiosResponse } from 'axios';\n\n// API Configuration\nconst API_BASE_URL = process.env.REACT_APP_API_URL || 'http://localhost:8001/api/v1';\n\nexport interface ApiResponse<T> {\n  data: T;\n  message?: string;\n  errors?: Record<string, string[]>;\n}\n\nclass UnifiedApiClient {\n  private client: AxiosInstance;\n  private isBackendAvailable = true;\n\n  constructor() {\n    this.client = axios.create({\n      baseURL: API_BASE_URL,\n      timeout: 10000,\n      headers: {\n        'Content-Type': 'application/json',\n      },\n    });\n\n    // Request interceptor for auth token\n    this.client.interceptors.request.use(\n      (config) => {\n        const token = localStorage.getItem('dentflow_token');\n        if (token) {\n          config.headers.Authorization = `Bearer ${token}`;\n        }\n        return config;\n      },\n      (error) => Promise.reject(error)\n    );\n\n    // Response interceptor for error handling\n    this.client.interceptors.response.use(\n      (response) => response,\n      (error) => {\n        if (error.response?.status === 401) {\n          localStorage.removeItem('dentflow_token');\n          window.location.href = '/login';\n        }\n        return Promise.reject(error);\n      }\n    );\n  }\n\n  async get<T>(url: string, config?: any): Promise<T> {\n    try {\n      const response: AxiosResponse<T> = await this.client.get(url, config);\n      this.isBackendAvailable = true;\n      return response.data;\n    } catch (error) {\n      this.isBackendAvailable = false;\n      throw error;\n    }\n  }\n\n  async getBlob(url: string): Promise<Blob> {\n    try {\n      const response: AxiosResponse<Blob> = await this.client.get(url, { responseType: 'blob' });\n      this.isBackendAvailable = true;\n      return response.data;\n    } catch (error) {\n      this.isBackendAvailable = false;\n      throw error;\n    }\n  }\n\n  async post<T>(url: string, data?: any): Promise<T> {\n    try {\n      const response: AxiosResponse<T> = await this.client.post(url, data);\n      this.isBackendAvailable = true;\n      return response.data;\n    } catch (error) {\n      this.isBackendAvailable = false;\n      throw error;\n    }\n  }\n\n  async put<T>(url: string, data?: any): Promise<T> {\n    try {\n      const response: AxiosResponse<T> = await this.client.put(url, data);\n      this.isBackendAvailable = true;\n      return response.data;\n    } catch (error) {\n      this.isBackendAvailable = false;\n      throw error;\n    }\n  }\n\n  async patch<T>(url: string, data?: any): Promise<T> {\n    try {\n      const response: AxiosResponse<T> = await this.client.patch(url, data);\n      this.isBackendAvailable = true;\n      return response.data;\n    } catch (error) {\n      this.isBackendAvailable = false;\n      throw error;\n    }\n  }\n\n  async delete<T>(url: string): Promise<T> {\n    try {\n      const response: AxiosResponse<T> = await this.client.delete(url);\n      this.isBackendAvailable = true;\n      return response.data;\n    } catch (error) {\n      this.isBackendAvailable = false;\n      throw error;\n    }\n  }\n\n  // Authentication methods\n  async login(email: string, password: string): Promise<{ access: string; refresh: string; user: any }> {\n    try {\n      const response = await this.client.post('/auth/login/', {\n        email,\n        password\n      });\n\n      // Store tokens for future requests\n      if (response.data.access) {\n        localStorage.setItem('dentflow_token', response.data.access);\n        localStorage.setItem('dentflow_refresh_token', response.data.refresh);\n        this.client.defaults.headers.common['Authorization'] = `Bearer ${response.data.access}`;\n      }\n\n      return response.data;\n    } catch (error: any) {\n      console.error('Login failed:', error.response?.data || error.message);\n      throw new Error(error.response?.data?.detail || 'Login failed');\n    }\n  }\n\n  async logout(): Promise<void> {\n    localStorage.removeItem('dentflow_token');\n    localStorage.removeItem('dentflow_refresh_token');\n    delete this.client.defaults.headers.common['Authorization'];\n  }\n\n  getBackendStatus() {\n    return {\n      available: this.isBackendAvailable,\n      mode: this.isBackendAvailable ? 'live' : 'mock'\n    };\n  }\n}\n\nexport const apiClient = new UnifiedApiClient();\n"], "mappings": "AAAA;AACA;AACA;AACA;;AAEA,OAAOA,KAAK,MAAwC,OAAO;;AAE3D;AACA,MAAMC,YAAY,GAAGC,OAAO,CAACC,GAAG,CAACC,iBAAiB,IAAI,8BAA8B;AAQpF,MAAMC,gBAAgB,CAAC;EAIrBC,WAAWA,CAAA,EAAG;IAAA,KAHNC,MAAM;IAAA,KACNC,kBAAkB,GAAG,IAAI;IAG/B,IAAI,CAACD,MAAM,GAAGP,KAAK,CAACS,MAAM,CAAC;MACzBC,OAAO,EAAET,YAAY;MACrBU,OAAO,EAAE,KAAK;MACdC,OAAO,EAAE;QACP,cAAc,EAAE;MAClB;IACF,CAAC,CAAC;;IAEF;IACA,IAAI,CAACL,MAAM,CAACM,YAAY,CAACC,OAAO,CAACC,GAAG,CACjCC,MAAM,IAAK;MACV,MAAMC,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,gBAAgB,CAAC;MACpD,IAAIF,KAAK,EAAE;QACTD,MAAM,CAACJ,OAAO,CAACQ,aAAa,GAAG,UAAUH,KAAK,EAAE;MAClD;MACA,OAAOD,MAAM;IACf,CAAC,EACAK,KAAK,IAAKC,OAAO,CAACC,MAAM,CAACF,KAAK,CACjC,CAAC;;IAED;IACA,IAAI,CAACd,MAAM,CAACM,YAAY,CAACW,QAAQ,CAACT,GAAG,CAClCS,QAAQ,IAAKA,QAAQ,EACrBH,KAAK,IAAK;MAAA,IAAAI,eAAA;MACT,IAAI,EAAAA,eAAA,GAAAJ,KAAK,CAACG,QAAQ,cAAAC,eAAA,uBAAdA,eAAA,CAAgBC,MAAM,MAAK,GAAG,EAAE;QAClCR,YAAY,CAACS,UAAU,CAAC,gBAAgB,CAAC;QACzCC,MAAM,CAACC,QAAQ,CAACC,IAAI,GAAG,QAAQ;MACjC;MACA,OAAOR,OAAO,CAACC,MAAM,CAACF,KAAK,CAAC;IAC9B,CACF,CAAC;EACH;EAEA,MAAMU,GAAGA,CAAIC,GAAW,EAAEhB,MAAY,EAAc;IAClD,IAAI;MACF,MAAMQ,QAA0B,GAAG,MAAM,IAAI,CAACjB,MAAM,CAACwB,GAAG,CAACC,GAAG,EAAEhB,MAAM,CAAC;MACrE,IAAI,CAACR,kBAAkB,GAAG,IAAI;MAC9B,OAAOgB,QAAQ,CAACS,IAAI;IACtB,CAAC,CAAC,OAAOZ,KAAK,EAAE;MACd,IAAI,CAACb,kBAAkB,GAAG,KAAK;MAC/B,MAAMa,KAAK;IACb;EACF;EAEA,MAAMa,OAAOA,CAACF,GAAW,EAAiB;IACxC,IAAI;MACF,MAAMR,QAA6B,GAAG,MAAM,IAAI,CAACjB,MAAM,CAACwB,GAAG,CAACC,GAAG,EAAE;QAAEG,YAAY,EAAE;MAAO,CAAC,CAAC;MAC1F,IAAI,CAAC3B,kBAAkB,GAAG,IAAI;MAC9B,OAAOgB,QAAQ,CAACS,IAAI;IACtB,CAAC,CAAC,OAAOZ,KAAK,EAAE;MACd,IAAI,CAACb,kBAAkB,GAAG,KAAK;MAC/B,MAAMa,KAAK;IACb;EACF;EAEA,MAAMe,IAAIA,CAAIJ,GAAW,EAAEC,IAAU,EAAc;IACjD,IAAI;MACF,MAAMT,QAA0B,GAAG,MAAM,IAAI,CAACjB,MAAM,CAAC6B,IAAI,CAACJ,GAAG,EAAEC,IAAI,CAAC;MACpE,IAAI,CAACzB,kBAAkB,GAAG,IAAI;MAC9B,OAAOgB,QAAQ,CAACS,IAAI;IACtB,CAAC,CAAC,OAAOZ,KAAK,EAAE;MACd,IAAI,CAACb,kBAAkB,GAAG,KAAK;MAC/B,MAAMa,KAAK;IACb;EACF;EAEA,MAAMgB,GAAGA,CAAIL,GAAW,EAAEC,IAAU,EAAc;IAChD,IAAI;MACF,MAAMT,QAA0B,GAAG,MAAM,IAAI,CAACjB,MAAM,CAAC8B,GAAG,CAACL,GAAG,EAAEC,IAAI,CAAC;MACnE,IAAI,CAACzB,kBAAkB,GAAG,IAAI;MAC9B,OAAOgB,QAAQ,CAACS,IAAI;IACtB,CAAC,CAAC,OAAOZ,KAAK,EAAE;MACd,IAAI,CAACb,kBAAkB,GAAG,KAAK;MAC/B,MAAMa,KAAK;IACb;EACF;EAEA,MAAMiB,KAAKA,CAAIN,GAAW,EAAEC,IAAU,EAAc;IAClD,IAAI;MACF,MAAMT,QAA0B,GAAG,MAAM,IAAI,CAACjB,MAAM,CAAC+B,KAAK,CAACN,GAAG,EAAEC,IAAI,CAAC;MACrE,IAAI,CAACzB,kBAAkB,GAAG,IAAI;MAC9B,OAAOgB,QAAQ,CAACS,IAAI;IACtB,CAAC,CAAC,OAAOZ,KAAK,EAAE;MACd,IAAI,CAACb,kBAAkB,GAAG,KAAK;MAC/B,MAAMa,KAAK;IACb;EACF;EAEA,MAAMkB,MAAMA,CAAIP,GAAW,EAAc;IACvC,IAAI;MACF,MAAMR,QAA0B,GAAG,MAAM,IAAI,CAACjB,MAAM,CAACgC,MAAM,CAACP,GAAG,CAAC;MAChE,IAAI,CAACxB,kBAAkB,GAAG,IAAI;MAC9B,OAAOgB,QAAQ,CAACS,IAAI;IACtB,CAAC,CAAC,OAAOZ,KAAK,EAAE;MACd,IAAI,CAACb,kBAAkB,GAAG,KAAK;MAC/B,MAAMa,KAAK;IACb;EACF;;EAEA;EACA,MAAMmB,KAAKA,CAACC,KAAa,EAAEC,QAAgB,EAA2D;IACpG,IAAI;MACF,MAAMlB,QAAQ,GAAG,MAAM,IAAI,CAACjB,MAAM,CAAC6B,IAAI,CAAC,cAAc,EAAE;QACtDK,KAAK;QACLC;MACF,CAAC,CAAC;;MAEF;MACA,IAAIlB,QAAQ,CAACS,IAAI,CAACU,MAAM,EAAE;QACxBzB,YAAY,CAAC0B,OAAO,CAAC,gBAAgB,EAAEpB,QAAQ,CAACS,IAAI,CAACU,MAAM,CAAC;QAC5DzB,YAAY,CAAC0B,OAAO,CAAC,wBAAwB,EAAEpB,QAAQ,CAACS,IAAI,CAACY,OAAO,CAAC;QACrE,IAAI,CAACtC,MAAM,CAACuC,QAAQ,CAAClC,OAAO,CAACmC,MAAM,CAAC,eAAe,CAAC,GAAG,UAAUvB,QAAQ,CAACS,IAAI,CAACU,MAAM,EAAE;MACzF;MAEA,OAAOnB,QAAQ,CAACS,IAAI;IACtB,CAAC,CAAC,OAAOZ,KAAU,EAAE;MAAA,IAAA2B,gBAAA,EAAAC,gBAAA,EAAAC,qBAAA;MACnBC,OAAO,CAAC9B,KAAK,CAAC,eAAe,EAAE,EAAA2B,gBAAA,GAAA3B,KAAK,CAACG,QAAQ,cAAAwB,gBAAA,uBAAdA,gBAAA,CAAgBf,IAAI,KAAIZ,KAAK,CAAC+B,OAAO,CAAC;MACrE,MAAM,IAAIC,KAAK,CAAC,EAAAJ,gBAAA,GAAA5B,KAAK,CAACG,QAAQ,cAAAyB,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgBhB,IAAI,cAAAiB,qBAAA,uBAApBA,qBAAA,CAAsBI,MAAM,KAAI,cAAc,CAAC;IACjE;EACF;EAEA,MAAMC,MAAMA,CAAA,EAAkB;IAC5BrC,YAAY,CAACS,UAAU,CAAC,gBAAgB,CAAC;IACzCT,YAAY,CAACS,UAAU,CAAC,wBAAwB,CAAC;IACjD,OAAO,IAAI,CAACpB,MAAM,CAACuC,QAAQ,CAAClC,OAAO,CAACmC,MAAM,CAAC,eAAe,CAAC;EAC7D;EAEAS,gBAAgBA,CAAA,EAAG;IACjB,OAAO;MACLC,SAAS,EAAE,IAAI,CAACjD,kBAAkB;MAClCkD,IAAI,EAAE,IAAI,CAAClD,kBAAkB,GAAG,MAAM,GAAG;IAC3C,CAAC;EACH;AACF;AAEA,OAAO,MAAMmD,SAAS,GAAG,IAAItD,gBAAgB,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}