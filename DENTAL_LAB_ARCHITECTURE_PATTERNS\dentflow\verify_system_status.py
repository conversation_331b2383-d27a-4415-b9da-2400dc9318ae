#!/usr/bin/env python3
"""
Quick system status verification
"""

import requests
import json
from datetime import datetime

def check_system_status():
    print("🔍 DentFlow System Status Check")
    print("=" * 50)
    print(f"⏰ Check time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    # Check backend server
    try:
        response = requests.get("http://localhost:8001/admin/", timeout=5)
        if response.status_code in [200, 302]:
            print("✅ Backend Server: RUNNING (http://localhost:8001)")
            print("   - Django admin accessible")
            print("   - Ready for testing")
        else:
            print(f"⚠️  Backend Server: ISSUE (Status: {response.status_code})")
    except requests.exceptions.RequestException as e:
        print("❌ Backend Server: NOT RUNNING")
        print(f"   Error: {e}")
        return False
    
    # Check frontend server
    try:
        response = requests.get("http://localhost:3001", timeout=5)
        if response.status_code == 200:
            print("✅ Frontend Server: RUNNING (http://localhost:3001)")
            print("   - React application accessible")
            print("   - Ready for UI testing")
        else:
            print(f"⚠️  Frontend Server: ISSUE (Status: {response.status_code})")
    except requests.exceptions.RequestException as e:
        print("❌ Frontend Server: NOT RUNNING")
        print(f"   Error: {e}")
    
    print()
    print("🎯 TESTING READY!")
    print("📋 Next Steps:")
    print("   1. Open admin: http://localhost:8001/admin")
    print("   2. Login with: admin / admin123")
    print("   3. Follow the comprehensive testing plan")
    print("   4. Open frontend: http://localhost:3001")
    print("   5. Test all modules systematically")
    
    print()
    print("📊 Key Testing Areas:")
    print("   • Admin Interface: Model registration and access")
    print("   • Cost Calculations: Material compositions and pricing")
    print("   • Frontend Integration: API connectivity and UI")
    print("   • End-to-End Workflow: Complete business processes")
    print("   • Performance: Load times and responsiveness")
    
    return True

if __name__ == '__main__':
    check_system_status()
