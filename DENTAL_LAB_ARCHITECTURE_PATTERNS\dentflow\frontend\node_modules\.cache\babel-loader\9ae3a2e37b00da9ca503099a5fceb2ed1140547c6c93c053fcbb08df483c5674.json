{"ast": null, "code": "var _s = $RefreshSig$(),\n  _s2 = $RefreshSig$(),\n  _s3 = $RefreshSig$(),\n  _s4 = $RefreshSig$(),\n  _s5 = $RefreshSig$(),\n  _s6 = $RefreshSig$(),\n  _s7 = $RefreshSig$(),\n  _s8 = $RefreshSig$(),\n  _s9 = $RefreshSig$(),\n  _s0 = $RefreshSig$(),\n  _s1 = $RefreshSig$(),\n  _s10 = $RefreshSig$(),\n  _s11 = $RefreshSig$(),\n  _s12 = $RefreshSig$(),\n  _s13 = $RefreshSig$(),\n  _s14 = $RefreshSig$(),\n  _s15 = $RefreshSig$(),\n  _s16 = $RefreshSig$(),\n  _s17 = $RefreshSig$(),\n  _s18 = $RefreshSig$(),\n  _s19 = $RefreshSig$(),\n  _s20 = $RefreshSig$(),\n  _s21 = $RefreshSig$(),\n  _s22 = $RefreshSig$(),\n  _s23 = $RefreshSig$(),\n  _s24 = $RefreshSig$();\n/**\n * React Query Hooks for DentFlow API\n * Provides data fetching, caching, and mutation hooks\n */\n\nimport { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';\nimport { DentFlowAPI } from '../api';\n// Query keys for React Query\nexport const queryKeys = {\n  cases: ['cases'],\n  case: id => ['cases', id],\n  casesByStatus: status => ['cases', 'status', status],\n  overdueCases: ['cases', 'overdue'],\n  dashboardStats: ['dashboard', 'stats'],\n  tasks: ['tasks'],\n  technicians: ['technicians'],\n  workflowStages: ['workflow', 'stages'],\n  invoices: ['invoices'],\n  invoice: id => ['invoices', id],\n  appointments: ['appointments'],\n  appointment: id => ['appointments', id],\n  reports: ['reports'],\n  currentUser: ['auth', 'current-user']\n};\n\n// ===== AUTHENTICATION HOOKS =====\n\nexport const useCurrentUser = options => {\n  _s();\n  return useQuery({\n    queryKey: queryKeys.currentUser,\n    queryFn: () => DentFlowAPI.auth.getCurrentUser(),\n    enabled: DentFlowAPI.auth.isAuthenticated(),\n    staleTime: 5 * 60 * 1000,\n    // 5 minutes\n    ...options\n  });\n};\n_s(useCurrentUser, \"4ZpngI1uv+Uo3WQHEZmTQ5FNM+k=\", false, function () {\n  return [useQuery];\n});\nexport const useLogin = options => {\n  _s2();\n  const queryClient = useQueryClient();\n  return useMutation({\n    mutationFn: ({\n      email,\n      password\n    }) => DentFlowAPI.auth.login({\n      email,\n      password\n    }),\n    onSuccess: data => {\n      queryClient.setQueryData(queryKeys.currentUser, data.user);\n      queryClient.invalidateQueries({\n        queryKey: ['auth']\n      });\n    },\n    ...options\n  });\n};\n_s2(useLogin, \"YK0wzM21ECnncaq5SECwU+/SVdQ=\", false, function () {\n  return [useQueryClient, useMutation];\n});\nexport const useLogout = options => {\n  _s3();\n  const queryClient = useQueryClient();\n  return useMutation({\n    mutationFn: () => DentFlowAPI.auth.logout(),\n    onSuccess: () => {\n      queryClient.clear();\n    },\n    ...options\n  });\n};\n\n// ===== CASES HOOKS =====\n_s3(useLogout, \"YK0wzM21ECnncaq5SECwU+/SVdQ=\", false, function () {\n  return [useQueryClient, useMutation];\n});\nexport const useCases = options => {\n  _s4();\n  return useQuery({\n    queryKey: queryKeys.cases,\n    queryFn: () => DentFlowAPI.cases.getCases(),\n    staleTime: 2 * 60 * 1000,\n    // 2 minutes\n    ...options\n  });\n};\n_s4(useCases, \"4ZpngI1uv+Uo3WQHEZmTQ5FNM+k=\", false, function () {\n  return [useQuery];\n});\nexport const useCase = (id, options) => {\n  _s5();\n  return useQuery({\n    queryKey: queryKeys.case(id),\n    queryFn: () => DentFlowAPI.cases.getCase(id),\n    enabled: !!id,\n    staleTime: 1 * 60 * 1000,\n    // 1 minute\n    ...options\n  });\n};\n_s5(useCase, \"4ZpngI1uv+Uo3WQHEZmTQ5FNM+k=\", false, function () {\n  return [useQuery];\n});\nexport const useCasesByStatus = (status, options) => {\n  _s6();\n  return useQuery({\n    queryKey: queryKeys.casesByStatus(status),\n    queryFn: () => DentFlowAPI.cases.getCasesByStatus(status),\n    enabled: !!status,\n    staleTime: 1 * 60 * 1000,\n    ...options\n  });\n};\n_s6(useCasesByStatus, \"4ZpngI1uv+Uo3WQHEZmTQ5FNM+k=\", false, function () {\n  return [useQuery];\n});\nexport const useOverdueCases = options => {\n  _s7();\n  return useQuery({\n    queryKey: queryKeys.overdueCases,\n    queryFn: () => DentFlowAPI.cases.getOverdueCases(),\n    staleTime: 30 * 1000,\n    // 30 seconds (more critical data)\n    ...options\n  });\n};\n_s7(useOverdueCases, \"4ZpngI1uv+Uo3WQHEZmTQ5FNM+k=\", false, function () {\n  return [useQuery];\n});\nexport const useCreateCase = options => {\n  _s8();\n  const queryClient = useQueryClient();\n  return useMutation({\n    mutationFn: data => DentFlowAPI.cases.createCase(data),\n    onSuccess: () => {\n      queryClient.invalidateQueries({\n        queryKey: queryKeys.cases\n      });\n      queryClient.invalidateQueries({\n        queryKey: queryKeys.dashboardStats\n      });\n    },\n    ...options\n  });\n};\n_s8(useCreateCase, \"YK0wzM21ECnncaq5SECwU+/SVdQ=\", false, function () {\n  return [useQueryClient, useMutation];\n});\nexport const useUpdateCase = options => {\n  _s9();\n  const queryClient = useQueryClient();\n  return useMutation({\n    mutationFn: ({\n      id,\n      data\n    }) => DentFlowAPI.cases.updateCase(id, data),\n    onSuccess: updatedCase => {\n      queryClient.setQueryData(queryKeys.case(updatedCase.id), updatedCase);\n      queryClient.invalidateQueries({\n        queryKey: queryKeys.cases\n      });\n      queryClient.invalidateQueries({\n        queryKey: queryKeys.dashboardStats\n      });\n    },\n    ...options\n  });\n};\n_s9(useUpdateCase, \"YK0wzM21ECnncaq5SECwU+/SVdQ=\", false, function () {\n  return [useQueryClient, useMutation];\n});\nexport const useAdvanceCase = options => {\n  _s0();\n  const queryClient = useQueryClient();\n  return useMutation({\n    mutationFn: ({\n      id,\n      notes\n    }) => DentFlowAPI.cases.advanceCase(id, {\n      notes\n    }),\n    onSuccess: updatedCase => {\n      queryClient.setQueryData(queryKeys.case(updatedCase.id), updatedCase);\n      queryClient.invalidateQueries({\n        queryKey: queryKeys.cases\n      });\n      queryClient.invalidateQueries({\n        queryKey: queryKeys.tasks\n      });\n    },\n    ...options\n  });\n};\n_s0(useAdvanceCase, \"YK0wzM21ECnncaq5SECwU+/SVdQ=\", false, function () {\n  return [useQueryClient, useMutation];\n});\nexport const useAssignTechnician = options => {\n  _s1();\n  const queryClient = useQueryClient();\n  return useMutation({\n    mutationFn: ({\n      caseId,\n      technicianId\n    }) => DentFlowAPI.cases.assignTechnician(caseId, {\n      technician_id: technicianId\n    }),\n    onSuccess: updatedCase => {\n      queryClient.setQueryData(queryKeys.case(updatedCase.id), updatedCase);\n      queryClient.invalidateQueries({\n        queryKey: queryKeys.cases\n      });\n      queryClient.invalidateQueries({\n        queryKey: queryKeys.tasks\n      });\n    },\n    ...options\n  });\n};\n\n// ===== DASHBOARD HOOKS =====\n_s1(useAssignTechnician, \"YK0wzM21ECnncaq5SECwU+/SVdQ=\", false, function () {\n  return [useQueryClient, useMutation];\n});\nexport const useDashboardStats = options => {\n  _s10();\n  return useQuery({\n    queryKey: queryKeys.dashboardStats,\n    queryFn: () => DentFlowAPI.cases.getDashboardStats(),\n    staleTime: 30 * 1000,\n    // 30 seconds\n    refetchInterval: 60 * 1000,\n    // Refresh every minute\n    ...options\n  });\n};\n\n// ===== WORKFLOW HOOKS =====\n_s10(useDashboardStats, \"4ZpngI1uv+Uo3WQHEZmTQ5FNM+k=\", false, function () {\n  return [useQuery];\n});\nexport const useTasks = (filters, options) => {\n  _s11();\n  return useQuery({\n    queryKey: [...queryKeys.tasks, filters],\n    queryFn: () => DentFlowAPI.workflow.getTasks(filters),\n    staleTime: 1 * 60 * 1000,\n    ...options\n  });\n};\n_s11(useTasks, \"4ZpngI1uv+Uo3WQHEZmTQ5FNM+k=\", false, function () {\n  return [useQuery];\n});\nexport const useTechnicians = options => {\n  _s12();\n  return useQuery({\n    queryKey: queryKeys.technicians,\n    queryFn: () => DentFlowAPI.workflow.getTechnicians(),\n    staleTime: 5 * 60 * 1000,\n    // 5 minutes (technician data changes less frequently)\n    ...options\n  });\n};\n_s12(useTechnicians, \"4ZpngI1uv+Uo3WQHEZmTQ5FNM+k=\", false, function () {\n  return [useQuery];\n});\nexport const useWorkflowStages = options => {\n  _s13();\n  return useQuery({\n    queryKey: queryKeys.workflowStages,\n    queryFn: () => DentFlowAPI.workflow.getWorkflowStages(),\n    staleTime: 10 * 60 * 1000,\n    // 10 minutes (rarely changes)\n    ...options\n  });\n};\n_s13(useWorkflowStages, \"4ZpngI1uv+Uo3WQHEZmTQ5FNM+k=\", false, function () {\n  return [useQuery];\n});\nexport const useStartTask = options => {\n  _s14();\n  const queryClient = useQueryClient();\n  return useMutation({\n    mutationFn: taskId => DentFlowAPI.workflow.startTask(taskId),\n    onSuccess: () => {\n      queryClient.invalidateQueries({\n        queryKey: queryKeys.tasks\n      });\n    },\n    ...options\n  });\n};\n_s14(useStartTask, \"YK0wzM21ECnncaq5SECwU+/SVdQ=\", false, function () {\n  return [useQueryClient, useMutation];\n});\nexport const useCompleteTask = options => {\n  _s15();\n  const queryClient = useQueryClient();\n  return useMutation({\n    mutationFn: ({\n      taskId,\n      notes\n    }) => DentFlowAPI.workflow.completeTask(taskId, notes),\n    onSuccess: () => {\n      queryClient.invalidateQueries({\n        queryKey: queryKeys.tasks\n      });\n      queryClient.invalidateQueries({\n        queryKey: queryKeys.cases\n      });\n    },\n    ...options\n  });\n};\n\n// ===== BILLING HOOKS =====\n_s15(useCompleteTask, \"YK0wzM21ECnncaq5SECwU+/SVdQ=\", false, function () {\n  return [useQueryClient, useMutation];\n});\nexport const useInvoices = (filters, options) => {\n  _s16();\n  return useQuery({\n    queryKey: [...queryKeys.invoices, filters],\n    queryFn: () => DentFlowAPI.billing.getInvoices(filters),\n    staleTime: 2 * 60 * 1000,\n    ...options\n  });\n};\n_s16(useInvoices, \"4ZpngI1uv+Uo3WQHEZmTQ5FNM+k=\", false, function () {\n  return [useQuery];\n});\nexport const useInvoice = (id, options) => {\n  _s17();\n  return useQuery({\n    queryKey: queryKeys.invoice(id),\n    queryFn: () => DentFlowAPI.billing.getInvoiceById(id),\n    enabled: !!id,\n    staleTime: 1 * 60 * 1000,\n    ...options\n  });\n};\n_s17(useInvoice, \"4ZpngI1uv+Uo3WQHEZmTQ5FNM+k=\", false, function () {\n  return [useQuery];\n});\nexport const useCreateInvoice = options => {\n  _s18();\n  const queryClient = useQueryClient();\n  return useMutation({\n    mutationFn: data => DentFlowAPI.billing.createInvoice(data),\n    onSuccess: () => {\n      queryClient.invalidateQueries({\n        queryKey: queryKeys.invoices\n      });\n    },\n    ...options\n  });\n};\n\n// ===== SCHEDULE HOOKS =====\n_s18(useCreateInvoice, \"YK0wzM21ECnncaq5SECwU+/SVdQ=\", false, function () {\n  return [useQueryClient, useMutation];\n});\nexport const useAppointments = (filters, options) => {\n  _s19();\n  return useQuery({\n    queryKey: [...queryKeys.appointments, filters],\n    queryFn: () => DentFlowAPI.schedule.getAppointments(filters),\n    staleTime: 1 * 60 * 1000,\n    ...options\n  });\n};\n_s19(useAppointments, \"4ZpngI1uv+Uo3WQHEZmTQ5FNM+k=\", false, function () {\n  return [useQuery];\n});\nexport const useCreateAppointment = options => {\n  _s20();\n  const queryClient = useQueryClient();\n  return useMutation({\n    mutationFn: data => DentFlowAPI.schedule.createAppointment(data),\n    onSuccess: () => {\n      queryClient.invalidateQueries({\n        queryKey: queryKeys.appointments\n      });\n    },\n    ...options\n  });\n};\n\n// ===== REPORTS HOOKS =====\n_s20(useCreateAppointment, \"YK0wzM21ECnncaq5SECwU+/SVdQ=\", false, function () {\n  return [useQueryClient, useMutation];\n});\nexport const useReports = options => {\n  _s21();\n  return useQuery({\n    queryKey: queryKeys.reports,\n    queryFn: () => DentFlowAPI.reports.getReports(),\n    staleTime: 5 * 60 * 1000,\n    ...options\n  });\n};\n_s21(useReports, \"4ZpngI1uv+Uo3WQHEZmTQ5FNM+k=\", false, function () {\n  return [useQuery];\n});\nexport const useGenerateReport = options => {\n  _s22();\n  const queryClient = useQueryClient();\n  return useMutation({\n    mutationFn: data => DentFlowAPI.reports.generateReport(data),\n    onSuccess: () => {\n      queryClient.invalidateQueries({\n        queryKey: queryKeys.reports\n      });\n    },\n    ...options\n  });\n};\n\n// ===== UTILITY HOOKS =====\n_s22(useGenerateReport, \"YK0wzM21ECnncaq5SECwU+/SVdQ=\", false, function () {\n  return [useQueryClient, useMutation];\n});\nexport const useApiHealth = () => {\n  _s23();\n  return useQuery({\n    queryKey: ['api', 'health'],\n    queryFn: () => DentFlowAPI.healthCheck(),\n    staleTime: 30 * 1000,\n    refetchInterval: 60 * 1000\n  });\n};\n\n// Custom hook for optimistic updates\n_s23(useApiHealth, \"4ZpngI1uv+Uo3WQHEZmTQ5FNM+k=\", false, function () {\n  return [useQuery];\n});\nexport const useOptimisticUpdate = (queryKey, updateFn) => {\n  _s24();\n  const queryClient = useQueryClient();\n  const updateOptimistically = updateData => {\n    queryClient.setQueryData(queryKey, old => old ? updateFn(old) : old);\n  };\n  const revert = () => {\n    queryClient.invalidateQueries({\n      queryKey\n    });\n  };\n  return {\n    updateOptimistically,\n    revert\n  };\n};\n_s24(useOptimisticUpdate, \"4R+oYVB2Uc11P7bp1KcuhpkfaTw=\", false, function () {\n  return [useQueryClient];\n});", "map": {"version": 3, "names": ["useQuery", "useMutation", "useQueryClient", "DentFlowAPI", "query<PERSON>eys", "cases", "case", "id", "casesByStatus", "status", "overdueCases", "dashboardStats", "tasks", "technicians", "workflowStages", "invoices", "invoice", "appointments", "appointment", "reports", "currentUser", "useCurrentUser", "options", "_s", "query<PERSON><PERSON>", "queryFn", "auth", "getCurrentUser", "enabled", "isAuthenticated", "staleTime", "useLogin", "_s2", "queryClient", "mutationFn", "email", "password", "login", "onSuccess", "data", "setQueryData", "user", "invalidateQueries", "useLogout", "_s3", "logout", "clear", "useCases", "_s4", "getCases", "useCase", "_s5", "getCase", "useCasesByStatus", "_s6", "getCasesByStatus", "useOverdueCases", "_s7", "getOverdueCases", "useCreateCase", "_s8", "createCase", "useUpdateCase", "_s9", "updateCase", "updatedCase", "useAdvanceCase", "_s0", "notes", "advanceCase", "useAssignTechnician", "_s1", "caseId", "technicianId", "assignTechnician", "technician_id", "useDashboardStats", "_s10", "getDashboardStats", "refetchInterval", "useTasks", "filters", "_s11", "workflow", "getTasks", "useTechnicians", "_s12", "getTechnicians", "useWorkflowStages", "_s13", "getWorkflowStages", "useStartTask", "_s14", "taskId", "startTask", "useCompleteTask", "_s15", "completeTask", "useInvoices", "_s16", "billing", "getInvoices", "useInvoice", "_s17", "getInvoiceById", "useCreateInvoice", "_s18", "createInvoice", "useAppointments", "_s19", "schedule", "getAppointments", "useCreateAppointment", "_s20", "createAppointment", "useReports", "_s21", "getReports", "useGenerateReport", "_s22", "generateReport", "useApiHealth", "_s23", "healthCheck", "useOptimisticUpdate", "updateFn", "_s24", "updateOptimistically", "updateData", "old", "revert"], "sources": ["C:/GPT4_PROJECTS/DENTAL_LAB_ARCHITECTURE_PATTERNS/dentflow/frontend/src/hooks/api.ts"], "sourcesContent": ["/**\n * React Query Hooks for DentFlow API\n * Provides data fetching, caching, and mutation hooks\n */\n\nimport { useQuery, useMutation, useQueryClient, UseQueryOptions, UseMutationOptions } from '@tanstack/react-query';\nimport { DentFlowAPI, Case, CreateCaseRequest, DashboardStats, Task, Technician, Invoice, Appointment } from '../api';\nimport { casesService, Case as ApiCase, CreateCaseRequest as ApiCreateCaseRequest } from '../services/api';\n\n// Query keys for React Query\nexport const queryKeys = {\n  cases: ['cases'] as const,\n  case: (id: string) => ['cases', id] as const,\n  casesByStatus: (status: string) => ['cases', 'status', status] as const,\n  overdueCases: ['cases', 'overdue'] as const,\n  dashboardStats: ['dashboard', 'stats'] as const,\n  tasks: ['tasks'] as const,\n  technicians: ['technicians'] as const,\n  workflowStages: ['workflow', 'stages'] as const,\n  invoices: ['invoices'] as const,\n  invoice: (id: string) => ['invoices', id] as const,\n  appointments: ['appointments'] as const,\n  appointment: (id: string) => ['appointments', id] as const,\n  reports: ['reports'] as const,\n  currentUser: ['auth', 'current-user'] as const,\n};\n\n// ===== AUTHENTICATION HOOKS =====\n\nexport const useCurrentUser = (options?: UseQueryOptions) => {\n  return useQuery({\n    queryKey: queryKeys.currentUser,\n    queryFn: () => DentFlowAPI.auth.getCurrentUser(),\n    enabled: DentFlowAPI.auth.isAuthenticated(),\n    staleTime: 5 * 60 * 1000, // 5 minutes\n    ...options,\n  });\n};\n\nexport const useLogin = (options?: UseMutationOptions<any, Error, { email: string; password: string }>) => {\n  const queryClient = useQueryClient();\n  \n  return useMutation({\n    mutationFn: ({ email, password }) => DentFlowAPI.auth.login({ email, password }),\n    onSuccess: (data) => {\n      queryClient.setQueryData(queryKeys.currentUser, data.user);\n      queryClient.invalidateQueries({ queryKey: ['auth'] });\n    },\n    ...options,\n  });\n};\n\nexport const useLogout = (options?: UseMutationOptions) => {\n  const queryClient = useQueryClient();\n  \n  return useMutation({\n    mutationFn: () => DentFlowAPI.auth.logout(),\n    onSuccess: () => {\n      queryClient.clear();\n    },\n    ...options,\n  });\n};\n\n// ===== CASES HOOKS =====\n\nexport const useCases = (options?: UseQueryOptions<Case[]>) => {\n  return useQuery({\n    queryKey: queryKeys.cases,\n    queryFn: () => DentFlowAPI.cases.getCases(),\n    staleTime: 2 * 60 * 1000, // 2 minutes\n    ...options,\n  });\n};\n\nexport const useCase = (id: string, options?: UseQueryOptions<Case>) => {\n  return useQuery({\n    queryKey: queryKeys.case(id),\n    queryFn: () => DentFlowAPI.cases.getCase(id),\n    enabled: !!id,\n    staleTime: 1 * 60 * 1000, // 1 minute\n    ...options,\n  });\n};\n\nexport const useCasesByStatus = (status: string, options?: UseQueryOptions<Case[]>) => {\n  return useQuery({\n    queryKey: queryKeys.casesByStatus(status),\n    queryFn: () => DentFlowAPI.cases.getCasesByStatus(status),\n    enabled: !!status,\n    staleTime: 1 * 60 * 1000,\n    ...options,\n  });\n};\n\nexport const useOverdueCases = (options?: UseQueryOptions<Case[]>) => {\n  return useQuery({\n    queryKey: queryKeys.overdueCases,\n    queryFn: () => DentFlowAPI.cases.getOverdueCases(),\n    staleTime: 30 * 1000, // 30 seconds (more critical data)\n    ...options,\n  });\n};\n\nexport const useCreateCase = (options?: UseMutationOptions<Case, Error, CreateCaseRequest>) => {\n  const queryClient = useQueryClient();\n  \n  return useMutation({\n    mutationFn: (data: CreateCaseRequest) => DentFlowAPI.cases.createCase(data),\n    onSuccess: () => {\n      queryClient.invalidateQueries({ queryKey: queryKeys.cases });\n      queryClient.invalidateQueries({ queryKey: queryKeys.dashboardStats });\n    },\n    ...options,\n  });\n};\n\nexport const useUpdateCase = (options?: UseMutationOptions<Case, Error, { id: string; data: Partial<Case> }>) => {\n  const queryClient = useQueryClient();\n  \n  return useMutation({\n    mutationFn: ({ id, data }) => DentFlowAPI.cases.updateCase(id, data),\n    onSuccess: (updatedCase) => {\n      queryClient.setQueryData(queryKeys.case(updatedCase.id), updatedCase);\n      queryClient.invalidateQueries({ queryKey: queryKeys.cases });\n      queryClient.invalidateQueries({ queryKey: queryKeys.dashboardStats });\n    },\n    ...options,\n  });\n};\n\nexport const useAdvanceCase = (options?: UseMutationOptions<Case, Error, { id: string; notes?: string }>) => {\n  const queryClient = useQueryClient();\n  \n  return useMutation({\n    mutationFn: ({ id, notes }) => DentFlowAPI.cases.advanceCase(id, { notes }),\n    onSuccess: (updatedCase) => {\n      queryClient.setQueryData(queryKeys.case(updatedCase.id), updatedCase);\n      queryClient.invalidateQueries({ queryKey: queryKeys.cases });\n      queryClient.invalidateQueries({ queryKey: queryKeys.tasks });\n    },\n    ...options,\n  });\n};\n\nexport const useAssignTechnician = (options?: UseMutationOptions<Case, Error, { caseId: string; technicianId: string }>) => {\n  const queryClient = useQueryClient();\n  \n  return useMutation({\n    mutationFn: ({ caseId, technicianId }) => DentFlowAPI.cases.assignTechnician(caseId, { technician_id: technicianId }),\n    onSuccess: (updatedCase) => {\n      queryClient.setQueryData(queryKeys.case(updatedCase.id), updatedCase);\n      queryClient.invalidateQueries({ queryKey: queryKeys.cases });\n      queryClient.invalidateQueries({ queryKey: queryKeys.tasks });\n    },\n    ...options,\n  });\n};\n\n// ===== DASHBOARD HOOKS =====\n\nexport const useDashboardStats = (options?: UseQueryOptions<DashboardStats>) => {\n  return useQuery({\n    queryKey: queryKeys.dashboardStats,\n    queryFn: () => DentFlowAPI.cases.getDashboardStats(),\n    staleTime: 30 * 1000, // 30 seconds\n    refetchInterval: 60 * 1000, // Refresh every minute\n    ...options,\n  });\n};\n\n// ===== WORKFLOW HOOKS =====\n\nexport const useTasks = (filters?: any, options?: UseQueryOptions<Task[]>) => {\n  return useQuery({\n    queryKey: [...queryKeys.tasks, filters],\n    queryFn: () => DentFlowAPI.workflow.getTasks(filters),\n    staleTime: 1 * 60 * 1000,\n    ...options,\n  });\n};\n\nexport const useTechnicians = (options?: UseQueryOptions<Technician[]>) => {\n  return useQuery({\n    queryKey: queryKeys.technicians,\n    queryFn: () => DentFlowAPI.workflow.getTechnicians(),\n    staleTime: 5 * 60 * 1000, // 5 minutes (technician data changes less frequently)\n    ...options,\n  });\n};\n\nexport const useWorkflowStages = (options?: UseQueryOptions) => {\n  return useQuery({\n    queryKey: queryKeys.workflowStages,\n    queryFn: () => DentFlowAPI.workflow.getWorkflowStages(),\n    staleTime: 10 * 60 * 1000, // 10 minutes (rarely changes)\n    ...options,\n  });\n};\n\nexport const useStartTask = (options?: UseMutationOptions<Task, Error, string>) => {\n  const queryClient = useQueryClient();\n  \n  return useMutation({\n    mutationFn: (taskId: string) => DentFlowAPI.workflow.startTask(taskId),\n    onSuccess: () => {\n      queryClient.invalidateQueries({ queryKey: queryKeys.tasks });\n    },\n    ...options,\n  });\n};\n\nexport const useCompleteTask = (options?: UseMutationOptions<Task, Error, { taskId: string; notes?: string }>) => {\n  const queryClient = useQueryClient();\n  \n  return useMutation({\n    mutationFn: ({ taskId, notes }) => DentFlowAPI.workflow.completeTask(taskId, notes),\n    onSuccess: () => {\n      queryClient.invalidateQueries({ queryKey: queryKeys.tasks });\n      queryClient.invalidateQueries({ queryKey: queryKeys.cases });\n    },\n    ...options,\n  });\n};\n\n// ===== BILLING HOOKS =====\n\nexport const useInvoices = (filters?: any, options?: UseQueryOptions<Invoice[]>) => {\n  return useQuery({\n    queryKey: [...queryKeys.invoices, filters],\n    queryFn: () => DentFlowAPI.billing.getInvoices(filters),\n    staleTime: 2 * 60 * 1000,\n    ...options,\n  });\n};\n\nexport const useInvoice = (id: string, options?: UseQueryOptions<Invoice>) => {\n  return useQuery({\n    queryKey: queryKeys.invoice(id),\n    queryFn: () => DentFlowAPI.billing.getInvoiceById(id),\n    enabled: !!id,\n    staleTime: 1 * 60 * 1000,\n    ...options,\n  });\n};\n\nexport const useCreateInvoice = (options?: UseMutationOptions<Invoice, Error, any>) => {\n  const queryClient = useQueryClient();\n  \n  return useMutation({\n    mutationFn: (data) => DentFlowAPI.billing.createInvoice(data),\n    onSuccess: () => {\n      queryClient.invalidateQueries({ queryKey: queryKeys.invoices });\n    },\n    ...options,\n  });\n};\n\n// ===== SCHEDULE HOOKS =====\n\nexport const useAppointments = (filters?: any, options?: UseQueryOptions<Appointment[]>) => {\n  return useQuery({\n    queryKey: [...queryKeys.appointments, filters],\n    queryFn: () => DentFlowAPI.schedule.getAppointments(filters),\n    staleTime: 1 * 60 * 1000,\n    ...options,\n  });\n};\n\nexport const useCreateAppointment = (options?: UseMutationOptions<Appointment, Error, any>) => {\n  const queryClient = useQueryClient();\n  \n  return useMutation({\n    mutationFn: (data) => DentFlowAPI.schedule.createAppointment(data),\n    onSuccess: () => {\n      queryClient.invalidateQueries({ queryKey: queryKeys.appointments });\n    },\n    ...options,\n  });\n};\n\n// ===== REPORTS HOOKS =====\n\nexport const useReports = (options?: UseQueryOptions) => {\n  return useQuery({\n    queryKey: queryKeys.reports,\n    queryFn: () => DentFlowAPI.reports.getReports(),\n    staleTime: 5 * 60 * 1000,\n    ...options,\n  });\n};\n\nexport const useGenerateReport = (options?: UseMutationOptions) => {\n  const queryClient = useQueryClient();\n  \n  return useMutation({\n    mutationFn: (data: any) => DentFlowAPI.reports.generateReport(data),\n    onSuccess: () => {\n      queryClient.invalidateQueries({ queryKey: queryKeys.reports });\n    },\n    ...options,\n  });\n};\n\n// ===== UTILITY HOOKS =====\n\nexport const useApiHealth = () => {\n  return useQuery({\n    queryKey: ['api', 'health'],\n    queryFn: () => DentFlowAPI.healthCheck(),\n    staleTime: 30 * 1000,\n    refetchInterval: 60 * 1000,\n  });\n};\n\n// Custom hook for optimistic updates\nexport const useOptimisticUpdate = <T>(queryKey: any[], updateFn: (old: T) => T) => {\n  const queryClient = useQueryClient();\n  \n  const updateOptimistically = (updateData: any) => {\n    queryClient.setQueryData(queryKey, (old: T | undefined) => old ? updateFn(old) : old);\n  };\n  \n  const revert = () => {\n    queryClient.invalidateQueries({ queryKey });\n  };\n  \n  return { updateOptimistically, revert };\n};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;AACA;AACA;AACA;;AAEA,SAASA,QAAQ,EAAEC,WAAW,EAAEC,cAAc,QAA6C,uBAAuB;AAClH,SAASC,WAAW,QAAyF,QAAQ;AAGrH;AACA,OAAO,MAAMC,SAAS,GAAG;EACvBC,KAAK,EAAE,CAAC,OAAO,CAAU;EACzBC,IAAI,EAAGC,EAAU,IAAK,CAAC,OAAO,EAAEA,EAAE,CAAU;EAC5CC,aAAa,EAAGC,MAAc,IAAK,CAAC,OAAO,EAAE,QAAQ,EAAEA,MAAM,CAAU;EACvEC,YAAY,EAAE,CAAC,OAAO,EAAE,SAAS,CAAU;EAC3CC,cAAc,EAAE,CAAC,WAAW,EAAE,OAAO,CAAU;EAC/CC,KAAK,EAAE,CAAC,OAAO,CAAU;EACzBC,WAAW,EAAE,CAAC,aAAa,CAAU;EACrCC,cAAc,EAAE,CAAC,UAAU,EAAE,QAAQ,CAAU;EAC/CC,QAAQ,EAAE,CAAC,UAAU,CAAU;EAC/BC,OAAO,EAAGT,EAAU,IAAK,CAAC,UAAU,EAAEA,EAAE,CAAU;EAClDU,YAAY,EAAE,CAAC,cAAc,CAAU;EACvCC,WAAW,EAAGX,EAAU,IAAK,CAAC,cAAc,EAAEA,EAAE,CAAU;EAC1DY,OAAO,EAAE,CAAC,SAAS,CAAU;EAC7BC,WAAW,EAAE,CAAC,MAAM,EAAE,cAAc;AACtC,CAAC;;AAED;;AAEA,OAAO,MAAMC,cAAc,GAAIC,OAAyB,IAAK;EAAAC,EAAA;EAC3D,OAAOvB,QAAQ,CAAC;IACdwB,QAAQ,EAAEpB,SAAS,CAACgB,WAAW;IAC/BK,OAAO,EAAEA,CAAA,KAAMtB,WAAW,CAACuB,IAAI,CAACC,cAAc,CAAC,CAAC;IAChDC,OAAO,EAAEzB,WAAW,CAACuB,IAAI,CAACG,eAAe,CAAC,CAAC;IAC3CC,SAAS,EAAE,CAAC,GAAG,EAAE,GAAG,IAAI;IAAE;IAC1B,GAAGR;EACL,CAAC,CAAC;AACJ,CAAC;AAACC,EAAA,CARWF,cAAc;EAAA,QAClBrB,QAAQ;AAAA;AASjB,OAAO,MAAM+B,QAAQ,GAAIT,OAA6E,IAAK;EAAAU,GAAA;EACzG,MAAMC,WAAW,GAAG/B,cAAc,CAAC,CAAC;EAEpC,OAAOD,WAAW,CAAC;IACjBiC,UAAU,EAAEA,CAAC;MAAEC,KAAK;MAAEC;IAAS,CAAC,KAAKjC,WAAW,CAACuB,IAAI,CAACW,KAAK,CAAC;MAAEF,KAAK;MAAEC;IAAS,CAAC,CAAC;IAChFE,SAAS,EAAGC,IAAI,IAAK;MACnBN,WAAW,CAACO,YAAY,CAACpC,SAAS,CAACgB,WAAW,EAAEmB,IAAI,CAACE,IAAI,CAAC;MAC1DR,WAAW,CAACS,iBAAiB,CAAC;QAAElB,QAAQ,EAAE,CAAC,MAAM;MAAE,CAAC,CAAC;IACvD,CAAC;IACD,GAAGF;EACL,CAAC,CAAC;AACJ,CAAC;AAACU,GAAA,CAXWD,QAAQ;EAAA,QACC7B,cAAc,EAE3BD,WAAW;AAAA;AAUpB,OAAO,MAAM0C,SAAS,GAAIrB,OAA4B,IAAK;EAAAsB,GAAA;EACzD,MAAMX,WAAW,GAAG/B,cAAc,CAAC,CAAC;EAEpC,OAAOD,WAAW,CAAC;IACjBiC,UAAU,EAAEA,CAAA,KAAM/B,WAAW,CAACuB,IAAI,CAACmB,MAAM,CAAC,CAAC;IAC3CP,SAAS,EAAEA,CAAA,KAAM;MACfL,WAAW,CAACa,KAAK,CAAC,CAAC;IACrB,CAAC;IACD,GAAGxB;EACL,CAAC,CAAC;AACJ,CAAC;;AAED;AAAAsB,GAAA,CAZaD,SAAS;EAAA,QACAzC,cAAc,EAE3BD,WAAW;AAAA;AAWpB,OAAO,MAAM8C,QAAQ,GAAIzB,OAAiC,IAAK;EAAA0B,GAAA;EAC7D,OAAOhD,QAAQ,CAAC;IACdwB,QAAQ,EAAEpB,SAAS,CAACC,KAAK;IACzBoB,OAAO,EAAEA,CAAA,KAAMtB,WAAW,CAACE,KAAK,CAAC4C,QAAQ,CAAC,CAAC;IAC3CnB,SAAS,EAAE,CAAC,GAAG,EAAE,GAAG,IAAI;IAAE;IAC1B,GAAGR;EACL,CAAC,CAAC;AACJ,CAAC;AAAC0B,GAAA,CAPWD,QAAQ;EAAA,QACZ/C,QAAQ;AAAA;AAQjB,OAAO,MAAMkD,OAAO,GAAGA,CAAC3C,EAAU,EAAEe,OAA+B,KAAK;EAAA6B,GAAA;EACtE,OAAOnD,QAAQ,CAAC;IACdwB,QAAQ,EAAEpB,SAAS,CAACE,IAAI,CAACC,EAAE,CAAC;IAC5BkB,OAAO,EAAEA,CAAA,KAAMtB,WAAW,CAACE,KAAK,CAAC+C,OAAO,CAAC7C,EAAE,CAAC;IAC5CqB,OAAO,EAAE,CAAC,CAACrB,EAAE;IACbuB,SAAS,EAAE,CAAC,GAAG,EAAE,GAAG,IAAI;IAAE;IAC1B,GAAGR;EACL,CAAC,CAAC;AACJ,CAAC;AAAC6B,GAAA,CARWD,OAAO;EAAA,QACXlD,QAAQ;AAAA;AASjB,OAAO,MAAMqD,gBAAgB,GAAGA,CAAC5C,MAAc,EAAEa,OAAiC,KAAK;EAAAgC,GAAA;EACrF,OAAOtD,QAAQ,CAAC;IACdwB,QAAQ,EAAEpB,SAAS,CAACI,aAAa,CAACC,MAAM,CAAC;IACzCgB,OAAO,EAAEA,CAAA,KAAMtB,WAAW,CAACE,KAAK,CAACkD,gBAAgB,CAAC9C,MAAM,CAAC;IACzDmB,OAAO,EAAE,CAAC,CAACnB,MAAM;IACjBqB,SAAS,EAAE,CAAC,GAAG,EAAE,GAAG,IAAI;IACxB,GAAGR;EACL,CAAC,CAAC;AACJ,CAAC;AAACgC,GAAA,CARWD,gBAAgB;EAAA,QACpBrD,QAAQ;AAAA;AASjB,OAAO,MAAMwD,eAAe,GAAIlC,OAAiC,IAAK;EAAAmC,GAAA;EACpE,OAAOzD,QAAQ,CAAC;IACdwB,QAAQ,EAAEpB,SAAS,CAACM,YAAY;IAChCe,OAAO,EAAEA,CAAA,KAAMtB,WAAW,CAACE,KAAK,CAACqD,eAAe,CAAC,CAAC;IAClD5B,SAAS,EAAE,EAAE,GAAG,IAAI;IAAE;IACtB,GAAGR;EACL,CAAC,CAAC;AACJ,CAAC;AAACmC,GAAA,CAPWD,eAAe;EAAA,QACnBxD,QAAQ;AAAA;AAQjB,OAAO,MAAM2D,aAAa,GAAIrC,OAA4D,IAAK;EAAAsC,GAAA;EAC7F,MAAM3B,WAAW,GAAG/B,cAAc,CAAC,CAAC;EAEpC,OAAOD,WAAW,CAAC;IACjBiC,UAAU,EAAGK,IAAuB,IAAKpC,WAAW,CAACE,KAAK,CAACwD,UAAU,CAACtB,IAAI,CAAC;IAC3ED,SAAS,EAAEA,CAAA,KAAM;MACfL,WAAW,CAACS,iBAAiB,CAAC;QAAElB,QAAQ,EAAEpB,SAAS,CAACC;MAAM,CAAC,CAAC;MAC5D4B,WAAW,CAACS,iBAAiB,CAAC;QAAElB,QAAQ,EAAEpB,SAAS,CAACO;MAAe,CAAC,CAAC;IACvE,CAAC;IACD,GAAGW;EACL,CAAC,CAAC;AACJ,CAAC;AAACsC,GAAA,CAXWD,aAAa;EAAA,QACJzD,cAAc,EAE3BD,WAAW;AAAA;AAUpB,OAAO,MAAM6D,aAAa,GAAIxC,OAA8E,IAAK;EAAAyC,GAAA;EAC/G,MAAM9B,WAAW,GAAG/B,cAAc,CAAC,CAAC;EAEpC,OAAOD,WAAW,CAAC;IACjBiC,UAAU,EAAEA,CAAC;MAAE3B,EAAE;MAAEgC;IAAK,CAAC,KAAKpC,WAAW,CAACE,KAAK,CAAC2D,UAAU,CAACzD,EAAE,EAAEgC,IAAI,CAAC;IACpED,SAAS,EAAG2B,WAAW,IAAK;MAC1BhC,WAAW,CAACO,YAAY,CAACpC,SAAS,CAACE,IAAI,CAAC2D,WAAW,CAAC1D,EAAE,CAAC,EAAE0D,WAAW,CAAC;MACrEhC,WAAW,CAACS,iBAAiB,CAAC;QAAElB,QAAQ,EAAEpB,SAAS,CAACC;MAAM,CAAC,CAAC;MAC5D4B,WAAW,CAACS,iBAAiB,CAAC;QAAElB,QAAQ,EAAEpB,SAAS,CAACO;MAAe,CAAC,CAAC;IACvE,CAAC;IACD,GAAGW;EACL,CAAC,CAAC;AACJ,CAAC;AAACyC,GAAA,CAZWD,aAAa;EAAA,QACJ5D,cAAc,EAE3BD,WAAW;AAAA;AAWpB,OAAO,MAAMiE,cAAc,GAAI5C,OAAyE,IAAK;EAAA6C,GAAA;EAC3G,MAAMlC,WAAW,GAAG/B,cAAc,CAAC,CAAC;EAEpC,OAAOD,WAAW,CAAC;IACjBiC,UAAU,EAAEA,CAAC;MAAE3B,EAAE;MAAE6D;IAAM,CAAC,KAAKjE,WAAW,CAACE,KAAK,CAACgE,WAAW,CAAC9D,EAAE,EAAE;MAAE6D;IAAM,CAAC,CAAC;IAC3E9B,SAAS,EAAG2B,WAAW,IAAK;MAC1BhC,WAAW,CAACO,YAAY,CAACpC,SAAS,CAACE,IAAI,CAAC2D,WAAW,CAAC1D,EAAE,CAAC,EAAE0D,WAAW,CAAC;MACrEhC,WAAW,CAACS,iBAAiB,CAAC;QAAElB,QAAQ,EAAEpB,SAAS,CAACC;MAAM,CAAC,CAAC;MAC5D4B,WAAW,CAACS,iBAAiB,CAAC;QAAElB,QAAQ,EAAEpB,SAAS,CAACQ;MAAM,CAAC,CAAC;IAC9D,CAAC;IACD,GAAGU;EACL,CAAC,CAAC;AACJ,CAAC;AAAC6C,GAAA,CAZWD,cAAc;EAAA,QACLhE,cAAc,EAE3BD,WAAW;AAAA;AAWpB,OAAO,MAAMqE,mBAAmB,GAAIhD,OAAmF,IAAK;EAAAiD,GAAA;EAC1H,MAAMtC,WAAW,GAAG/B,cAAc,CAAC,CAAC;EAEpC,OAAOD,WAAW,CAAC;IACjBiC,UAAU,EAAEA,CAAC;MAAEsC,MAAM;MAAEC;IAAa,CAAC,KAAKtE,WAAW,CAACE,KAAK,CAACqE,gBAAgB,CAACF,MAAM,EAAE;MAAEG,aAAa,EAAEF;IAAa,CAAC,CAAC;IACrHnC,SAAS,EAAG2B,WAAW,IAAK;MAC1BhC,WAAW,CAACO,YAAY,CAACpC,SAAS,CAACE,IAAI,CAAC2D,WAAW,CAAC1D,EAAE,CAAC,EAAE0D,WAAW,CAAC;MACrEhC,WAAW,CAACS,iBAAiB,CAAC;QAAElB,QAAQ,EAAEpB,SAAS,CAACC;MAAM,CAAC,CAAC;MAC5D4B,WAAW,CAACS,iBAAiB,CAAC;QAAElB,QAAQ,EAAEpB,SAAS,CAACQ;MAAM,CAAC,CAAC;IAC9D,CAAC;IACD,GAAGU;EACL,CAAC,CAAC;AACJ,CAAC;;AAED;AAAAiD,GAAA,CAdaD,mBAAmB;EAAA,QACVpE,cAAc,EAE3BD,WAAW;AAAA;AAapB,OAAO,MAAM2E,iBAAiB,GAAItD,OAAyC,IAAK;EAAAuD,IAAA;EAC9E,OAAO7E,QAAQ,CAAC;IACdwB,QAAQ,EAAEpB,SAAS,CAACO,cAAc;IAClCc,OAAO,EAAEA,CAAA,KAAMtB,WAAW,CAACE,KAAK,CAACyE,iBAAiB,CAAC,CAAC;IACpDhD,SAAS,EAAE,EAAE,GAAG,IAAI;IAAE;IACtBiD,eAAe,EAAE,EAAE,GAAG,IAAI;IAAE;IAC5B,GAAGzD;EACL,CAAC,CAAC;AACJ,CAAC;;AAED;AAAAuD,IAAA,CAVaD,iBAAiB;EAAA,QACrB5E,QAAQ;AAAA;AAWjB,OAAO,MAAMgF,QAAQ,GAAGA,CAACC,OAAa,EAAE3D,OAAiC,KAAK;EAAA4D,IAAA;EAC5E,OAAOlF,QAAQ,CAAC;IACdwB,QAAQ,EAAE,CAAC,GAAGpB,SAAS,CAACQ,KAAK,EAAEqE,OAAO,CAAC;IACvCxD,OAAO,EAAEA,CAAA,KAAMtB,WAAW,CAACgF,QAAQ,CAACC,QAAQ,CAACH,OAAO,CAAC;IACrDnD,SAAS,EAAE,CAAC,GAAG,EAAE,GAAG,IAAI;IACxB,GAAGR;EACL,CAAC,CAAC;AACJ,CAAC;AAAC4D,IAAA,CAPWF,QAAQ;EAAA,QACZhF,QAAQ;AAAA;AAQjB,OAAO,MAAMqF,cAAc,GAAI/D,OAAuC,IAAK;EAAAgE,IAAA;EACzE,OAAOtF,QAAQ,CAAC;IACdwB,QAAQ,EAAEpB,SAAS,CAACS,WAAW;IAC/BY,OAAO,EAAEA,CAAA,KAAMtB,WAAW,CAACgF,QAAQ,CAACI,cAAc,CAAC,CAAC;IACpDzD,SAAS,EAAE,CAAC,GAAG,EAAE,GAAG,IAAI;IAAE;IAC1B,GAAGR;EACL,CAAC,CAAC;AACJ,CAAC;AAACgE,IAAA,CAPWD,cAAc;EAAA,QAClBrF,QAAQ;AAAA;AAQjB,OAAO,MAAMwF,iBAAiB,GAAIlE,OAAyB,IAAK;EAAAmE,IAAA;EAC9D,OAAOzF,QAAQ,CAAC;IACdwB,QAAQ,EAAEpB,SAAS,CAACU,cAAc;IAClCW,OAAO,EAAEA,CAAA,KAAMtB,WAAW,CAACgF,QAAQ,CAACO,iBAAiB,CAAC,CAAC;IACvD5D,SAAS,EAAE,EAAE,GAAG,EAAE,GAAG,IAAI;IAAE;IAC3B,GAAGR;EACL,CAAC,CAAC;AACJ,CAAC;AAACmE,IAAA,CAPWD,iBAAiB;EAAA,QACrBxF,QAAQ;AAAA;AAQjB,OAAO,MAAM2F,YAAY,GAAIrE,OAAiD,IAAK;EAAAsE,IAAA;EACjF,MAAM3D,WAAW,GAAG/B,cAAc,CAAC,CAAC;EAEpC,OAAOD,WAAW,CAAC;IACjBiC,UAAU,EAAG2D,MAAc,IAAK1F,WAAW,CAACgF,QAAQ,CAACW,SAAS,CAACD,MAAM,CAAC;IACtEvD,SAAS,EAAEA,CAAA,KAAM;MACfL,WAAW,CAACS,iBAAiB,CAAC;QAAElB,QAAQ,EAAEpB,SAAS,CAACQ;MAAM,CAAC,CAAC;IAC9D,CAAC;IACD,GAAGU;EACL,CAAC,CAAC;AACJ,CAAC;AAACsE,IAAA,CAVWD,YAAY;EAAA,QACHzF,cAAc,EAE3BD,WAAW;AAAA;AASpB,OAAO,MAAM8F,eAAe,GAAIzE,OAA6E,IAAK;EAAA0E,IAAA;EAChH,MAAM/D,WAAW,GAAG/B,cAAc,CAAC,CAAC;EAEpC,OAAOD,WAAW,CAAC;IACjBiC,UAAU,EAAEA,CAAC;MAAE2D,MAAM;MAAEzB;IAAM,CAAC,KAAKjE,WAAW,CAACgF,QAAQ,CAACc,YAAY,CAACJ,MAAM,EAAEzB,KAAK,CAAC;IACnF9B,SAAS,EAAEA,CAAA,KAAM;MACfL,WAAW,CAACS,iBAAiB,CAAC;QAAElB,QAAQ,EAAEpB,SAAS,CAACQ;MAAM,CAAC,CAAC;MAC5DqB,WAAW,CAACS,iBAAiB,CAAC;QAAElB,QAAQ,EAAEpB,SAAS,CAACC;MAAM,CAAC,CAAC;IAC9D,CAAC;IACD,GAAGiB;EACL,CAAC,CAAC;AACJ,CAAC;;AAED;AAAA0E,IAAA,CAbaD,eAAe;EAAA,QACN7F,cAAc,EAE3BD,WAAW;AAAA;AAYpB,OAAO,MAAMiG,WAAW,GAAGA,CAACjB,OAAa,EAAE3D,OAAoC,KAAK;EAAA6E,IAAA;EAClF,OAAOnG,QAAQ,CAAC;IACdwB,QAAQ,EAAE,CAAC,GAAGpB,SAAS,CAACW,QAAQ,EAAEkE,OAAO,CAAC;IAC1CxD,OAAO,EAAEA,CAAA,KAAMtB,WAAW,CAACiG,OAAO,CAACC,WAAW,CAACpB,OAAO,CAAC;IACvDnD,SAAS,EAAE,CAAC,GAAG,EAAE,GAAG,IAAI;IACxB,GAAGR;EACL,CAAC,CAAC;AACJ,CAAC;AAAC6E,IAAA,CAPWD,WAAW;EAAA,QACflG,QAAQ;AAAA;AAQjB,OAAO,MAAMsG,UAAU,GAAGA,CAAC/F,EAAU,EAAEe,OAAkC,KAAK;EAAAiF,IAAA;EAC5E,OAAOvG,QAAQ,CAAC;IACdwB,QAAQ,EAAEpB,SAAS,CAACY,OAAO,CAACT,EAAE,CAAC;IAC/BkB,OAAO,EAAEA,CAAA,KAAMtB,WAAW,CAACiG,OAAO,CAACI,cAAc,CAACjG,EAAE,CAAC;IACrDqB,OAAO,EAAE,CAAC,CAACrB,EAAE;IACbuB,SAAS,EAAE,CAAC,GAAG,EAAE,GAAG,IAAI;IACxB,GAAGR;EACL,CAAC,CAAC;AACJ,CAAC;AAACiF,IAAA,CARWD,UAAU;EAAA,QACdtG,QAAQ;AAAA;AASjB,OAAO,MAAMyG,gBAAgB,GAAInF,OAAiD,IAAK;EAAAoF,IAAA;EACrF,MAAMzE,WAAW,GAAG/B,cAAc,CAAC,CAAC;EAEpC,OAAOD,WAAW,CAAC;IACjBiC,UAAU,EAAGK,IAAI,IAAKpC,WAAW,CAACiG,OAAO,CAACO,aAAa,CAACpE,IAAI,CAAC;IAC7DD,SAAS,EAAEA,CAAA,KAAM;MACfL,WAAW,CAACS,iBAAiB,CAAC;QAAElB,QAAQ,EAAEpB,SAAS,CAACW;MAAS,CAAC,CAAC;IACjE,CAAC;IACD,GAAGO;EACL,CAAC,CAAC;AACJ,CAAC;;AAED;AAAAoF,IAAA,CAZaD,gBAAgB;EAAA,QACPvG,cAAc,EAE3BD,WAAW;AAAA;AAWpB,OAAO,MAAM2G,eAAe,GAAGA,CAAC3B,OAAa,EAAE3D,OAAwC,KAAK;EAAAuF,IAAA;EAC1F,OAAO7G,QAAQ,CAAC;IACdwB,QAAQ,EAAE,CAAC,GAAGpB,SAAS,CAACa,YAAY,EAAEgE,OAAO,CAAC;IAC9CxD,OAAO,EAAEA,CAAA,KAAMtB,WAAW,CAAC2G,QAAQ,CAACC,eAAe,CAAC9B,OAAO,CAAC;IAC5DnD,SAAS,EAAE,CAAC,GAAG,EAAE,GAAG,IAAI;IACxB,GAAGR;EACL,CAAC,CAAC;AACJ,CAAC;AAACuF,IAAA,CAPWD,eAAe;EAAA,QACnB5G,QAAQ;AAAA;AAQjB,OAAO,MAAMgH,oBAAoB,GAAI1F,OAAqD,IAAK;EAAA2F,IAAA;EAC7F,MAAMhF,WAAW,GAAG/B,cAAc,CAAC,CAAC;EAEpC,OAAOD,WAAW,CAAC;IACjBiC,UAAU,EAAGK,IAAI,IAAKpC,WAAW,CAAC2G,QAAQ,CAACI,iBAAiB,CAAC3E,IAAI,CAAC;IAClED,SAAS,EAAEA,CAAA,KAAM;MACfL,WAAW,CAACS,iBAAiB,CAAC;QAAElB,QAAQ,EAAEpB,SAAS,CAACa;MAAa,CAAC,CAAC;IACrE,CAAC;IACD,GAAGK;EACL,CAAC,CAAC;AACJ,CAAC;;AAED;AAAA2F,IAAA,CAZaD,oBAAoB;EAAA,QACX9G,cAAc,EAE3BD,WAAW;AAAA;AAWpB,OAAO,MAAMkH,UAAU,GAAI7F,OAAyB,IAAK;EAAA8F,IAAA;EACvD,OAAOpH,QAAQ,CAAC;IACdwB,QAAQ,EAAEpB,SAAS,CAACe,OAAO;IAC3BM,OAAO,EAAEA,CAAA,KAAMtB,WAAW,CAACgB,OAAO,CAACkG,UAAU,CAAC,CAAC;IAC/CvF,SAAS,EAAE,CAAC,GAAG,EAAE,GAAG,IAAI;IACxB,GAAGR;EACL,CAAC,CAAC;AACJ,CAAC;AAAC8F,IAAA,CAPWD,UAAU;EAAA,QACdnH,QAAQ;AAAA;AAQjB,OAAO,MAAMsH,iBAAiB,GAAIhG,OAA4B,IAAK;EAAAiG,IAAA;EACjE,MAAMtF,WAAW,GAAG/B,cAAc,CAAC,CAAC;EAEpC,OAAOD,WAAW,CAAC;IACjBiC,UAAU,EAAGK,IAAS,IAAKpC,WAAW,CAACgB,OAAO,CAACqG,cAAc,CAACjF,IAAI,CAAC;IACnED,SAAS,EAAEA,CAAA,KAAM;MACfL,WAAW,CAACS,iBAAiB,CAAC;QAAElB,QAAQ,EAAEpB,SAAS,CAACe;MAAQ,CAAC,CAAC;IAChE,CAAC;IACD,GAAGG;EACL,CAAC,CAAC;AACJ,CAAC;;AAED;AAAAiG,IAAA,CAZaD,iBAAiB;EAAA,QACRpH,cAAc,EAE3BD,WAAW;AAAA;AAWpB,OAAO,MAAMwH,YAAY,GAAGA,CAAA,KAAM;EAAAC,IAAA;EAChC,OAAO1H,QAAQ,CAAC;IACdwB,QAAQ,EAAE,CAAC,KAAK,EAAE,QAAQ,CAAC;IAC3BC,OAAO,EAAEA,CAAA,KAAMtB,WAAW,CAACwH,WAAW,CAAC,CAAC;IACxC7F,SAAS,EAAE,EAAE,GAAG,IAAI;IACpBiD,eAAe,EAAE,EAAE,GAAG;EACxB,CAAC,CAAC;AACJ,CAAC;;AAED;AAAA2C,IAAA,CATaD,YAAY;EAAA,QAChBzH,QAAQ;AAAA;AASjB,OAAO,MAAM4H,mBAAmB,GAAGA,CAAIpG,QAAe,EAAEqG,QAAuB,KAAK;EAAAC,IAAA;EAClF,MAAM7F,WAAW,GAAG/B,cAAc,CAAC,CAAC;EAEpC,MAAM6H,oBAAoB,GAAIC,UAAe,IAAK;IAChD/F,WAAW,CAACO,YAAY,CAAChB,QAAQ,EAAGyG,GAAkB,IAAKA,GAAG,GAAGJ,QAAQ,CAACI,GAAG,CAAC,GAAGA,GAAG,CAAC;EACvF,CAAC;EAED,MAAMC,MAAM,GAAGA,CAAA,KAAM;IACnBjG,WAAW,CAACS,iBAAiB,CAAC;MAAElB;IAAS,CAAC,CAAC;EAC7C,CAAC;EAED,OAAO;IAAEuG,oBAAoB;IAAEG;EAAO,CAAC;AACzC,CAAC;AAACJ,IAAA,CAZWF,mBAAmB;EAAA,QACV1H,cAAc;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}