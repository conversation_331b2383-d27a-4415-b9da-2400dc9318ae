#!/usr/bin/env python3
import os
import django

os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'dentflow_project.settings')
django.setup()

from django.contrib.auth.models import User

# Create superuser
user, created = User.objects.get_or_create(
    username='admin',
    defaults={
        'email': '<EMAIL>',
        'is_staff': True,
        'is_superuser': True,
        'first_name': 'Admin',
        'last_name': 'User'
    }
)

if created or not user.check_password('admin123'):
    user.set_password('admin123')
    user.save()
    print('✅ Admin user created/updated successfully')
    print('Username: admin')
    print('Password: admin123')
else:
    print('✅ Admin user already exists')
    print('Username: admin')
    print('Password: admin123')
