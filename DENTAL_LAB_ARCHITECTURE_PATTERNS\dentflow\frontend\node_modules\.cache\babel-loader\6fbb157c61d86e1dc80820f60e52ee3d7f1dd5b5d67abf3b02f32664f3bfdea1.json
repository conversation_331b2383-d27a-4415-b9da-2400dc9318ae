{"ast": null, "code": "var _jsxFileName = \"C:\\\\GPT4_PROJECTS\\\\DENTAL_LAB_ARCHITECTURE_PATTERNS\\\\dentflow\\\\frontend\\\\src\\\\components\\\\Dashboard.tsx\",\n  _s = $RefreshSig$();\n/**\n * DentFlow Dashboard Component - Enhanced Version\n * Comprehensive dashboard with advanced widgets, charts, and real-time data visualization\n */\n\nimport React, { useState, useEffect } from 'react';\nimport { CasesService } from '../api/casesService';\nimport { useAuth } from '../context/AuthContext';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Dashboard = () => {\n  _s();\n  const {\n    user\n  } = useAuth();\n  const [stats, setStats] = useState({\n    active_cases: 0,\n    completed_today: 0,\n    pending_qc: 0,\n    revenue_today: 0,\n    overdue_cases: 0\n  });\n  const [recentCases, setRecentCases] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState('');\n  const [backendStatus, setBackendStatus] = useState({\n    available: false,\n    mode: 'mock'\n  });\n  useEffect(() => {\n    loadDashboardData();\n  }, []);\n  const loadDashboardData = async () => {\n    try {\n      setLoading(true);\n      setError('');\n\n      // Check backend health first\n      await CasesService.checkBackendHealth();\n      const [dashboardStats, cases] = await Promise.all([CasesService.getDashboardStats(), CasesService.getCases()]);\n      setStats(dashboardStats);\n      setRecentCases(cases.slice(0, 5)); // Show last 5 cases\n      setBackendStatus(CasesService.getBackendStatus());\n    } catch (err) {\n      console.error('Error loading dashboard:', err);\n      setError('Failed to load some dashboard data');\n      setBackendStatus(CasesService.getBackendStatus());\n    } finally {\n      setLoading(false);\n    }\n  };\n  const formatCurrency = amount => {\n    return new Intl.NumberFormat('en-US', {\n      style: 'currency',\n      currency: 'USD'\n    }).format(amount);\n  };\n  const getStatusColor = status => {\n    const colors = {\n      'received': '#2196f3',\n      'design': '#ff9800',\n      'milling': '#9c27b0',\n      'sintering': '#e91e63',\n      'qc': '#ff5722',\n      'shipped': '#4caf50',\n      'delivered': '#8bc34a',\n      'cancelled': '#757575'\n    };\n    return colors[status] || '#666';\n  };\n  const getPriorityColor = priority => {\n    const colors = {\n      'low': '#4caf50',\n      'normal': '#2196f3',\n      'urgent': '#ff9800',\n      'stat': '#f44336'\n    };\n    return colors[priority] || '#666';\n  };\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        padding: '2rem',\n        textAlign: 'center'\n      },\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          fontSize: '1.2rem',\n          color: '#666'\n        },\n        children: \"Loading dashboard...\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 147,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 146,\n      columnNumber: 7\n    }, this);\n  }\n  if (error) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        padding: '2rem',\n        textAlign: 'center'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          color: '#f44336',\n          marginBottom: '1rem'\n        },\n        children: error\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 157,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        onClick: loadDashboardData,\n        style: {\n          background: '#1976d2',\n          color: 'white',\n          border: 'none',\n          padding: '0.5rem 1rem',\n          borderRadius: '4px',\n          cursor: 'pointer'\n        },\n        children: \"Retry\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 160,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 156,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    style: {\n      padding: '2rem'\n    },\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        marginBottom: '2rem'\n      },\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          display: 'flex',\n          justifyContent: 'space-between',\n          alignItems: 'center'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n            style: {\n              margin: 0,\n              color: '#1976d2'\n            },\n            children: \"\\uD83E\\uDDB7 DentFlow Dashboard\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 182,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            style: {\n              margin: '0.5rem 0',\n              color: '#666'\n            },\n            children: [\"Welcome back, \", user === null || user === void 0 ? void 0 : user.first_name, \" \", user === null || user === void 0 ? void 0 : user.last_name, \" (\", user === null || user === void 0 ? void 0 : user.role, \")\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 185,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 181,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            background: backendStatus.available ? '#e8f5e8' : '#fff3cd',\n            color: backendStatus.available ? '#2e7d32' : '#856404',\n            padding: '0.5rem 1rem',\n            borderRadius: '20px',\n            fontSize: '0.9rem',\n            fontWeight: 'bold',\n            border: backendStatus.available ? '1px solid #4caf50' : '1px solid #ffc107'\n          },\n          children: backendStatus.available ? '🟢 Live Data' : '🟡 Demo Data'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 189,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 180,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 179,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        display: 'grid',\n        gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))',\n        gap: '1rem',\n        marginBottom: '2rem'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        style: cardStyle,\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          style: {\n            margin: '0 0 0.5rem 0',\n            color: '#666'\n          },\n          children: \"Active Cases\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 211,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            fontSize: '2rem',\n            fontWeight: 'bold',\n            color: '#1976d2'\n          },\n          children: stats.active_cases\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 212,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"small\", {\n          style: {\n            color: '#666'\n          },\n          children: \"In production\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 215,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 210,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: cardStyle,\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          style: {\n            margin: '0 0 0.5rem 0',\n            color: '#666'\n          },\n          children: \"Completed Today\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 219,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            fontSize: '2rem',\n            fontWeight: 'bold',\n            color: '#4caf50'\n          },\n          children: stats.completed_today\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 220,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"small\", {\n          style: {\n            color: '#666'\n          },\n          children: \"Cases finished\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 223,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 218,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: cardStyle,\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          style: {\n            margin: '0 0 0.5rem 0',\n            color: '#666'\n          },\n          children: \"Pending QC\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 227,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            fontSize: '2rem',\n            fontWeight: 'bold',\n            color: '#ff9800'\n          },\n          children: stats.pending_qc\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 228,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"small\", {\n          style: {\n            color: '#666'\n          },\n          children: \"Quality control needed\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 231,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 226,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: cardStyle,\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          style: {\n            margin: '0 0 0.5rem 0',\n            color: '#666'\n          },\n          children: \"Revenue Today\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 235,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            fontSize: '2rem',\n            fontWeight: 'bold',\n            color: '#2196f3'\n          },\n          children: formatCurrency(stats.revenue_today)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 236,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"small\", {\n          style: {\n            color: '#666'\n          },\n          children: \"Daily earnings\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 239,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 234,\n        columnNumber: 9\n      }, this), stats.overdue_cases > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          ...cardStyle,\n          borderLeft: '4px solid #f44336'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          style: {\n            margin: '0 0 0.5rem 0',\n            color: '#666'\n          },\n          children: \"Overdue Cases\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 244,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            fontSize: '2rem',\n            fontWeight: 'bold',\n            color: '#f44336'\n          },\n          children: stats.overdue_cases\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 245,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"small\", {\n          style: {\n            color: '#666'\n          },\n          children: \"Immediate attention needed\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 248,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 243,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 204,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n        style: {\n          marginBottom: '1rem',\n          color: '#333'\n        },\n        children: \"Recent Cases\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 254,\n        columnNumber: 9\n      }, this), recentCases.length === 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n        style: cardStyle,\n        children: /*#__PURE__*/_jsxDEV(\"p\", {\n          style: {\n            margin: 0,\n            color: '#666',\n            textAlign: 'center'\n          },\n          children: \"No cases found. Create your first case to get started!\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 257,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 256,\n        columnNumber: 11\n      }, this) : recentCases.map(case_ => /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          ...cardStyle,\n          marginBottom: '1rem',\n          borderLeft: `4px solid ${getStatusColor(case_.status)}`\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            display: 'flex',\n            justifyContent: 'space-between',\n            alignItems: 'flex-start',\n            marginBottom: '0.5rem'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n              style: {\n                margin: '0 0 0.25rem 0',\n                color: '#333'\n              },\n              children: [\"Case \", case_.id]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 275,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              style: {\n                margin: 0,\n                color: '#666'\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                children: \"Patient:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 279,\n                columnNumber: 21\n              }, this), \" \", case_.patient_name]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 278,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 274,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              textAlign: 'right'\n            },\n            children: /*#__PURE__*/_jsxDEV(\"span\", {\n              style: {\n                background: getPriorityColor(case_.priority),\n                color: 'white',\n                padding: '0.25rem 0.5rem',\n                borderRadius: '4px',\n                fontSize: '0.8rem',\n                textTransform: 'uppercase'\n              },\n              children: case_.priority\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 283,\n              columnNumber: 19\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 282,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 268,\n          columnNumber: 15\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            display: 'grid',\n            gridTemplateColumns: 'repeat(auto-fit, minmax(150px, 1fr))',\n            gap: '1rem',\n            color: '#666',\n            fontSize: '0.9rem'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n              children: \"Type:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 304,\n              columnNumber: 19\n            }, this), \" \", case_.service_type]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 303,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n              children: \"Tooth:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 307,\n              columnNumber: 19\n            }, this), \" #\", case_.tooth_number]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 306,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n              children: \"Status:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 310,\n              columnNumber: 19\n            }, this), ' ', /*#__PURE__*/_jsxDEV(\"span\", {\n              style: {\n                color: getStatusColor(case_.status)\n              },\n              children: case_.current_stage\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 311,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 309,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n              children: \"Due:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 316,\n              columnNumber: 19\n            }, this), ' ', case_.due_date ? /*#__PURE__*/_jsxDEV(\"span\", {\n              style: {\n                color: case_.is_overdue ? '#f44336' : '#666'\n              },\n              children: case_.days_until_due !== null && case_.days_until_due !== undefined ? case_.days_until_due >= 0 ? `${case_.days_until_due} days` : `${Math.abs(case_.days_until_due)} days overdue` : 'Today'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 318,\n              columnNumber: 21\n            }, this) : 'Not set']\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 315,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 296,\n          columnNumber: 15\n        }, this)]\n      }, case_.id, true, {\n        fileName: _jsxFileName,\n        lineNumber: 263,\n        columnNumber: 13\n      }, this))]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 253,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        marginTop: '2rem',\n        padding: '1rem',\n        background: backendStatus.available ? '#e8f5e8' : '#fff3cd',\n        borderRadius: '4px',\n        border: backendStatus.available ? '1px solid #4caf50' : '1px solid #ffc107'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n        style: {\n          color: backendStatus.available ? '#2e7d32' : '#856404',\n          margin: '0 0 0.5rem 0'\n        },\n        children: [backendStatus.available ? '✅' : '⚠️', \" Backend Status: \", backendStatus.mode === 'live' ? 'Connected' : 'Mock Data']\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 344,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        style: {\n          margin: 0,\n          color: backendStatus.available ? '#2e7d32' : '#856404'\n        },\n        children: backendStatus.available ? 'React frontend successfully connected to Django REST API backend. Real-time data loading from DentFlow case management system.' : 'Backend unavailable. Using demonstration data. All features remain functional for testing.'\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 350,\n        columnNumber: 9\n      }, this), !backendStatus.available && /*#__PURE__*/_jsxDEV(\"button\", {\n        onClick: loadDashboardData,\n        style: {\n          marginTop: '0.5rem',\n          background: '#1976d2',\n          color: 'white',\n          border: 'none',\n          padding: '0.5rem 1rem',\n          borderRadius: '4px',\n          cursor: 'pointer'\n        },\n        children: \"\\uD83D\\uDD04 Try Reconnect\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 360,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 337,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 177,\n    columnNumber: 5\n  }, this);\n};\n\n// Styles\n_s(Dashboard, \"bf7iL0sBDWMd8ILPF7n0z3T4H+U=\", false, function () {\n  return [useAuth];\n});\n_c = Dashboard;\nconst cardStyle = {\n  background: '#fff',\n  padding: '1.5rem',\n  borderRadius: '8px',\n  boxShadow: '0 2px 8px rgba(0,0,0,0.1)',\n  border: '1px solid #e0e0e0'\n};\nexport default Dashboard;\nvar _c;\n$RefreshReg$(_c, \"Dashboard\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "CasesService", "useAuth", "jsxDEV", "_jsxDEV", "Dashboard", "_s", "user", "stats", "setStats", "active_cases", "completed_today", "pending_qc", "revenue_today", "overdue_cases", "recentCases", "setRecentCases", "loading", "setLoading", "error", "setError", "backendStatus", "setBackendStatus", "available", "mode", "loadDashboardData", "checkBackendHealth", "dashboardStats", "cases", "Promise", "all", "getDashboardStats", "getCases", "slice", "getBackendStatus", "err", "console", "formatCurrency", "amount", "Intl", "NumberFormat", "style", "currency", "format", "getStatusColor", "status", "colors", "getPriorityColor", "priority", "padding", "textAlign", "children", "fontSize", "color", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "marginBottom", "onClick", "background", "border", "borderRadius", "cursor", "display", "justifyContent", "alignItems", "margin", "first_name", "last_name", "role", "fontWeight", "gridTemplateColumns", "gap", "cardStyle", "borderLeft", "length", "map", "case_", "id", "patient_name", "textTransform", "service_type", "tooth_number", "current_stage", "due_date", "is_overdue", "days_until_due", "undefined", "Math", "abs", "marginTop", "_c", "boxShadow", "$RefreshReg$"], "sources": ["C:/GPT4_PROJECTS/DENTAL_LAB_ARCHITECTURE_PATTERNS/dentflow/frontend/src/components/Dashboard.tsx"], "sourcesContent": ["/**\n * DentFlow Dashboard Component - Enhanced Version\n * Comprehensive dashboard with advanced widgets, charts, and real-time data visualization\n */\n\nimport React, { useState, useEffect } from 'react';\nimport {\n  Box,\n  Grid,\n  Card,\n  CardContent,\n  Typography,\n  Avatar,\n  Chip,\n  LinearProgress,\n  IconButton,\n  Button,\n  Alert,\n  Paper,\n  List,\n  ListItem,\n  ListItemText,\n  ListItemAvatar,\n  Divider,\n  Badge,\n  Tooltip,\n  CircularProgress,\n  Table,\n  TableBody,\n  TableCell,\n  TableContainer,\n  TableHead,\n  TableRow,\n  useTheme,\n  alpha,\n} from '@mui/material';\nimport {\n  TrendingUp,\n  TrendingDown,\n  Assignment,\n  CheckCircle,\n  Warning,\n  Schedule,\n  AttachMoney,\n  People,\n  Inventory,\n  Analytics,\n  Refresh,\n  Notifications,\n  CalendarToday,\n  Build,\n  LocalShipping,\n  QualityControl,\n} from '@mui/icons-material';\nimport { CasesService } from '../api/casesService';\nimport { Case } from '../api/types';\nimport { useAuth } from '../context/AuthContext';\n\ninterface DashboardStats {\n  active_cases: number;\n  completed_today: number;\n  pending_qc: number;\n  revenue_today: number;\n  overdue_cases: number;\n}\n\nconst Dashboard: React.FC = () => {\n  const { user } = useAuth();\n  const [stats, setStats] = useState<DashboardStats>({\n    active_cases: 0,\n    completed_today: 0,\n    pending_qc: 0,\n    revenue_today: 0,\n    overdue_cases: 0,\n  });\n  const [recentCases, setRecentCases] = useState<Case[]>([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState<string>('');\n  const [backendStatus, setBackendStatus] = useState<{available: boolean; mode: string}>({\n    available: false,\n    mode: 'mock'\n  });\n\n  useEffect(() => {\n    loadDashboardData();\n  }, []);\n\n  const loadDashboardData = async () => {\n    try {\n      setLoading(true);\n      setError('');\n      \n      // Check backend health first\n      await CasesService.checkBackendHealth();\n      \n      const [dashboardStats, cases] = await Promise.all([\n        CasesService.getDashboardStats(),\n        CasesService.getCases(),\n      ]);\n      \n      setStats(dashboardStats);\n      setRecentCases(cases.slice(0, 5)); // Show last 5 cases\n      setBackendStatus(CasesService.getBackendStatus());\n      \n    } catch (err: any) {\n      console.error('Error loading dashboard:', err);\n      setError('Failed to load some dashboard data');\n      setBackendStatus(CasesService.getBackendStatus());\n    } finally {\n      setLoading(false);\n    }\n  };\n  const formatCurrency = (amount: number) => {\n    return new Intl.NumberFormat('en-US', {\n      style: 'currency',\n      currency: 'USD',\n    }).format(amount);\n  };\n\n  const getStatusColor = (status: string) => {\n    const colors: Record<string, string> = {\n      'received': '#2196f3',\n      'design': '#ff9800',\n      'milling': '#9c27b0',\n      'sintering': '#e91e63',\n      'qc': '#ff5722',\n      'shipped': '#4caf50',\n      'delivered': '#8bc34a',\n      'cancelled': '#757575',\n    };\n    return colors[status] || '#666';\n  };\n\n  const getPriorityColor = (priority: string) => {\n    const colors: Record<string, string> = {\n      'low': '#4caf50',\n      'normal': '#2196f3',\n      'urgent': '#ff9800',\n      'stat': '#f44336',\n    };\n    return colors[priority] || '#666';\n  };\n\n  if (loading) {\n    return (\n      <div style={{ padding: '2rem', textAlign: 'center' }}>\n        <div style={{ fontSize: '1.2rem', color: '#666' }}>\n          Loading dashboard...\n        </div>\n      </div>\n    );\n  }\n\n  if (error) {\n    return (\n      <div style={{ padding: '2rem', textAlign: 'center' }}>\n        <div style={{ color: '#f44336', marginBottom: '1rem' }}>\n          {error}\n        </div>\n        <button \n          onClick={loadDashboardData}\n          style={{\n            background: '#1976d2',\n            color: 'white',\n            border: 'none',\n            padding: '0.5rem 1rem',\n            borderRadius: '4px',\n            cursor: 'pointer',\n          }}\n        >\n          Retry\n        </button>\n      </div>\n    );\n  }\n  return (\n    <div style={{ padding: '2rem' }}>\n      {/* Header */}\n      <div style={{ marginBottom: '2rem' }}>\n        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>\n          <div>\n            <h1 style={{ margin: 0, color: '#1976d2' }}>\n              🦷 DentFlow Dashboard\n            </h1>\n            <p style={{ margin: '0.5rem 0', color: '#666' }}>\n              Welcome back, {user?.first_name} {user?.last_name} ({user?.role})\n            </p>\n          </div>\n          <div style={{\n            background: backendStatus.available ? '#e8f5e8' : '#fff3cd',\n            color: backendStatus.available ? '#2e7d32' : '#856404',\n            padding: '0.5rem 1rem',\n            borderRadius: '20px',\n            fontSize: '0.9rem',\n            fontWeight: 'bold',\n            border: backendStatus.available ? '1px solid #4caf50' : '1px solid #ffc107'\n          }}>\n            {backendStatus.available ? '🟢 Live Data' : '🟡 Demo Data'}\n          </div>\n        </div>\n      </div>\n\n      {/* Stats Grid */}\n      <div style={{\n        display: 'grid',\n        gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))',\n        gap: '1rem',\n        marginBottom: '2rem',\n      }}>\n        <div style={cardStyle}>\n          <h3 style={{ margin: '0 0 0.5rem 0', color: '#666' }}>Active Cases</h3>\n          <div style={{ fontSize: '2rem', fontWeight: 'bold', color: '#1976d2' }}>\n            {stats.active_cases}\n          </div>\n          <small style={{ color: '#666' }}>In production</small>\n        </div>\n\n        <div style={cardStyle}>\n          <h3 style={{ margin: '0 0 0.5rem 0', color: '#666' }}>Completed Today</h3>\n          <div style={{ fontSize: '2rem', fontWeight: 'bold', color: '#4caf50' }}>\n            {stats.completed_today}\n          </div>\n          <small style={{ color: '#666' }}>Cases finished</small>\n        </div>\n\n        <div style={cardStyle}>\n          <h3 style={{ margin: '0 0 0.5rem 0', color: '#666' }}>Pending QC</h3>\n          <div style={{ fontSize: '2rem', fontWeight: 'bold', color: '#ff9800' }}>\n            {stats.pending_qc}\n          </div>\n          <small style={{ color: '#666' }}>Quality control needed</small>\n        </div>\n\n        <div style={cardStyle}>\n          <h3 style={{ margin: '0 0 0.5rem 0', color: '#666' }}>Revenue Today</h3>\n          <div style={{ fontSize: '2rem', fontWeight: 'bold', color: '#2196f3' }}>\n            {formatCurrency(stats.revenue_today)}\n          </div>\n          <small style={{ color: '#666' }}>Daily earnings</small>\n        </div>\n\n        {stats.overdue_cases > 0 && (\n          <div style={{ ...cardStyle, borderLeft: '4px solid #f44336' }}>\n            <h3 style={{ margin: '0 0 0.5rem 0', color: '#666' }}>Overdue Cases</h3>\n            <div style={{ fontSize: '2rem', fontWeight: 'bold', color: '#f44336' }}>\n              {stats.overdue_cases}\n            </div>\n            <small style={{ color: '#666' }}>Immediate attention needed</small>\n          </div>\n        )}\n      </div>\n      {/* Recent Cases */}\n      <div>\n        <h2 style={{ marginBottom: '1rem', color: '#333' }}>Recent Cases</h2>\n        {recentCases.length === 0 ? (\n          <div style={cardStyle}>\n            <p style={{ margin: 0, color: '#666', textAlign: 'center' }}>\n              No cases found. Create your first case to get started!\n            </p>\n          </div>\n        ) : (\n          recentCases.map((case_) => (\n            <div key={case_.id} style={{\n              ...cardStyle,\n              marginBottom: '1rem',\n              borderLeft: `4px solid ${getStatusColor(case_.status)}`,\n            }}>\n              <div style={{\n                display: 'flex',\n                justifyContent: 'space-between',\n                alignItems: 'flex-start',\n                marginBottom: '0.5rem',\n              }}>\n                <div>\n                  <h4 style={{ margin: '0 0 0.25rem 0', color: '#333' }}>\n                    Case {case_.id}\n                  </h4>\n                  <p style={{ margin: 0, color: '#666' }}>\n                    <strong>Patient:</strong> {case_.patient_name}\n                  </p>\n                </div>\n                <div style={{ textAlign: 'right' }}>\n                  <span style={{\n                    background: getPriorityColor(case_.priority),\n                    color: 'white',\n                    padding: '0.25rem 0.5rem',\n                    borderRadius: '4px',\n                    fontSize: '0.8rem',\n                    textTransform: 'uppercase',\n                  }}>\n                    {case_.priority}\n                  </span>\n                </div>\n              </div>\n              \n              <div style={{\n                display: 'grid',\n                gridTemplateColumns: 'repeat(auto-fit, minmax(150px, 1fr))',\n                gap: '1rem',\n                color: '#666',\n                fontSize: '0.9rem',\n              }}>\n                <div>\n                  <strong>Type:</strong> {case_.service_type}\n                </div>\n                <div>\n                  <strong>Tooth:</strong> #{case_.tooth_number}\n                </div>\n                <div>\n                  <strong>Status:</strong>{' '}\n                  <span style={{ color: getStatusColor(case_.status) }}>\n                    {case_.current_stage}\n                  </span>\n                </div>\n                <div>\n                  <strong>Due:</strong>{' '}\n                  {case_.due_date ? (\n                    <span style={{ \n                      color: case_.is_overdue ? '#f44336' : '#666' \n                    }}>\n                      {case_.days_until_due !== null && case_.days_until_due !== undefined ? (\n                        case_.days_until_due >= 0 ?\n                          `${case_.days_until_due} days` :\n                          `${Math.abs(case_.days_until_due)} days overdue`\n                      ) : 'Today'}\n                    </span>\n                  ) : (\n                    'Not set'\n                  )}\n                </div>\n              </div>\n            </div>\n          ))\n        )}\n      </div>\n\n      <div style={{\n        marginTop: '2rem',\n        padding: '1rem',\n        background: backendStatus.available ? '#e8f5e8' : '#fff3cd',\n        borderRadius: '4px',\n        border: backendStatus.available ? '1px solid #4caf50' : '1px solid #ffc107',\n      }}>\n        <h4 style={{ \n          color: backendStatus.available ? '#2e7d32' : '#856404', \n          margin: '0 0 0.5rem 0' \n        }}>\n          {backendStatus.available ? '✅' : '⚠️'} Backend Status: {backendStatus.mode === 'live' ? 'Connected' : 'Mock Data'}\n        </h4>\n        <p style={{ \n          margin: 0, \n          color: backendStatus.available ? '#2e7d32' : '#856404' \n        }}>\n          {backendStatus.available \n            ? 'React frontend successfully connected to Django REST API backend. Real-time data loading from DentFlow case management system.'\n            : 'Backend unavailable. Using demonstration data. All features remain functional for testing.'\n          }\n        </p>\n        {!backendStatus.available && (\n          <button\n            onClick={loadDashboardData}\n            style={{\n              marginTop: '0.5rem',\n              background: '#1976d2',\n              color: 'white',\n              border: 'none',\n              padding: '0.5rem 1rem',\n              borderRadius: '4px',\n              cursor: 'pointer',\n            }}\n          >\n            🔄 Try Reconnect\n          </button>\n        )}\n      </div>\n    </div>\n  );\n};\n\n// Styles\nconst cardStyle: React.CSSProperties = {\n  background: '#fff',\n  padding: '1.5rem',\n  borderRadius: '8px',\n  boxShadow: '0 2px 8px rgba(0,0,0,0.1)',\n  border: '1px solid #e0e0e0',\n};\n\nexport default Dashboard;"], "mappings": ";;AAAA;AACA;AACA;AACA;;AAEA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAiDlD,SAASC,YAAY,QAAQ,qBAAqB;AAElD,SAASC,OAAO,QAAQ,wBAAwB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAUjD,MAAMC,SAAmB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAChC,MAAM;IAAEC;EAAK,CAAC,GAAGL,OAAO,CAAC,CAAC;EAC1B,MAAM,CAACM,KAAK,EAAEC,QAAQ,CAAC,GAAGV,QAAQ,CAAiB;IACjDW,YAAY,EAAE,CAAC;IACfC,eAAe,EAAE,CAAC;IAClBC,UAAU,EAAE,CAAC;IACbC,aAAa,EAAE,CAAC;IAChBC,aAAa,EAAE;EACjB,CAAC,CAAC;EACF,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAGjB,QAAQ,CAAS,EAAE,CAAC;EAC1D,MAAM,CAACkB,OAAO,EAAEC,UAAU,CAAC,GAAGnB,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACoB,KAAK,EAAEC,QAAQ,CAAC,GAAGrB,QAAQ,CAAS,EAAE,CAAC;EAC9C,MAAM,CAACsB,aAAa,EAAEC,gBAAgB,CAAC,GAAGvB,QAAQ,CAAqC;IACrFwB,SAAS,EAAE,KAAK;IAChBC,IAAI,EAAE;EACR,CAAC,CAAC;EAEFxB,SAAS,CAAC,MAAM;IACdyB,iBAAiB,CAAC,CAAC;EACrB,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMA,iBAAiB,GAAG,MAAAA,CAAA,KAAY;IACpC,IAAI;MACFP,UAAU,CAAC,IAAI,CAAC;MAChBE,QAAQ,CAAC,EAAE,CAAC;;MAEZ;MACA,MAAMnB,YAAY,CAACyB,kBAAkB,CAAC,CAAC;MAEvC,MAAM,CAACC,cAAc,EAAEC,KAAK,CAAC,GAAG,MAAMC,OAAO,CAACC,GAAG,CAAC,CAChD7B,YAAY,CAAC8B,iBAAiB,CAAC,CAAC,EAChC9B,YAAY,CAAC+B,QAAQ,CAAC,CAAC,CACxB,CAAC;MAEFvB,QAAQ,CAACkB,cAAc,CAAC;MACxBX,cAAc,CAACY,KAAK,CAACK,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;MACnCX,gBAAgB,CAACrB,YAAY,CAACiC,gBAAgB,CAAC,CAAC,CAAC;IAEnD,CAAC,CAAC,OAAOC,GAAQ,EAAE;MACjBC,OAAO,CAACjB,KAAK,CAAC,0BAA0B,EAAEgB,GAAG,CAAC;MAC9Cf,QAAQ,CAAC,oCAAoC,CAAC;MAC9CE,gBAAgB,CAACrB,YAAY,CAACiC,gBAAgB,CAAC,CAAC,CAAC;IACnD,CAAC,SAAS;MACRhB,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EACD,MAAMmB,cAAc,GAAIC,MAAc,IAAK;IACzC,OAAO,IAAIC,IAAI,CAACC,YAAY,CAAC,OAAO,EAAE;MACpCC,KAAK,EAAE,UAAU;MACjBC,QAAQ,EAAE;IACZ,CAAC,CAAC,CAACC,MAAM,CAACL,MAAM,CAAC;EACnB,CAAC;EAED,MAAMM,cAAc,GAAIC,MAAc,IAAK;IACzC,MAAMC,MAA8B,GAAG;MACrC,UAAU,EAAE,SAAS;MACrB,QAAQ,EAAE,SAAS;MACnB,SAAS,EAAE,SAAS;MACpB,WAAW,EAAE,SAAS;MACtB,IAAI,EAAE,SAAS;MACf,SAAS,EAAE,SAAS;MACpB,WAAW,EAAE,SAAS;MACtB,WAAW,EAAE;IACf,CAAC;IACD,OAAOA,MAAM,CAACD,MAAM,CAAC,IAAI,MAAM;EACjC,CAAC;EAED,MAAME,gBAAgB,GAAIC,QAAgB,IAAK;IAC7C,MAAMF,MAA8B,GAAG;MACrC,KAAK,EAAE,SAAS;MAChB,QAAQ,EAAE,SAAS;MACnB,QAAQ,EAAE,SAAS;MACnB,MAAM,EAAE;IACV,CAAC;IACD,OAAOA,MAAM,CAACE,QAAQ,CAAC,IAAI,MAAM;EACnC,CAAC;EAED,IAAI/B,OAAO,EAAE;IACX,oBACEb,OAAA;MAAKqC,KAAK,EAAE;QAAEQ,OAAO,EAAE,MAAM;QAAEC,SAAS,EAAE;MAAS,CAAE;MAAAC,QAAA,eACnD/C,OAAA;QAAKqC,KAAK,EAAE;UAAEW,QAAQ,EAAE,QAAQ;UAAEC,KAAK,EAAE;QAAO,CAAE;QAAAF,QAAA,EAAC;MAEnD;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;EAEA,IAAItC,KAAK,EAAE;IACT,oBACEf,OAAA;MAAKqC,KAAK,EAAE;QAAEQ,OAAO,EAAE,MAAM;QAAEC,SAAS,EAAE;MAAS,CAAE;MAAAC,QAAA,gBACnD/C,OAAA;QAAKqC,KAAK,EAAE;UAAEY,KAAK,EAAE,SAAS;UAAEK,YAAY,EAAE;QAAO,CAAE;QAAAP,QAAA,EACpDhC;MAAK;QAAAmC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eACNrD,OAAA;QACEuD,OAAO,EAAElC,iBAAkB;QAC3BgB,KAAK,EAAE;UACLmB,UAAU,EAAE,SAAS;UACrBP,KAAK,EAAE,OAAO;UACdQ,MAAM,EAAE,MAAM;UACdZ,OAAO,EAAE,aAAa;UACtBa,YAAY,EAAE,KAAK;UACnBC,MAAM,EAAE;QACV,CAAE;QAAAZ,QAAA,EACH;MAED;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC;EAEV;EACA,oBACErD,OAAA;IAAKqC,KAAK,EAAE;MAAEQ,OAAO,EAAE;IAAO,CAAE;IAAAE,QAAA,gBAE9B/C,OAAA;MAAKqC,KAAK,EAAE;QAAEiB,YAAY,EAAE;MAAO,CAAE;MAAAP,QAAA,eACnC/C,OAAA;QAAKqC,KAAK,EAAE;UAAEuB,OAAO,EAAE,MAAM;UAAEC,cAAc,EAAE,eAAe;UAAEC,UAAU,EAAE;QAAS,CAAE;QAAAf,QAAA,gBACrF/C,OAAA;UAAA+C,QAAA,gBACE/C,OAAA;YAAIqC,KAAK,EAAE;cAAE0B,MAAM,EAAE,CAAC;cAAEd,KAAK,EAAE;YAAU,CAAE;YAAAF,QAAA,EAAC;UAE5C;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACLrD,OAAA;YAAGqC,KAAK,EAAE;cAAE0B,MAAM,EAAE,UAAU;cAAEd,KAAK,EAAE;YAAO,CAAE;YAAAF,QAAA,GAAC,gBACjC,EAAC5C,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE6D,UAAU,EAAC,GAAC,EAAC7D,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE8D,SAAS,EAAC,IAAE,EAAC9D,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE+D,IAAI,EAAC,GAClE;UAAA;YAAAhB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC,eACNrD,OAAA;UAAKqC,KAAK,EAAE;YACVmB,UAAU,EAAEvC,aAAa,CAACE,SAAS,GAAG,SAAS,GAAG,SAAS;YAC3D8B,KAAK,EAAEhC,aAAa,CAACE,SAAS,GAAG,SAAS,GAAG,SAAS;YACtD0B,OAAO,EAAE,aAAa;YACtBa,YAAY,EAAE,MAAM;YACpBV,QAAQ,EAAE,QAAQ;YAClBmB,UAAU,EAAE,MAAM;YAClBV,MAAM,EAAExC,aAAa,CAACE,SAAS,GAAG,mBAAmB,GAAG;UAC1D,CAAE;UAAA4B,QAAA,EACC9B,aAAa,CAACE,SAAS,GAAG,cAAc,GAAG;QAAc;UAAA+B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACvD,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNrD,OAAA;MAAKqC,KAAK,EAAE;QACVuB,OAAO,EAAE,MAAM;QACfQ,mBAAmB,EAAE,sCAAsC;QAC3DC,GAAG,EAAE,MAAM;QACXf,YAAY,EAAE;MAChB,CAAE;MAAAP,QAAA,gBACA/C,OAAA;QAAKqC,KAAK,EAAEiC,SAAU;QAAAvB,QAAA,gBACpB/C,OAAA;UAAIqC,KAAK,EAAE;YAAE0B,MAAM,EAAE,cAAc;YAAEd,KAAK,EAAE;UAAO,CAAE;UAAAF,QAAA,EAAC;QAAY;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACvErD,OAAA;UAAKqC,KAAK,EAAE;YAAEW,QAAQ,EAAE,MAAM;YAAEmB,UAAU,EAAE,MAAM;YAAElB,KAAK,EAAE;UAAU,CAAE;UAAAF,QAAA,EACpE3C,KAAK,CAACE;QAAY;UAAA4C,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChB,CAAC,eACNrD,OAAA;UAAOqC,KAAK,EAAE;YAAEY,KAAK,EAAE;UAAO,CAAE;UAAAF,QAAA,EAAC;QAAa;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACnD,CAAC,eAENrD,OAAA;QAAKqC,KAAK,EAAEiC,SAAU;QAAAvB,QAAA,gBACpB/C,OAAA;UAAIqC,KAAK,EAAE;YAAE0B,MAAM,EAAE,cAAc;YAAEd,KAAK,EAAE;UAAO,CAAE;UAAAF,QAAA,EAAC;QAAe;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC1ErD,OAAA;UAAKqC,KAAK,EAAE;YAAEW,QAAQ,EAAE,MAAM;YAAEmB,UAAU,EAAE,MAAM;YAAElB,KAAK,EAAE;UAAU,CAAE;UAAAF,QAAA,EACpE3C,KAAK,CAACG;QAAe;UAAA2C,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnB,CAAC,eACNrD,OAAA;UAAOqC,KAAK,EAAE;YAAEY,KAAK,EAAE;UAAO,CAAE;UAAAF,QAAA,EAAC;QAAc;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACpD,CAAC,eAENrD,OAAA;QAAKqC,KAAK,EAAEiC,SAAU;QAAAvB,QAAA,gBACpB/C,OAAA;UAAIqC,KAAK,EAAE;YAAE0B,MAAM,EAAE,cAAc;YAAEd,KAAK,EAAE;UAAO,CAAE;UAAAF,QAAA,EAAC;QAAU;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACrErD,OAAA;UAAKqC,KAAK,EAAE;YAAEW,QAAQ,EAAE,MAAM;YAAEmB,UAAU,EAAE,MAAM;YAAElB,KAAK,EAAE;UAAU,CAAE;UAAAF,QAAA,EACpE3C,KAAK,CAACI;QAAU;UAAA0C,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACd,CAAC,eACNrD,OAAA;UAAOqC,KAAK,EAAE;YAAEY,KAAK,EAAE;UAAO,CAAE;UAAAF,QAAA,EAAC;QAAsB;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC5D,CAAC,eAENrD,OAAA;QAAKqC,KAAK,EAAEiC,SAAU;QAAAvB,QAAA,gBACpB/C,OAAA;UAAIqC,KAAK,EAAE;YAAE0B,MAAM,EAAE,cAAc;YAAEd,KAAK,EAAE;UAAO,CAAE;UAAAF,QAAA,EAAC;QAAa;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACxErD,OAAA;UAAKqC,KAAK,EAAE;YAAEW,QAAQ,EAAE,MAAM;YAAEmB,UAAU,EAAE,MAAM;YAAElB,KAAK,EAAE;UAAU,CAAE;UAAAF,QAAA,EACpEd,cAAc,CAAC7B,KAAK,CAACK,aAAa;QAAC;UAAAyC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjC,CAAC,eACNrD,OAAA;UAAOqC,KAAK,EAAE;YAAEY,KAAK,EAAE;UAAO,CAAE;UAAAF,QAAA,EAAC;QAAc;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACpD,CAAC,EAELjD,KAAK,CAACM,aAAa,GAAG,CAAC,iBACtBV,OAAA;QAAKqC,KAAK,EAAE;UAAE,GAAGiC,SAAS;UAAEC,UAAU,EAAE;QAAoB,CAAE;QAAAxB,QAAA,gBAC5D/C,OAAA;UAAIqC,KAAK,EAAE;YAAE0B,MAAM,EAAE,cAAc;YAAEd,KAAK,EAAE;UAAO,CAAE;UAAAF,QAAA,EAAC;QAAa;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACxErD,OAAA;UAAKqC,KAAK,EAAE;YAAEW,QAAQ,EAAE,MAAM;YAAEmB,UAAU,EAAE,MAAM;YAAElB,KAAK,EAAE;UAAU,CAAE;UAAAF,QAAA,EACpE3C,KAAK,CAACM;QAAa;UAAAwC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjB,CAAC,eACNrD,OAAA;UAAOqC,KAAK,EAAE;YAAEY,KAAK,EAAE;UAAO,CAAE;UAAAF,QAAA,EAAC;QAA0B;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAChE,CACN;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC,eAENrD,OAAA;MAAA+C,QAAA,gBACE/C,OAAA;QAAIqC,KAAK,EAAE;UAAEiB,YAAY,EAAE,MAAM;UAAEL,KAAK,EAAE;QAAO,CAAE;QAAAF,QAAA,EAAC;MAAY;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,EACpE1C,WAAW,CAAC6D,MAAM,KAAK,CAAC,gBACvBxE,OAAA;QAAKqC,KAAK,EAAEiC,SAAU;QAAAvB,QAAA,eACpB/C,OAAA;UAAGqC,KAAK,EAAE;YAAE0B,MAAM,EAAE,CAAC;YAAEd,KAAK,EAAE,MAAM;YAAEH,SAAS,EAAE;UAAS,CAAE;UAAAC,QAAA,EAAC;QAE7D;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC,GAEN1C,WAAW,CAAC8D,GAAG,CAAEC,KAAK,iBACpB1E,OAAA;QAAoBqC,KAAK,EAAE;UACzB,GAAGiC,SAAS;UACZhB,YAAY,EAAE,MAAM;UACpBiB,UAAU,EAAE,aAAa/B,cAAc,CAACkC,KAAK,CAACjC,MAAM,CAAC;QACvD,CAAE;QAAAM,QAAA,gBACA/C,OAAA;UAAKqC,KAAK,EAAE;YACVuB,OAAO,EAAE,MAAM;YACfC,cAAc,EAAE,eAAe;YAC/BC,UAAU,EAAE,YAAY;YACxBR,YAAY,EAAE;UAChB,CAAE;UAAAP,QAAA,gBACA/C,OAAA;YAAA+C,QAAA,gBACE/C,OAAA;cAAIqC,KAAK,EAAE;gBAAE0B,MAAM,EAAE,eAAe;gBAAEd,KAAK,EAAE;cAAO,CAAE;cAAAF,QAAA,GAAC,OAChD,EAAC2B,KAAK,CAACC,EAAE;YAAA;cAAAzB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACZ,CAAC,eACLrD,OAAA;cAAGqC,KAAK,EAAE;gBAAE0B,MAAM,EAAE,CAAC;gBAAEd,KAAK,EAAE;cAAO,CAAE;cAAAF,QAAA,gBACrC/C,OAAA;gBAAA+C,QAAA,EAAQ;cAAQ;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,KAAC,EAACqB,KAAK,CAACE,YAAY;YAAA;cAAA1B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC5C,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CAAC,eACNrD,OAAA;YAAKqC,KAAK,EAAE;cAAES,SAAS,EAAE;YAAQ,CAAE;YAAAC,QAAA,eACjC/C,OAAA;cAAMqC,KAAK,EAAE;gBACXmB,UAAU,EAAEb,gBAAgB,CAAC+B,KAAK,CAAC9B,QAAQ,CAAC;gBAC5CK,KAAK,EAAE,OAAO;gBACdJ,OAAO,EAAE,gBAAgB;gBACzBa,YAAY,EAAE,KAAK;gBACnBV,QAAQ,EAAE,QAAQ;gBAClB6B,aAAa,EAAE;cACjB,CAAE;cAAA9B,QAAA,EACC2B,KAAK,CAAC9B;YAAQ;cAAAM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACX;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAENrD,OAAA;UAAKqC,KAAK,EAAE;YACVuB,OAAO,EAAE,MAAM;YACfQ,mBAAmB,EAAE,sCAAsC;YAC3DC,GAAG,EAAE,MAAM;YACXpB,KAAK,EAAE,MAAM;YACbD,QAAQ,EAAE;UACZ,CAAE;UAAAD,QAAA,gBACA/C,OAAA;YAAA+C,QAAA,gBACE/C,OAAA;cAAA+C,QAAA,EAAQ;YAAK;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,KAAC,EAACqB,KAAK,CAACI,YAAY;UAAA;YAAA5B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACvC,CAAC,eACNrD,OAAA;YAAA+C,QAAA,gBACE/C,OAAA;cAAA+C,QAAA,EAAQ;YAAM;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,MAAE,EAACqB,KAAK,CAACK,YAAY;UAAA;YAAA7B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACzC,CAAC,eACNrD,OAAA;YAAA+C,QAAA,gBACE/C,OAAA;cAAA+C,QAAA,EAAQ;YAAO;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,EAAC,GAAG,eAC5BrD,OAAA;cAAMqC,KAAK,EAAE;gBAAEY,KAAK,EAAET,cAAc,CAACkC,KAAK,CAACjC,MAAM;cAAE,CAAE;cAAAM,QAAA,EAClD2B,KAAK,CAACM;YAAa;cAAA9B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChB,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ,CAAC,eACNrD,OAAA;YAAA+C,QAAA,gBACE/C,OAAA;cAAA+C,QAAA,EAAQ;YAAI;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,EAAC,GAAG,EACxBqB,KAAK,CAACO,QAAQ,gBACbjF,OAAA;cAAMqC,KAAK,EAAE;gBACXY,KAAK,EAAEyB,KAAK,CAACQ,UAAU,GAAG,SAAS,GAAG;cACxC,CAAE;cAAAnC,QAAA,EACC2B,KAAK,CAACS,cAAc,KAAK,IAAI,IAAIT,KAAK,CAACS,cAAc,KAAKC,SAAS,GAClEV,KAAK,CAACS,cAAc,IAAI,CAAC,GACvB,GAAGT,KAAK,CAACS,cAAc,OAAO,GAC9B,GAAGE,IAAI,CAACC,GAAG,CAACZ,KAAK,CAACS,cAAc,CAAC,eAAe,GAChD;YAAO;cAAAjC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACP,CAAC,GAEP,SACD;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA,GApEEqB,KAAK,CAACC,EAAE;QAAAzB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAqEb,CACN,CACF;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC,eAENrD,OAAA;MAAKqC,KAAK,EAAE;QACVkD,SAAS,EAAE,MAAM;QACjB1C,OAAO,EAAE,MAAM;QACfW,UAAU,EAAEvC,aAAa,CAACE,SAAS,GAAG,SAAS,GAAG,SAAS;QAC3DuC,YAAY,EAAE,KAAK;QACnBD,MAAM,EAAExC,aAAa,CAACE,SAAS,GAAG,mBAAmB,GAAG;MAC1D,CAAE;MAAA4B,QAAA,gBACA/C,OAAA;QAAIqC,KAAK,EAAE;UACTY,KAAK,EAAEhC,aAAa,CAACE,SAAS,GAAG,SAAS,GAAG,SAAS;UACtD4C,MAAM,EAAE;QACV,CAAE;QAAAhB,QAAA,GACC9B,aAAa,CAACE,SAAS,GAAG,GAAG,GAAG,IAAI,EAAC,mBAAiB,EAACF,aAAa,CAACG,IAAI,KAAK,MAAM,GAAG,WAAW,GAAG,WAAW;MAAA;QAAA8B,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC/G,CAAC,eACLrD,OAAA;QAAGqC,KAAK,EAAE;UACR0B,MAAM,EAAE,CAAC;UACTd,KAAK,EAAEhC,aAAa,CAACE,SAAS,GAAG,SAAS,GAAG;QAC/C,CAAE;QAAA4B,QAAA,EACC9B,aAAa,CAACE,SAAS,GACpB,gIAAgI,GAChI;MAA4F;QAAA+B,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAE/F,CAAC,EACH,CAACpC,aAAa,CAACE,SAAS,iBACvBnB,OAAA;QACEuD,OAAO,EAAElC,iBAAkB;QAC3BgB,KAAK,EAAE;UACLkD,SAAS,EAAE,QAAQ;UACnB/B,UAAU,EAAE,SAAS;UACrBP,KAAK,EAAE,OAAO;UACdQ,MAAM,EAAE,MAAM;UACdZ,OAAO,EAAE,aAAa;UACtBa,YAAY,EAAE,KAAK;UACnBC,MAAM,EAAE;QACV,CAAE;QAAAZ,QAAA,EACH;MAED;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CACT;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;;AAED;AAAAnD,EAAA,CAzTMD,SAAmB;EAAA,QACNH,OAAO;AAAA;AAAA0F,EAAA,GADpBvF,SAAmB;AA0TzB,MAAMqE,SAA8B,GAAG;EACrCd,UAAU,EAAE,MAAM;EAClBX,OAAO,EAAE,QAAQ;EACjBa,YAAY,EAAE,KAAK;EACnB+B,SAAS,EAAE,2BAA2B;EACtChC,MAAM,EAAE;AACV,CAAC;AAED,eAAexD,SAAS;AAAC,IAAAuF,EAAA;AAAAE,YAAA,CAAAF,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}