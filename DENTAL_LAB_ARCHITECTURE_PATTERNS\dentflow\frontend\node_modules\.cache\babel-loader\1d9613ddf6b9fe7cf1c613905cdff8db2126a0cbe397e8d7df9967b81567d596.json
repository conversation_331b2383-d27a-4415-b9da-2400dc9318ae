{"ast": null, "code": "var _jsxFileName = \"C:\\\\GPT4_PROJECTS\\\\DENTAL_LAB_ARCHITECTURE_PATTERNS\\\\dentflow\\\\frontend\\\\src\\\\components\\\\Dashboard.tsx\",\n  _s = $RefreshSig$();\n/**\n * DentFlow Dashboard Component - Enhanced Version\n * Comprehensive dashboard with advanced widgets, charts, and real-time data visualization\n */\n\nimport React, { useState, useEffect } from 'react';\nimport { useTheme } from '@mui/material';\nimport { CasesService } from '../api/casesService';\nimport { useAuth } from '../context/AuthContext';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Dashboard = () => {\n  _s();\n  const {\n    user\n  } = useAuth();\n  const theme = useTheme();\n  const [stats, setStats] = useState({\n    active_cases: 0,\n    completed_today: 0,\n    pending_qc: 0,\n    revenue_today: 0,\n    overdue_cases: 0,\n    total_cases_this_month: 0,\n    revenue_this_month: 0,\n    average_completion_time: 0,\n    customer_satisfaction: 0,\n    inventory_alerts: 0,\n    technician_utilization: 0,\n    pending_invoices: 0\n  });\n  const [recentCases, setRecentCases] = useState([]);\n  const [recentActivities, setRecentActivities] = useState([]);\n  const [technicianStats, setTechnicianStats] = useState([]);\n  const [inventoryAlerts, setInventoryAlerts] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState('');\n  const [backendStatus, setBackendStatus] = useState({\n    available: false,\n    mode: 'mock'\n  });\n  useEffect(() => {\n    loadDashboardData();\n  }, []);\n  const loadDashboardData = async () => {\n    try {\n      setLoading(true);\n      setError('');\n\n      // Check backend health first\n      await CasesService.checkBackendHealth();\n      const [dashboardStats, cases] = await Promise.all([CasesService.getDashboardStats(), CasesService.getCases()]);\n      setStats(dashboardStats);\n      setRecentCases(cases.slice(0, 5)); // Show last 5 cases\n      setBackendStatus(CasesService.getBackendStatus());\n    } catch (err) {\n      console.error('Error loading dashboard:', err);\n      setError('Failed to load some dashboard data');\n      setBackendStatus(CasesService.getBackendStatus());\n    } finally {\n      setLoading(false);\n    }\n  };\n  const formatCurrency = amount => {\n    return new Intl.NumberFormat('en-US', {\n      style: 'currency',\n      currency: 'USD'\n    }).format(amount);\n  };\n  const getStatusColor = status => {\n    const colors = {\n      'received': '#2196f3',\n      'design': '#ff9800',\n      'milling': '#9c27b0',\n      'sintering': '#e91e63',\n      'qc': '#ff5722',\n      'shipped': '#4caf50',\n      'delivered': '#8bc34a',\n      'cancelled': '#757575'\n    };\n    return colors[status] || '#666';\n  };\n  const getPriorityColor = priority => {\n    const colors = {\n      'low': '#4caf50',\n      'normal': '#2196f3',\n      'urgent': '#ff9800',\n      'stat': '#f44336'\n    };\n    return colors[priority] || '#666';\n  };\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        padding: '2rem',\n        textAlign: 'center'\n      },\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          fontSize: '1.2rem',\n          color: '#666'\n        },\n        children: \"Loading dashboard...\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 195,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 194,\n      columnNumber: 7\n    }, this);\n  }\n  if (error) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        padding: '2rem',\n        textAlign: 'center'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          color: '#f44336',\n          marginBottom: '1rem'\n        },\n        children: error\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 205,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        onClick: loadDashboardData,\n        style: {\n          background: '#1976d2',\n          color: 'white',\n          border: 'none',\n          padding: '0.5rem 1rem',\n          borderRadius: '4px',\n          cursor: 'pointer'\n        },\n        children: \"Retry\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 208,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 204,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    style: {\n      padding: '2rem'\n    },\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        marginBottom: '2rem'\n      },\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          display: 'flex',\n          justifyContent: 'space-between',\n          alignItems: 'center'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n            style: {\n              margin: 0,\n              color: '#1976d2'\n            },\n            children: \"\\uD83E\\uDDB7 DentFlow Dashboard\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 230,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            style: {\n              margin: '0.5rem 0',\n              color: '#666'\n            },\n            children: [\"Welcome back, \", user === null || user === void 0 ? void 0 : user.first_name, \" \", user === null || user === void 0 ? void 0 : user.last_name, \" (\", user === null || user === void 0 ? void 0 : user.role, \")\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 233,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 229,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            background: backendStatus.available ? '#e8f5e8' : '#fff3cd',\n            color: backendStatus.available ? '#2e7d32' : '#856404',\n            padding: '0.5rem 1rem',\n            borderRadius: '20px',\n            fontSize: '0.9rem',\n            fontWeight: 'bold',\n            border: backendStatus.available ? '1px solid #4caf50' : '1px solid #ffc107'\n          },\n          children: backendStatus.available ? '🟢 Live Data' : '🟡 Demo Data'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 237,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 228,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 227,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        display: 'grid',\n        gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))',\n        gap: '1rem',\n        marginBottom: '2rem'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        style: cardStyle,\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          style: {\n            margin: '0 0 0.5rem 0',\n            color: '#666'\n          },\n          children: \"Active Cases\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 259,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            fontSize: '2rem',\n            fontWeight: 'bold',\n            color: '#1976d2'\n          },\n          children: stats.active_cases\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 260,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"small\", {\n          style: {\n            color: '#666'\n          },\n          children: \"In production\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 263,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 258,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: cardStyle,\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          style: {\n            margin: '0 0 0.5rem 0',\n            color: '#666'\n          },\n          children: \"Completed Today\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 267,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            fontSize: '2rem',\n            fontWeight: 'bold',\n            color: '#4caf50'\n          },\n          children: stats.completed_today\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 268,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"small\", {\n          style: {\n            color: '#666'\n          },\n          children: \"Cases finished\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 271,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 266,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: cardStyle,\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          style: {\n            margin: '0 0 0.5rem 0',\n            color: '#666'\n          },\n          children: \"Pending QC\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 275,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            fontSize: '2rem',\n            fontWeight: 'bold',\n            color: '#ff9800'\n          },\n          children: stats.pending_qc\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 276,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"small\", {\n          style: {\n            color: '#666'\n          },\n          children: \"Quality control needed\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 279,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 274,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: cardStyle,\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          style: {\n            margin: '0 0 0.5rem 0',\n            color: '#666'\n          },\n          children: \"Revenue Today\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 283,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            fontSize: '2rem',\n            fontWeight: 'bold',\n            color: '#2196f3'\n          },\n          children: formatCurrency(stats.revenue_today)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 284,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"small\", {\n          style: {\n            color: '#666'\n          },\n          children: \"Daily earnings\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 287,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 282,\n        columnNumber: 9\n      }, this), stats.overdue_cases > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          ...cardStyle,\n          borderLeft: '4px solid #f44336'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          style: {\n            margin: '0 0 0.5rem 0',\n            color: '#666'\n          },\n          children: \"Overdue Cases\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 292,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            fontSize: '2rem',\n            fontWeight: 'bold',\n            color: '#f44336'\n          },\n          children: stats.overdue_cases\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 293,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"small\", {\n          style: {\n            color: '#666'\n          },\n          children: \"Immediate attention needed\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 296,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 291,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 252,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n        style: {\n          marginBottom: '1rem',\n          color: '#333'\n        },\n        children: \"Recent Cases\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 302,\n        columnNumber: 9\n      }, this), recentCases.length === 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n        style: cardStyle,\n        children: /*#__PURE__*/_jsxDEV(\"p\", {\n          style: {\n            margin: 0,\n            color: '#666',\n            textAlign: 'center'\n          },\n          children: \"No cases found. Create your first case to get started!\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 305,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 304,\n        columnNumber: 11\n      }, this) : recentCases.map(case_ => /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          ...cardStyle,\n          marginBottom: '1rem',\n          borderLeft: `4px solid ${getStatusColor(case_.status)}`\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            display: 'flex',\n            justifyContent: 'space-between',\n            alignItems: 'flex-start',\n            marginBottom: '0.5rem'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n              style: {\n                margin: '0 0 0.25rem 0',\n                color: '#333'\n              },\n              children: [\"Case \", case_.id]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 323,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              style: {\n                margin: 0,\n                color: '#666'\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                children: \"Patient:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 327,\n                columnNumber: 21\n              }, this), \" \", case_.patient_name]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 326,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 322,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              textAlign: 'right'\n            },\n            children: /*#__PURE__*/_jsxDEV(\"span\", {\n              style: {\n                background: getPriorityColor(case_.priority),\n                color: 'white',\n                padding: '0.25rem 0.5rem',\n                borderRadius: '4px',\n                fontSize: '0.8rem',\n                textTransform: 'uppercase'\n              },\n              children: case_.priority\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 331,\n              columnNumber: 19\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 330,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 316,\n          columnNumber: 15\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            display: 'grid',\n            gridTemplateColumns: 'repeat(auto-fit, minmax(150px, 1fr))',\n            gap: '1rem',\n            color: '#666',\n            fontSize: '0.9rem'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n              children: \"Type:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 352,\n              columnNumber: 19\n            }, this), \" \", case_.service_type]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 351,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n              children: \"Tooth:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 355,\n              columnNumber: 19\n            }, this), \" #\", case_.tooth_number]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 354,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n              children: \"Status:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 358,\n              columnNumber: 19\n            }, this), ' ', /*#__PURE__*/_jsxDEV(\"span\", {\n              style: {\n                color: getStatusColor(case_.status)\n              },\n              children: case_.current_stage\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 359,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 357,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n              children: \"Due:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 364,\n              columnNumber: 19\n            }, this), ' ', case_.due_date ? /*#__PURE__*/_jsxDEV(\"span\", {\n              style: {\n                color: case_.is_overdue ? '#f44336' : '#666'\n              },\n              children: case_.days_until_due !== null && case_.days_until_due !== undefined ? case_.days_until_due >= 0 ? `${case_.days_until_due} days` : `${Math.abs(case_.days_until_due)} days overdue` : 'Today'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 366,\n              columnNumber: 21\n            }, this) : 'Not set']\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 363,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 344,\n          columnNumber: 15\n        }, this)]\n      }, case_.id, true, {\n        fileName: _jsxFileName,\n        lineNumber: 311,\n        columnNumber: 13\n      }, this))]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 301,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        marginTop: '2rem',\n        padding: '1rem',\n        background: backendStatus.available ? '#e8f5e8' : '#fff3cd',\n        borderRadius: '4px',\n        border: backendStatus.available ? '1px solid #4caf50' : '1px solid #ffc107'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n        style: {\n          color: backendStatus.available ? '#2e7d32' : '#856404',\n          margin: '0 0 0.5rem 0'\n        },\n        children: [backendStatus.available ? '✅' : '⚠️', \" Backend Status: \", backendStatus.mode === 'live' ? 'Connected' : 'Mock Data']\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 392,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        style: {\n          margin: 0,\n          color: backendStatus.available ? '#2e7d32' : '#856404'\n        },\n        children: backendStatus.available ? 'React frontend successfully connected to Django REST API backend. Real-time data loading from DentFlow case management system.' : 'Backend unavailable. Using demonstration data. All features remain functional for testing.'\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 398,\n        columnNumber: 9\n      }, this), !backendStatus.available && /*#__PURE__*/_jsxDEV(\"button\", {\n        onClick: loadDashboardData,\n        style: {\n          marginTop: '0.5rem',\n          background: '#1976d2',\n          color: 'white',\n          border: 'none',\n          padding: '0.5rem 1rem',\n          borderRadius: '4px',\n          cursor: 'pointer'\n        },\n        children: \"\\uD83D\\uDD04 Try Reconnect\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 408,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 385,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 225,\n    columnNumber: 5\n  }, this);\n};\n\n// Styles\n_s(Dashboard, \"TpSWFmBzfBX/bnEMSRexAAm8j9w=\", false, function () {\n  return [useAuth, useTheme];\n});\n_c = Dashboard;\nconst cardStyle = {\n  background: '#fff',\n  padding: '1.5rem',\n  borderRadius: '8px',\n  boxShadow: '0 2px 8px rgba(0,0,0,0.1)',\n  border: '1px solid #e0e0e0'\n};\nexport default Dashboard;\nvar _c;\n$RefreshReg$(_c, \"Dashboard\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useTheme", "CasesService", "useAuth", "jsxDEV", "_jsxDEV", "Dashboard", "_s", "user", "theme", "stats", "setStats", "active_cases", "completed_today", "pending_qc", "revenue_today", "overdue_cases", "total_cases_this_month", "revenue_this_month", "average_completion_time", "customer_satisfaction", "inventory_alerts", "technician_utilization", "pending_invoices", "recentCases", "setRecentCases", "recentActivities", "setRecentActivities", "technicianStats", "setTechnicianStats", "inventoryAlerts", "setInventoryAlerts", "loading", "setLoading", "error", "setError", "backendStatus", "setBackendStatus", "available", "mode", "loadDashboardData", "checkBackendHealth", "dashboardStats", "cases", "Promise", "all", "getDashboardStats", "getCases", "slice", "getBackendStatus", "err", "console", "formatCurrency", "amount", "Intl", "NumberFormat", "style", "currency", "format", "getStatusColor", "status", "colors", "getPriorityColor", "priority", "padding", "textAlign", "children", "fontSize", "color", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "marginBottom", "onClick", "background", "border", "borderRadius", "cursor", "display", "justifyContent", "alignItems", "margin", "first_name", "last_name", "role", "fontWeight", "gridTemplateColumns", "gap", "cardStyle", "borderLeft", "length", "map", "case_", "id", "patient_name", "textTransform", "service_type", "tooth_number", "current_stage", "due_date", "is_overdue", "days_until_due", "undefined", "Math", "abs", "marginTop", "_c", "boxShadow", "$RefreshReg$"], "sources": ["C:/GPT4_PROJECTS/DENTAL_LAB_ARCHITECTURE_PATTERNS/dentflow/frontend/src/components/Dashboard.tsx"], "sourcesContent": ["/**\n * DentFlow Dashboard Component - Enhanced Version\n * Comprehensive dashboard with advanced widgets, charts, and real-time data visualization\n */\n\nimport React, { useState, useEffect } from 'react';\nimport {\n  Box,\n  Grid,\n  Card,\n  CardContent,\n  Typography,\n  Avatar,\n  Chip,\n  LinearProgress,\n  IconButton,\n  Button,\n  Alert,\n  Paper,\n  List,\n  ListItem,\n  ListItemText,\n  ListItemAvatar,\n  Divider,\n  Badge,\n  Tooltip,\n  CircularProgress,\n  Table,\n  TableBody,\n  TableCell,\n  TableContainer,\n  TableHead,\n  TableRow,\n  useTheme,\n  alpha,\n} from '@mui/material';\nimport {\n  TrendingUp,\n  TrendingDown,\n  Assignment,\n  CheckCircle,\n  Warning,\n  Schedule,\n  AttachMoney,\n  People,\n  Inventory,\n  Analytics,\n  Refresh,\n  Notifications,\n  CalendarToday,\n  Build,\n  LocalShipping,\n  QualityControl,\n} from '@mui/icons-material';\nimport { CasesService } from '../api/casesService';\nimport { Case } from '../api/types';\nimport { useAuth } from '../context/AuthContext';\n\ninterface DashboardStats {\n  active_cases: number;\n  completed_today: number;\n  pending_qc: number;\n  revenue_today: number;\n  overdue_cases: number;\n  total_cases_this_month: number;\n  revenue_this_month: number;\n  average_completion_time: number;\n  customer_satisfaction: number;\n  inventory_alerts: number;\n  technician_utilization: number;\n  pending_invoices: number;\n}\n\ninterface ActivityItem {\n  id: string;\n  type: 'case_created' | 'case_completed' | 'payment_received' | 'inventory_low' | 'qc_failed';\n  title: string;\n  description: string;\n  timestamp: string;\n  user?: string;\n  priority?: 'low' | 'medium' | 'high';\n}\n\ninterface TechnicianStats {\n  id: string;\n  name: string;\n  active_cases: number;\n  completed_today: number;\n  efficiency_score: number;\n  specialization: string;\n}\n\ninterface InventoryAlert {\n  id: string;\n  material_name: string;\n  current_stock: number;\n  minimum_stock: number;\n  unit: string;\n  urgency: 'low' | 'medium' | 'high';\n}\n\nconst Dashboard: React.FC = () => {\n  const { user } = useAuth();\n  const theme = useTheme();\n\n  const [stats, setStats] = useState<DashboardStats>({\n    active_cases: 0,\n    completed_today: 0,\n    pending_qc: 0,\n    revenue_today: 0,\n    overdue_cases: 0,\n    total_cases_this_month: 0,\n    revenue_this_month: 0,\n    average_completion_time: 0,\n    customer_satisfaction: 0,\n    inventory_alerts: 0,\n    technician_utilization: 0,\n    pending_invoices: 0,\n  });\n\n  const [recentCases, setRecentCases] = useState<Case[]>([]);\n  const [recentActivities, setRecentActivities] = useState<ActivityItem[]>([]);\n  const [technicianStats, setTechnicianStats] = useState<TechnicianStats[]>([]);\n  const [inventoryAlerts, setInventoryAlerts] = useState<InventoryAlert[]>([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState<string>('');\n  const [backendStatus, setBackendStatus] = useState<{available: boolean; mode: string}>({\n    available: false,\n    mode: 'mock'\n  });\n\n  useEffect(() => {\n    loadDashboardData();\n  }, []);\n\n  const loadDashboardData = async () => {\n    try {\n      setLoading(true);\n      setError('');\n      \n      // Check backend health first\n      await CasesService.checkBackendHealth();\n      \n      const [dashboardStats, cases] = await Promise.all([\n        CasesService.getDashboardStats(),\n        CasesService.getCases(),\n      ]);\n      \n      setStats(dashboardStats);\n      setRecentCases(cases.slice(0, 5)); // Show last 5 cases\n      setBackendStatus(CasesService.getBackendStatus());\n      \n    } catch (err: any) {\n      console.error('Error loading dashboard:', err);\n      setError('Failed to load some dashboard data');\n      setBackendStatus(CasesService.getBackendStatus());\n    } finally {\n      setLoading(false);\n    }\n  };\n  const formatCurrency = (amount: number) => {\n    return new Intl.NumberFormat('en-US', {\n      style: 'currency',\n      currency: 'USD',\n    }).format(amount);\n  };\n\n  const getStatusColor = (status: string) => {\n    const colors: Record<string, string> = {\n      'received': '#2196f3',\n      'design': '#ff9800',\n      'milling': '#9c27b0',\n      'sintering': '#e91e63',\n      'qc': '#ff5722',\n      'shipped': '#4caf50',\n      'delivered': '#8bc34a',\n      'cancelled': '#757575',\n    };\n    return colors[status] || '#666';\n  };\n\n  const getPriorityColor = (priority: string) => {\n    const colors: Record<string, string> = {\n      'low': '#4caf50',\n      'normal': '#2196f3',\n      'urgent': '#ff9800',\n      'stat': '#f44336',\n    };\n    return colors[priority] || '#666';\n  };\n\n  if (loading) {\n    return (\n      <div style={{ padding: '2rem', textAlign: 'center' }}>\n        <div style={{ fontSize: '1.2rem', color: '#666' }}>\n          Loading dashboard...\n        </div>\n      </div>\n    );\n  }\n\n  if (error) {\n    return (\n      <div style={{ padding: '2rem', textAlign: 'center' }}>\n        <div style={{ color: '#f44336', marginBottom: '1rem' }}>\n          {error}\n        </div>\n        <button \n          onClick={loadDashboardData}\n          style={{\n            background: '#1976d2',\n            color: 'white',\n            border: 'none',\n            padding: '0.5rem 1rem',\n            borderRadius: '4px',\n            cursor: 'pointer',\n          }}\n        >\n          Retry\n        </button>\n      </div>\n    );\n  }\n  return (\n    <div style={{ padding: '2rem' }}>\n      {/* Header */}\n      <div style={{ marginBottom: '2rem' }}>\n        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>\n          <div>\n            <h1 style={{ margin: 0, color: '#1976d2' }}>\n              🦷 DentFlow Dashboard\n            </h1>\n            <p style={{ margin: '0.5rem 0', color: '#666' }}>\n              Welcome back, {user?.first_name} {user?.last_name} ({user?.role})\n            </p>\n          </div>\n          <div style={{\n            background: backendStatus.available ? '#e8f5e8' : '#fff3cd',\n            color: backendStatus.available ? '#2e7d32' : '#856404',\n            padding: '0.5rem 1rem',\n            borderRadius: '20px',\n            fontSize: '0.9rem',\n            fontWeight: 'bold',\n            border: backendStatus.available ? '1px solid #4caf50' : '1px solid #ffc107'\n          }}>\n            {backendStatus.available ? '🟢 Live Data' : '🟡 Demo Data'}\n          </div>\n        </div>\n      </div>\n\n      {/* Stats Grid */}\n      <div style={{\n        display: 'grid',\n        gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))',\n        gap: '1rem',\n        marginBottom: '2rem',\n      }}>\n        <div style={cardStyle}>\n          <h3 style={{ margin: '0 0 0.5rem 0', color: '#666' }}>Active Cases</h3>\n          <div style={{ fontSize: '2rem', fontWeight: 'bold', color: '#1976d2' }}>\n            {stats.active_cases}\n          </div>\n          <small style={{ color: '#666' }}>In production</small>\n        </div>\n\n        <div style={cardStyle}>\n          <h3 style={{ margin: '0 0 0.5rem 0', color: '#666' }}>Completed Today</h3>\n          <div style={{ fontSize: '2rem', fontWeight: 'bold', color: '#4caf50' }}>\n            {stats.completed_today}\n          </div>\n          <small style={{ color: '#666' }}>Cases finished</small>\n        </div>\n\n        <div style={cardStyle}>\n          <h3 style={{ margin: '0 0 0.5rem 0', color: '#666' }}>Pending QC</h3>\n          <div style={{ fontSize: '2rem', fontWeight: 'bold', color: '#ff9800' }}>\n            {stats.pending_qc}\n          </div>\n          <small style={{ color: '#666' }}>Quality control needed</small>\n        </div>\n\n        <div style={cardStyle}>\n          <h3 style={{ margin: '0 0 0.5rem 0', color: '#666' }}>Revenue Today</h3>\n          <div style={{ fontSize: '2rem', fontWeight: 'bold', color: '#2196f3' }}>\n            {formatCurrency(stats.revenue_today)}\n          </div>\n          <small style={{ color: '#666' }}>Daily earnings</small>\n        </div>\n\n        {stats.overdue_cases > 0 && (\n          <div style={{ ...cardStyle, borderLeft: '4px solid #f44336' }}>\n            <h3 style={{ margin: '0 0 0.5rem 0', color: '#666' }}>Overdue Cases</h3>\n            <div style={{ fontSize: '2rem', fontWeight: 'bold', color: '#f44336' }}>\n              {stats.overdue_cases}\n            </div>\n            <small style={{ color: '#666' }}>Immediate attention needed</small>\n          </div>\n        )}\n      </div>\n      {/* Recent Cases */}\n      <div>\n        <h2 style={{ marginBottom: '1rem', color: '#333' }}>Recent Cases</h2>\n        {recentCases.length === 0 ? (\n          <div style={cardStyle}>\n            <p style={{ margin: 0, color: '#666', textAlign: 'center' }}>\n              No cases found. Create your first case to get started!\n            </p>\n          </div>\n        ) : (\n          recentCases.map((case_) => (\n            <div key={case_.id} style={{\n              ...cardStyle,\n              marginBottom: '1rem',\n              borderLeft: `4px solid ${getStatusColor(case_.status)}`,\n            }}>\n              <div style={{\n                display: 'flex',\n                justifyContent: 'space-between',\n                alignItems: 'flex-start',\n                marginBottom: '0.5rem',\n              }}>\n                <div>\n                  <h4 style={{ margin: '0 0 0.25rem 0', color: '#333' }}>\n                    Case {case_.id}\n                  </h4>\n                  <p style={{ margin: 0, color: '#666' }}>\n                    <strong>Patient:</strong> {case_.patient_name}\n                  </p>\n                </div>\n                <div style={{ textAlign: 'right' }}>\n                  <span style={{\n                    background: getPriorityColor(case_.priority),\n                    color: 'white',\n                    padding: '0.25rem 0.5rem',\n                    borderRadius: '4px',\n                    fontSize: '0.8rem',\n                    textTransform: 'uppercase',\n                  }}>\n                    {case_.priority}\n                  </span>\n                </div>\n              </div>\n              \n              <div style={{\n                display: 'grid',\n                gridTemplateColumns: 'repeat(auto-fit, minmax(150px, 1fr))',\n                gap: '1rem',\n                color: '#666',\n                fontSize: '0.9rem',\n              }}>\n                <div>\n                  <strong>Type:</strong> {case_.service_type}\n                </div>\n                <div>\n                  <strong>Tooth:</strong> #{case_.tooth_number}\n                </div>\n                <div>\n                  <strong>Status:</strong>{' '}\n                  <span style={{ color: getStatusColor(case_.status) }}>\n                    {case_.current_stage}\n                  </span>\n                </div>\n                <div>\n                  <strong>Due:</strong>{' '}\n                  {case_.due_date ? (\n                    <span style={{ \n                      color: case_.is_overdue ? '#f44336' : '#666' \n                    }}>\n                      {case_.days_until_due !== null && case_.days_until_due !== undefined ? (\n                        case_.days_until_due >= 0 ?\n                          `${case_.days_until_due} days` :\n                          `${Math.abs(case_.days_until_due)} days overdue`\n                      ) : 'Today'}\n                    </span>\n                  ) : (\n                    'Not set'\n                  )}\n                </div>\n              </div>\n            </div>\n          ))\n        )}\n      </div>\n\n      <div style={{\n        marginTop: '2rem',\n        padding: '1rem',\n        background: backendStatus.available ? '#e8f5e8' : '#fff3cd',\n        borderRadius: '4px',\n        border: backendStatus.available ? '1px solid #4caf50' : '1px solid #ffc107',\n      }}>\n        <h4 style={{ \n          color: backendStatus.available ? '#2e7d32' : '#856404', \n          margin: '0 0 0.5rem 0' \n        }}>\n          {backendStatus.available ? '✅' : '⚠️'} Backend Status: {backendStatus.mode === 'live' ? 'Connected' : 'Mock Data'}\n        </h4>\n        <p style={{ \n          margin: 0, \n          color: backendStatus.available ? '#2e7d32' : '#856404' \n        }}>\n          {backendStatus.available \n            ? 'React frontend successfully connected to Django REST API backend. Real-time data loading from DentFlow case management system.'\n            : 'Backend unavailable. Using demonstration data. All features remain functional for testing.'\n          }\n        </p>\n        {!backendStatus.available && (\n          <button\n            onClick={loadDashboardData}\n            style={{\n              marginTop: '0.5rem',\n              background: '#1976d2',\n              color: 'white',\n              border: 'none',\n              padding: '0.5rem 1rem',\n              borderRadius: '4px',\n              cursor: 'pointer',\n            }}\n          >\n            🔄 Try Reconnect\n          </button>\n        )}\n      </div>\n    </div>\n  );\n};\n\n// Styles\nconst cardStyle: React.CSSProperties = {\n  background: '#fff',\n  padding: '1.5rem',\n  borderRadius: '8px',\n  boxShadow: '0 2px 8px rgba(0,0,0,0.1)',\n  border: '1px solid #e0e0e0',\n};\n\nexport default Dashboard;"], "mappings": ";;AAAA;AACA;AACA;AACA;;AAEA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SA2BEC,QAAQ,QAEH,eAAe;AAmBtB,SAASC,YAAY,QAAQ,qBAAqB;AAElD,SAASC,OAAO,QAAQ,wBAAwB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AA6CjD,MAAMC,SAAmB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAChC,MAAM;IAAEC;EAAK,CAAC,GAAGL,OAAO,CAAC,CAAC;EAC1B,MAAMM,KAAK,GAAGR,QAAQ,CAAC,CAAC;EAExB,MAAM,CAACS,KAAK,EAAEC,QAAQ,CAAC,GAAGZ,QAAQ,CAAiB;IACjDa,YAAY,EAAE,CAAC;IACfC,eAAe,EAAE,CAAC;IAClBC,UAAU,EAAE,CAAC;IACbC,aAAa,EAAE,CAAC;IAChBC,aAAa,EAAE,CAAC;IAChBC,sBAAsB,EAAE,CAAC;IACzBC,kBAAkB,EAAE,CAAC;IACrBC,uBAAuB,EAAE,CAAC;IAC1BC,qBAAqB,EAAE,CAAC;IACxBC,gBAAgB,EAAE,CAAC;IACnBC,sBAAsB,EAAE,CAAC;IACzBC,gBAAgB,EAAE;EACpB,CAAC,CAAC;EAEF,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAG1B,QAAQ,CAAS,EAAE,CAAC;EAC1D,MAAM,CAAC2B,gBAAgB,EAAEC,mBAAmB,CAAC,GAAG5B,QAAQ,CAAiB,EAAE,CAAC;EAC5E,MAAM,CAAC6B,eAAe,EAAEC,kBAAkB,CAAC,GAAG9B,QAAQ,CAAoB,EAAE,CAAC;EAC7E,MAAM,CAAC+B,eAAe,EAAEC,kBAAkB,CAAC,GAAGhC,QAAQ,CAAmB,EAAE,CAAC;EAC5E,MAAM,CAACiC,OAAO,EAAEC,UAAU,CAAC,GAAGlC,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACmC,KAAK,EAAEC,QAAQ,CAAC,GAAGpC,QAAQ,CAAS,EAAE,CAAC;EAC9C,MAAM,CAACqC,aAAa,EAAEC,gBAAgB,CAAC,GAAGtC,QAAQ,CAAqC;IACrFuC,SAAS,EAAE,KAAK;IAChBC,IAAI,EAAE;EACR,CAAC,CAAC;EAEFvC,SAAS,CAAC,MAAM;IACdwC,iBAAiB,CAAC,CAAC;EACrB,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMA,iBAAiB,GAAG,MAAAA,CAAA,KAAY;IACpC,IAAI;MACFP,UAAU,CAAC,IAAI,CAAC;MAChBE,QAAQ,CAAC,EAAE,CAAC;;MAEZ;MACA,MAAMjC,YAAY,CAACuC,kBAAkB,CAAC,CAAC;MAEvC,MAAM,CAACC,cAAc,EAAEC,KAAK,CAAC,GAAG,MAAMC,OAAO,CAACC,GAAG,CAAC,CAChD3C,YAAY,CAAC4C,iBAAiB,CAAC,CAAC,EAChC5C,YAAY,CAAC6C,QAAQ,CAAC,CAAC,CACxB,CAAC;MAEFpC,QAAQ,CAAC+B,cAAc,CAAC;MACxBjB,cAAc,CAACkB,KAAK,CAACK,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;MACnCX,gBAAgB,CAACnC,YAAY,CAAC+C,gBAAgB,CAAC,CAAC,CAAC;IAEnD,CAAC,CAAC,OAAOC,GAAQ,EAAE;MACjBC,OAAO,CAACjB,KAAK,CAAC,0BAA0B,EAAEgB,GAAG,CAAC;MAC9Cf,QAAQ,CAAC,oCAAoC,CAAC;MAC9CE,gBAAgB,CAACnC,YAAY,CAAC+C,gBAAgB,CAAC,CAAC,CAAC;IACnD,CAAC,SAAS;MACRhB,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EACD,MAAMmB,cAAc,GAAIC,MAAc,IAAK;IACzC,OAAO,IAAIC,IAAI,CAACC,YAAY,CAAC,OAAO,EAAE;MACpCC,KAAK,EAAE,UAAU;MACjBC,QAAQ,EAAE;IACZ,CAAC,CAAC,CAACC,MAAM,CAACL,MAAM,CAAC;EACnB,CAAC;EAED,MAAMM,cAAc,GAAIC,MAAc,IAAK;IACzC,MAAMC,MAA8B,GAAG;MACrC,UAAU,EAAE,SAAS;MACrB,QAAQ,EAAE,SAAS;MACnB,SAAS,EAAE,SAAS;MACpB,WAAW,EAAE,SAAS;MACtB,IAAI,EAAE,SAAS;MACf,SAAS,EAAE,SAAS;MACpB,WAAW,EAAE,SAAS;MACtB,WAAW,EAAE;IACf,CAAC;IACD,OAAOA,MAAM,CAACD,MAAM,CAAC,IAAI,MAAM;EACjC,CAAC;EAED,MAAME,gBAAgB,GAAIC,QAAgB,IAAK;IAC7C,MAAMF,MAA8B,GAAG;MACrC,KAAK,EAAE,SAAS;MAChB,QAAQ,EAAE,SAAS;MACnB,QAAQ,EAAE,SAAS;MACnB,MAAM,EAAE;IACV,CAAC;IACD,OAAOA,MAAM,CAACE,QAAQ,CAAC,IAAI,MAAM;EACnC,CAAC;EAED,IAAI/B,OAAO,EAAE;IACX,oBACE3B,OAAA;MAAKmD,KAAK,EAAE;QAAEQ,OAAO,EAAE,MAAM;QAAEC,SAAS,EAAE;MAAS,CAAE;MAAAC,QAAA,eACnD7D,OAAA;QAAKmD,KAAK,EAAE;UAAEW,QAAQ,EAAE,QAAQ;UAAEC,KAAK,EAAE;QAAO,CAAE;QAAAF,QAAA,EAAC;MAEnD;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;EAEA,IAAItC,KAAK,EAAE;IACT,oBACE7B,OAAA;MAAKmD,KAAK,EAAE;QAAEQ,OAAO,EAAE,MAAM;QAAEC,SAAS,EAAE;MAAS,CAAE;MAAAC,QAAA,gBACnD7D,OAAA;QAAKmD,KAAK,EAAE;UAAEY,KAAK,EAAE,SAAS;UAAEK,YAAY,EAAE;QAAO,CAAE;QAAAP,QAAA,EACpDhC;MAAK;QAAAmC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eACNnE,OAAA;QACEqE,OAAO,EAAElC,iBAAkB;QAC3BgB,KAAK,EAAE;UACLmB,UAAU,EAAE,SAAS;UACrBP,KAAK,EAAE,OAAO;UACdQ,MAAM,EAAE,MAAM;UACdZ,OAAO,EAAE,aAAa;UACtBa,YAAY,EAAE,KAAK;UACnBC,MAAM,EAAE;QACV,CAAE;QAAAZ,QAAA,EACH;MAED;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC;EAEV;EACA,oBACEnE,OAAA;IAAKmD,KAAK,EAAE;MAAEQ,OAAO,EAAE;IAAO,CAAE;IAAAE,QAAA,gBAE9B7D,OAAA;MAAKmD,KAAK,EAAE;QAAEiB,YAAY,EAAE;MAAO,CAAE;MAAAP,QAAA,eACnC7D,OAAA;QAAKmD,KAAK,EAAE;UAAEuB,OAAO,EAAE,MAAM;UAAEC,cAAc,EAAE,eAAe;UAAEC,UAAU,EAAE;QAAS,CAAE;QAAAf,QAAA,gBACrF7D,OAAA;UAAA6D,QAAA,gBACE7D,OAAA;YAAImD,KAAK,EAAE;cAAE0B,MAAM,EAAE,CAAC;cAAEd,KAAK,EAAE;YAAU,CAAE;YAAAF,QAAA,EAAC;UAE5C;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACLnE,OAAA;YAAGmD,KAAK,EAAE;cAAE0B,MAAM,EAAE,UAAU;cAAEd,KAAK,EAAE;YAAO,CAAE;YAAAF,QAAA,GAAC,gBACjC,EAAC1D,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE2E,UAAU,EAAC,GAAC,EAAC3E,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE4E,SAAS,EAAC,IAAE,EAAC5E,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE6E,IAAI,EAAC,GAClE;UAAA;YAAAhB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC,eACNnE,OAAA;UAAKmD,KAAK,EAAE;YACVmB,UAAU,EAAEvC,aAAa,CAACE,SAAS,GAAG,SAAS,GAAG,SAAS;YAC3D8B,KAAK,EAAEhC,aAAa,CAACE,SAAS,GAAG,SAAS,GAAG,SAAS;YACtD0B,OAAO,EAAE,aAAa;YACtBa,YAAY,EAAE,MAAM;YACpBV,QAAQ,EAAE,QAAQ;YAClBmB,UAAU,EAAE,MAAM;YAClBV,MAAM,EAAExC,aAAa,CAACE,SAAS,GAAG,mBAAmB,GAAG;UAC1D,CAAE;UAAA4B,QAAA,EACC9B,aAAa,CAACE,SAAS,GAAG,cAAc,GAAG;QAAc;UAAA+B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACvD,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNnE,OAAA;MAAKmD,KAAK,EAAE;QACVuB,OAAO,EAAE,MAAM;QACfQ,mBAAmB,EAAE,sCAAsC;QAC3DC,GAAG,EAAE,MAAM;QACXf,YAAY,EAAE;MAChB,CAAE;MAAAP,QAAA,gBACA7D,OAAA;QAAKmD,KAAK,EAAEiC,SAAU;QAAAvB,QAAA,gBACpB7D,OAAA;UAAImD,KAAK,EAAE;YAAE0B,MAAM,EAAE,cAAc;YAAEd,KAAK,EAAE;UAAO,CAAE;UAAAF,QAAA,EAAC;QAAY;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACvEnE,OAAA;UAAKmD,KAAK,EAAE;YAAEW,QAAQ,EAAE,MAAM;YAAEmB,UAAU,EAAE,MAAM;YAAElB,KAAK,EAAE;UAAU,CAAE;UAAAF,QAAA,EACpExD,KAAK,CAACE;QAAY;UAAAyD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChB,CAAC,eACNnE,OAAA;UAAOmD,KAAK,EAAE;YAAEY,KAAK,EAAE;UAAO,CAAE;UAAAF,QAAA,EAAC;QAAa;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACnD,CAAC,eAENnE,OAAA;QAAKmD,KAAK,EAAEiC,SAAU;QAAAvB,QAAA,gBACpB7D,OAAA;UAAImD,KAAK,EAAE;YAAE0B,MAAM,EAAE,cAAc;YAAEd,KAAK,EAAE;UAAO,CAAE;UAAAF,QAAA,EAAC;QAAe;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC1EnE,OAAA;UAAKmD,KAAK,EAAE;YAAEW,QAAQ,EAAE,MAAM;YAAEmB,UAAU,EAAE,MAAM;YAAElB,KAAK,EAAE;UAAU,CAAE;UAAAF,QAAA,EACpExD,KAAK,CAACG;QAAe;UAAAwD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnB,CAAC,eACNnE,OAAA;UAAOmD,KAAK,EAAE;YAAEY,KAAK,EAAE;UAAO,CAAE;UAAAF,QAAA,EAAC;QAAc;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACpD,CAAC,eAENnE,OAAA;QAAKmD,KAAK,EAAEiC,SAAU;QAAAvB,QAAA,gBACpB7D,OAAA;UAAImD,KAAK,EAAE;YAAE0B,MAAM,EAAE,cAAc;YAAEd,KAAK,EAAE;UAAO,CAAE;UAAAF,QAAA,EAAC;QAAU;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACrEnE,OAAA;UAAKmD,KAAK,EAAE;YAAEW,QAAQ,EAAE,MAAM;YAAEmB,UAAU,EAAE,MAAM;YAAElB,KAAK,EAAE;UAAU,CAAE;UAAAF,QAAA,EACpExD,KAAK,CAACI;QAAU;UAAAuD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACd,CAAC,eACNnE,OAAA;UAAOmD,KAAK,EAAE;YAAEY,KAAK,EAAE;UAAO,CAAE;UAAAF,QAAA,EAAC;QAAsB;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC5D,CAAC,eAENnE,OAAA;QAAKmD,KAAK,EAAEiC,SAAU;QAAAvB,QAAA,gBACpB7D,OAAA;UAAImD,KAAK,EAAE;YAAE0B,MAAM,EAAE,cAAc;YAAEd,KAAK,EAAE;UAAO,CAAE;UAAAF,QAAA,EAAC;QAAa;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACxEnE,OAAA;UAAKmD,KAAK,EAAE;YAAEW,QAAQ,EAAE,MAAM;YAAEmB,UAAU,EAAE,MAAM;YAAElB,KAAK,EAAE;UAAU,CAAE;UAAAF,QAAA,EACpEd,cAAc,CAAC1C,KAAK,CAACK,aAAa;QAAC;UAAAsD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjC,CAAC,eACNnE,OAAA;UAAOmD,KAAK,EAAE;YAAEY,KAAK,EAAE;UAAO,CAAE;UAAAF,QAAA,EAAC;QAAc;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACpD,CAAC,EAEL9D,KAAK,CAACM,aAAa,GAAG,CAAC,iBACtBX,OAAA;QAAKmD,KAAK,EAAE;UAAE,GAAGiC,SAAS;UAAEC,UAAU,EAAE;QAAoB,CAAE;QAAAxB,QAAA,gBAC5D7D,OAAA;UAAImD,KAAK,EAAE;YAAE0B,MAAM,EAAE,cAAc;YAAEd,KAAK,EAAE;UAAO,CAAE;UAAAF,QAAA,EAAC;QAAa;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACxEnE,OAAA;UAAKmD,KAAK,EAAE;YAAEW,QAAQ,EAAE,MAAM;YAAEmB,UAAU,EAAE,MAAM;YAAElB,KAAK,EAAE;UAAU,CAAE;UAAAF,QAAA,EACpExD,KAAK,CAACM;QAAa;UAAAqD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjB,CAAC,eACNnE,OAAA;UAAOmD,KAAK,EAAE;YAAEY,KAAK,EAAE;UAAO,CAAE;UAAAF,QAAA,EAAC;QAA0B;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAChE,CACN;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC,eAENnE,OAAA;MAAA6D,QAAA,gBACE7D,OAAA;QAAImD,KAAK,EAAE;UAAEiB,YAAY,EAAE,MAAM;UAAEL,KAAK,EAAE;QAAO,CAAE;QAAAF,QAAA,EAAC;MAAY;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,EACpEhD,WAAW,CAACmE,MAAM,KAAK,CAAC,gBACvBtF,OAAA;QAAKmD,KAAK,EAAEiC,SAAU;QAAAvB,QAAA,eACpB7D,OAAA;UAAGmD,KAAK,EAAE;YAAE0B,MAAM,EAAE,CAAC;YAAEd,KAAK,EAAE,MAAM;YAAEH,SAAS,EAAE;UAAS,CAAE;UAAAC,QAAA,EAAC;QAE7D;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC,GAENhD,WAAW,CAACoE,GAAG,CAAEC,KAAK,iBACpBxF,OAAA;QAAoBmD,KAAK,EAAE;UACzB,GAAGiC,SAAS;UACZhB,YAAY,EAAE,MAAM;UACpBiB,UAAU,EAAE,aAAa/B,cAAc,CAACkC,KAAK,CAACjC,MAAM,CAAC;QACvD,CAAE;QAAAM,QAAA,gBACA7D,OAAA;UAAKmD,KAAK,EAAE;YACVuB,OAAO,EAAE,MAAM;YACfC,cAAc,EAAE,eAAe;YAC/BC,UAAU,EAAE,YAAY;YACxBR,YAAY,EAAE;UAChB,CAAE;UAAAP,QAAA,gBACA7D,OAAA;YAAA6D,QAAA,gBACE7D,OAAA;cAAImD,KAAK,EAAE;gBAAE0B,MAAM,EAAE,eAAe;gBAAEd,KAAK,EAAE;cAAO,CAAE;cAAAF,QAAA,GAAC,OAChD,EAAC2B,KAAK,CAACC,EAAE;YAAA;cAAAzB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACZ,CAAC,eACLnE,OAAA;cAAGmD,KAAK,EAAE;gBAAE0B,MAAM,EAAE,CAAC;gBAAEd,KAAK,EAAE;cAAO,CAAE;cAAAF,QAAA,gBACrC7D,OAAA;gBAAA6D,QAAA,EAAQ;cAAQ;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,KAAC,EAACqB,KAAK,CAACE,YAAY;YAAA;cAAA1B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC5C,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CAAC,eACNnE,OAAA;YAAKmD,KAAK,EAAE;cAAES,SAAS,EAAE;YAAQ,CAAE;YAAAC,QAAA,eACjC7D,OAAA;cAAMmD,KAAK,EAAE;gBACXmB,UAAU,EAAEb,gBAAgB,CAAC+B,KAAK,CAAC9B,QAAQ,CAAC;gBAC5CK,KAAK,EAAE,OAAO;gBACdJ,OAAO,EAAE,gBAAgB;gBACzBa,YAAY,EAAE,KAAK;gBACnBV,QAAQ,EAAE,QAAQ;gBAClB6B,aAAa,EAAE;cACjB,CAAE;cAAA9B,QAAA,EACC2B,KAAK,CAAC9B;YAAQ;cAAAM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACX;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAENnE,OAAA;UAAKmD,KAAK,EAAE;YACVuB,OAAO,EAAE,MAAM;YACfQ,mBAAmB,EAAE,sCAAsC;YAC3DC,GAAG,EAAE,MAAM;YACXpB,KAAK,EAAE,MAAM;YACbD,QAAQ,EAAE;UACZ,CAAE;UAAAD,QAAA,gBACA7D,OAAA;YAAA6D,QAAA,gBACE7D,OAAA;cAAA6D,QAAA,EAAQ;YAAK;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,KAAC,EAACqB,KAAK,CAACI,YAAY;UAAA;YAAA5B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACvC,CAAC,eACNnE,OAAA;YAAA6D,QAAA,gBACE7D,OAAA;cAAA6D,QAAA,EAAQ;YAAM;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,MAAE,EAACqB,KAAK,CAACK,YAAY;UAAA;YAAA7B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACzC,CAAC,eACNnE,OAAA;YAAA6D,QAAA,gBACE7D,OAAA;cAAA6D,QAAA,EAAQ;YAAO;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,EAAC,GAAG,eAC5BnE,OAAA;cAAMmD,KAAK,EAAE;gBAAEY,KAAK,EAAET,cAAc,CAACkC,KAAK,CAACjC,MAAM;cAAE,CAAE;cAAAM,QAAA,EAClD2B,KAAK,CAACM;YAAa;cAAA9B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChB,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ,CAAC,eACNnE,OAAA;YAAA6D,QAAA,gBACE7D,OAAA;cAAA6D,QAAA,EAAQ;YAAI;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,EAAC,GAAG,EACxBqB,KAAK,CAACO,QAAQ,gBACb/F,OAAA;cAAMmD,KAAK,EAAE;gBACXY,KAAK,EAAEyB,KAAK,CAACQ,UAAU,GAAG,SAAS,GAAG;cACxC,CAAE;cAAAnC,QAAA,EACC2B,KAAK,CAACS,cAAc,KAAK,IAAI,IAAIT,KAAK,CAACS,cAAc,KAAKC,SAAS,GAClEV,KAAK,CAACS,cAAc,IAAI,CAAC,GACvB,GAAGT,KAAK,CAACS,cAAc,OAAO,GAC9B,GAAGE,IAAI,CAACC,GAAG,CAACZ,KAAK,CAACS,cAAc,CAAC,eAAe,GAChD;YAAO;cAAAjC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACP,CAAC,GAEP,SACD;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA,GApEEqB,KAAK,CAACC,EAAE;QAAAzB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAqEb,CACN,CACF;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC,eAENnE,OAAA;MAAKmD,KAAK,EAAE;QACVkD,SAAS,EAAE,MAAM;QACjB1C,OAAO,EAAE,MAAM;QACfW,UAAU,EAAEvC,aAAa,CAACE,SAAS,GAAG,SAAS,GAAG,SAAS;QAC3DuC,YAAY,EAAE,KAAK;QACnBD,MAAM,EAAExC,aAAa,CAACE,SAAS,GAAG,mBAAmB,GAAG;MAC1D,CAAE;MAAA4B,QAAA,gBACA7D,OAAA;QAAImD,KAAK,EAAE;UACTY,KAAK,EAAEhC,aAAa,CAACE,SAAS,GAAG,SAAS,GAAG,SAAS;UACtD4C,MAAM,EAAE;QACV,CAAE;QAAAhB,QAAA,GACC9B,aAAa,CAACE,SAAS,GAAG,GAAG,GAAG,IAAI,EAAC,mBAAiB,EAACF,aAAa,CAACG,IAAI,KAAK,MAAM,GAAG,WAAW,GAAG,WAAW;MAAA;QAAA8B,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC/G,CAAC,eACLnE,OAAA;QAAGmD,KAAK,EAAE;UACR0B,MAAM,EAAE,CAAC;UACTd,KAAK,EAAEhC,aAAa,CAACE,SAAS,GAAG,SAAS,GAAG;QAC/C,CAAE;QAAA4B,QAAA,EACC9B,aAAa,CAACE,SAAS,GACpB,gIAAgI,GAChI;MAA4F;QAAA+B,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAE/F,CAAC,EACH,CAACpC,aAAa,CAACE,SAAS,iBACvBjC,OAAA;QACEqE,OAAO,EAAElC,iBAAkB;QAC3BgB,KAAK,EAAE;UACLkD,SAAS,EAAE,QAAQ;UACnB/B,UAAU,EAAE,SAAS;UACrBP,KAAK,EAAE,OAAO;UACdQ,MAAM,EAAE,MAAM;UACdZ,OAAO,EAAE,aAAa;UACtBa,YAAY,EAAE,KAAK;UACnBC,MAAM,EAAE;QACV,CAAE;QAAAZ,QAAA,EACH;MAED;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CACT;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;;AAED;AAAAjE,EAAA,CAtUMD,SAAmB;EAAA,QACNH,OAAO,EACVF,QAAQ;AAAA;AAAA0G,EAAA,GAFlBrG,SAAmB;AAuUzB,MAAMmF,SAA8B,GAAG;EACrCd,UAAU,EAAE,MAAM;EAClBX,OAAO,EAAE,QAAQ;EACjBa,YAAY,EAAE,KAAK;EACnB+B,SAAS,EAAE,2BAA2B;EACtChC,MAAM,EAAE;AACV,CAAC;AAED,eAAetE,SAAS;AAAC,IAAAqG,EAAA;AAAAE,YAAA,CAAAF,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}