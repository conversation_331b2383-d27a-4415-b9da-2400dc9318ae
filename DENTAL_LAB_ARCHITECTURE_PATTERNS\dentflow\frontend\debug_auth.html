<!DOCTYPE html>
<html>
<head>
    <title>Dent<PERSON>low Auth Debug</title>
</head>
<body>
    <h1>DentFlow Authentication Debug</h1>
    <div id="status">Checking...</div>
    
    <script>
        // Check localStorage
        const token = localStorage.getItem('dentflow_token');
        console.log('Token in localStorage:', token);
        
        // Check if backend is accessible
        fetch('http://localhost:8001/health/')
            .then(response => {
                console.log('Backend health check:', response.status);
                return response.json();
            })
            .then(data => {
                console.log('Backend response:', data);
                document.getElementById('status').innerHTML = `
                    <h2>Debug Results:</h2>
                    <p><strong>Token:</strong> ${token ? 'Present' : 'Not found'}</p>
                    <p><strong>Backend:</strong> Accessible (${JSON.stringify(data)})</p>
                    <p><strong>Frontend URL:</strong> <a href="http://localhost:3001">http://localhost:3001</a></p>
                `;
            })
            .catch(error => {
                console.error('Backend error:', error);
                document.getElementById('status').innerHTML = `
                    <h2>Debug Results:</h2>
                    <p><strong>Token:</strong> ${token ? 'Present' : 'Not found'}</p>
                    <p><strong>Backend:</strong> Not accessible (${error.message})</p>
                    <p><strong>Issue:</strong> Backend server may not be running or CORS issue</p>
                `;
            });
    </script>
</body>
</html>
