{"ast": null, "code": "/**\n * API Service Layer for DentFlow\n * Centralized API communication with error handling and caching\n */\n\nimport axios from 'axios';\n\n// API Configuration\nconst API_BASE_URL = process.env.REACT_APP_API_URL || 'http://localhost:8000/api';\nconst API_TIMEOUT = 30000; // 30 seconds\n\n// Create axios instance with default configuration\nconst apiClient = axios.create({\n  baseURL: API_BASE_URL,\n  timeout: API_TIMEOUT,\n  headers: {\n    'Content-Type': 'application/json'\n  }\n});\n\n// Request interceptor for authentication\napiClient.interceptors.request.use(config => {\n  const token = localStorage.getItem('authToken');\n  if (token) {\n    config.headers.Authorization = `Bearer ${token}`;\n  }\n  return config;\n}, error => Promise.reject(error));\n\n// Response interceptor for error handling\napiClient.interceptors.response.use(response => response, error => {\n  var _error$response;\n  if (((_error$response = error.response) === null || _error$response === void 0 ? void 0 : _error$response.status) === 401) {\n    // Handle unauthorized - redirect to login\n    localStorage.removeItem('authToken');\n    window.location.href = '/login';\n  }\n  return Promise.reject(error);\n});\n\n// Base API service class\nclass BaseApiService {\n  async get(url, config) {\n    const response = await apiClient.get(url, config);\n    return response.data;\n  }\n  async post(url, data, config) {\n    const response = await apiClient.post(url, data, config);\n    return response.data;\n  }\n  async put(url, data, config) {\n    const response = await apiClient.put(url, data, config);\n    return response.data;\n  }\n  async patch(url, data, config) {\n    const response = await apiClient.patch(url, data, config);\n    return response.data;\n  }\n  async delete(url, config) {\n    const response = await apiClient.delete(url, config);\n    return response.data;\n  }\n}\n\n// Authentication Service\n\nclass AuthService extends BaseApiService {\n  async login(credentials) {\n    return this.post('/auth/login', credentials);\n  }\n  async logout() {\n    await this.post('/auth/logout');\n    localStorage.removeItem('authToken');\n  }\n  async refreshToken(refreshToken) {\n    return this.post('/auth/refresh', {\n      refreshToken\n    });\n  }\n  async getCurrentUser() {\n    return this.get('/auth/me');\n  }\n  async resetPassword(email) {\n    return this.post('/auth/reset-password', {\n      email\n    });\n  }\n}\n\n// Cases Service - Updated to match backend structure\n\nclass CasesService extends BaseApiService {\n  async getCases(params) {\n    return this.get('/cases/', {\n      params\n    });\n  }\n  async getCaseById(id) {\n    return this.get(`/cases/${id}/`);\n  }\n  async createCase(data) {\n    return this.post('/cases/', data);\n  }\n  async updateCase(id, data) {\n    return this.patch(`/cases/${id}/`, data);\n  }\n  async deleteCase(id) {\n    return this.delete(`/cases/${id}/`);\n  }\n\n  // Advance case to next workflow stage\n  async advanceCase(id, notes, quality_check_passed) {\n    return this.post(`/cases/${id}/advance/`, {\n      notes: notes || '',\n      quality_check_passed: quality_check_passed !== false\n    });\n  }\n\n  // Assign technician to case\n  async assignTechnician(id, technician_id, stage) {\n    return this.post(`/cases/${id}/assign_technician/`, {\n      technician_id,\n      stage: stage || ''\n    });\n  }\n\n  // Get overdue cases\n  async getOverdueCases() {\n    return this.get('/cases/overdue/');\n  }\n\n  // Get cases by status\n  async getCasesByStatus(status) {\n    return this.get(`/cases/by_status/?status=${status}`);\n  }\n  async uploadCaseFiles(id, files) {\n    const formData = new FormData();\n    files.forEach(file => formData.append('files', file));\n    return this.post(`/cases/${id}/files/`, formData, {\n      headers: {\n        'Content-Type': 'multipart/form-data'\n      }\n    });\n  }\n}\n\n// Clinics Service\nclass ClinicsService extends BaseApiService {\n  async getClinics() {\n    const response = await this.get('/clinics/');\n    return response.results || [];\n  }\n  async getClinicById(id) {\n    return this.get(`/clinics/${id}/`);\n  }\n  async createClinic(data) {\n    return this.post('/clinics/', data);\n  }\n  async updateClinic(id, data) {\n    return this.patch(`/clinics/${id}/`, data);\n  }\n  async deleteClinic(id) {\n    return this.delete(`/clinics/${id}/`);\n  }\n}\n\n// Workflow Service\n\nclass WorkflowService extends BaseApiService {\n  async getWorkflowStages() {\n    return this.get('/workflow/stages');\n  }\n  async getTasks(params) {\n    return this.get('/workflow/tasks', {\n      params\n    });\n  }\n  async createTask(data) {\n    return this.post('/workflow/tasks', data);\n  }\n  async updateTask(id, data) {\n    return this.put(`/workflow/tasks/${id}`, data);\n  }\n  async assignTask(taskId, assigneeId) {\n    return this.patch(`/workflow/tasks/${taskId}/assign`, {\n      assigneeId\n    });\n  }\n  async startTask(taskId) {\n    return this.patch(`/workflow/tasks/${taskId}/start`);\n  }\n  async completeTask(taskId, notes) {\n    return this.patch(`/workflow/tasks/${taskId}/complete`, {\n      notes\n    });\n  }\n}\n\n// Billing Service\n\nclass BillingService extends BaseApiService {\n  async getInvoices(params) {\n    return this.get('/billing/invoices', {\n      params\n    });\n  }\n  async getInvoiceById(id) {\n    return this.get(`/billing/invoices/${id}`);\n  }\n  async createInvoice(data) {\n    return this.post('/billing/invoices', data);\n  }\n  async updateInvoice(id, data) {\n    return this.put(`/billing/invoices/${id}`, data);\n  }\n  async sendInvoice(id) {\n    return this.post(`/billing/invoices/${id}/send`);\n  }\n  async recordPayment(invoiceId, payment) {\n    return this.post(`/billing/invoices/${invoiceId}/payments`, payment);\n  }\n  async getPayments(invoiceId) {\n    const url = invoiceId ? `/billing/invoices/${invoiceId}/payments` : '/billing/payments';\n    return this.get(url);\n  }\n  async getFinancialStats(period) {\n    return this.get('/billing/stats', {\n      params: {\n        period\n      }\n    });\n  }\n}\n\n// Schedule Service\n\nclass ScheduleService extends BaseApiService {\n  async getAppointments(params) {\n    return this.get('/schedule/appointments', {\n      params\n    });\n  }\n  async getAppointmentById(id) {\n    return this.get(`/schedule/appointments/${id}`);\n  }\n  async createAppointment(data) {\n    return this.post('/schedule/appointments', data);\n  }\n  async updateAppointment(id, data) {\n    return this.put(`/schedule/appointments/${id}`, data);\n  }\n  async deleteAppointment(id) {\n    return this.delete(`/schedule/appointments/${id}`);\n  }\n  async getAvailableSlots(date, duration) {\n    return this.get('/schedule/available-slots', {\n      params: {\n        date,\n        duration\n      }\n    });\n  }\n}\n\n// Reports Service\n\nclass ReportsService extends BaseApiService {\n  async generateReport(request) {\n    return this.post('/reports/generate', request);\n  }\n  async getReports() {\n    return this.get('/reports');\n  }\n  async getReportById(id) {\n    return this.get(`/reports/${id}`);\n  }\n  async downloadReport(id) {\n    const response = await apiClient.get(`/reports/${id}/download`, {\n      responseType: 'blob'\n    });\n    return response.data;\n  }\n  async getDashboardStats() {\n    return this.get('/reports/dashboard-stats');\n  }\n}\n\n// Export service instances\nexport const authService = new AuthService();\nexport const casesService = new CasesService();\nexport const clinicsService = new ClinicsService();\nexport const workflowService = new WorkflowService();\nexport const billingService = new BillingService();\nexport const scheduleService = new ScheduleService();\nexport const reportsService = new ReportsService();\n\n// Export the configured axios instance for custom requests\nexport { apiClient };\n\n// Utility functions for error handling\nexport const handleApiError = error => {\n  var _error$response2, _error$response2$data;\n  if ((_error$response2 = error.response) !== null && _error$response2 !== void 0 && (_error$response2$data = _error$response2.data) !== null && _error$response2$data !== void 0 && _error$response2$data.message) {\n    return error.response.data.message;\n  }\n  if (error.message) {\n    return error.message;\n  }\n  return 'An unexpected error occurred';\n};\nexport const isNetworkError = error => {\n  return !error.response && error.request;\n};\nexport default {\n  authService,\n  casesService,\n  clinicsService,\n  workflowService,\n  billingService,\n  scheduleService,\n  reportsService,\n  apiClient,\n  handleApiError,\n  isNetworkError\n};", "map": {"version": 3, "names": ["axios", "API_BASE_URL", "process", "env", "REACT_APP_API_URL", "API_TIMEOUT", "apiClient", "create", "baseURL", "timeout", "headers", "interceptors", "request", "use", "config", "token", "localStorage", "getItem", "Authorization", "error", "Promise", "reject", "response", "_error$response", "status", "removeItem", "window", "location", "href", "BaseApiService", "get", "url", "data", "post", "put", "patch", "delete", "AuthService", "login", "credentials", "logout", "refreshToken", "getCurrentUser", "resetPassword", "email", "CasesService", "getCases", "params", "getCaseById", "id", "createCase", "updateCase", "deleteCase", "advanceCase", "notes", "quality_check_passed", "assignTechnician", "technician_id", "stage", "getOverdueCases", "getCasesByStatus", "uploadCaseFiles", "files", "formData", "FormData", "for<PERSON>ach", "file", "append", "ClinicsService", "getClinics", "results", "getClinicById", "createClinic", "updateClinic", "deleteClinic", "WorkflowService", "getWorkflowStages", "getTasks", "createTask", "updateTask", "assignTask", "taskId", "assigneeId", "startTask", "completeTask", "BillingService", "getInvoices", "getInvoiceById", "createInvoice", "updateInvoice", "sendInvoice", "recordPayment", "invoiceId", "payment", "getPayments", "getFinancialStats", "period", "ScheduleService", "getAppointments", "getAppointmentById", "createAppointment", "updateAppointment", "deleteAppointment", "getAvailableSlots", "date", "duration", "ReportsService", "generateReport", "getReports", "getReportById", "downloadReport", "responseType", "getDashboardStats", "authService", "casesService", "clinicsService", "workflowService", "billingService", "scheduleService", "reportsService", "handleApiError", "_error$response2", "_error$response2$data", "message", "isNetworkError"], "sources": ["C:/GPT4_PROJECTS/DENTAL_LAB_ARCHITECTURE_PATTERNS/dentflow/frontend/src/services/api.ts"], "sourcesContent": ["/**\n * API Service Layer for DentFlow\n * Centralized API communication with error handling and caching\n */\n\nimport axios, { AxiosInstance, AxiosRequestConfig, AxiosResponse } from 'axios';\n\n// API Configuration\nconst API_BASE_URL = process.env.REACT_APP_API_URL || 'http://localhost:8000/api';\nconst API_TIMEOUT = 30000; // 30 seconds\n\n// Create axios instance with default configuration\nconst apiClient: AxiosInstance = axios.create({\n  baseURL: API_BASE_URL,\n  timeout: API_TIMEOUT,\n  headers: {\n    'Content-Type': 'application/json',\n  },\n});\n\n// Request interceptor for authentication\napiClient.interceptors.request.use(\n  (config) => {\n    const token = localStorage.getItem('authToken');\n    if (token) {\n      config.headers.Authorization = `Bearer ${token}`;\n    }\n    return config;\n  },\n  (error) => Promise.reject(error)\n);\n\n// Response interceptor for error handling\napiClient.interceptors.response.use(\n  (response: AxiosResponse) => response,\n  (error) => {\n    if (error.response?.status === 401) {\n      // Handle unauthorized - redirect to login\n      localStorage.removeItem('authToken');\n      window.location.href = '/login';\n    }\n    return Promise.reject(error);\n  }\n);\n\n// Base API service class\nclass BaseApiService {\n  protected async get<T>(url: string, config?: AxiosRequestConfig): Promise<T> {\n    const response = await apiClient.get<T>(url, config);\n    return response.data;\n  }\n\n  protected async post<T>(url: string, data?: any, config?: AxiosRequestConfig): Promise<T> {\n    const response = await apiClient.post<T>(url, data, config);\n    return response.data;\n  }\n\n  protected async put<T>(url: string, data?: any, config?: AxiosRequestConfig): Promise<T> {\n    const response = await apiClient.put<T>(url, data, config);\n    return response.data;\n  }\n\n  protected async patch<T>(url: string, data?: any, config?: AxiosRequestConfig): Promise<T> {\n    const response = await apiClient.patch<T>(url, data, config);\n    return response.data;\n  }\n\n  protected async delete<T>(url: string, config?: AxiosRequestConfig): Promise<T> {\n    const response = await apiClient.delete<T>(url, config);\n    return response.data;\n  }\n}\n\n// Authentication Service\nexport interface LoginCredentials {\n  email: string;\n  password: string;\n}\n\nexport interface AuthUser {\n  id: string;\n  email: string;\n  firstName: string;\n  lastName: string;\n  role: string;\n  permissions: string[];\n}\n\nexport interface AuthResponse {\n  user: AuthUser;\n  token: string;\n  refreshToken: string;\n}\n\nclass AuthService extends BaseApiService {\n  async login(credentials: LoginCredentials): Promise<AuthResponse> {\n    return this.post<AuthResponse>('/auth/login', credentials);\n  }\n\n  async logout(): Promise<void> {\n    await this.post('/auth/logout');\n    localStorage.removeItem('authToken');\n  }\n\n  async refreshToken(refreshToken: string): Promise<AuthResponse> {\n    return this.post<AuthResponse>('/auth/refresh', { refreshToken });\n  }\n\n  async getCurrentUser(): Promise<AuthUser> {\n    return this.get<AuthUser>('/auth/me');\n  }\n\n  async resetPassword(email: string): Promise<void> {\n    return this.post('/auth/reset-password', { email });\n  }\n}\n\n// Cases Service - Updated to match backend structure\nexport interface Clinic {\n  id: string;\n  name: string;\n  email: string;\n  phone: string;\n  address: string;\n}\n\nexport interface WorkflowStage {\n  name: string;\n  department: string;\n  estimated_duration_minutes: number;\n  auto_assign: boolean;\n  requires_quality_check: boolean;\n  machine_required?: string;\n}\n\nexport interface Case {\n  id: string;\n  clinic: Clinic;\n  patient_name: string;\n  tooth_number: string;\n  service_type: string;\n  priority: 'low' | 'normal' | 'urgent' | 'stat';\n  status: string;\n  current_stage_index: number;\n  workflow_stages: WorkflowStage[];\n  assigned_technician_id?: string;\n  due_date?: string;\n  created_at: string;\n  updated_at: string;\n  notes: string[];\n  files: string[];\n  days_until_due?: number;\n  is_overdue: boolean;\n  current_stage?: WorkflowStage;\n}\n\nexport interface CreateCaseRequest {\n  clinic_id: string;\n  patient_name: string;\n  tooth_number: string;\n  service_type: string; // Allow any string for flexibility\n  priority?: 'low' | 'normal' | 'urgent' | 'stat';\n  notes?: string;\n  files?: string[];\n}\n\nexport interface CasesListResponse {\n  results: Case[];\n  count: number;\n  next?: string;\n  previous?: string;\n}\n\nclass CasesService extends BaseApiService {\n  async getCases(params?: {\n    page?: number;\n    limit?: number;\n    status?: string;\n    priority?: string;\n    search?: string;\n  }): Promise<CasesListResponse> {\n    return this.get<CasesListResponse>('/cases/', { params });\n  }\n\n  async getCaseById(id: string): Promise<Case> {\n    return this.get<Case>(`/cases/${id}/`);\n  }\n\n  async createCase(data: CreateCaseRequest): Promise<Case> {\n    return this.post<Case>('/cases/', data);\n  }\n\n  async updateCase(id: string, data: Partial<CreateCaseRequest>): Promise<Case> {\n    return this.patch<Case>(`/cases/${id}/`, data);\n  }\n\n  async deleteCase(id: string): Promise<void> {\n    return this.delete(`/cases/${id}/`);\n  }\n\n  // Advance case to next workflow stage\n  async advanceCase(id: string, notes?: string, quality_check_passed?: boolean): Promise<Case> {\n    return this.post<Case>(`/cases/${id}/advance/`, {\n      notes: notes || '',\n      quality_check_passed: quality_check_passed !== false\n    });\n  }\n\n  // Assign technician to case\n  async assignTechnician(id: string, technician_id: string, stage?: string): Promise<Case> {\n    return this.post<Case>(`/cases/${id}/assign_technician/`, {\n      technician_id,\n      stage: stage || ''\n    });\n  }\n\n  // Get overdue cases\n  async getOverdueCases(): Promise<Case[]> {\n    return this.get<Case[]>('/cases/overdue/');\n  }\n\n  // Get cases by status\n  async getCasesByStatus(status: string): Promise<Case[]> {\n    return this.get<Case[]>(`/cases/by_status/?status=${status}`);\n  }\n\n  async uploadCaseFiles(id: string, files: File[]): Promise<any> {\n    const formData = new FormData();\n    files.forEach(file => formData.append('files', file));\n\n    return this.post(`/cases/${id}/files/`, formData, {\n      headers: { 'Content-Type': 'multipart/form-data' }\n    });\n  }\n}\n\n// Clinics Service\nclass ClinicsService extends BaseApiService {\n  async getClinics(): Promise<Clinic[]> {\n    const response = await this.get<{ results: Clinic[] }>('/clinics/');\n    return response.results || [];\n  }\n\n  async getClinicById(id: string): Promise<Clinic> {\n    return this.get<Clinic>(`/clinics/${id}/`);\n  }\n\n  async createClinic(data: Omit<Clinic, 'id'>): Promise<Clinic> {\n    return this.post<Clinic>('/clinics/', data);\n  }\n\n  async updateClinic(id: string, data: Partial<Clinic>): Promise<Clinic> {\n    return this.patch<Clinic>(`/clinics/${id}/`, data);\n  }\n\n  async deleteClinic(id: string): Promise<void> {\n    return this.delete(`/clinics/${id}/`);\n  }\n}\n\n// Workflow Service\nexport interface WorkflowStageOld {\n  id: string;\n  name: string;\n  description: string;\n  order: number;\n  estimatedTime: number;\n  color: string;\n}\n\nexport interface Task {\n  id: string;\n  caseId: string;\n  stageId: string;\n  assigneeId: string;\n  status: 'pending' | 'in_progress' | 'completed' | 'blocked';\n  priority: 'stat' | 'urgent' | 'normal' | 'low';\n  startTime?: string;\n  endTime?: string;\n  notes?: string;\n}\n\nclass WorkflowService extends BaseApiService {\n  async getWorkflowStages(): Promise<WorkflowStage[]> {\n    return this.get<WorkflowStage[]>('/workflow/stages');\n  }\n\n  async getTasks(params?: {\n    status?: string;\n    assigneeId?: string;\n    stageId?: string;\n  }): Promise<Task[]> {\n    return this.get<Task[]>('/workflow/tasks', { params });\n  }\n\n  async createTask(data: Omit<Task, 'id'>): Promise<Task> {\n    return this.post<Task>('/workflow/tasks', data);\n  }\n\n  async updateTask(id: string, data: Partial<Task>): Promise<Task> {\n    return this.put<Task>(`/workflow/tasks/${id}`, data);\n  }\n\n  async assignTask(taskId: string, assigneeId: string): Promise<Task> {\n    return this.patch<Task>(`/workflow/tasks/${taskId}/assign`, { assigneeId });\n  }\n\n  async startTask(taskId: string): Promise<Task> {\n    return this.patch<Task>(`/workflow/tasks/${taskId}/start`);\n  }\n\n  async completeTask(taskId: string, notes?: string): Promise<Task> {\n    return this.patch<Task>(`/workflow/tasks/${taskId}/complete`, { notes });\n  }\n}\n\n// Billing Service\nexport interface Invoice {\n  id: string;\n  caseId: string;\n  amount: number;\n  status: 'draft' | 'sent' | 'paid' | 'overdue' | 'cancelled';\n  dueDate: string;\n  paidDate?: string;\n  items: InvoiceItem[];\n  patient: string;\n  dentist: string;\n  clinic: string;\n}\n\nexport interface InvoiceItem {\n  description: string;\n  quantity: number;\n  unitPrice: number;\n  total: number;\n}\n\nexport interface Payment {\n  id: string;\n  invoiceId: string;\n  amount: number;\n  method: 'bank_transfer' | 'credit_card' | 'check' | 'cash';\n  reference: string;\n  paidDate: string;\n  notes?: string;\n}\n\nclass BillingService extends BaseApiService {\n  async getInvoices(params?: {\n    status?: string;\n    dateFrom?: string;\n    dateTo?: string;\n  }): Promise<Invoice[]> {\n    return this.get<Invoice[]>('/billing/invoices', { params });\n  }\n\n  async getInvoiceById(id: string): Promise<Invoice> {\n    return this.get<Invoice>(`/billing/invoices/${id}`);\n  }\n\n  async createInvoice(data: Omit<Invoice, 'id' | 'status'>): Promise<Invoice> {\n    return this.post<Invoice>('/billing/invoices', data);\n  }\n\n  async updateInvoice(id: string, data: Partial<Invoice>): Promise<Invoice> {\n    return this.put<Invoice>(`/billing/invoices/${id}`, data);\n  }\n\n  async sendInvoice(id: string): Promise<void> {\n    return this.post(`/billing/invoices/${id}/send`);\n  }\n\n  async recordPayment(invoiceId: string, payment: Omit<Payment, 'id' | 'invoiceId'>): Promise<Payment> {\n    return this.post<Payment>(`/billing/invoices/${invoiceId}/payments`, payment);\n  }\n\n  async getPayments(invoiceId?: string): Promise<Payment[]> {\n    const url = invoiceId ? `/billing/invoices/${invoiceId}/payments` : '/billing/payments';\n    return this.get<Payment[]>(url);\n  }\n\n  async getFinancialStats(period?: string): Promise<any> {\n    return this.get('/billing/stats', { params: { period } });\n  }\n}\n\n// Schedule Service\nexport interface Appointment {\n  id: string;\n  title: string;\n  caseId?: string;\n  patient: string;\n  dentist: string;\n  type: 'consultation' | 'delivery' | 'qc_review' | 'design_review' | 'pickup';\n  date: string;\n  startTime: string;\n  endTime: string;\n  status: 'scheduled' | 'confirmed' | 'completed' | 'cancelled';\n  notes?: string;\n}\n\nclass ScheduleService extends BaseApiService {\n  async getAppointments(params?: {\n    dateFrom?: string;\n    dateTo?: string;\n    type?: string;\n    status?: string;\n  }): Promise<Appointment[]> {\n    return this.get<Appointment[]>('/schedule/appointments', { params });\n  }\n\n  async getAppointmentById(id: string): Promise<Appointment> {\n    return this.get<Appointment>(`/schedule/appointments/${id}`);\n  }\n\n  async createAppointment(data: Omit<Appointment, 'id'>): Promise<Appointment> {\n    return this.post<Appointment>('/schedule/appointments', data);\n  }\n\n  async updateAppointment(id: string, data: Partial<Appointment>): Promise<Appointment> {\n    return this.put<Appointment>(`/schedule/appointments/${id}`, data);\n  }\n\n  async deleteAppointment(id: string): Promise<void> {\n    return this.delete(`/schedule/appointments/${id}`);\n  }\n\n  async getAvailableSlots(date: string, duration: number): Promise<string[]> {\n    return this.get<string[]>('/schedule/available-slots', { \n      params: { date, duration } \n    });\n  }\n}\n\n// Reports Service\nexport interface ReportRequest {\n  type: 'production' | 'financial' | 'quality' | 'customer' | 'performance';\n  dateRange: {\n    from: string;\n    to: string;\n  };\n  filters?: Record<string, any>;\n}\n\nexport interface ReportResponse {\n  id: string;\n  type: string;\n  status: 'generating' | 'completed' | 'failed';\n  url?: string;\n  createdAt: string;\n}\n\nclass ReportsService extends BaseApiService {\n  async generateReport(request: ReportRequest): Promise<ReportResponse> {\n    return this.post<ReportResponse>('/reports/generate', request);\n  }\n\n  async getReports(): Promise<ReportResponse[]> {\n    return this.get<ReportResponse[]>('/reports');\n  }\n\n  async getReportById(id: string): Promise<ReportResponse> {\n    return this.get<ReportResponse>(`/reports/${id}`);\n  }\n\n  async downloadReport(id: string): Promise<Blob> {\n    const response = await apiClient.get(`/reports/${id}/download`, {\n      responseType: 'blob'\n    });\n    return response.data;\n  }\n\n  async getDashboardStats(): Promise<any> {\n    return this.get('/reports/dashboard-stats');\n  }\n}\n\n// Export service instances\nexport const authService = new AuthService();\nexport const casesService = new CasesService();\nexport const clinicsService = new ClinicsService();\nexport const workflowService = new WorkflowService();\nexport const billingService = new BillingService();\nexport const scheduleService = new ScheduleService();\nexport const reportsService = new ReportsService();\n\n// Export the configured axios instance for custom requests\nexport { apiClient };\n\n// Utility functions for error handling\nexport const handleApiError = (error: any): string => {\n  if (error.response?.data?.message) {\n    return error.response.data.message;\n  }\n  if (error.message) {\n    return error.message;\n  }\n  return 'An unexpected error occurred';\n};\n\nexport const isNetworkError = (error: any): boolean => {\n  return !error.response && error.request;\n};\n\nexport default {\n  authService,\n  casesService,\n  clinicsService,\n  workflowService,\n  billingService,\n  scheduleService,\n  reportsService,\n  apiClient,\n  handleApiError,\n  isNetworkError,\n};\n"], "mappings": "AAAA;AACA;AACA;AACA;;AAEA,OAAOA,KAAK,MAA4D,OAAO;;AAE/E;AACA,MAAMC,YAAY,GAAGC,OAAO,CAACC,GAAG,CAACC,iBAAiB,IAAI,2BAA2B;AACjF,MAAMC,WAAW,GAAG,KAAK,CAAC,CAAC;;AAE3B;AACA,MAAMC,SAAwB,GAAGN,KAAK,CAACO,MAAM,CAAC;EAC5CC,OAAO,EAAEP,YAAY;EACrBQ,OAAO,EAAEJ,WAAW;EACpBK,OAAO,EAAE;IACP,cAAc,EAAE;EAClB;AACF,CAAC,CAAC;;AAEF;AACAJ,SAAS,CAACK,YAAY,CAACC,OAAO,CAACC,GAAG,CAC/BC,MAAM,IAAK;EACV,MAAMC,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,WAAW,CAAC;EAC/C,IAAIF,KAAK,EAAE;IACTD,MAAM,CAACJ,OAAO,CAACQ,aAAa,GAAG,UAAUH,KAAK,EAAE;EAClD;EACA,OAAOD,MAAM;AACf,CAAC,EACAK,KAAK,IAAKC,OAAO,CAACC,MAAM,CAACF,KAAK,CACjC,CAAC;;AAED;AACAb,SAAS,CAACK,YAAY,CAACW,QAAQ,CAACT,GAAG,CAChCS,QAAuB,IAAKA,QAAQ,EACpCH,KAAK,IAAK;EAAA,IAAAI,eAAA;EACT,IAAI,EAAAA,eAAA,GAAAJ,KAAK,CAACG,QAAQ,cAAAC,eAAA,uBAAdA,eAAA,CAAgBC,MAAM,MAAK,GAAG,EAAE;IAClC;IACAR,YAAY,CAACS,UAAU,CAAC,WAAW,CAAC;IACpCC,MAAM,CAACC,QAAQ,CAACC,IAAI,GAAG,QAAQ;EACjC;EACA,OAAOR,OAAO,CAACC,MAAM,CAACF,KAAK,CAAC;AAC9B,CACF,CAAC;;AAED;AACA,MAAMU,cAAc,CAAC;EACnB,MAAgBC,GAAGA,CAAIC,GAAW,EAAEjB,MAA2B,EAAc;IAC3E,MAAMQ,QAAQ,GAAG,MAAMhB,SAAS,CAACwB,GAAG,CAAIC,GAAG,EAAEjB,MAAM,CAAC;IACpD,OAAOQ,QAAQ,CAACU,IAAI;EACtB;EAEA,MAAgBC,IAAIA,CAAIF,GAAW,EAAEC,IAAU,EAAElB,MAA2B,EAAc;IACxF,MAAMQ,QAAQ,GAAG,MAAMhB,SAAS,CAAC2B,IAAI,CAAIF,GAAG,EAAEC,IAAI,EAAElB,MAAM,CAAC;IAC3D,OAAOQ,QAAQ,CAACU,IAAI;EACtB;EAEA,MAAgBE,GAAGA,CAAIH,GAAW,EAAEC,IAAU,EAAElB,MAA2B,EAAc;IACvF,MAAMQ,QAAQ,GAAG,MAAMhB,SAAS,CAAC4B,GAAG,CAAIH,GAAG,EAAEC,IAAI,EAAElB,MAAM,CAAC;IAC1D,OAAOQ,QAAQ,CAACU,IAAI;EACtB;EAEA,MAAgBG,KAAKA,CAAIJ,GAAW,EAAEC,IAAU,EAAElB,MAA2B,EAAc;IACzF,MAAMQ,QAAQ,GAAG,MAAMhB,SAAS,CAAC6B,KAAK,CAAIJ,GAAG,EAAEC,IAAI,EAAElB,MAAM,CAAC;IAC5D,OAAOQ,QAAQ,CAACU,IAAI;EACtB;EAEA,MAAgBI,MAAMA,CAAIL,GAAW,EAAEjB,MAA2B,EAAc;IAC9E,MAAMQ,QAAQ,GAAG,MAAMhB,SAAS,CAAC8B,MAAM,CAAIL,GAAG,EAAEjB,MAAM,CAAC;IACvD,OAAOQ,QAAQ,CAACU,IAAI;EACtB;AACF;;AAEA;;AAqBA,MAAMK,WAAW,SAASR,cAAc,CAAC;EACvC,MAAMS,KAAKA,CAACC,WAA6B,EAAyB;IAChE,OAAO,IAAI,CAACN,IAAI,CAAe,aAAa,EAAEM,WAAW,CAAC;EAC5D;EAEA,MAAMC,MAAMA,CAAA,EAAkB;IAC5B,MAAM,IAAI,CAACP,IAAI,CAAC,cAAc,CAAC;IAC/BjB,YAAY,CAACS,UAAU,CAAC,WAAW,CAAC;EACtC;EAEA,MAAMgB,YAAYA,CAACA,YAAoB,EAAyB;IAC9D,OAAO,IAAI,CAACR,IAAI,CAAe,eAAe,EAAE;MAAEQ;IAAa,CAAC,CAAC;EACnE;EAEA,MAAMC,cAAcA,CAAA,EAAsB;IACxC,OAAO,IAAI,CAACZ,GAAG,CAAW,UAAU,CAAC;EACvC;EAEA,MAAMa,aAAaA,CAACC,KAAa,EAAiB;IAChD,OAAO,IAAI,CAACX,IAAI,CAAC,sBAAsB,EAAE;MAAEW;IAAM,CAAC,CAAC;EACrD;AACF;;AAEA;;AAwDA,MAAMC,YAAY,SAAShB,cAAc,CAAC;EACxC,MAAMiB,QAAQA,CAACC,MAMd,EAA8B;IAC7B,OAAO,IAAI,CAACjB,GAAG,CAAoB,SAAS,EAAE;MAAEiB;IAAO,CAAC,CAAC;EAC3D;EAEA,MAAMC,WAAWA,CAACC,EAAU,EAAiB;IAC3C,OAAO,IAAI,CAACnB,GAAG,CAAO,UAAUmB,EAAE,GAAG,CAAC;EACxC;EAEA,MAAMC,UAAUA,CAAClB,IAAuB,EAAiB;IACvD,OAAO,IAAI,CAACC,IAAI,CAAO,SAAS,EAAED,IAAI,CAAC;EACzC;EAEA,MAAMmB,UAAUA,CAACF,EAAU,EAAEjB,IAAgC,EAAiB;IAC5E,OAAO,IAAI,CAACG,KAAK,CAAO,UAAUc,EAAE,GAAG,EAAEjB,IAAI,CAAC;EAChD;EAEA,MAAMoB,UAAUA,CAACH,EAAU,EAAiB;IAC1C,OAAO,IAAI,CAACb,MAAM,CAAC,UAAUa,EAAE,GAAG,CAAC;EACrC;;EAEA;EACA,MAAMI,WAAWA,CAACJ,EAAU,EAAEK,KAAc,EAAEC,oBAA8B,EAAiB;IAC3F,OAAO,IAAI,CAACtB,IAAI,CAAO,UAAUgB,EAAE,WAAW,EAAE;MAC9CK,KAAK,EAAEA,KAAK,IAAI,EAAE;MAClBC,oBAAoB,EAAEA,oBAAoB,KAAK;IACjD,CAAC,CAAC;EACJ;;EAEA;EACA,MAAMC,gBAAgBA,CAACP,EAAU,EAAEQ,aAAqB,EAAEC,KAAc,EAAiB;IACvF,OAAO,IAAI,CAACzB,IAAI,CAAO,UAAUgB,EAAE,qBAAqB,EAAE;MACxDQ,aAAa;MACbC,KAAK,EAAEA,KAAK,IAAI;IAClB,CAAC,CAAC;EACJ;;EAEA;EACA,MAAMC,eAAeA,CAAA,EAAoB;IACvC,OAAO,IAAI,CAAC7B,GAAG,CAAS,iBAAiB,CAAC;EAC5C;;EAEA;EACA,MAAM8B,gBAAgBA,CAACpC,MAAc,EAAmB;IACtD,OAAO,IAAI,CAACM,GAAG,CAAS,4BAA4BN,MAAM,EAAE,CAAC;EAC/D;EAEA,MAAMqC,eAAeA,CAACZ,EAAU,EAAEa,KAAa,EAAgB;IAC7D,MAAMC,QAAQ,GAAG,IAAIC,QAAQ,CAAC,CAAC;IAC/BF,KAAK,CAACG,OAAO,CAACC,IAAI,IAAIH,QAAQ,CAACI,MAAM,CAAC,OAAO,EAAED,IAAI,CAAC,CAAC;IAErD,OAAO,IAAI,CAACjC,IAAI,CAAC,UAAUgB,EAAE,SAAS,EAAEc,QAAQ,EAAE;MAChDrD,OAAO,EAAE;QAAE,cAAc,EAAE;MAAsB;IACnD,CAAC,CAAC;EACJ;AACF;;AAEA;AACA,MAAM0D,cAAc,SAASvC,cAAc,CAAC;EAC1C,MAAMwC,UAAUA,CAAA,EAAsB;IACpC,MAAM/C,QAAQ,GAAG,MAAM,IAAI,CAACQ,GAAG,CAAwB,WAAW,CAAC;IACnE,OAAOR,QAAQ,CAACgD,OAAO,IAAI,EAAE;EAC/B;EAEA,MAAMC,aAAaA,CAACtB,EAAU,EAAmB;IAC/C,OAAO,IAAI,CAACnB,GAAG,CAAS,YAAYmB,EAAE,GAAG,CAAC;EAC5C;EAEA,MAAMuB,YAAYA,CAACxC,IAAwB,EAAmB;IAC5D,OAAO,IAAI,CAACC,IAAI,CAAS,WAAW,EAAED,IAAI,CAAC;EAC7C;EAEA,MAAMyC,YAAYA,CAACxB,EAAU,EAAEjB,IAAqB,EAAmB;IACrE,OAAO,IAAI,CAACG,KAAK,CAAS,YAAYc,EAAE,GAAG,EAAEjB,IAAI,CAAC;EACpD;EAEA,MAAM0C,YAAYA,CAACzB,EAAU,EAAiB;IAC5C,OAAO,IAAI,CAACb,MAAM,CAAC,YAAYa,EAAE,GAAG,CAAC;EACvC;AACF;;AAEA;;AAsBA,MAAM0B,eAAe,SAAS9C,cAAc,CAAC;EAC3C,MAAM+C,iBAAiBA,CAAA,EAA6B;IAClD,OAAO,IAAI,CAAC9C,GAAG,CAAkB,kBAAkB,CAAC;EACtD;EAEA,MAAM+C,QAAQA,CAAC9B,MAId,EAAmB;IAClB,OAAO,IAAI,CAACjB,GAAG,CAAS,iBAAiB,EAAE;MAAEiB;IAAO,CAAC,CAAC;EACxD;EAEA,MAAM+B,UAAUA,CAAC9C,IAAsB,EAAiB;IACtD,OAAO,IAAI,CAACC,IAAI,CAAO,iBAAiB,EAAED,IAAI,CAAC;EACjD;EAEA,MAAM+C,UAAUA,CAAC9B,EAAU,EAAEjB,IAAmB,EAAiB;IAC/D,OAAO,IAAI,CAACE,GAAG,CAAO,mBAAmBe,EAAE,EAAE,EAAEjB,IAAI,CAAC;EACtD;EAEA,MAAMgD,UAAUA,CAACC,MAAc,EAAEC,UAAkB,EAAiB;IAClE,OAAO,IAAI,CAAC/C,KAAK,CAAO,mBAAmB8C,MAAM,SAAS,EAAE;MAAEC;IAAW,CAAC,CAAC;EAC7E;EAEA,MAAMC,SAASA,CAACF,MAAc,EAAiB;IAC7C,OAAO,IAAI,CAAC9C,KAAK,CAAO,mBAAmB8C,MAAM,QAAQ,CAAC;EAC5D;EAEA,MAAMG,YAAYA,CAACH,MAAc,EAAE3B,KAAc,EAAiB;IAChE,OAAO,IAAI,CAACnB,KAAK,CAAO,mBAAmB8C,MAAM,WAAW,EAAE;MAAE3B;IAAM,CAAC,CAAC;EAC1E;AACF;;AAEA;;AA+BA,MAAM+B,cAAc,SAASxD,cAAc,CAAC;EAC1C,MAAMyD,WAAWA,CAACvC,MAIjB,EAAsB;IACrB,OAAO,IAAI,CAACjB,GAAG,CAAY,mBAAmB,EAAE;MAAEiB;IAAO,CAAC,CAAC;EAC7D;EAEA,MAAMwC,cAAcA,CAACtC,EAAU,EAAoB;IACjD,OAAO,IAAI,CAACnB,GAAG,CAAU,qBAAqBmB,EAAE,EAAE,CAAC;EACrD;EAEA,MAAMuC,aAAaA,CAACxD,IAAoC,EAAoB;IAC1E,OAAO,IAAI,CAACC,IAAI,CAAU,mBAAmB,EAAED,IAAI,CAAC;EACtD;EAEA,MAAMyD,aAAaA,CAACxC,EAAU,EAAEjB,IAAsB,EAAoB;IACxE,OAAO,IAAI,CAACE,GAAG,CAAU,qBAAqBe,EAAE,EAAE,EAAEjB,IAAI,CAAC;EAC3D;EAEA,MAAM0D,WAAWA,CAACzC,EAAU,EAAiB;IAC3C,OAAO,IAAI,CAAChB,IAAI,CAAC,qBAAqBgB,EAAE,OAAO,CAAC;EAClD;EAEA,MAAM0C,aAAaA,CAACC,SAAiB,EAAEC,OAA0C,EAAoB;IACnG,OAAO,IAAI,CAAC5D,IAAI,CAAU,qBAAqB2D,SAAS,WAAW,EAAEC,OAAO,CAAC;EAC/E;EAEA,MAAMC,WAAWA,CAACF,SAAkB,EAAsB;IACxD,MAAM7D,GAAG,GAAG6D,SAAS,GAAG,qBAAqBA,SAAS,WAAW,GAAG,mBAAmB;IACvF,OAAO,IAAI,CAAC9D,GAAG,CAAYC,GAAG,CAAC;EACjC;EAEA,MAAMgE,iBAAiBA,CAACC,MAAe,EAAgB;IACrD,OAAO,IAAI,CAAClE,GAAG,CAAC,gBAAgB,EAAE;MAAEiB,MAAM,EAAE;QAAEiD;MAAO;IAAE,CAAC,CAAC;EAC3D;AACF;;AAEA;;AAeA,MAAMC,eAAe,SAASpE,cAAc,CAAC;EAC3C,MAAMqE,eAAeA,CAACnD,MAKrB,EAA0B;IACzB,OAAO,IAAI,CAACjB,GAAG,CAAgB,wBAAwB,EAAE;MAAEiB;IAAO,CAAC,CAAC;EACtE;EAEA,MAAMoD,kBAAkBA,CAAClD,EAAU,EAAwB;IACzD,OAAO,IAAI,CAACnB,GAAG,CAAc,0BAA0BmB,EAAE,EAAE,CAAC;EAC9D;EAEA,MAAMmD,iBAAiBA,CAACpE,IAA6B,EAAwB;IAC3E,OAAO,IAAI,CAACC,IAAI,CAAc,wBAAwB,EAAED,IAAI,CAAC;EAC/D;EAEA,MAAMqE,iBAAiBA,CAACpD,EAAU,EAAEjB,IAA0B,EAAwB;IACpF,OAAO,IAAI,CAACE,GAAG,CAAc,0BAA0Be,EAAE,EAAE,EAAEjB,IAAI,CAAC;EACpE;EAEA,MAAMsE,iBAAiBA,CAACrD,EAAU,EAAiB;IACjD,OAAO,IAAI,CAACb,MAAM,CAAC,0BAA0Ba,EAAE,EAAE,CAAC;EACpD;EAEA,MAAMsD,iBAAiBA,CAACC,IAAY,EAAEC,QAAgB,EAAqB;IACzE,OAAO,IAAI,CAAC3E,GAAG,CAAW,2BAA2B,EAAE;MACrDiB,MAAM,EAAE;QAAEyD,IAAI;QAAEC;MAAS;IAC3B,CAAC,CAAC;EACJ;AACF;;AAEA;;AAkBA,MAAMC,cAAc,SAAS7E,cAAc,CAAC;EAC1C,MAAM8E,cAAcA,CAAC/F,OAAsB,EAA2B;IACpE,OAAO,IAAI,CAACqB,IAAI,CAAiB,mBAAmB,EAAErB,OAAO,CAAC;EAChE;EAEA,MAAMgG,UAAUA,CAAA,EAA8B;IAC5C,OAAO,IAAI,CAAC9E,GAAG,CAAmB,UAAU,CAAC;EAC/C;EAEA,MAAM+E,aAAaA,CAAC5D,EAAU,EAA2B;IACvD,OAAO,IAAI,CAACnB,GAAG,CAAiB,YAAYmB,EAAE,EAAE,CAAC;EACnD;EAEA,MAAM6D,cAAcA,CAAC7D,EAAU,EAAiB;IAC9C,MAAM3B,QAAQ,GAAG,MAAMhB,SAAS,CAACwB,GAAG,CAAC,YAAYmB,EAAE,WAAW,EAAE;MAC9D8D,YAAY,EAAE;IAChB,CAAC,CAAC;IACF,OAAOzF,QAAQ,CAACU,IAAI;EACtB;EAEA,MAAMgF,iBAAiBA,CAAA,EAAiB;IACtC,OAAO,IAAI,CAAClF,GAAG,CAAC,0BAA0B,CAAC;EAC7C;AACF;;AAEA;AACA,OAAO,MAAMmF,WAAW,GAAG,IAAI5E,WAAW,CAAC,CAAC;AAC5C,OAAO,MAAM6E,YAAY,GAAG,IAAIrE,YAAY,CAAC,CAAC;AAC9C,OAAO,MAAMsE,cAAc,GAAG,IAAI/C,cAAc,CAAC,CAAC;AAClD,OAAO,MAAMgD,eAAe,GAAG,IAAIzC,eAAe,CAAC,CAAC;AACpD,OAAO,MAAM0C,cAAc,GAAG,IAAIhC,cAAc,CAAC,CAAC;AAClD,OAAO,MAAMiC,eAAe,GAAG,IAAIrB,eAAe,CAAC,CAAC;AACpD,OAAO,MAAMsB,cAAc,GAAG,IAAIb,cAAc,CAAC,CAAC;;AAElD;AACA,SAASpG,SAAS;;AAElB;AACA,OAAO,MAAMkH,cAAc,GAAIrG,KAAU,IAAa;EAAA,IAAAsG,gBAAA,EAAAC,qBAAA;EACpD,KAAAD,gBAAA,GAAItG,KAAK,CAACG,QAAQ,cAAAmG,gBAAA,gBAAAC,qBAAA,GAAdD,gBAAA,CAAgBzF,IAAI,cAAA0F,qBAAA,eAApBA,qBAAA,CAAsBC,OAAO,EAAE;IACjC,OAAOxG,KAAK,CAACG,QAAQ,CAACU,IAAI,CAAC2F,OAAO;EACpC;EACA,IAAIxG,KAAK,CAACwG,OAAO,EAAE;IACjB,OAAOxG,KAAK,CAACwG,OAAO;EACtB;EACA,OAAO,8BAA8B;AACvC,CAAC;AAED,OAAO,MAAMC,cAAc,GAAIzG,KAAU,IAAc;EACrD,OAAO,CAACA,KAAK,CAACG,QAAQ,IAAIH,KAAK,CAACP,OAAO;AACzC,CAAC;AAED,eAAe;EACbqG,WAAW;EACXC,YAAY;EACZC,cAAc;EACdC,eAAe;EACfC,cAAc;EACdC,eAAe;EACfC,cAAc;EACdjH,SAAS;EACTkH,cAAc;EACdI;AACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}