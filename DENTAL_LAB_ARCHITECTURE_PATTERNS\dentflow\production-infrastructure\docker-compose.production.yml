version: '3.8'

services:
  # Nginx Reverse Proxy with SSL
  nginx:
    image: nginx:alpine
    container_name: dentflow-nginx
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/conf.d:/etc/nginx/conf.d:ro
      - ./nginx/ssl:/etc/nginx/ssl:ro
      - static_files:/app/static:ro
      - media_files:/app/media:ro
    depends_on:
      - backend
      - frontend
    restart: unless-stopped
    networks:
      - dentflow-network

  # PostgreSQL Production Database
  postgres:
    image: postgres:15-alpine
    container_name: dentflow-postgres
    environment:
      POSTGRES_DB: ${POSTGRES_DB}
      POSTGRES_USER: ${POSTGRES_USER}
      POSTGRES_PASSWORD: ${POSTGRES_PASSWORD}
      POSTGRES_INITDB_ARGS: "--encoding=UTF-8 --locale=C"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./backups:/backups
      - ./postgres/init:/docker-entrypoint-initdb.d
    ports:
      - "5432:5432"
    restart: unless-stopped
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U ${POSTGRES_USER} -d ${POSTGRES_DB}"]
      interval: 30s
      timeout: 5s
      retries: 5
      start_period: 30s
    networks:
      - dentflow-network

  # Redis Cache & Session Store
  redis:
    image: redis:7-alpine
    container_name: dentflow-redis
    command: redis-server --appendonly yes --requirepass ${REDIS_PASSWORD}
    volumes:
      - redis_data:/data
    ports:
      - "6379:6379"
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "redis-cli", "--raw", "incr", "ping"]
      interval: 30s
      timeout: 5s
      retries: 5
    networks:
      - dentflow-network
  # Django Backend API
  backend:
    build:
      context: ../backend
      dockerfile: Dockerfile.production
      target: production
    container_name: dentflow-backend
    environment:
      - DJANGO_SETTINGS_MODULE=dentflow_project.settings.production
      - DATABASE_URL=postgresql://${POSTGRES_USER}:${POSTGRES_PASSWORD}@postgres:5432/${POSTGRES_DB}
      - REDIS_URL=redis://:${REDIS_PASSWORD}@redis:6379/0
      - SECRET_KEY=${DJANGO_SECRET_KEY}
      - ALLOWED_HOSTS=${ALLOWED_HOSTS}
      - CORS_ALLOWED_ORIGINS=${CORS_ALLOWED_ORIGINS}
      - EMAIL_HOST=${EMAIL_HOST}
      - EMAIL_HOST_USER=${EMAIL_HOST_USER}
      - EMAIL_HOST_PASSWORD=${EMAIL_HOST_PASSWORD}
      - AWS_ACCESS_KEY_ID=${AWS_ACCESS_KEY_ID}
      - AWS_SECRET_ACCESS_KEY=${AWS_SECRET_ACCESS_KEY}
      - AWS_STORAGE_BUCKET_NAME=${AWS_STORAGE_BUCKET_NAME}
      - SENTRY_DSN=${SENTRY_DSN}
    volumes:
      - static_files:/app/static
      - media_files:/app/media
      - logs:/app/logs
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "python", "manage.py", "health_check"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s
    networks:
      - dentflow-network
    deploy:
      resources:
        limits:
          memory: 1G
          cpus: '0.5'
        reservations:
          memory: 512M
          cpus: '0.25'

  # React Frontend
  frontend:
    build:
      context: ../frontend
      dockerfile: Dockerfile.production
      target: production
    container_name: dentflow-frontend
    environment:
      - REACT_APP_API_URL=${REACT_APP_API_URL}
      - REACT_APP_WS_URL=${REACT_APP_WS_URL}
      - REACT_APP_SENTRY_DSN=${FRONTEND_SENTRY_DSN}
    volumes:
      - frontend_build:/app/build
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3000"]
      interval: 30s
      timeout: 10s
      retries: 3
    networks:
      - dentflow-network
  # Celery Worker for Background Tasks
  celery-worker:
    build:
      context: ../backend
      dockerfile: Dockerfile.production
      target: production
    container_name: dentflow-celery-worker
    command: celery -A dentflow_project worker --loglevel=info --concurrency=4
    environment:
      - DJANGO_SETTINGS_MODULE=dentflow_project.settings.production
      - DATABASE_URL=postgresql://${POSTGRES_USER}:${POSTGRES_PASSWORD}@postgres:5432/${POSTGRES_DB}
      - REDIS_URL=redis://:${REDIS_PASSWORD}@redis:6379/0
      - SECRET_KEY=${DJANGO_SECRET_KEY}
    volumes:
      - media_files:/app/media
      - logs:/app/logs
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    restart: unless-stopped
    networks:
      - dentflow-network
    deploy:
      replicas: 2
      resources:
        limits:
          memory: 512M
          cpus: '0.5'

  # Celery Beat Scheduler
  celery-beat:
    build:
      context: ../backend
      dockerfile: Dockerfile.production
      target: production
    container_name: dentflow-celery-beat
    command: celery -A dentflow_project beat --loglevel=info --scheduler django_celery_beat.schedulers:DatabaseScheduler
    environment:
      - DJANGO_SETTINGS_MODULE=dentflow_project.settings.production
      - DATABASE_URL=postgresql://${POSTGRES_USER}:${POSTGRES_PASSWORD}@postgres:5432/${POSTGRES_DB}
      - REDIS_URL=redis://:${REDIS_PASSWORD}@redis:6379/0
      - SECRET_KEY=${DJANGO_SECRET_KEY}
    volumes:
      - logs:/app/logs
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    restart: unless-stopped
    networks:
      - dentflow-network

  # Flower - Celery Monitoring
  flower:
    build:
      context: ../backend
      dockerfile: Dockerfile.production
      target: production
    container_name: dentflow-flower
    command: celery -A dentflow_project flower --port=5555 --basic_auth=${FLOWER_USER}:${FLOWER_PASSWORD}
    environment:
      - DJANGO_SETTINGS_MODULE=dentflow_project.settings.production
      - REDIS_URL=redis://:${REDIS_PASSWORD}@redis:6379/0
    ports:
      - "5555:5555"
    depends_on:
      - redis
    restart: unless-stopped
    networks:
      - dentflow-network
volumes:
  postgres_data:
    driver: local
    driver_opts:
      type: none
      o: bind
      device: /opt/dentflow/data/postgres
  
  redis_data:
    driver: local
    driver_opts:
      type: none
      o: bind
      device: /opt/dentflow/data/redis
  
  static_files:
    driver: local
    driver_opts:
      type: none
      o: bind
      device: /opt/dentflow/static
  
  media_files:
    driver: local
    driver_opts:
      type: none
      o: bind
      device: /opt/dentflow/media
  
  logs:
    driver: local
    driver_opts:
      type: none
      o: bind
      device: /opt/dentflow/logs
  
  frontend_build:
    driver: local

networks:
  dentflow-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16