{"ast": null, "code": "var _jsxFileName = \"C:\\\\GPT4_PROJECTS\\\\DENTAL_LAB_ARCHITECTURE_PATTERNS\\\\dentflow\\\\frontend\\\\src\\\\components\\\\Dashboard.tsx\",\n  _s = $RefreshSig$();\n/**\n * DentFlow Dashboard Component - Enhanced Version\n * Comprehensive dashboard with advanced widgets, charts, and real-time data visualization\n */\n\nimport React, { useState, useEffect } from 'react';\nimport { Box, Grid, Card, CardContent, Typography, Avatar, Chip, LinearProgress, IconButton, Button, Alert, List, ListItem, ListItemText, ListItemAvatar, Divider, Badge, Tooltip, CircularProgress, Table, TableBody, TableCell, TableContainer, TableHead, TableRow, useTheme, alpha } from '@mui/material';\nimport { TrendingUp, Assignment, CheckCircle, Warning, AttachMoney, People, Inventory, Analytics, Refresh, Notifications, Build } from '@mui/icons-material';\nimport { CasesService } from '../api/casesService';\nimport { useAuth } from '../context/AuthContext';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Dashboard = () => {\n  _s();\n  const {\n    user\n  } = useAuth();\n  const theme = useTheme();\n  const [stats, setStats] = useState({\n    active_cases: 0,\n    completed_today: 0,\n    pending_qc: 0,\n    revenue_today: 0,\n    overdue_cases: 0,\n    total_cases_this_month: 0,\n    revenue_this_month: 0,\n    average_completion_time: 0,\n    customer_satisfaction: 0,\n    inventory_alerts: 0,\n    technician_utilization: 0,\n    pending_invoices: 0\n  });\n  const [recentCases, setRecentCases] = useState([]);\n  const [recentActivities, setRecentActivities] = useState([]);\n  const [technicianStats, setTechnicianStats] = useState([]);\n  const [inventoryAlerts, setInventoryAlerts] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState('');\n  const [backendStatus, setBackendStatus] = useState({\n    available: false,\n    mode: 'mock'\n  });\n  useEffect(() => {\n    loadDashboardData();\n  }, []);\n  const loadDashboardData = async () => {\n    try {\n      setLoading(true);\n      setError('');\n\n      // Check backend health first\n      await CasesService.checkBackendHealth();\n      const [dashboardStats, cases] = await Promise.all([CasesService.getDashboardStats(), CasesService.getCases()]);\n\n      // Enhanced stats with mock data for demo\n      const enhancedStats = {\n        ...dashboardStats,\n        total_cases_this_month: dashboardStats.active_cases + dashboardStats.completed_today + 45,\n        revenue_this_month: dashboardStats.revenue_today * 22,\n        average_completion_time: 3.2,\n        customer_satisfaction: 4.7,\n        inventory_alerts: 3,\n        technician_utilization: 87,\n        pending_invoices: 12\n      };\n      setStats(enhancedStats);\n      setRecentCases(cases.slice(0, 8)); // Show more cases\n      setBackendStatus(CasesService.getBackendStatus());\n\n      // Load mock data for enhanced features\n      loadMockEnhancedData();\n    } catch (err) {\n      console.error('Error loading dashboard:', err);\n      setError('Failed to load some dashboard data');\n      setBackendStatus(CasesService.getBackendStatus());\n      loadMockEnhancedData(); // Load mock data even on error\n    } finally {\n      setLoading(false);\n    }\n  };\n  const loadMockEnhancedData = () => {\n    // Mock recent activities\n    setRecentActivities([{\n      id: '1',\n      type: 'case_completed',\n      title: 'Crown Case #1234 Completed',\n      description: 'Zirconia crown for patient John Doe completed by Dr. Smith',\n      timestamp: new Date(Date.now() - 1000 * 60 * 15).toISOString(),\n      user: 'Dr. Smith',\n      priority: 'medium'\n    }, {\n      id: '2',\n      type: 'inventory_low',\n      title: 'Low Inventory Alert',\n      description: 'Zirconia blocks running low (5 units remaining)',\n      timestamp: new Date(Date.now() - 1000 * 60 * 45).toISOString(),\n      priority: 'high'\n    }, {\n      id: '3',\n      type: 'payment_received',\n      title: 'Payment Received',\n      description: '$2,450 payment received from Dental Clinic ABC',\n      timestamp: new Date(Date.now() - 1000 * 60 * 120).toISOString(),\n      priority: 'low'\n    }, {\n      id: '4',\n      type: 'case_created',\n      title: 'New Bridge Case',\n      description: '3-unit bridge case received from Dr. Johnson',\n      timestamp: new Date(Date.now() - 1000 * 60 * 180).toISOString(),\n      user: 'Dr. Johnson',\n      priority: 'medium'\n    }]);\n\n    // Mock technician stats\n    setTechnicianStats([{\n      id: '1',\n      name: 'Mike Rodriguez',\n      active_cases: 8,\n      completed_today: 3,\n      efficiency_score: 94,\n      specialization: 'Crowns & Bridges'\n    }, {\n      id: '2',\n      name: 'Sarah Chen',\n      active_cases: 6,\n      completed_today: 4,\n      efficiency_score: 97,\n      specialization: 'Implants'\n    }, {\n      id: '3',\n      name: 'David Kim',\n      active_cases: 5,\n      completed_today: 2,\n      efficiency_score: 89,\n      specialization: 'Orthodontics'\n    }]);\n\n    // Mock inventory alerts\n    setInventoryAlerts([{\n      id: '1',\n      material_name: 'Zirconia Blocks',\n      current_stock: 5,\n      minimum_stock: 10,\n      unit: 'blocks',\n      urgency: 'high'\n    }, {\n      id: '2',\n      material_name: 'Titanium Abutments',\n      current_stock: 8,\n      minimum_stock: 15,\n      unit: 'pieces',\n      urgency: 'medium'\n    }, {\n      id: '3',\n      material_name: 'Ceramic Stain',\n      current_stock: 12,\n      minimum_stock: 20,\n      unit: 'ml',\n      urgency: 'low'\n    }]);\n  };\n  const formatCurrency = amount => {\n    return new Intl.NumberFormat('en-US', {\n      style: 'currency',\n      currency: 'USD'\n    }).format(amount);\n  };\n  const getStatusColor = status => {\n    const colors = {\n      'received': '#2196f3',\n      'design': '#ff9800',\n      'milling': '#9c27b0',\n      'sintering': '#e91e63',\n      'qc': '#ff5722',\n      'shipped': '#4caf50',\n      'delivered': '#8bc34a',\n      'cancelled': '#757575'\n    };\n    return colors[status] || '#666';\n  };\n  const getPriorityColor = priority => {\n    const colors = {\n      'low': '#4caf50',\n      'normal': '#2196f3',\n      'urgent': '#ff9800',\n      'stat': '#f44336'\n    };\n    return colors[priority] || '#666';\n  };\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(Box, {\n      display: \"flex\",\n      justifyContent: \"center\",\n      alignItems: \"center\",\n      minHeight: \"60vh\",\n      children: /*#__PURE__*/_jsxDEV(Box, {\n        textAlign: \"center\",\n        children: [/*#__PURE__*/_jsxDEV(CircularProgress, {\n          size: 60\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 308,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"h6\",\n          sx: {\n            mt: 2,\n            color: 'text.secondary'\n          },\n          children: \"Loading dashboard...\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 309,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 307,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 306,\n      columnNumber: 7\n    }, this);\n  }\n  if (error) {\n    return /*#__PURE__*/_jsxDEV(Box, {\n      p: 3,\n      children: /*#__PURE__*/_jsxDEV(Alert, {\n        severity: \"error\",\n        action: /*#__PURE__*/_jsxDEV(Button, {\n          color: \"inherit\",\n          size: \"small\",\n          onClick: loadDashboardData,\n          children: \"Retry\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 323,\n          columnNumber: 13\n        }, this),\n        children: error\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 320,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 319,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(Box, {\n    sx: {\n      p: 3,\n      backgroundColor: '#f5f5f5',\n      minHeight: '100vh'\n    },\n    children: [/*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        mb: 4\n      },\n      children: /*#__PURE__*/_jsxDEV(Box, {\n        display: \"flex\",\n        justifyContent: \"space-between\",\n        alignItems: \"center\",\n        mb: 2,\n        children: [/*#__PURE__*/_jsxDEV(Box, {\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h3\",\n            component: \"h1\",\n            sx: {\n              color: 'primary.main',\n              fontWeight: 'bold',\n              display: 'flex',\n              alignItems: 'center',\n              gap: 1\n            },\n            children: \"\\uD83E\\uDDB7 DentFlow Dashboard\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 339,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h6\",\n            color: \"text.secondary\",\n            sx: {\n              mt: 1\n            },\n            children: [\"Welcome back, \", user === null || user === void 0 ? void 0 : user.first_name, \" \", user === null || user === void 0 ? void 0 : user.last_name, (user === null || user === void 0 ? void 0 : user.role) && /*#__PURE__*/_jsxDEV(Chip, {\n              label: user.role,\n              size: \"small\",\n              color: \"primary\",\n              variant: \"outlined\",\n              sx: {\n                ml: 1\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 351,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 348,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 338,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Box, {\n          display: \"flex\",\n          alignItems: \"center\",\n          gap: 2,\n          children: [/*#__PURE__*/_jsxDEV(Tooltip, {\n            title: \"Refresh Dashboard\",\n            children: /*#__PURE__*/_jsxDEV(IconButton, {\n              onClick: loadDashboardData,\n              color: \"primary\",\n              children: /*#__PURE__*/_jsxDEV(Refresh, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 364,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 363,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 362,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Chip, {\n            icon: backendStatus.available ? /*#__PURE__*/_jsxDEV(CheckCircle, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 368,\n              columnNumber: 47\n            }, this) : /*#__PURE__*/_jsxDEV(Warning, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 368,\n              columnNumber: 65\n            }, this),\n            label: backendStatus.available ? 'Live Data' : 'Demo Data',\n            color: backendStatus.available ? 'success' : 'warning',\n            variant: \"filled\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 367,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 361,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 337,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 336,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Grid, {\n      container: true,\n      spacing: 3,\n      sx: {\n        mb: 4\n      },\n      children: [/*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        sm: 6,\n        md: 3,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          sx: {\n            height: '100%',\n            background: 'linear-gradient(135deg, #1976d2 0%, #42a5f5 100%)'\n          },\n          children: /*#__PURE__*/_jsxDEV(CardContent, {\n            children: [/*#__PURE__*/_jsxDEV(Box, {\n              display: \"flex\",\n              alignItems: \"center\",\n              justifyContent: \"space-between\",\n              children: [/*#__PURE__*/_jsxDEV(Box, {\n                children: [/*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"h4\",\n                  sx: {\n                    color: 'white',\n                    fontWeight: 'bold'\n                  },\n                  children: stats.active_cases\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 385,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body2\",\n                  sx: {\n                    color: 'rgba(255,255,255,0.8)'\n                  },\n                  children: \"Active Cases\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 388,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 384,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Avatar, {\n                sx: {\n                  bgcolor: 'rgba(255,255,255,0.2)',\n                  width: 56,\n                  height: 56\n                },\n                children: /*#__PURE__*/_jsxDEV(Assignment, {\n                  sx: {\n                    color: 'white',\n                    fontSize: 30\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 393,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 392,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 383,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Box, {\n              mt: 2,\n              children: [/*#__PURE__*/_jsxDEV(LinearProgress, {\n                variant: \"determinate\",\n                value: 75,\n                sx: {\n                  backgroundColor: 'rgba(255,255,255,0.2)',\n                  '& .MuiLinearProgress-bar': {\n                    backgroundColor: 'white'\n                  }\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 397,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"caption\",\n                sx: {\n                  color: 'rgba(255,255,255,0.8)',\n                  mt: 1\n                },\n                children: \"75% capacity utilized\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 405,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 396,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 382,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 381,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 380,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        sm: 6,\n        md: 3,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          sx: {\n            height: '100%',\n            background: 'linear-gradient(135deg, #4caf50 0%, #81c784 100%)'\n          },\n          children: /*#__PURE__*/_jsxDEV(CardContent, {\n            children: [/*#__PURE__*/_jsxDEV(Box, {\n              display: \"flex\",\n              alignItems: \"center\",\n              justifyContent: \"space-between\",\n              children: [/*#__PURE__*/_jsxDEV(Box, {\n                children: [/*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"h4\",\n                  sx: {\n                    color: 'white',\n                    fontWeight: 'bold'\n                  },\n                  children: stats.completed_today\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 419,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body2\",\n                  sx: {\n                    color: 'rgba(255,255,255,0.8)'\n                  },\n                  children: \"Completed Today\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 422,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 418,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Avatar, {\n                sx: {\n                  bgcolor: 'rgba(255,255,255,0.2)',\n                  width: 56,\n                  height: 56\n                },\n                children: /*#__PURE__*/_jsxDEV(CheckCircle, {\n                  sx: {\n                    color: 'white',\n                    fontSize: 30\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 427,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 426,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 417,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Box, {\n              mt: 2,\n              display: \"flex\",\n              alignItems: \"center\",\n              children: [/*#__PURE__*/_jsxDEV(TrendingUp, {\n                sx: {\n                  color: 'white',\n                  mr: 1\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 431,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                sx: {\n                  color: 'white'\n                },\n                children: \"+12% from yesterday\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 432,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 430,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 416,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 415,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 414,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        sm: 6,\n        md: 3,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          sx: {\n            height: '100%',\n            background: 'linear-gradient(135deg, #ff9800 0%, #ffb74d 100%)'\n          },\n          children: /*#__PURE__*/_jsxDEV(CardContent, {\n            children: [/*#__PURE__*/_jsxDEV(Box, {\n              display: \"flex\",\n              alignItems: \"center\",\n              justifyContent: \"space-between\",\n              children: [/*#__PURE__*/_jsxDEV(Box, {\n                children: [/*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"h4\",\n                  sx: {\n                    color: 'white',\n                    fontWeight: 'bold'\n                  },\n                  children: formatCurrency(stats.revenue_today)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 446,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body2\",\n                  sx: {\n                    color: 'rgba(255,255,255,0.8)'\n                  },\n                  children: \"Revenue Today\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 449,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 445,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Avatar, {\n                sx: {\n                  bgcolor: 'rgba(255,255,255,0.2)',\n                  width: 56,\n                  height: 56\n                },\n                children: /*#__PURE__*/_jsxDEV(AttachMoney, {\n                  sx: {\n                    color: 'white',\n                    fontSize: 30\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 454,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 453,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 444,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Box, {\n              mt: 2,\n              display: \"flex\",\n              alignItems: \"center\",\n              children: [/*#__PURE__*/_jsxDEV(TrendingUp, {\n                sx: {\n                  color: 'white',\n                  mr: 1\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 458,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                sx: {\n                  color: 'white'\n                },\n                children: [\"Target: \", formatCurrency(stats.revenue_today * 1.2)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 459,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 457,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 443,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 442,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 441,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        sm: 6,\n        md: 3,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          sx: {\n            height: '100%',\n            background: stats.pending_qc > 5 ? 'linear-gradient(135deg, #f44336 0%, #ef5350 100%)' : 'linear-gradient(135deg, #9c27b0 0%, #ba68c8 100%)'\n          },\n          children: /*#__PURE__*/_jsxDEV(CardContent, {\n            children: [/*#__PURE__*/_jsxDEV(Box, {\n              display: \"flex\",\n              alignItems: \"center\",\n              justifyContent: \"space-between\",\n              children: [/*#__PURE__*/_jsxDEV(Box, {\n                children: [/*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"h4\",\n                  sx: {\n                    color: 'white',\n                    fontWeight: 'bold'\n                  },\n                  children: stats.pending_qc\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 478,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body2\",\n                  sx: {\n                    color: 'rgba(255,255,255,0.8)'\n                  },\n                  children: \"Pending QC\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 481,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 477,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Avatar, {\n                sx: {\n                  bgcolor: 'rgba(255,255,255,0.2)',\n                  width: 56,\n                  height: 56\n                },\n                children: /*#__PURE__*/_jsxDEV(QualityControl, {\n                  sx: {\n                    color: 'white',\n                    fontSize: 30\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 486,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 485,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 476,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Box, {\n              mt: 2,\n              children: stats.pending_qc > 5 && /*#__PURE__*/_jsxDEV(Chip, {\n                label: \"High Priority\",\n                size: \"small\",\n                sx: {\n                  backgroundColor: 'rgba(255,255,255,0.2)',\n                  color: 'white',\n                  fontWeight: 'bold'\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 491,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 489,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 475,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 469,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 468,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        sm: 6,\n        md: 3,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          children: /*#__PURE__*/_jsxDEV(CardContent, {\n            children: [/*#__PURE__*/_jsxDEV(Box, {\n              display: \"flex\",\n              alignItems: \"center\",\n              justifyContent: \"space-between\",\n              children: [/*#__PURE__*/_jsxDEV(Box, {\n                children: [/*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"h5\",\n                  sx: {\n                    fontWeight: 'bold',\n                    color: 'primary.main'\n                  },\n                  children: formatCurrency(stats.revenue_this_month)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 512,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body2\",\n                  color: \"text.secondary\",\n                  children: \"Monthly Revenue\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 515,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 511,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Analytics, {\n                color: \"primary\",\n                sx: {\n                  fontSize: 40\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 519,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 510,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Box, {\n              mt: 1,\n              children: /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"caption\",\n                color: \"success.main\",\n                children: \"+18% vs last month\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 522,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 521,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 509,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 508,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 507,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        sm: 6,\n        md: 3,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          children: /*#__PURE__*/_jsxDEV(CardContent, {\n            children: [/*#__PURE__*/_jsxDEV(Box, {\n              display: \"flex\",\n              alignItems: \"center\",\n              justifyContent: \"space-between\",\n              children: [/*#__PURE__*/_jsxDEV(Box, {\n                children: [/*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"h5\",\n                  sx: {\n                    fontWeight: 'bold',\n                    color: 'success.main'\n                  },\n                  children: [stats.customer_satisfaction, \"/5.0\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 536,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body2\",\n                  color: \"text.secondary\",\n                  children: \"Customer Rating\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 539,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 535,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(People, {\n                color: \"success\",\n                sx: {\n                  fontSize: 40\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 543,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 534,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Box, {\n              mt: 1,\n              children: /*#__PURE__*/_jsxDEV(LinearProgress, {\n                variant: \"determinate\",\n                value: stats.customer_satisfaction / 5 * 100,\n                color: \"success\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 546,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 545,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 533,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 532,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 531,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        sm: 6,\n        md: 3,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          children: /*#__PURE__*/_jsxDEV(CardContent, {\n            children: [/*#__PURE__*/_jsxDEV(Box, {\n              display: \"flex\",\n              alignItems: \"center\",\n              justifyContent: \"space-between\",\n              children: [/*#__PURE__*/_jsxDEV(Box, {\n                children: [/*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"h5\",\n                  sx: {\n                    fontWeight: 'bold',\n                    color: 'warning.main'\n                  },\n                  children: stats.inventory_alerts\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 562,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body2\",\n                  color: \"text.secondary\",\n                  children: \"Inventory Alerts\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 565,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 561,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Badge, {\n                badgeContent: stats.inventory_alerts,\n                color: \"error\",\n                children: /*#__PURE__*/_jsxDEV(Inventory, {\n                  color: \"warning\",\n                  sx: {\n                    fontSize: 40\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 570,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 569,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 560,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Box, {\n              mt: 1,\n              children: /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"caption\",\n                color: \"warning.main\",\n                children: \"Materials need restocking\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 574,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 573,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 559,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 558,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 557,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        sm: 6,\n        md: 3,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          children: /*#__PURE__*/_jsxDEV(CardContent, {\n            children: [/*#__PURE__*/_jsxDEV(Box, {\n              display: \"flex\",\n              alignItems: \"center\",\n              justifyContent: \"space-between\",\n              children: [/*#__PURE__*/_jsxDEV(Box, {\n                children: [/*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"h5\",\n                  sx: {\n                    fontWeight: 'bold',\n                    color: 'info.main'\n                  },\n                  children: [stats.technician_utilization, \"%\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 588,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body2\",\n                  color: \"text.secondary\",\n                  children: \"Team Utilization\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 591,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 587,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Build, {\n                color: \"info\",\n                sx: {\n                  fontSize: 40\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 595,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 586,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Box, {\n              mt: 1,\n              children: /*#__PURE__*/_jsxDEV(LinearProgress, {\n                variant: \"determinate\",\n                value: stats.technician_utilization,\n                color: \"info\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 598,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 597,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 585,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 584,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 583,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 378,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Grid, {\n      container: true,\n      spacing: 3,\n      children: [/*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        lg: 8,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          children: /*#__PURE__*/_jsxDEV(CardContent, {\n            children: [/*#__PURE__*/_jsxDEV(Box, {\n              display: \"flex\",\n              justifyContent: \"space-between\",\n              alignItems: \"center\",\n              mb: 2,\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"h6\",\n                sx: {\n                  fontWeight: 'bold'\n                },\n                children: \"Recent Cases\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 615,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Button, {\n                variant: \"outlined\",\n                size: \"small\",\n                children: \"View All\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 618,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 614,\n              columnNumber: 15\n            }, this), recentCases.length === 0 ? /*#__PURE__*/_jsxDEV(Box, {\n              textAlign: \"center\",\n              py: 4,\n              children: /*#__PURE__*/_jsxDEV(Typography, {\n                color: \"text.secondary\",\n                children: \"No cases found. Create your first case to get started!\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 625,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 624,\n              columnNumber: 17\n            }, this) : /*#__PURE__*/_jsxDEV(TableContainer, {\n              children: /*#__PURE__*/_jsxDEV(Table, {\n                children: [/*#__PURE__*/_jsxDEV(TableHead, {\n                  children: /*#__PURE__*/_jsxDEV(TableRow, {\n                    children: [/*#__PURE__*/_jsxDEV(TableCell, {\n                      children: \"Case ID\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 634,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                      children: \"Patient\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 635,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                      children: \"Type\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 636,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                      children: \"Status\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 637,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                      children: \"Priority\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 638,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                      children: \"Due Date\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 639,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 633,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 632,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(TableBody, {\n                  children: recentCases.map(case_ => /*#__PURE__*/_jsxDEV(TableRow, {\n                    hover: true,\n                    children: [/*#__PURE__*/_jsxDEV(TableCell, {\n                      children: /*#__PURE__*/_jsxDEV(Typography, {\n                        variant: \"body2\",\n                        fontWeight: \"bold\",\n                        children: [\"#\", case_.id]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 646,\n                        columnNumber: 29\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 645,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                      children: case_.patient_name\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 650,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                      children: /*#__PURE__*/_jsxDEV(Chip, {\n                        label: case_.service_type,\n                        size: \"small\",\n                        variant: \"outlined\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 652,\n                        columnNumber: 29\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 651,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                      children: /*#__PURE__*/_jsxDEV(Chip, {\n                        label: case_.current_stage,\n                        size: \"small\",\n                        sx: {\n                          backgroundColor: alpha(getStatusColor(case_.status), 0.1),\n                          color: getStatusColor(case_.status),\n                          border: `1px solid ${getStatusColor(case_.status)}`\n                        }\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 659,\n                        columnNumber: 29\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 658,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                      children: /*#__PURE__*/_jsxDEV(Chip, {\n                        label: case_.priority,\n                        size: \"small\",\n                        color: case_.priority === 'stat' ? 'error' : case_.priority === 'urgent' ? 'warning' : case_.priority === 'normal' ? 'primary' : 'default'\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 670,\n                        columnNumber: 29\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 669,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                      children: /*#__PURE__*/_jsxDEV(Typography, {\n                        variant: \"body2\",\n                        color: case_.is_overdue ? 'error.main' : 'text.primary',\n                        children: case_.due_date ? case_.days_until_due !== null && case_.days_until_due !== undefined ? case_.days_until_due >= 0 ? `${case_.days_until_due} days` : `${Math.abs(case_.days_until_due)} days overdue` : 'Today' : 'Not set'\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 681,\n                        columnNumber: 29\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 680,\n                      columnNumber: 27\n                    }, this)]\n                  }, case_.id, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 644,\n                    columnNumber: 25\n                  }, this))\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 642,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 631,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 630,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 613,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 612,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 611,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        lg: 4,\n        children: [/*#__PURE__*/_jsxDEV(Card, {\n          sx: {\n            mb: 3\n          },\n          children: /*#__PURE__*/_jsxDEV(CardContent, {\n            children: [/*#__PURE__*/_jsxDEV(Box, {\n              display: \"flex\",\n              justifyContent: \"space-between\",\n              alignItems: \"center\",\n              mb: 2,\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"h6\",\n                sx: {\n                  fontWeight: 'bold'\n                },\n                children: \"Recent Activities\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 710,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Badge, {\n                badgeContent: recentActivities.length,\n                color: \"primary\",\n                children: /*#__PURE__*/_jsxDEV(Notifications, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 714,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 713,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 709,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(List, {\n              children: recentActivities.map((activity, index) => /*#__PURE__*/_jsxDEV(React.Fragment, {\n                children: [/*#__PURE__*/_jsxDEV(ListItem, {\n                  alignItems: \"flex-start\",\n                  sx: {\n                    px: 0\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(ListItemAvatar, {\n                    children: /*#__PURE__*/_jsxDEV(Avatar, {\n                      sx: {\n                        bgcolor: activity.type === 'case_completed' ? 'success.main' : activity.type === 'inventory_low' ? 'warning.main' : activity.type === 'payment_received' ? 'info.main' : 'primary.main',\n                        width: 32,\n                        height: 32\n                      },\n                      children: activity.type === 'case_completed' ? /*#__PURE__*/_jsxDEV(CheckCircle, {\n                        sx: {\n                          fontSize: 16\n                        }\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 732,\n                        columnNumber: 65\n                      }, this) : activity.type === 'inventory_low' ? /*#__PURE__*/_jsxDEV(Warning, {\n                        sx: {\n                          fontSize: 16\n                        }\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 733,\n                        columnNumber: 64\n                      }, this) : activity.type === 'payment_received' ? /*#__PURE__*/_jsxDEV(AttachMoney, {\n                        sx: {\n                          fontSize: 16\n                        }\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 734,\n                        columnNumber: 67\n                      }, this) : /*#__PURE__*/_jsxDEV(Assignment, {\n                        sx: {\n                          fontSize: 16\n                        }\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 735,\n                        columnNumber: 28\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 723,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 722,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(ListItemText, {\n                    primary: /*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"body2\",\n                      sx: {\n                        fontWeight: 'bold'\n                      },\n                      children: activity.title\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 740,\n                      columnNumber: 27\n                    }, this),\n                    secondary: /*#__PURE__*/_jsxDEV(Box, {\n                      children: [/*#__PURE__*/_jsxDEV(Typography, {\n                        variant: \"caption\",\n                        color: \"text.secondary\",\n                        children: activity.description\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 746,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                        variant: \"caption\",\n                        display: \"block\",\n                        color: \"text.secondary\",\n                        children: new Date(activity.timestamp).toLocaleString()\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 749,\n                        columnNumber: 29\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 745,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 738,\n                    columnNumber: 23\n                  }, this), activity.priority === 'high' && /*#__PURE__*/_jsxDEV(Chip, {\n                    label: \"High\",\n                    size: \"small\",\n                    color: \"error\",\n                    sx: {\n                      ml: 1\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 756,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 721,\n                  columnNumber: 21\n                }, this), index < recentActivities.length - 1 && /*#__PURE__*/_jsxDEV(Divider, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 764,\n                  columnNumber: 61\n                }, this)]\n              }, activity.id, true, {\n                fileName: _jsxFileName,\n                lineNumber: 720,\n                columnNumber: 19\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 718,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 708,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 707,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Card, {\n          sx: {\n            mb: 3\n          },\n          children: /*#__PURE__*/_jsxDEV(CardContent, {\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"h6\",\n              sx: {\n                fontWeight: 'bold',\n                mb: 2\n              },\n              children: \"Inventory Alerts\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 774,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(List, {\n              children: inventoryAlerts.map((alert, index) => /*#__PURE__*/_jsxDEV(React.Fragment, {\n                children: [/*#__PURE__*/_jsxDEV(ListItem, {\n                  sx: {\n                    px: 0\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(ListItemAvatar, {\n                    children: /*#__PURE__*/_jsxDEV(Avatar, {\n                      sx: {\n                        bgcolor: alert.urgency === 'high' ? 'error.main' : alert.urgency === 'medium' ? 'warning.main' : 'info.main',\n                        width: 32,\n                        height: 32\n                      },\n                      children: /*#__PURE__*/_jsxDEV(Inventory, {\n                        sx: {\n                          fontSize: 16\n                        }\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 791,\n                        columnNumber: 27\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 783,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 782,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(ListItemText, {\n                    primary: alert.material_name,\n                    secondary: /*#__PURE__*/_jsxDEV(Box, {\n                      children: [/*#__PURE__*/_jsxDEV(Typography, {\n                        variant: \"caption\",\n                        color: \"text.secondary\",\n                        children: [\"Current: \", alert.current_stock, \" \", alert.unit]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 798,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                        variant: \"caption\",\n                        display: \"block\",\n                        color: \"text.secondary\",\n                        children: [\"Minimum: \", alert.minimum_stock, \" \", alert.unit]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 801,\n                        columnNumber: 29\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 797,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 794,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(Chip, {\n                    label: alert.urgency,\n                    size: \"small\",\n                    color: alert.urgency === 'high' ? 'error' : alert.urgency === 'medium' ? 'warning' : 'info'\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 807,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 781,\n                  columnNumber: 21\n                }, this), index < inventoryAlerts.length - 1 && /*#__PURE__*/_jsxDEV(Divider, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 816,\n                  columnNumber: 60\n                }, this)]\n              }, alert.id, true, {\n                fileName: _jsxFileName,\n                lineNumber: 780,\n                columnNumber: 19\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 778,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 773,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 772,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Card, {\n          children: /*#__PURE__*/_jsxDEV(CardContent, {\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"h6\",\n              sx: {\n                fontWeight: 'bold',\n                mb: 2\n              },\n              children: \"Team Performance\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 826,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(List, {\n              children: technicianStats.map((tech, index) => /*#__PURE__*/_jsxDEV(React.Fragment, {\n                children: [/*#__PURE__*/_jsxDEV(ListItem, {\n                  sx: {\n                    px: 0\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(ListItemAvatar, {\n                    children: /*#__PURE__*/_jsxDEV(Avatar, {\n                      sx: {\n                        bgcolor: 'primary.main'\n                      },\n                      children: tech.name.split(' ').map(n => n[0]).join('')\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 835,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 834,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(ListItemText, {\n                    primary: tech.name,\n                    secondary: /*#__PURE__*/_jsxDEV(Box, {\n                      children: [/*#__PURE__*/_jsxDEV(Typography, {\n                        variant: \"caption\",\n                        color: \"text.secondary\",\n                        children: tech.specialization\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 843,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(Box, {\n                        display: \"flex\",\n                        alignItems: \"center\",\n                        mt: 0.5,\n                        children: [/*#__PURE__*/_jsxDEV(Typography, {\n                          variant: \"caption\",\n                          sx: {\n                            mr: 1\n                          },\n                          children: [\"Efficiency: \", tech.efficiency_score, \"%\"]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 847,\n                          columnNumber: 31\n                        }, this), /*#__PURE__*/_jsxDEV(LinearProgress, {\n                          variant: \"determinate\",\n                          value: tech.efficiency_score,\n                          sx: {\n                            flexGrow: 1,\n                            height: 4\n                          },\n                          color: tech.efficiency_score >= 95 ? 'success' : tech.efficiency_score >= 85 ? 'primary' : 'warning'\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 850,\n                          columnNumber: 31\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 846,\n                        columnNumber: 29\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 842,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 839,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(Box, {\n                    textAlign: \"center\",\n                    children: [/*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"caption\",\n                      color: \"text.secondary\",\n                      children: [\"Active: \", tech.active_cases]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 864,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"caption\",\n                      display: \"block\",\n                      color: \"text.secondary\",\n                      children: [\"Done: \", tech.completed_today]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 867,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 863,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 833,\n                  columnNumber: 21\n                }, this), index < technicianStats.length - 1 && /*#__PURE__*/_jsxDEV(Divider, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 872,\n                  columnNumber: 60\n                }, this)]\n              }, tech.id, true, {\n                fileName: _jsxFileName,\n                lineNumber: 832,\n                columnNumber: 19\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 830,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 825,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 824,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 705,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 609,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        mt: 4\n      },\n      children: /*#__PURE__*/_jsxDEV(Alert, {\n        severity: backendStatus.available ? 'success' : 'warning',\n        action: !backendStatus.available && /*#__PURE__*/_jsxDEV(Button, {\n          color: \"inherit\",\n          size: \"small\",\n          onClick: loadDashboardData,\n          children: \"\\uD83D\\uDD04 Reconnect\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 887,\n          columnNumber: 15\n        }, this),\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"body2\",\n          sx: {\n            fontWeight: 'bold'\n          },\n          children: [\"Backend Status: \", backendStatus.mode === 'live' ? 'Connected' : 'Demo Mode']\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 893,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"caption\",\n          children: backendStatus.available ? 'React frontend successfully connected to Django REST API backend. Real-time data loading from DentFlow case management system.' : 'Backend unavailable. Using demonstration data. All features remain functional for testing.'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 896,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 883,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 882,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 334,\n    columnNumber: 5\n  }, this);\n};\n_s(Dashboard, \"TpSWFmBzfBX/bnEMSRexAAm8j9w=\", false, function () {\n  return [useAuth, useTheme];\n});\n_c = Dashboard;\nexport default Dashboard;\nvar _c;\n$RefreshReg$(_c, \"Dashboard\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Box", "Grid", "Card", "<PERSON><PERSON><PERSON><PERSON>", "Typography", "Avatar", "Chip", "LinearProgress", "IconButton", "<PERSON><PERSON>", "<PERSON><PERSON>", "List", "ListItem", "ListItemText", "ListItemAvatar", "Divider", "Badge", "<PERSON><PERSON><PERSON>", "CircularProgress", "Table", "TableBody", "TableCell", "TableContainer", "TableHead", "TableRow", "useTheme", "alpha", "TrendingUp", "Assignment", "CheckCircle", "Warning", "AttachMoney", "People", "Inventory", "Analytics", "Refresh", "Notifications", "Build", "CasesService", "useAuth", "jsxDEV", "_jsxDEV", "Dashboard", "_s", "user", "theme", "stats", "setStats", "active_cases", "completed_today", "pending_qc", "revenue_today", "overdue_cases", "total_cases_this_month", "revenue_this_month", "average_completion_time", "customer_satisfaction", "inventory_alerts", "technician_utilization", "pending_invoices", "recentCases", "setRecentCases", "recentActivities", "setRecentActivities", "technicianStats", "setTechnicianStats", "inventoryAlerts", "setInventoryAlerts", "loading", "setLoading", "error", "setError", "backendStatus", "setBackendStatus", "available", "mode", "loadDashboardData", "checkBackendHealth", "dashboardStats", "cases", "Promise", "all", "getDashboardStats", "getCases", "enhancedStats", "slice", "getBackendStatus", "loadMockEnhancedData", "err", "console", "id", "type", "title", "description", "timestamp", "Date", "now", "toISOString", "priority", "name", "efficiency_score", "specialization", "material_name", "current_stock", "minimum_stock", "unit", "urgency", "formatCurrency", "amount", "Intl", "NumberFormat", "style", "currency", "format", "getStatusColor", "status", "colors", "getPriorityColor", "display", "justifyContent", "alignItems", "minHeight", "children", "textAlign", "size", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "variant", "sx", "mt", "color", "p", "severity", "action", "onClick", "backgroundColor", "mb", "component", "fontWeight", "gap", "first_name", "last_name", "role", "label", "ml", "icon", "container", "spacing", "item", "xs", "sm", "md", "height", "background", "bgcolor", "width", "fontSize", "value", "mr", "QualityControl", "badgeContent", "lg", "length", "py", "map", "case_", "hover", "patient_name", "service_type", "current_stage", "border", "is_overdue", "due_date", "days_until_due", "undefined", "Math", "abs", "activity", "index", "Fragment", "px", "primary", "secondary", "toLocaleString", "alert", "tech", "split", "n", "join", "flexGrow", "_c", "$RefreshReg$"], "sources": ["C:/GPT4_PROJECTS/DENTAL_LAB_ARCHITECTURE_PATTERNS/dentflow/frontend/src/components/Dashboard.tsx"], "sourcesContent": ["/**\n * DentFlow Dashboard Component - Enhanced Version\n * Comprehensive dashboard with advanced widgets, charts, and real-time data visualization\n */\n\nimport React, { useState, useEffect } from 'react';\nimport {\n  Box,\n  Grid,\n  Card,\n  CardContent,\n  Typography,\n  Avatar,\n  Chip,\n  LinearProgress,\n  IconButton,\n  Button,\n  Alert,\n  Paper,\n  List,\n  ListItem,\n  ListItemText,\n  ListItemAvatar,\n  Divider,\n  Badge,\n  Tooltip,\n  CircularProgress,\n  Table,\n  TableBody,\n  TableCell,\n  TableContainer,\n  TableHead,\n  TableRow,\n  useTheme,\n  alpha,\n} from '@mui/material';\nimport {\n  TrendingUp,\n  TrendingDown,\n  Assignment,\n  CheckCircle,\n  Warning,\n  Schedule,\n  AttachMoney,\n  People,\n  Inventory,\n  Analytics,\n  Refresh,\n  Notifications,\n  CalendarToday,\n  Build,\n  LocalShipping,\n  VerifiedUser,\n} from '@mui/icons-material';\nimport { CasesService } from '../api/casesService';\nimport { Case } from '../api/types';\nimport { useAuth } from '../context/AuthContext';\n\ninterface DashboardStats {\n  active_cases: number;\n  completed_today: number;\n  pending_qc: number;\n  revenue_today: number;\n  overdue_cases: number;\n  total_cases_this_month: number;\n  revenue_this_month: number;\n  average_completion_time: number;\n  customer_satisfaction: number;\n  inventory_alerts: number;\n  technician_utilization: number;\n  pending_invoices: number;\n}\n\ninterface ActivityItem {\n  id: string;\n  type: 'case_created' | 'case_completed' | 'payment_received' | 'inventory_low' | 'qc_failed';\n  title: string;\n  description: string;\n  timestamp: string;\n  user?: string;\n  priority?: 'low' | 'medium' | 'high';\n}\n\ninterface TechnicianStats {\n  id: string;\n  name: string;\n  active_cases: number;\n  completed_today: number;\n  efficiency_score: number;\n  specialization: string;\n}\n\ninterface InventoryAlert {\n  id: string;\n  material_name: string;\n  current_stock: number;\n  minimum_stock: number;\n  unit: string;\n  urgency: 'low' | 'medium' | 'high';\n}\n\nconst Dashboard: React.FC = () => {\n  const { user } = useAuth();\n  const theme = useTheme();\n\n  const [stats, setStats] = useState<DashboardStats>({\n    active_cases: 0,\n    completed_today: 0,\n    pending_qc: 0,\n    revenue_today: 0,\n    overdue_cases: 0,\n    total_cases_this_month: 0,\n    revenue_this_month: 0,\n    average_completion_time: 0,\n    customer_satisfaction: 0,\n    inventory_alerts: 0,\n    technician_utilization: 0,\n    pending_invoices: 0,\n  });\n\n  const [recentCases, setRecentCases] = useState<Case[]>([]);\n  const [recentActivities, setRecentActivities] = useState<ActivityItem[]>([]);\n  const [technicianStats, setTechnicianStats] = useState<TechnicianStats[]>([]);\n  const [inventoryAlerts, setInventoryAlerts] = useState<InventoryAlert[]>([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState<string>('');\n  const [backendStatus, setBackendStatus] = useState<{available: boolean; mode: string}>({\n    available: false,\n    mode: 'mock'\n  });\n\n  useEffect(() => {\n    loadDashboardData();\n  }, []);\n\n  const loadDashboardData = async () => {\n    try {\n      setLoading(true);\n      setError('');\n\n      // Check backend health first\n      await CasesService.checkBackendHealth();\n\n      const [dashboardStats, cases] = await Promise.all([\n        CasesService.getDashboardStats(),\n        CasesService.getCases(),\n      ]);\n\n      // Enhanced stats with mock data for demo\n      const enhancedStats = {\n        ...dashboardStats,\n        total_cases_this_month: dashboardStats.active_cases + dashboardStats.completed_today + 45,\n        revenue_this_month: dashboardStats.revenue_today * 22,\n        average_completion_time: 3.2,\n        customer_satisfaction: 4.7,\n        inventory_alerts: 3,\n        technician_utilization: 87,\n        pending_invoices: 12,\n      };\n\n      setStats(enhancedStats);\n      setRecentCases(cases.slice(0, 8)); // Show more cases\n      setBackendStatus(CasesService.getBackendStatus());\n\n      // Load mock data for enhanced features\n      loadMockEnhancedData();\n\n    } catch (err: any) {\n      console.error('Error loading dashboard:', err);\n      setError('Failed to load some dashboard data');\n      setBackendStatus(CasesService.getBackendStatus());\n      loadMockEnhancedData(); // Load mock data even on error\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const loadMockEnhancedData = () => {\n    // Mock recent activities\n    setRecentActivities([\n      {\n        id: '1',\n        type: 'case_completed',\n        title: 'Crown Case #1234 Completed',\n        description: 'Zirconia crown for patient John Doe completed by Dr. Smith',\n        timestamp: new Date(Date.now() - 1000 * 60 * 15).toISOString(),\n        user: 'Dr. Smith',\n        priority: 'medium'\n      },\n      {\n        id: '2',\n        type: 'inventory_low',\n        title: 'Low Inventory Alert',\n        description: 'Zirconia blocks running low (5 units remaining)',\n        timestamp: new Date(Date.now() - 1000 * 60 * 45).toISOString(),\n        priority: 'high'\n      },\n      {\n        id: '3',\n        type: 'payment_received',\n        title: 'Payment Received',\n        description: '$2,450 payment received from Dental Clinic ABC',\n        timestamp: new Date(Date.now() - 1000 * 60 * 120).toISOString(),\n        priority: 'low'\n      },\n      {\n        id: '4',\n        type: 'case_created',\n        title: 'New Bridge Case',\n        description: '3-unit bridge case received from Dr. Johnson',\n        timestamp: new Date(Date.now() - 1000 * 60 * 180).toISOString(),\n        user: 'Dr. Johnson',\n        priority: 'medium'\n      }\n    ]);\n\n    // Mock technician stats\n    setTechnicianStats([\n      {\n        id: '1',\n        name: 'Mike Rodriguez',\n        active_cases: 8,\n        completed_today: 3,\n        efficiency_score: 94,\n        specialization: 'Crowns & Bridges'\n      },\n      {\n        id: '2',\n        name: 'Sarah Chen',\n        active_cases: 6,\n        completed_today: 4,\n        efficiency_score: 97,\n        specialization: 'Implants'\n      },\n      {\n        id: '3',\n        name: 'David Kim',\n        active_cases: 5,\n        completed_today: 2,\n        efficiency_score: 89,\n        specialization: 'Orthodontics'\n      }\n    ]);\n\n    // Mock inventory alerts\n    setInventoryAlerts([\n      {\n        id: '1',\n        material_name: 'Zirconia Blocks',\n        current_stock: 5,\n        minimum_stock: 10,\n        unit: 'blocks',\n        urgency: 'high'\n      },\n      {\n        id: '2',\n        material_name: 'Titanium Abutments',\n        current_stock: 8,\n        minimum_stock: 15,\n        unit: 'pieces',\n        urgency: 'medium'\n      },\n      {\n        id: '3',\n        material_name: 'Ceramic Stain',\n        current_stock: 12,\n        minimum_stock: 20,\n        unit: 'ml',\n        urgency: 'low'\n      }\n    ]);\n  };\n  const formatCurrency = (amount: number) => {\n    return new Intl.NumberFormat('en-US', {\n      style: 'currency',\n      currency: 'USD',\n    }).format(amount);\n  };\n\n  const getStatusColor = (status: string) => {\n    const colors: Record<string, string> = {\n      'received': '#2196f3',\n      'design': '#ff9800',\n      'milling': '#9c27b0',\n      'sintering': '#e91e63',\n      'qc': '#ff5722',\n      'shipped': '#4caf50',\n      'delivered': '#8bc34a',\n      'cancelled': '#757575',\n    };\n    return colors[status] || '#666';\n  };\n\n  const getPriorityColor = (priority: string) => {\n    const colors: Record<string, string> = {\n      'low': '#4caf50',\n      'normal': '#2196f3',\n      'urgent': '#ff9800',\n      'stat': '#f44336',\n    };\n    return colors[priority] || '#666';\n  };\n\n  if (loading) {\n    return (\n      <Box display=\"flex\" justifyContent=\"center\" alignItems=\"center\" minHeight=\"60vh\">\n        <Box textAlign=\"center\">\n          <CircularProgress size={60} />\n          <Typography variant=\"h6\" sx={{ mt: 2, color: 'text.secondary' }}>\n            Loading dashboard...\n          </Typography>\n        </Box>\n      </Box>\n    );\n  }\n\n  if (error) {\n    return (\n      <Box p={3}>\n        <Alert\n          severity=\"error\"\n          action={\n            <Button color=\"inherit\" size=\"small\" onClick={loadDashboardData}>\n              Retry\n            </Button>\n          }\n        >\n          {error}\n        </Alert>\n      </Box>\n    );\n  }\n  return (\n    <Box sx={{ p: 3, backgroundColor: '#f5f5f5', minHeight: '100vh' }}>\n      {/* Header */}\n      <Box sx={{ mb: 4 }}>\n        <Box display=\"flex\" justifyContent=\"space-between\" alignItems=\"center\" mb={2}>\n          <Box>\n            <Typography variant=\"h3\" component=\"h1\" sx={{\n              color: 'primary.main',\n              fontWeight: 'bold',\n              display: 'flex',\n              alignItems: 'center',\n              gap: 1\n            }}>\n              🦷 DentFlow Dashboard\n            </Typography>\n            <Typography variant=\"h6\" color=\"text.secondary\" sx={{ mt: 1 }}>\n              Welcome back, {user?.first_name} {user?.last_name}\n              {user?.role && (\n                <Chip\n                  label={user.role}\n                  size=\"small\"\n                  color=\"primary\"\n                  variant=\"outlined\"\n                  sx={{ ml: 1 }}\n                />\n              )}\n            </Typography>\n          </Box>\n          <Box display=\"flex\" alignItems=\"center\" gap={2}>\n            <Tooltip title=\"Refresh Dashboard\">\n              <IconButton onClick={loadDashboardData} color=\"primary\">\n                <Refresh />\n              </IconButton>\n            </Tooltip>\n            <Chip\n              icon={backendStatus.available ? <CheckCircle /> : <Warning />}\n              label={backendStatus.available ? 'Live Data' : 'Demo Data'}\n              color={backendStatus.available ? 'success' : 'warning'}\n              variant=\"filled\"\n            />\n          </Box>\n        </Box>\n      </Box>\n\n      {/* Enhanced KPI Cards */}\n      <Grid container spacing={3} sx={{ mb: 4 }}>\n        {/* Active Cases */}\n        <Grid item xs={12} sm={6} md={3}>\n          <Card sx={{ height: '100%', background: 'linear-gradient(135deg, #1976d2 0%, #42a5f5 100%)' }}>\n            <CardContent>\n              <Box display=\"flex\" alignItems=\"center\" justifyContent=\"space-between\">\n                <Box>\n                  <Typography variant=\"h4\" sx={{ color: 'white', fontWeight: 'bold' }}>\n                    {stats.active_cases}\n                  </Typography>\n                  <Typography variant=\"body2\" sx={{ color: 'rgba(255,255,255,0.8)' }}>\n                    Active Cases\n                  </Typography>\n                </Box>\n                <Avatar sx={{ bgcolor: 'rgba(255,255,255,0.2)', width: 56, height: 56 }}>\n                  <Assignment sx={{ color: 'white', fontSize: 30 }} />\n                </Avatar>\n              </Box>\n              <Box mt={2}>\n                <LinearProgress\n                  variant=\"determinate\"\n                  value={75}\n                  sx={{\n                    backgroundColor: 'rgba(255,255,255,0.2)',\n                    '& .MuiLinearProgress-bar': { backgroundColor: 'white' }\n                  }}\n                />\n                <Typography variant=\"caption\" sx={{ color: 'rgba(255,255,255,0.8)', mt: 1 }}>\n                  75% capacity utilized\n                </Typography>\n              </Box>\n            </CardContent>\n          </Card>\n        </Grid>\n\n        {/* Completed Today */}\n        <Grid item xs={12} sm={6} md={3}>\n          <Card sx={{ height: '100%', background: 'linear-gradient(135deg, #4caf50 0%, #81c784 100%)' }}>\n            <CardContent>\n              <Box display=\"flex\" alignItems=\"center\" justifyContent=\"space-between\">\n                <Box>\n                  <Typography variant=\"h4\" sx={{ color: 'white', fontWeight: 'bold' }}>\n                    {stats.completed_today}\n                  </Typography>\n                  <Typography variant=\"body2\" sx={{ color: 'rgba(255,255,255,0.8)' }}>\n                    Completed Today\n                  </Typography>\n                </Box>\n                <Avatar sx={{ bgcolor: 'rgba(255,255,255,0.2)', width: 56, height: 56 }}>\n                  <CheckCircle sx={{ color: 'white', fontSize: 30 }} />\n                </Avatar>\n              </Box>\n              <Box mt={2} display=\"flex\" alignItems=\"center\">\n                <TrendingUp sx={{ color: 'white', mr: 1 }} />\n                <Typography variant=\"body2\" sx={{ color: 'white' }}>\n                  +12% from yesterday\n                </Typography>\n              </Box>\n            </CardContent>\n          </Card>\n        </Grid>\n\n        {/* Revenue Today */}\n        <Grid item xs={12} sm={6} md={3}>\n          <Card sx={{ height: '100%', background: 'linear-gradient(135deg, #ff9800 0%, #ffb74d 100%)' }}>\n            <CardContent>\n              <Box display=\"flex\" alignItems=\"center\" justifyContent=\"space-between\">\n                <Box>\n                  <Typography variant=\"h4\" sx={{ color: 'white', fontWeight: 'bold' }}>\n                    {formatCurrency(stats.revenue_today)}\n                  </Typography>\n                  <Typography variant=\"body2\" sx={{ color: 'rgba(255,255,255,0.8)' }}>\n                    Revenue Today\n                  </Typography>\n                </Box>\n                <Avatar sx={{ bgcolor: 'rgba(255,255,255,0.2)', width: 56, height: 56 }}>\n                  <AttachMoney sx={{ color: 'white', fontSize: 30 }} />\n                </Avatar>\n              </Box>\n              <Box mt={2} display=\"flex\" alignItems=\"center\">\n                <TrendingUp sx={{ color: 'white', mr: 1 }} />\n                <Typography variant=\"body2\" sx={{ color: 'white' }}>\n                  Target: {formatCurrency(stats.revenue_today * 1.2)}\n                </Typography>\n              </Box>\n            </CardContent>\n          </Card>\n        </Grid>\n\n        {/* Pending QC */}\n        <Grid item xs={12} sm={6} md={3}>\n          <Card sx={{\n            height: '100%',\n            background: stats.pending_qc > 5 ?\n              'linear-gradient(135deg, #f44336 0%, #ef5350 100%)' :\n              'linear-gradient(135deg, #9c27b0 0%, #ba68c8 100%)'\n          }}>\n            <CardContent>\n              <Box display=\"flex\" alignItems=\"center\" justifyContent=\"space-between\">\n                <Box>\n                  <Typography variant=\"h4\" sx={{ color: 'white', fontWeight: 'bold' }}>\n                    {stats.pending_qc}\n                  </Typography>\n                  <Typography variant=\"body2\" sx={{ color: 'rgba(255,255,255,0.8)' }}>\n                    Pending QC\n                  </Typography>\n                </Box>\n                <Avatar sx={{ bgcolor: 'rgba(255,255,255,0.2)', width: 56, height: 56 }}>\n                  <QualityControl sx={{ color: 'white', fontSize: 30 }} />\n                </Avatar>\n              </Box>\n              <Box mt={2}>\n                {stats.pending_qc > 5 && (\n                  <Chip\n                    label=\"High Priority\"\n                    size=\"small\"\n                    sx={{\n                      backgroundColor: 'rgba(255,255,255,0.2)',\n                      color: 'white',\n                      fontWeight: 'bold'\n                    }}\n                  />\n                )}\n              </Box>\n            </CardContent>\n          </Card>\n        </Grid>\n\n        {/* Monthly Revenue */}\n        <Grid item xs={12} sm={6} md={3}>\n          <Card>\n            <CardContent>\n              <Box display=\"flex\" alignItems=\"center\" justifyContent=\"space-between\">\n                <Box>\n                  <Typography variant=\"h5\" sx={{ fontWeight: 'bold', color: 'primary.main' }}>\n                    {formatCurrency(stats.revenue_this_month)}\n                  </Typography>\n                  <Typography variant=\"body2\" color=\"text.secondary\">\n                    Monthly Revenue\n                  </Typography>\n                </Box>\n                <Analytics color=\"primary\" sx={{ fontSize: 40 }} />\n              </Box>\n              <Box mt={1}>\n                <Typography variant=\"caption\" color=\"success.main\">\n                  +18% vs last month\n                </Typography>\n              </Box>\n            </CardContent>\n          </Card>\n        </Grid>\n\n        {/* Customer Satisfaction */}\n        <Grid item xs={12} sm={6} md={3}>\n          <Card>\n            <CardContent>\n              <Box display=\"flex\" alignItems=\"center\" justifyContent=\"space-between\">\n                <Box>\n                  <Typography variant=\"h5\" sx={{ fontWeight: 'bold', color: 'success.main' }}>\n                    {stats.customer_satisfaction}/5.0\n                  </Typography>\n                  <Typography variant=\"body2\" color=\"text.secondary\">\n                    Customer Rating\n                  </Typography>\n                </Box>\n                <People color=\"success\" sx={{ fontSize: 40 }} />\n              </Box>\n              <Box mt={1}>\n                <LinearProgress\n                  variant=\"determinate\"\n                  value={(stats.customer_satisfaction / 5) * 100}\n                  color=\"success\"\n                />\n              </Box>\n            </CardContent>\n          </Card>\n        </Grid>\n\n        {/* Inventory Alerts */}\n        <Grid item xs={12} sm={6} md={3}>\n          <Card>\n            <CardContent>\n              <Box display=\"flex\" alignItems=\"center\" justifyContent=\"space-between\">\n                <Box>\n                  <Typography variant=\"h5\" sx={{ fontWeight: 'bold', color: 'warning.main' }}>\n                    {stats.inventory_alerts}\n                  </Typography>\n                  <Typography variant=\"body2\" color=\"text.secondary\">\n                    Inventory Alerts\n                  </Typography>\n                </Box>\n                <Badge badgeContent={stats.inventory_alerts} color=\"error\">\n                  <Inventory color=\"warning\" sx={{ fontSize: 40 }} />\n                </Badge>\n              </Box>\n              <Box mt={1}>\n                <Typography variant=\"caption\" color=\"warning.main\">\n                  Materials need restocking\n                </Typography>\n              </Box>\n            </CardContent>\n          </Card>\n        </Grid>\n\n        {/* Technician Utilization */}\n        <Grid item xs={12} sm={6} md={3}>\n          <Card>\n            <CardContent>\n              <Box display=\"flex\" alignItems=\"center\" justifyContent=\"space-between\">\n                <Box>\n                  <Typography variant=\"h5\" sx={{ fontWeight: 'bold', color: 'info.main' }}>\n                    {stats.technician_utilization}%\n                  </Typography>\n                  <Typography variant=\"body2\" color=\"text.secondary\">\n                    Team Utilization\n                  </Typography>\n                </Box>\n                <Build color=\"info\" sx={{ fontSize: 40 }} />\n              </Box>\n              <Box mt={1}>\n                <LinearProgress\n                  variant=\"determinate\"\n                  value={stats.technician_utilization}\n                  color=\"info\"\n                />\n              </Box>\n            </CardContent>\n          </Card>\n        </Grid>\n      </Grid>\n      {/* Main Content Grid */}\n      <Grid container spacing={3}>\n        {/* Recent Cases */}\n        <Grid item xs={12} lg={8}>\n          <Card>\n            <CardContent>\n              <Box display=\"flex\" justifyContent=\"space-between\" alignItems=\"center\" mb={2}>\n                <Typography variant=\"h6\" sx={{ fontWeight: 'bold' }}>\n                  Recent Cases\n                </Typography>\n                <Button variant=\"outlined\" size=\"small\">\n                  View All\n                </Button>\n              </Box>\n\n              {recentCases.length === 0 ? (\n                <Box textAlign=\"center\" py={4}>\n                  <Typography color=\"text.secondary\">\n                    No cases found. Create your first case to get started!\n                  </Typography>\n                </Box>\n              ) : (\n                <TableContainer>\n                  <Table>\n                    <TableHead>\n                      <TableRow>\n                        <TableCell>Case ID</TableCell>\n                        <TableCell>Patient</TableCell>\n                        <TableCell>Type</TableCell>\n                        <TableCell>Status</TableCell>\n                        <TableCell>Priority</TableCell>\n                        <TableCell>Due Date</TableCell>\n                      </TableRow>\n                    </TableHead>\n                    <TableBody>\n                      {recentCases.map((case_) => (\n                        <TableRow key={case_.id} hover>\n                          <TableCell>\n                            <Typography variant=\"body2\" fontWeight=\"bold\">\n                              #{case_.id}\n                            </Typography>\n                          </TableCell>\n                          <TableCell>{case_.patient_name}</TableCell>\n                          <TableCell>\n                            <Chip\n                              label={case_.service_type}\n                              size=\"small\"\n                              variant=\"outlined\"\n                            />\n                          </TableCell>\n                          <TableCell>\n                            <Chip\n                              label={case_.current_stage}\n                              size=\"small\"\n                              sx={{\n                                backgroundColor: alpha(getStatusColor(case_.status), 0.1),\n                                color: getStatusColor(case_.status),\n                                border: `1px solid ${getStatusColor(case_.status)}`\n                              }}\n                            />\n                          </TableCell>\n                          <TableCell>\n                            <Chip\n                              label={case_.priority}\n                              size=\"small\"\n                              color={\n                                case_.priority === 'stat' ? 'error' :\n                                case_.priority === 'urgent' ? 'warning' :\n                                case_.priority === 'normal' ? 'primary' : 'default'\n                              }\n                            />\n                          </TableCell>\n                          <TableCell>\n                            <Typography\n                              variant=\"body2\"\n                              color={case_.is_overdue ? 'error.main' : 'text.primary'}\n                            >\n                              {case_.due_date ? (\n                                case_.days_until_due !== null && case_.days_until_due !== undefined ? (\n                                  case_.days_until_due >= 0 ?\n                                    `${case_.days_until_due} days` :\n                                    `${Math.abs(case_.days_until_due)} days overdue`\n                                ) : 'Today'\n                              ) : 'Not set'}\n                            </Typography>\n                          </TableCell>\n                        </TableRow>\n                      ))}\n                    </TableBody>\n                  </Table>\n                </TableContainer>\n              )}\n            </CardContent>\n          </Card>\n        </Grid>\n\n        {/* Right Sidebar */}\n        <Grid item xs={12} lg={4}>\n          {/* Recent Activities */}\n          <Card sx={{ mb: 3 }}>\n            <CardContent>\n              <Box display=\"flex\" justifyContent=\"space-between\" alignItems=\"center\" mb={2}>\n                <Typography variant=\"h6\" sx={{ fontWeight: 'bold' }}>\n                  Recent Activities\n                </Typography>\n                <Badge badgeContent={recentActivities.length} color=\"primary\">\n                  <Notifications />\n                </Badge>\n              </Box>\n\n              <List>\n                {recentActivities.map((activity, index) => (\n                  <React.Fragment key={activity.id}>\n                    <ListItem alignItems=\"flex-start\" sx={{ px: 0 }}>\n                      <ListItemAvatar>\n                        <Avatar sx={{\n                          bgcolor:\n                            activity.type === 'case_completed' ? 'success.main' :\n                            activity.type === 'inventory_low' ? 'warning.main' :\n                            activity.type === 'payment_received' ? 'info.main' :\n                            'primary.main',\n                          width: 32,\n                          height: 32\n                        }}>\n                          {activity.type === 'case_completed' ? <CheckCircle sx={{ fontSize: 16 }} /> :\n                           activity.type === 'inventory_low' ? <Warning sx={{ fontSize: 16 }} /> :\n                           activity.type === 'payment_received' ? <AttachMoney sx={{ fontSize: 16 }} /> :\n                           <Assignment sx={{ fontSize: 16 }} />}\n                        </Avatar>\n                      </ListItemAvatar>\n                      <ListItemText\n                        primary={\n                          <Typography variant=\"body2\" sx={{ fontWeight: 'bold' }}>\n                            {activity.title}\n                          </Typography>\n                        }\n                        secondary={\n                          <Box>\n                            <Typography variant=\"caption\" color=\"text.secondary\">\n                              {activity.description}\n                            </Typography>\n                            <Typography variant=\"caption\" display=\"block\" color=\"text.secondary\">\n                              {new Date(activity.timestamp).toLocaleString()}\n                            </Typography>\n                          </Box>\n                        }\n                      />\n                      {activity.priority === 'high' && (\n                        <Chip\n                          label=\"High\"\n                          size=\"small\"\n                          color=\"error\"\n                          sx={{ ml: 1 }}\n                        />\n                      )}\n                    </ListItem>\n                    {index < recentActivities.length - 1 && <Divider />}\n                  </React.Fragment>\n                ))}\n              </List>\n            </CardContent>\n          </Card>\n\n          {/* Inventory Alerts */}\n          <Card sx={{ mb: 3 }}>\n            <CardContent>\n              <Typography variant=\"h6\" sx={{ fontWeight: 'bold', mb: 2 }}>\n                Inventory Alerts\n              </Typography>\n\n              <List>\n                {inventoryAlerts.map((alert, index) => (\n                  <React.Fragment key={alert.id}>\n                    <ListItem sx={{ px: 0 }}>\n                      <ListItemAvatar>\n                        <Avatar sx={{\n                          bgcolor:\n                            alert.urgency === 'high' ? 'error.main' :\n                            alert.urgency === 'medium' ? 'warning.main' :\n                            'info.main',\n                          width: 32,\n                          height: 32\n                        }}>\n                          <Inventory sx={{ fontSize: 16 }} />\n                        </Avatar>\n                      </ListItemAvatar>\n                      <ListItemText\n                        primary={alert.material_name}\n                        secondary={\n                          <Box>\n                            <Typography variant=\"caption\" color=\"text.secondary\">\n                              Current: {alert.current_stock} {alert.unit}\n                            </Typography>\n                            <Typography variant=\"caption\" display=\"block\" color=\"text.secondary\">\n                              Minimum: {alert.minimum_stock} {alert.unit}\n                            </Typography>\n                          </Box>\n                        }\n                      />\n                      <Chip\n                        label={alert.urgency}\n                        size=\"small\"\n                        color={\n                          alert.urgency === 'high' ? 'error' :\n                          alert.urgency === 'medium' ? 'warning' : 'info'\n                        }\n                      />\n                    </ListItem>\n                    {index < inventoryAlerts.length - 1 && <Divider />}\n                  </React.Fragment>\n                ))}\n              </List>\n            </CardContent>\n          </Card>\n\n          {/* Technician Performance */}\n          <Card>\n            <CardContent>\n              <Typography variant=\"h6\" sx={{ fontWeight: 'bold', mb: 2 }}>\n                Team Performance\n              </Typography>\n\n              <List>\n                {technicianStats.map((tech, index) => (\n                  <React.Fragment key={tech.id}>\n                    <ListItem sx={{ px: 0 }}>\n                      <ListItemAvatar>\n                        <Avatar sx={{ bgcolor: 'primary.main' }}>\n                          {tech.name.split(' ').map(n => n[0]).join('')}\n                        </Avatar>\n                      </ListItemAvatar>\n                      <ListItemText\n                        primary={tech.name}\n                        secondary={\n                          <Box>\n                            <Typography variant=\"caption\" color=\"text.secondary\">\n                              {tech.specialization}\n                            </Typography>\n                            <Box display=\"flex\" alignItems=\"center\" mt={0.5}>\n                              <Typography variant=\"caption\" sx={{ mr: 1 }}>\n                                Efficiency: {tech.efficiency_score}%\n                              </Typography>\n                              <LinearProgress\n                                variant=\"determinate\"\n                                value={tech.efficiency_score}\n                                sx={{ flexGrow: 1, height: 4 }}\n                                color={\n                                  tech.efficiency_score >= 95 ? 'success' :\n                                  tech.efficiency_score >= 85 ? 'primary' : 'warning'\n                                }\n                              />\n                            </Box>\n                          </Box>\n                        }\n                      />\n                      <Box textAlign=\"center\">\n                        <Typography variant=\"caption\" color=\"text.secondary\">\n                          Active: {tech.active_cases}\n                        </Typography>\n                        <Typography variant=\"caption\" display=\"block\" color=\"text.secondary\">\n                          Done: {tech.completed_today}\n                        </Typography>\n                      </Box>\n                    </ListItem>\n                    {index < technicianStats.length - 1 && <Divider />}\n                  </React.Fragment>\n                ))}\n              </List>\n            </CardContent>\n          </Card>\n        </Grid>\n      </Grid>\n\n      {/* System Status Footer */}\n      <Box sx={{ mt: 4 }}>\n        <Alert\n          severity={backendStatus.available ? 'success' : 'warning'}\n          action={\n            !backendStatus.available && (\n              <Button color=\"inherit\" size=\"small\" onClick={loadDashboardData}>\n                🔄 Reconnect\n              </Button>\n            )\n          }\n        >\n          <Typography variant=\"body2\" sx={{ fontWeight: 'bold' }}>\n            Backend Status: {backendStatus.mode === 'live' ? 'Connected' : 'Demo Mode'}\n          </Typography>\n          <Typography variant=\"caption\">\n            {backendStatus.available\n              ? 'React frontend successfully connected to Django REST API backend. Real-time data loading from DentFlow case management system.'\n              : 'Backend unavailable. Using demonstration data. All features remain functional for testing.'\n            }\n          </Typography>\n        </Alert>\n      </Box>\n    </Box>\n  );\n};\n\nexport default Dashboard;"], "mappings": ";;AAAA;AACA;AACA;AACA;;AAEA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SACEC,GAAG,EACHC,IAAI,EACJC,IAAI,EACJC,WAAW,EACXC,UAAU,EACVC,MAAM,EACNC,IAAI,EACJC,cAAc,EACdC,UAAU,EACVC,MAAM,EACNC,KAAK,EAELC,IAAI,EACJC,QAAQ,EACRC,YAAY,EACZC,cAAc,EACdC,OAAO,EACPC,KAAK,EACLC,OAAO,EACPC,gBAAgB,EAChBC,KAAK,EACLC,SAAS,EACTC,SAAS,EACTC,cAAc,EACdC,SAAS,EACTC,QAAQ,EACRC,QAAQ,EACRC,KAAK,QACA,eAAe;AACtB,SACEC,UAAU,EAEVC,UAAU,EACVC,WAAW,EACXC,OAAO,EAEPC,WAAW,EACXC,MAAM,EACNC,SAAS,EACTC,SAAS,EACTC,OAAO,EACPC,aAAa,EAEbC,KAAK,QAGA,qBAAqB;AAC5B,SAASC,YAAY,QAAQ,qBAAqB;AAElD,SAASC,OAAO,QAAQ,wBAAwB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AA6CjD,MAAMC,SAAmB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAChC,MAAM;IAAEC;EAAK,CAAC,GAAGL,OAAO,CAAC,CAAC;EAC1B,MAAMM,KAAK,GAAGpB,QAAQ,CAAC,CAAC;EAExB,MAAM,CAACqB,KAAK,EAAEC,QAAQ,CAAC,GAAGjD,QAAQ,CAAiB;IACjDkD,YAAY,EAAE,CAAC;IACfC,eAAe,EAAE,CAAC;IAClBC,UAAU,EAAE,CAAC;IACbC,aAAa,EAAE,CAAC;IAChBC,aAAa,EAAE,CAAC;IAChBC,sBAAsB,EAAE,CAAC;IACzBC,kBAAkB,EAAE,CAAC;IACrBC,uBAAuB,EAAE,CAAC;IAC1BC,qBAAqB,EAAE,CAAC;IACxBC,gBAAgB,EAAE,CAAC;IACnBC,sBAAsB,EAAE,CAAC;IACzBC,gBAAgB,EAAE;EACpB,CAAC,CAAC;EAEF,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAG/D,QAAQ,CAAS,EAAE,CAAC;EAC1D,MAAM,CAACgE,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGjE,QAAQ,CAAiB,EAAE,CAAC;EAC5E,MAAM,CAACkE,eAAe,EAAEC,kBAAkB,CAAC,GAAGnE,QAAQ,CAAoB,EAAE,CAAC;EAC7E,MAAM,CAACoE,eAAe,EAAEC,kBAAkB,CAAC,GAAGrE,QAAQ,CAAmB,EAAE,CAAC;EAC5E,MAAM,CAACsE,OAAO,EAAEC,UAAU,CAAC,GAAGvE,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACwE,KAAK,EAAEC,QAAQ,CAAC,GAAGzE,QAAQ,CAAS,EAAE,CAAC;EAC9C,MAAM,CAAC0E,aAAa,EAAEC,gBAAgB,CAAC,GAAG3E,QAAQ,CAAqC;IACrF4E,SAAS,EAAE,KAAK;IAChBC,IAAI,EAAE;EACR,CAAC,CAAC;EAEF5E,SAAS,CAAC,MAAM;IACd6E,iBAAiB,CAAC,CAAC;EACrB,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMA,iBAAiB,GAAG,MAAAA,CAAA,KAAY;IACpC,IAAI;MACFP,UAAU,CAAC,IAAI,CAAC;MAChBE,QAAQ,CAAC,EAAE,CAAC;;MAEZ;MACA,MAAMjC,YAAY,CAACuC,kBAAkB,CAAC,CAAC;MAEvC,MAAM,CAACC,cAAc,EAAEC,KAAK,CAAC,GAAG,MAAMC,OAAO,CAACC,GAAG,CAAC,CAChD3C,YAAY,CAAC4C,iBAAiB,CAAC,CAAC,EAChC5C,YAAY,CAAC6C,QAAQ,CAAC,CAAC,CACxB,CAAC;;MAEF;MACA,MAAMC,aAAa,GAAG;QACpB,GAAGN,cAAc;QACjBzB,sBAAsB,EAAEyB,cAAc,CAAC9B,YAAY,GAAG8B,cAAc,CAAC7B,eAAe,GAAG,EAAE;QACzFK,kBAAkB,EAAEwB,cAAc,CAAC3B,aAAa,GAAG,EAAE;QACrDI,uBAAuB,EAAE,GAAG;QAC5BC,qBAAqB,EAAE,GAAG;QAC1BC,gBAAgB,EAAE,CAAC;QACnBC,sBAAsB,EAAE,EAAE;QAC1BC,gBAAgB,EAAE;MACpB,CAAC;MAEDZ,QAAQ,CAACqC,aAAa,CAAC;MACvBvB,cAAc,CAACkB,KAAK,CAACM,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;MACnCZ,gBAAgB,CAACnC,YAAY,CAACgD,gBAAgB,CAAC,CAAC,CAAC;;MAEjD;MACAC,oBAAoB,CAAC,CAAC;IAExB,CAAC,CAAC,OAAOC,GAAQ,EAAE;MACjBC,OAAO,CAACnB,KAAK,CAAC,0BAA0B,EAAEkB,GAAG,CAAC;MAC9CjB,QAAQ,CAAC,oCAAoC,CAAC;MAC9CE,gBAAgB,CAACnC,YAAY,CAACgD,gBAAgB,CAAC,CAAC,CAAC;MACjDC,oBAAoB,CAAC,CAAC,CAAC,CAAC;IAC1B,CAAC,SAAS;MACRlB,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAMkB,oBAAoB,GAAGA,CAAA,KAAM;IACjC;IACAxB,mBAAmB,CAAC,CAClB;MACE2B,EAAE,EAAE,GAAG;MACPC,IAAI,EAAE,gBAAgB;MACtBC,KAAK,EAAE,4BAA4B;MACnCC,WAAW,EAAE,4DAA4D;MACzEC,SAAS,EAAE,IAAIC,IAAI,CAACA,IAAI,CAACC,GAAG,CAAC,CAAC,GAAG,IAAI,GAAG,EAAE,GAAG,EAAE,CAAC,CAACC,WAAW,CAAC,CAAC;MAC9DrD,IAAI,EAAE,WAAW;MACjBsD,QAAQ,EAAE;IACZ,CAAC,EACD;MACER,EAAE,EAAE,GAAG;MACPC,IAAI,EAAE,eAAe;MACrBC,KAAK,EAAE,qBAAqB;MAC5BC,WAAW,EAAE,iDAAiD;MAC9DC,SAAS,EAAE,IAAIC,IAAI,CAACA,IAAI,CAACC,GAAG,CAAC,CAAC,GAAG,IAAI,GAAG,EAAE,GAAG,EAAE,CAAC,CAACC,WAAW,CAAC,CAAC;MAC9DC,QAAQ,EAAE;IACZ,CAAC,EACD;MACER,EAAE,EAAE,GAAG;MACPC,IAAI,EAAE,kBAAkB;MACxBC,KAAK,EAAE,kBAAkB;MACzBC,WAAW,EAAE,gDAAgD;MAC7DC,SAAS,EAAE,IAAIC,IAAI,CAACA,IAAI,CAACC,GAAG,CAAC,CAAC,GAAG,IAAI,GAAG,EAAE,GAAG,GAAG,CAAC,CAACC,WAAW,CAAC,CAAC;MAC/DC,QAAQ,EAAE;IACZ,CAAC,EACD;MACER,EAAE,EAAE,GAAG;MACPC,IAAI,EAAE,cAAc;MACpBC,KAAK,EAAE,iBAAiB;MACxBC,WAAW,EAAE,8CAA8C;MAC3DC,SAAS,EAAE,IAAIC,IAAI,CAACA,IAAI,CAACC,GAAG,CAAC,CAAC,GAAG,IAAI,GAAG,EAAE,GAAG,GAAG,CAAC,CAACC,WAAW,CAAC,CAAC;MAC/DrD,IAAI,EAAE,aAAa;MACnBsD,QAAQ,EAAE;IACZ,CAAC,CACF,CAAC;;IAEF;IACAjC,kBAAkB,CAAC,CACjB;MACEyB,EAAE,EAAE,GAAG;MACPS,IAAI,EAAE,gBAAgB;MACtBnD,YAAY,EAAE,CAAC;MACfC,eAAe,EAAE,CAAC;MAClBmD,gBAAgB,EAAE,EAAE;MACpBC,cAAc,EAAE;IAClB,CAAC,EACD;MACEX,EAAE,EAAE,GAAG;MACPS,IAAI,EAAE,YAAY;MAClBnD,YAAY,EAAE,CAAC;MACfC,eAAe,EAAE,CAAC;MAClBmD,gBAAgB,EAAE,EAAE;MACpBC,cAAc,EAAE;IAClB,CAAC,EACD;MACEX,EAAE,EAAE,GAAG;MACPS,IAAI,EAAE,WAAW;MACjBnD,YAAY,EAAE,CAAC;MACfC,eAAe,EAAE,CAAC;MAClBmD,gBAAgB,EAAE,EAAE;MACpBC,cAAc,EAAE;IAClB,CAAC,CACF,CAAC;;IAEF;IACAlC,kBAAkB,CAAC,CACjB;MACEuB,EAAE,EAAE,GAAG;MACPY,aAAa,EAAE,iBAAiB;MAChCC,aAAa,EAAE,CAAC;MAChBC,aAAa,EAAE,EAAE;MACjBC,IAAI,EAAE,QAAQ;MACdC,OAAO,EAAE;IACX,CAAC,EACD;MACEhB,EAAE,EAAE,GAAG;MACPY,aAAa,EAAE,oBAAoB;MACnCC,aAAa,EAAE,CAAC;MAChBC,aAAa,EAAE,EAAE;MACjBC,IAAI,EAAE,QAAQ;MACdC,OAAO,EAAE;IACX,CAAC,EACD;MACEhB,EAAE,EAAE,GAAG;MACPY,aAAa,EAAE,eAAe;MAC9BC,aAAa,EAAE,EAAE;MACjBC,aAAa,EAAE,EAAE;MACjBC,IAAI,EAAE,IAAI;MACVC,OAAO,EAAE;IACX,CAAC,CACF,CAAC;EACJ,CAAC;EACD,MAAMC,cAAc,GAAIC,MAAc,IAAK;IACzC,OAAO,IAAIC,IAAI,CAACC,YAAY,CAAC,OAAO,EAAE;MACpCC,KAAK,EAAE,UAAU;MACjBC,QAAQ,EAAE;IACZ,CAAC,CAAC,CAACC,MAAM,CAACL,MAAM,CAAC;EACnB,CAAC;EAED,MAAMM,cAAc,GAAIC,MAAc,IAAK;IACzC,MAAMC,MAA8B,GAAG;MACrC,UAAU,EAAE,SAAS;MACrB,QAAQ,EAAE,SAAS;MACnB,SAAS,EAAE,SAAS;MACpB,WAAW,EAAE,SAAS;MACtB,IAAI,EAAE,SAAS;MACf,SAAS,EAAE,SAAS;MACpB,WAAW,EAAE,SAAS;MACtB,WAAW,EAAE;IACf,CAAC;IACD,OAAOA,MAAM,CAACD,MAAM,CAAC,IAAI,MAAM;EACjC,CAAC;EAED,MAAME,gBAAgB,GAAInB,QAAgB,IAAK;IAC7C,MAAMkB,MAA8B,GAAG;MACrC,KAAK,EAAE,SAAS;MAChB,QAAQ,EAAE,SAAS;MACnB,QAAQ,EAAE,SAAS;MACnB,MAAM,EAAE;IACV,CAAC;IACD,OAAOA,MAAM,CAAClB,QAAQ,CAAC,IAAI,MAAM;EACnC,CAAC;EAED,IAAI9B,OAAO,EAAE;IACX,oBACE3B,OAAA,CAACzC,GAAG;MAACsH,OAAO,EAAC,MAAM;MAACC,cAAc,EAAC,QAAQ;MAACC,UAAU,EAAC,QAAQ;MAACC,SAAS,EAAC,MAAM;MAAAC,QAAA,eAC9EjF,OAAA,CAACzC,GAAG;QAAC2H,SAAS,EAAC,QAAQ;QAAAD,QAAA,gBACrBjF,OAAA,CAACvB,gBAAgB;UAAC0G,IAAI,EAAE;QAAG;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAC9BvF,OAAA,CAACrC,UAAU;UAAC6H,OAAO,EAAC,IAAI;UAACC,EAAE,EAAE;YAAEC,EAAE,EAAE,CAAC;YAAEC,KAAK,EAAE;UAAiB,CAAE;UAAAV,QAAA,EAAC;QAEjE;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;EAEA,IAAI1D,KAAK,EAAE;IACT,oBACE7B,OAAA,CAACzC,GAAG;MAACqI,CAAC,EAAE,CAAE;MAAAX,QAAA,eACRjF,OAAA,CAAC/B,KAAK;QACJ4H,QAAQ,EAAC,OAAO;QAChBC,MAAM,eACJ9F,OAAA,CAAChC,MAAM;UAAC2H,KAAK,EAAC,SAAS;UAACR,IAAI,EAAC,OAAO;UAACY,OAAO,EAAE5D,iBAAkB;UAAA8C,QAAA,EAAC;QAEjE;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CACT;QAAAN,QAAA,EAEApD;MAAK;QAAAuD,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC;EAEV;EACA,oBACEvF,OAAA,CAACzC,GAAG;IAACkI,EAAE,EAAE;MAAEG,CAAC,EAAE,CAAC;MAAEI,eAAe,EAAE,SAAS;MAAEhB,SAAS,EAAE;IAAQ,CAAE;IAAAC,QAAA,gBAEhEjF,OAAA,CAACzC,GAAG;MAACkI,EAAE,EAAE;QAAEQ,EAAE,EAAE;MAAE,CAAE;MAAAhB,QAAA,eACjBjF,OAAA,CAACzC,GAAG;QAACsH,OAAO,EAAC,MAAM;QAACC,cAAc,EAAC,eAAe;QAACC,UAAU,EAAC,QAAQ;QAACkB,EAAE,EAAE,CAAE;QAAAhB,QAAA,gBAC3EjF,OAAA,CAACzC,GAAG;UAAA0H,QAAA,gBACFjF,OAAA,CAACrC,UAAU;YAAC6H,OAAO,EAAC,IAAI;YAACU,SAAS,EAAC,IAAI;YAACT,EAAE,EAAE;cAC1CE,KAAK,EAAE,cAAc;cACrBQ,UAAU,EAAE,MAAM;cAClBtB,OAAO,EAAE,MAAM;cACfE,UAAU,EAAE,QAAQ;cACpBqB,GAAG,EAAE;YACP,CAAE;YAAAnB,QAAA,EAAC;UAEH;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eACbvF,OAAA,CAACrC,UAAU;YAAC6H,OAAO,EAAC,IAAI;YAACG,KAAK,EAAC,gBAAgB;YAACF,EAAE,EAAE;cAAEC,EAAE,EAAE;YAAE,CAAE;YAAAT,QAAA,GAAC,gBAC/C,EAAC9E,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEkG,UAAU,EAAC,GAAC,EAAClG,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEmG,SAAS,EAChD,CAAAnG,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEoG,IAAI,kBACTvG,OAAA,CAACnC,IAAI;cACH2I,KAAK,EAAErG,IAAI,CAACoG,IAAK;cACjBpB,IAAI,EAAC,OAAO;cACZQ,KAAK,EAAC,SAAS;cACfH,OAAO,EAAC,UAAU;cAClBC,EAAE,EAAE;gBAAEgB,EAAE,EAAE;cAAE;YAAE;cAAArB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACf,CACF;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACS,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV,CAAC,eACNvF,OAAA,CAACzC,GAAG;UAACsH,OAAO,EAAC,MAAM;UAACE,UAAU,EAAC,QAAQ;UAACqB,GAAG,EAAE,CAAE;UAAAnB,QAAA,gBAC7CjF,OAAA,CAACxB,OAAO;YAAC2E,KAAK,EAAC,mBAAmB;YAAA8B,QAAA,eAChCjF,OAAA,CAACjC,UAAU;cAACgI,OAAO,EAAE5D,iBAAkB;cAACwD,KAAK,EAAC,SAAS;cAAAV,QAAA,eACrDjF,OAAA,CAACN,OAAO;gBAAA0F,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC,eACVvF,OAAA,CAACnC,IAAI;YACH6I,IAAI,EAAE3E,aAAa,CAACE,SAAS,gBAAGjC,OAAA,CAACZ,WAAW;cAAAgG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,gBAAGvF,OAAA,CAACX,OAAO;cAAA+F,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YAC9DiB,KAAK,EAAEzE,aAAa,CAACE,SAAS,GAAG,WAAW,GAAG,WAAY;YAC3D0D,KAAK,EAAE5D,aAAa,CAACE,SAAS,GAAG,SAAS,GAAG,SAAU;YACvDuD,OAAO,EAAC;UAAQ;YAAAJ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACjB,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNvF,OAAA,CAACxC,IAAI;MAACmJ,SAAS;MAACC,OAAO,EAAE,CAAE;MAACnB,EAAE,EAAE;QAAEQ,EAAE,EAAE;MAAE,CAAE;MAAAhB,QAAA,gBAExCjF,OAAA,CAACxC,IAAI;QAACqJ,IAAI;QAACC,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,CAAE;QAACC,EAAE,EAAE,CAAE;QAAA/B,QAAA,eAC9BjF,OAAA,CAACvC,IAAI;UAACgI,EAAE,EAAE;YAAEwB,MAAM,EAAE,MAAM;YAAEC,UAAU,EAAE;UAAoD,CAAE;UAAAjC,QAAA,eAC5FjF,OAAA,CAACtC,WAAW;YAAAuH,QAAA,gBACVjF,OAAA,CAACzC,GAAG;cAACsH,OAAO,EAAC,MAAM;cAACE,UAAU,EAAC,QAAQ;cAACD,cAAc,EAAC,eAAe;cAAAG,QAAA,gBACpEjF,OAAA,CAACzC,GAAG;gBAAA0H,QAAA,gBACFjF,OAAA,CAACrC,UAAU;kBAAC6H,OAAO,EAAC,IAAI;kBAACC,EAAE,EAAE;oBAAEE,KAAK,EAAE,OAAO;oBAAEQ,UAAU,EAAE;kBAAO,CAAE;kBAAAlB,QAAA,EACjE5E,KAAK,CAACE;gBAAY;kBAAA6E,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACT,CAAC,eACbvF,OAAA,CAACrC,UAAU;kBAAC6H,OAAO,EAAC,OAAO;kBAACC,EAAE,EAAE;oBAAEE,KAAK,EAAE;kBAAwB,CAAE;kBAAAV,QAAA,EAAC;gBAEpE;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACV,CAAC,eACNvF,OAAA,CAACpC,MAAM;gBAAC6H,EAAE,EAAE;kBAAE0B,OAAO,EAAE,uBAAuB;kBAAEC,KAAK,EAAE,EAAE;kBAAEH,MAAM,EAAE;gBAAG,CAAE;gBAAAhC,QAAA,eACtEjF,OAAA,CAACb,UAAU;kBAACsG,EAAE,EAAE;oBAAEE,KAAK,EAAE,OAAO;oBAAE0B,QAAQ,EAAE;kBAAG;gBAAE;kBAAAjC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC9C,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC,eACNvF,OAAA,CAACzC,GAAG;cAACmI,EAAE,EAAE,CAAE;cAAAT,QAAA,gBACTjF,OAAA,CAAClC,cAAc;gBACb0H,OAAO,EAAC,aAAa;gBACrB8B,KAAK,EAAE,EAAG;gBACV7B,EAAE,EAAE;kBACFO,eAAe,EAAE,uBAAuB;kBACxC,0BAA0B,EAAE;oBAAEA,eAAe,EAAE;kBAAQ;gBACzD;cAAE;gBAAAZ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eACFvF,OAAA,CAACrC,UAAU;gBAAC6H,OAAO,EAAC,SAAS;gBAACC,EAAE,EAAE;kBAAEE,KAAK,EAAE,uBAAuB;kBAAED,EAAE,EAAE;gBAAE,CAAE;gBAAAT,QAAA,EAAC;cAE7E;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACK;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGPvF,OAAA,CAACxC,IAAI;QAACqJ,IAAI;QAACC,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,CAAE;QAACC,EAAE,EAAE,CAAE;QAAA/B,QAAA,eAC9BjF,OAAA,CAACvC,IAAI;UAACgI,EAAE,EAAE;YAAEwB,MAAM,EAAE,MAAM;YAAEC,UAAU,EAAE;UAAoD,CAAE;UAAAjC,QAAA,eAC5FjF,OAAA,CAACtC,WAAW;YAAAuH,QAAA,gBACVjF,OAAA,CAACzC,GAAG;cAACsH,OAAO,EAAC,MAAM;cAACE,UAAU,EAAC,QAAQ;cAACD,cAAc,EAAC,eAAe;cAAAG,QAAA,gBACpEjF,OAAA,CAACzC,GAAG;gBAAA0H,QAAA,gBACFjF,OAAA,CAACrC,UAAU;kBAAC6H,OAAO,EAAC,IAAI;kBAACC,EAAE,EAAE;oBAAEE,KAAK,EAAE,OAAO;oBAAEQ,UAAU,EAAE;kBAAO,CAAE;kBAAAlB,QAAA,EACjE5E,KAAK,CAACG;gBAAe;kBAAA4E,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACZ,CAAC,eACbvF,OAAA,CAACrC,UAAU;kBAAC6H,OAAO,EAAC,OAAO;kBAACC,EAAE,EAAE;oBAAEE,KAAK,EAAE;kBAAwB,CAAE;kBAAAV,QAAA,EAAC;gBAEpE;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACV,CAAC,eACNvF,OAAA,CAACpC,MAAM;gBAAC6H,EAAE,EAAE;kBAAE0B,OAAO,EAAE,uBAAuB;kBAAEC,KAAK,EAAE,EAAE;kBAAEH,MAAM,EAAE;gBAAG,CAAE;gBAAAhC,QAAA,eACtEjF,OAAA,CAACZ,WAAW;kBAACqG,EAAE,EAAE;oBAAEE,KAAK,EAAE,OAAO;oBAAE0B,QAAQ,EAAE;kBAAG;gBAAE;kBAAAjC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC/C,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC,eACNvF,OAAA,CAACzC,GAAG;cAACmI,EAAE,EAAE,CAAE;cAACb,OAAO,EAAC,MAAM;cAACE,UAAU,EAAC,QAAQ;cAAAE,QAAA,gBAC5CjF,OAAA,CAACd,UAAU;gBAACuG,EAAE,EAAE;kBAAEE,KAAK,EAAE,OAAO;kBAAE4B,EAAE,EAAE;gBAAE;cAAE;gBAAAnC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eAC7CvF,OAAA,CAACrC,UAAU;gBAAC6H,OAAO,EAAC,OAAO;gBAACC,EAAE,EAAE;kBAAEE,KAAK,EAAE;gBAAQ,CAAE;gBAAAV,QAAA,EAAC;cAEpD;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACK;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGPvF,OAAA,CAACxC,IAAI;QAACqJ,IAAI;QAACC,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,CAAE;QAACC,EAAE,EAAE,CAAE;QAAA/B,QAAA,eAC9BjF,OAAA,CAACvC,IAAI;UAACgI,EAAE,EAAE;YAAEwB,MAAM,EAAE,MAAM;YAAEC,UAAU,EAAE;UAAoD,CAAE;UAAAjC,QAAA,eAC5FjF,OAAA,CAACtC,WAAW;YAAAuH,QAAA,gBACVjF,OAAA,CAACzC,GAAG;cAACsH,OAAO,EAAC,MAAM;cAACE,UAAU,EAAC,QAAQ;cAACD,cAAc,EAAC,eAAe;cAAAG,QAAA,gBACpEjF,OAAA,CAACzC,GAAG;gBAAA0H,QAAA,gBACFjF,OAAA,CAACrC,UAAU;kBAAC6H,OAAO,EAAC,IAAI;kBAACC,EAAE,EAAE;oBAAEE,KAAK,EAAE,OAAO;oBAAEQ,UAAU,EAAE;kBAAO,CAAE;kBAAAlB,QAAA,EACjEf,cAAc,CAAC7D,KAAK,CAACK,aAAa;gBAAC;kBAAA0E,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC1B,CAAC,eACbvF,OAAA,CAACrC,UAAU;kBAAC6H,OAAO,EAAC,OAAO;kBAACC,EAAE,EAAE;oBAAEE,KAAK,EAAE;kBAAwB,CAAE;kBAAAV,QAAA,EAAC;gBAEpE;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACV,CAAC,eACNvF,OAAA,CAACpC,MAAM;gBAAC6H,EAAE,EAAE;kBAAE0B,OAAO,EAAE,uBAAuB;kBAAEC,KAAK,EAAE,EAAE;kBAAEH,MAAM,EAAE;gBAAG,CAAE;gBAAAhC,QAAA,eACtEjF,OAAA,CAACV,WAAW;kBAACmG,EAAE,EAAE;oBAAEE,KAAK,EAAE,OAAO;oBAAE0B,QAAQ,EAAE;kBAAG;gBAAE;kBAAAjC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC/C,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC,eACNvF,OAAA,CAACzC,GAAG;cAACmI,EAAE,EAAE,CAAE;cAACb,OAAO,EAAC,MAAM;cAACE,UAAU,EAAC,QAAQ;cAAAE,QAAA,gBAC5CjF,OAAA,CAACd,UAAU;gBAACuG,EAAE,EAAE;kBAAEE,KAAK,EAAE,OAAO;kBAAE4B,EAAE,EAAE;gBAAE;cAAE;gBAAAnC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eAC7CvF,OAAA,CAACrC,UAAU;gBAAC6H,OAAO,EAAC,OAAO;gBAACC,EAAE,EAAE;kBAAEE,KAAK,EAAE;gBAAQ,CAAE;gBAAAV,QAAA,GAAC,UAC1C,EAACf,cAAc,CAAC7D,KAAK,CAACK,aAAa,GAAG,GAAG,CAAC;cAAA;gBAAA0E,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACxC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACK;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGPvF,OAAA,CAACxC,IAAI;QAACqJ,IAAI;QAACC,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,CAAE;QAACC,EAAE,EAAE,CAAE;QAAA/B,QAAA,eAC9BjF,OAAA,CAACvC,IAAI;UAACgI,EAAE,EAAE;YACRwB,MAAM,EAAE,MAAM;YACdC,UAAU,EAAE7G,KAAK,CAACI,UAAU,GAAG,CAAC,GAC9B,mDAAmD,GACnD;UACJ,CAAE;UAAAwE,QAAA,eACAjF,OAAA,CAACtC,WAAW;YAAAuH,QAAA,gBACVjF,OAAA,CAACzC,GAAG;cAACsH,OAAO,EAAC,MAAM;cAACE,UAAU,EAAC,QAAQ;cAACD,cAAc,EAAC,eAAe;cAAAG,QAAA,gBACpEjF,OAAA,CAACzC,GAAG;gBAAA0H,QAAA,gBACFjF,OAAA,CAACrC,UAAU;kBAAC6H,OAAO,EAAC,IAAI;kBAACC,EAAE,EAAE;oBAAEE,KAAK,EAAE,OAAO;oBAAEQ,UAAU,EAAE;kBAAO,CAAE;kBAAAlB,QAAA,EACjE5E,KAAK,CAACI;gBAAU;kBAAA2E,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACP,CAAC,eACbvF,OAAA,CAACrC,UAAU;kBAAC6H,OAAO,EAAC,OAAO;kBAACC,EAAE,EAAE;oBAAEE,KAAK,EAAE;kBAAwB,CAAE;kBAAAV,QAAA,EAAC;gBAEpE;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACV,CAAC,eACNvF,OAAA,CAACpC,MAAM;gBAAC6H,EAAE,EAAE;kBAAE0B,OAAO,EAAE,uBAAuB;kBAAEC,KAAK,EAAE,EAAE;kBAAEH,MAAM,EAAE;gBAAG,CAAE;gBAAAhC,QAAA,eACtEjF,OAAA,CAACwH,cAAc;kBAAC/B,EAAE,EAAE;oBAAEE,KAAK,EAAE,OAAO;oBAAE0B,QAAQ,EAAE;kBAAG;gBAAE;kBAAAjC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAClD,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC,eACNvF,OAAA,CAACzC,GAAG;cAACmI,EAAE,EAAE,CAAE;cAAAT,QAAA,EACR5E,KAAK,CAACI,UAAU,GAAG,CAAC,iBACnBT,OAAA,CAACnC,IAAI;gBACH2I,KAAK,EAAC,eAAe;gBACrBrB,IAAI,EAAC,OAAO;gBACZM,EAAE,EAAE;kBACFO,eAAe,EAAE,uBAAuB;kBACxCL,KAAK,EAAE,OAAO;kBACdQ,UAAU,EAAE;gBACd;cAAE;gBAAAf,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH;YACF;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACK;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGPvF,OAAA,CAACxC,IAAI;QAACqJ,IAAI;QAACC,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,CAAE;QAACC,EAAE,EAAE,CAAE;QAAA/B,QAAA,eAC9BjF,OAAA,CAACvC,IAAI;UAAAwH,QAAA,eACHjF,OAAA,CAACtC,WAAW;YAAAuH,QAAA,gBACVjF,OAAA,CAACzC,GAAG;cAACsH,OAAO,EAAC,MAAM;cAACE,UAAU,EAAC,QAAQ;cAACD,cAAc,EAAC,eAAe;cAAAG,QAAA,gBACpEjF,OAAA,CAACzC,GAAG;gBAAA0H,QAAA,gBACFjF,OAAA,CAACrC,UAAU;kBAAC6H,OAAO,EAAC,IAAI;kBAACC,EAAE,EAAE;oBAAEU,UAAU,EAAE,MAAM;oBAAER,KAAK,EAAE;kBAAe,CAAE;kBAAAV,QAAA,EACxEf,cAAc,CAAC7D,KAAK,CAACQ,kBAAkB;gBAAC;kBAAAuE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC/B,CAAC,eACbvF,OAAA,CAACrC,UAAU;kBAAC6H,OAAO,EAAC,OAAO;kBAACG,KAAK,EAAC,gBAAgB;kBAAAV,QAAA,EAAC;gBAEnD;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACV,CAAC,eACNvF,OAAA,CAACP,SAAS;gBAACkG,KAAK,EAAC,SAAS;gBAACF,EAAE,EAAE;kBAAE4B,QAAQ,EAAE;gBAAG;cAAE;gBAAAjC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChD,CAAC,eACNvF,OAAA,CAACzC,GAAG;cAACmI,EAAE,EAAE,CAAE;cAAAT,QAAA,eACTjF,OAAA,CAACrC,UAAU;gBAAC6H,OAAO,EAAC,SAAS;gBAACG,KAAK,EAAC,cAAc;gBAAAV,QAAA,EAAC;cAEnD;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACK;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGPvF,OAAA,CAACxC,IAAI;QAACqJ,IAAI;QAACC,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,CAAE;QAACC,EAAE,EAAE,CAAE;QAAA/B,QAAA,eAC9BjF,OAAA,CAACvC,IAAI;UAAAwH,QAAA,eACHjF,OAAA,CAACtC,WAAW;YAAAuH,QAAA,gBACVjF,OAAA,CAACzC,GAAG;cAACsH,OAAO,EAAC,MAAM;cAACE,UAAU,EAAC,QAAQ;cAACD,cAAc,EAAC,eAAe;cAAAG,QAAA,gBACpEjF,OAAA,CAACzC,GAAG;gBAAA0H,QAAA,gBACFjF,OAAA,CAACrC,UAAU;kBAAC6H,OAAO,EAAC,IAAI;kBAACC,EAAE,EAAE;oBAAEU,UAAU,EAAE,MAAM;oBAAER,KAAK,EAAE;kBAAe,CAAE;kBAAAV,QAAA,GACxE5E,KAAK,CAACU,qBAAqB,EAAC,MAC/B;gBAAA;kBAAAqE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACbvF,OAAA,CAACrC,UAAU;kBAAC6H,OAAO,EAAC,OAAO;kBAACG,KAAK,EAAC,gBAAgB;kBAAAV,QAAA,EAAC;gBAEnD;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACV,CAAC,eACNvF,OAAA,CAACT,MAAM;gBAACoG,KAAK,EAAC,SAAS;gBAACF,EAAE,EAAE;kBAAE4B,QAAQ,EAAE;gBAAG;cAAE;gBAAAjC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC7C,CAAC,eACNvF,OAAA,CAACzC,GAAG;cAACmI,EAAE,EAAE,CAAE;cAAAT,QAAA,eACTjF,OAAA,CAAClC,cAAc;gBACb0H,OAAO,EAAC,aAAa;gBACrB8B,KAAK,EAAGjH,KAAK,CAACU,qBAAqB,GAAG,CAAC,GAAI,GAAI;gBAC/C4E,KAAK,EAAC;cAAS;gBAAAP,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAChB;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACK;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGPvF,OAAA,CAACxC,IAAI;QAACqJ,IAAI;QAACC,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,CAAE;QAACC,EAAE,EAAE,CAAE;QAAA/B,QAAA,eAC9BjF,OAAA,CAACvC,IAAI;UAAAwH,QAAA,eACHjF,OAAA,CAACtC,WAAW;YAAAuH,QAAA,gBACVjF,OAAA,CAACzC,GAAG;cAACsH,OAAO,EAAC,MAAM;cAACE,UAAU,EAAC,QAAQ;cAACD,cAAc,EAAC,eAAe;cAAAG,QAAA,gBACpEjF,OAAA,CAACzC,GAAG;gBAAA0H,QAAA,gBACFjF,OAAA,CAACrC,UAAU;kBAAC6H,OAAO,EAAC,IAAI;kBAACC,EAAE,EAAE;oBAAEU,UAAU,EAAE,MAAM;oBAAER,KAAK,EAAE;kBAAe,CAAE;kBAAAV,QAAA,EACxE5E,KAAK,CAACW;gBAAgB;kBAAAoE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACb,CAAC,eACbvF,OAAA,CAACrC,UAAU;kBAAC6H,OAAO,EAAC,OAAO;kBAACG,KAAK,EAAC,gBAAgB;kBAAAV,QAAA,EAAC;gBAEnD;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACV,CAAC,eACNvF,OAAA,CAACzB,KAAK;gBAACkJ,YAAY,EAAEpH,KAAK,CAACW,gBAAiB;gBAAC2E,KAAK,EAAC,OAAO;gBAAAV,QAAA,eACxDjF,OAAA,CAACR,SAAS;kBAACmG,KAAK,EAAC,SAAS;kBAACF,EAAE,EAAE;oBAAE4B,QAAQ,EAAE;kBAAG;gBAAE;kBAAAjC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC9C,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL,CAAC,eACNvF,OAAA,CAACzC,GAAG;cAACmI,EAAE,EAAE,CAAE;cAAAT,QAAA,eACTjF,OAAA,CAACrC,UAAU;gBAAC6H,OAAO,EAAC,SAAS;gBAACG,KAAK,EAAC,cAAc;gBAAAV,QAAA,EAAC;cAEnD;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACK;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGPvF,OAAA,CAACxC,IAAI;QAACqJ,IAAI;QAACC,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,CAAE;QAACC,EAAE,EAAE,CAAE;QAAA/B,QAAA,eAC9BjF,OAAA,CAACvC,IAAI;UAAAwH,QAAA,eACHjF,OAAA,CAACtC,WAAW;YAAAuH,QAAA,gBACVjF,OAAA,CAACzC,GAAG;cAACsH,OAAO,EAAC,MAAM;cAACE,UAAU,EAAC,QAAQ;cAACD,cAAc,EAAC,eAAe;cAAAG,QAAA,gBACpEjF,OAAA,CAACzC,GAAG;gBAAA0H,QAAA,gBACFjF,OAAA,CAACrC,UAAU;kBAAC6H,OAAO,EAAC,IAAI;kBAACC,EAAE,EAAE;oBAAEU,UAAU,EAAE,MAAM;oBAAER,KAAK,EAAE;kBAAY,CAAE;kBAAAV,QAAA,GACrE5E,KAAK,CAACY,sBAAsB,EAAC,GAChC;gBAAA;kBAAAmE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACbvF,OAAA,CAACrC,UAAU;kBAAC6H,OAAO,EAAC,OAAO;kBAACG,KAAK,EAAC,gBAAgB;kBAAAV,QAAA,EAAC;gBAEnD;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACV,CAAC,eACNvF,OAAA,CAACJ,KAAK;gBAAC+F,KAAK,EAAC,MAAM;gBAACF,EAAE,EAAE;kBAAE4B,QAAQ,EAAE;gBAAG;cAAE;gBAAAjC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACzC,CAAC,eACNvF,OAAA,CAACzC,GAAG;cAACmI,EAAE,EAAE,CAAE;cAAAT,QAAA,eACTjF,OAAA,CAAClC,cAAc;gBACb0H,OAAO,EAAC,aAAa;gBACrB8B,KAAK,EAAEjH,KAAK,CAACY,sBAAuB;gBACpC0E,KAAK,EAAC;cAAM;gBAAAP,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACb;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACK;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAEPvF,OAAA,CAACxC,IAAI;MAACmJ,SAAS;MAACC,OAAO,EAAE,CAAE;MAAA3B,QAAA,gBAEzBjF,OAAA,CAACxC,IAAI;QAACqJ,IAAI;QAACC,EAAE,EAAE,EAAG;QAACY,EAAE,EAAE,CAAE;QAAAzC,QAAA,eACvBjF,OAAA,CAACvC,IAAI;UAAAwH,QAAA,eACHjF,OAAA,CAACtC,WAAW;YAAAuH,QAAA,gBACVjF,OAAA,CAACzC,GAAG;cAACsH,OAAO,EAAC,MAAM;cAACC,cAAc,EAAC,eAAe;cAACC,UAAU,EAAC,QAAQ;cAACkB,EAAE,EAAE,CAAE;cAAAhB,QAAA,gBAC3EjF,OAAA,CAACrC,UAAU;gBAAC6H,OAAO,EAAC,IAAI;gBAACC,EAAE,EAAE;kBAAEU,UAAU,EAAE;gBAAO,CAAE;gBAAAlB,QAAA,EAAC;cAErD;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eACbvF,OAAA,CAAChC,MAAM;gBAACwH,OAAO,EAAC,UAAU;gBAACL,IAAI,EAAC,OAAO;gBAAAF,QAAA,EAAC;cAExC;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC,EAELpE,WAAW,CAACwG,MAAM,KAAK,CAAC,gBACvB3H,OAAA,CAACzC,GAAG;cAAC2H,SAAS,EAAC,QAAQ;cAAC0C,EAAE,EAAE,CAAE;cAAA3C,QAAA,eAC5BjF,OAAA,CAACrC,UAAU;gBAACgI,KAAK,EAAC,gBAAgB;gBAAAV,QAAA,EAAC;cAEnC;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV,CAAC,gBAENvF,OAAA,CAACnB,cAAc;cAAAoG,QAAA,eACbjF,OAAA,CAACtB,KAAK;gBAAAuG,QAAA,gBACJjF,OAAA,CAAClB,SAAS;kBAAAmG,QAAA,eACRjF,OAAA,CAACjB,QAAQ;oBAAAkG,QAAA,gBACPjF,OAAA,CAACpB,SAAS;sBAAAqG,QAAA,EAAC;oBAAO;sBAAAG,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAW,CAAC,eAC9BvF,OAAA,CAACpB,SAAS;sBAAAqG,QAAA,EAAC;oBAAO;sBAAAG,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAW,CAAC,eAC9BvF,OAAA,CAACpB,SAAS;sBAAAqG,QAAA,EAAC;oBAAI;sBAAAG,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAW,CAAC,eAC3BvF,OAAA,CAACpB,SAAS;sBAAAqG,QAAA,EAAC;oBAAM;sBAAAG,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAW,CAAC,eAC7BvF,OAAA,CAACpB,SAAS;sBAAAqG,QAAA,EAAC;oBAAQ;sBAAAG,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAW,CAAC,eAC/BvF,OAAA,CAACpB,SAAS;sBAAAqG,QAAA,EAAC;oBAAQ;sBAAAG,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAW,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACvB;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACF,CAAC,eACZvF,OAAA,CAACrB,SAAS;kBAAAsG,QAAA,EACP9D,WAAW,CAAC0G,GAAG,CAAEC,KAAK,iBACrB9H,OAAA,CAACjB,QAAQ;oBAAgBgJ,KAAK;oBAAA9C,QAAA,gBAC5BjF,OAAA,CAACpB,SAAS;sBAAAqG,QAAA,eACRjF,OAAA,CAACrC,UAAU;wBAAC6H,OAAO,EAAC,OAAO;wBAACW,UAAU,EAAC,MAAM;wBAAAlB,QAAA,GAAC,GAC3C,EAAC6C,KAAK,CAAC7E,EAAE;sBAAA;wBAAAmC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACA;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACJ,CAAC,eACZvF,OAAA,CAACpB,SAAS;sBAAAqG,QAAA,EAAE6C,KAAK,CAACE;oBAAY;sBAAA5C,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAY,CAAC,eAC3CvF,OAAA,CAACpB,SAAS;sBAAAqG,QAAA,eACRjF,OAAA,CAACnC,IAAI;wBACH2I,KAAK,EAAEsB,KAAK,CAACG,YAAa;wBAC1B9C,IAAI,EAAC,OAAO;wBACZK,OAAO,EAAC;sBAAU;wBAAAJ,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACnB;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACO,CAAC,eACZvF,OAAA,CAACpB,SAAS;sBAAAqG,QAAA,eACRjF,OAAA,CAACnC,IAAI;wBACH2I,KAAK,EAAEsB,KAAK,CAACI,aAAc;wBAC3B/C,IAAI,EAAC,OAAO;wBACZM,EAAE,EAAE;0BACFO,eAAe,EAAE/G,KAAK,CAACwF,cAAc,CAACqD,KAAK,CAACpD,MAAM,CAAC,EAAE,GAAG,CAAC;0BACzDiB,KAAK,EAAElB,cAAc,CAACqD,KAAK,CAACpD,MAAM,CAAC;0BACnCyD,MAAM,EAAE,aAAa1D,cAAc,CAACqD,KAAK,CAACpD,MAAM,CAAC;wBACnD;sBAAE;wBAAAU,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACH;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACO,CAAC,eACZvF,OAAA,CAACpB,SAAS;sBAAAqG,QAAA,eACRjF,OAAA,CAACnC,IAAI;wBACH2I,KAAK,EAAEsB,KAAK,CAACrE,QAAS;wBACtB0B,IAAI,EAAC,OAAO;wBACZQ,KAAK,EACHmC,KAAK,CAACrE,QAAQ,KAAK,MAAM,GAAG,OAAO,GACnCqE,KAAK,CAACrE,QAAQ,KAAK,QAAQ,GAAG,SAAS,GACvCqE,KAAK,CAACrE,QAAQ,KAAK,QAAQ,GAAG,SAAS,GAAG;sBAC3C;wBAAA2B,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACF;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACO,CAAC,eACZvF,OAAA,CAACpB,SAAS;sBAAAqG,QAAA,eACRjF,OAAA,CAACrC,UAAU;wBACT6H,OAAO,EAAC,OAAO;wBACfG,KAAK,EAAEmC,KAAK,CAACM,UAAU,GAAG,YAAY,GAAG,cAAe;wBAAAnD,QAAA,EAEvD6C,KAAK,CAACO,QAAQ,GACbP,KAAK,CAACQ,cAAc,KAAK,IAAI,IAAIR,KAAK,CAACQ,cAAc,KAAKC,SAAS,GACjET,KAAK,CAACQ,cAAc,IAAI,CAAC,GACvB,GAAGR,KAAK,CAACQ,cAAc,OAAO,GAC9B,GAAGE,IAAI,CAACC,GAAG,CAACX,KAAK,CAACQ,cAAc,CAAC,eAAe,GAChD,OAAO,GACT;sBAAS;wBAAAlD,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACH;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACJ,CAAC;kBAAA,GAjDCuC,KAAK,CAAC7E,EAAE;oBAAAmC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAkDb,CACX;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACO,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACP;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACM,CACjB;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACU;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGPvF,OAAA,CAACxC,IAAI;QAACqJ,IAAI;QAACC,EAAE,EAAE,EAAG;QAACY,EAAE,EAAE,CAAE;QAAAzC,QAAA,gBAEvBjF,OAAA,CAACvC,IAAI;UAACgI,EAAE,EAAE;YAAEQ,EAAE,EAAE;UAAE,CAAE;UAAAhB,QAAA,eAClBjF,OAAA,CAACtC,WAAW;YAAAuH,QAAA,gBACVjF,OAAA,CAACzC,GAAG;cAACsH,OAAO,EAAC,MAAM;cAACC,cAAc,EAAC,eAAe;cAACC,UAAU,EAAC,QAAQ;cAACkB,EAAE,EAAE,CAAE;cAAAhB,QAAA,gBAC3EjF,OAAA,CAACrC,UAAU;gBAAC6H,OAAO,EAAC,IAAI;gBAACC,EAAE,EAAE;kBAAEU,UAAU,EAAE;gBAAO,CAAE;gBAAAlB,QAAA,EAAC;cAErD;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eACbvF,OAAA,CAACzB,KAAK;gBAACkJ,YAAY,EAAEpG,gBAAgB,CAACsG,MAAO;gBAAChC,KAAK,EAAC,SAAS;gBAAAV,QAAA,eAC3DjF,OAAA,CAACL,aAAa;kBAAAyF,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACZ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL,CAAC,eAENvF,OAAA,CAAC9B,IAAI;cAAA+G,QAAA,EACF5D,gBAAgB,CAACwG,GAAG,CAAC,CAACa,QAAQ,EAAEC,KAAK,kBACpC3I,OAAA,CAAC5C,KAAK,CAACwL,QAAQ;gBAAA3D,QAAA,gBACbjF,OAAA,CAAC7B,QAAQ;kBAAC4G,UAAU,EAAC,YAAY;kBAACU,EAAE,EAAE;oBAAEoD,EAAE,EAAE;kBAAE,CAAE;kBAAA5D,QAAA,gBAC9CjF,OAAA,CAAC3B,cAAc;oBAAA4G,QAAA,eACbjF,OAAA,CAACpC,MAAM;sBAAC6H,EAAE,EAAE;wBACV0B,OAAO,EACLuB,QAAQ,CAACxF,IAAI,KAAK,gBAAgB,GAAG,cAAc,GACnDwF,QAAQ,CAACxF,IAAI,KAAK,eAAe,GAAG,cAAc,GAClDwF,QAAQ,CAACxF,IAAI,KAAK,kBAAkB,GAAG,WAAW,GAClD,cAAc;wBAChBkE,KAAK,EAAE,EAAE;wBACTH,MAAM,EAAE;sBACV,CAAE;sBAAAhC,QAAA,EACCyD,QAAQ,CAACxF,IAAI,KAAK,gBAAgB,gBAAGlD,OAAA,CAACZ,WAAW;wBAACqG,EAAE,EAAE;0BAAE4B,QAAQ,EAAE;wBAAG;sBAAE;wBAAAjC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE,CAAC,GAC1EmD,QAAQ,CAACxF,IAAI,KAAK,eAAe,gBAAGlD,OAAA,CAACX,OAAO;wBAACoG,EAAE,EAAE;0BAAE4B,QAAQ,EAAE;wBAAG;sBAAE;wBAAAjC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE,CAAC,GACrEmD,QAAQ,CAACxF,IAAI,KAAK,kBAAkB,gBAAGlD,OAAA,CAACV,WAAW;wBAACmG,EAAE,EAAE;0BAAE4B,QAAQ,EAAE;wBAAG;sBAAE;wBAAAjC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE,CAAC,gBAC5EvF,OAAA,CAACb,UAAU;wBAACsG,EAAE,EAAE;0BAAE4B,QAAQ,EAAE;wBAAG;sBAAE;wBAAAjC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC/B;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACK,CAAC,eACjBvF,OAAA,CAAC5B,YAAY;oBACX0K,OAAO,eACL9I,OAAA,CAACrC,UAAU;sBAAC6H,OAAO,EAAC,OAAO;sBAACC,EAAE,EAAE;wBAAEU,UAAU,EAAE;sBAAO,CAAE;sBAAAlB,QAAA,EACpDyD,QAAQ,CAACvF;oBAAK;sBAAAiC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACL,CACb;oBACDwD,SAAS,eACP/I,OAAA,CAACzC,GAAG;sBAAA0H,QAAA,gBACFjF,OAAA,CAACrC,UAAU;wBAAC6H,OAAO,EAAC,SAAS;wBAACG,KAAK,EAAC,gBAAgB;wBAAAV,QAAA,EACjDyD,QAAQ,CAACtF;sBAAW;wBAAAgC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACX,CAAC,eACbvF,OAAA,CAACrC,UAAU;wBAAC6H,OAAO,EAAC,SAAS;wBAACX,OAAO,EAAC,OAAO;wBAACc,KAAK,EAAC,gBAAgB;wBAAAV,QAAA,EACjE,IAAI3B,IAAI,CAACoF,QAAQ,CAACrF,SAAS,CAAC,CAAC2F,cAAc,CAAC;sBAAC;wBAAA5D,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACpC,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACV;kBACN;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACF,CAAC,EACDmD,QAAQ,CAACjF,QAAQ,KAAK,MAAM,iBAC3BzD,OAAA,CAACnC,IAAI;oBACH2I,KAAK,EAAC,MAAM;oBACZrB,IAAI,EAAC,OAAO;oBACZQ,KAAK,EAAC,OAAO;oBACbF,EAAE,EAAE;sBAAEgB,EAAE,EAAE;oBAAE;kBAAE;oBAAArB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACf,CACF;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACO,CAAC,EACVoD,KAAK,GAAGtH,gBAAgB,CAACsG,MAAM,GAAG,CAAC,iBAAI3H,OAAA,CAAC1B,OAAO;kBAAA8G,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC;cAAA,GA5ChCmD,QAAQ,CAACzF,EAAE;gBAAAmC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OA6ChB,CACjB;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACI;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV,CAAC,eAGPvF,OAAA,CAACvC,IAAI;UAACgI,EAAE,EAAE;YAAEQ,EAAE,EAAE;UAAE,CAAE;UAAAhB,QAAA,eAClBjF,OAAA,CAACtC,WAAW;YAAAuH,QAAA,gBACVjF,OAAA,CAACrC,UAAU;cAAC6H,OAAO,EAAC,IAAI;cAACC,EAAE,EAAE;gBAAEU,UAAU,EAAE,MAAM;gBAAEF,EAAE,EAAE;cAAE,CAAE;cAAAhB,QAAA,EAAC;YAE5D;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eAEbvF,OAAA,CAAC9B,IAAI;cAAA+G,QAAA,EACFxD,eAAe,CAACoG,GAAG,CAAC,CAACoB,KAAK,EAAEN,KAAK,kBAChC3I,OAAA,CAAC5C,KAAK,CAACwL,QAAQ;gBAAA3D,QAAA,gBACbjF,OAAA,CAAC7B,QAAQ;kBAACsH,EAAE,EAAE;oBAAEoD,EAAE,EAAE;kBAAE,CAAE;kBAAA5D,QAAA,gBACtBjF,OAAA,CAAC3B,cAAc;oBAAA4G,QAAA,eACbjF,OAAA,CAACpC,MAAM;sBAAC6H,EAAE,EAAE;wBACV0B,OAAO,EACL8B,KAAK,CAAChF,OAAO,KAAK,MAAM,GAAG,YAAY,GACvCgF,KAAK,CAAChF,OAAO,KAAK,QAAQ,GAAG,cAAc,GAC3C,WAAW;wBACbmD,KAAK,EAAE,EAAE;wBACTH,MAAM,EAAE;sBACV,CAAE;sBAAAhC,QAAA,eACAjF,OAAA,CAACR,SAAS;wBAACiG,EAAE,EAAE;0BAAE4B,QAAQ,EAAE;wBAAG;sBAAE;wBAAAjC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC7B;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACK,CAAC,eACjBvF,OAAA,CAAC5B,YAAY;oBACX0K,OAAO,EAAEG,KAAK,CAACpF,aAAc;oBAC7BkF,SAAS,eACP/I,OAAA,CAACzC,GAAG;sBAAA0H,QAAA,gBACFjF,OAAA,CAACrC,UAAU;wBAAC6H,OAAO,EAAC,SAAS;wBAACG,KAAK,EAAC,gBAAgB;wBAAAV,QAAA,GAAC,WAC1C,EAACgE,KAAK,CAACnF,aAAa,EAAC,GAAC,EAACmF,KAAK,CAACjF,IAAI;sBAAA;wBAAAoB,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAChC,CAAC,eACbvF,OAAA,CAACrC,UAAU;wBAAC6H,OAAO,EAAC,SAAS;wBAACX,OAAO,EAAC,OAAO;wBAACc,KAAK,EAAC,gBAAgB;wBAAAV,QAAA,GAAC,WAC1D,EAACgE,KAAK,CAAClF,aAAa,EAAC,GAAC,EAACkF,KAAK,CAACjF,IAAI;sBAAA;wBAAAoB,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAChC,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACV;kBACN;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACF,CAAC,eACFvF,OAAA,CAACnC,IAAI;oBACH2I,KAAK,EAAEyC,KAAK,CAAChF,OAAQ;oBACrBkB,IAAI,EAAC,OAAO;oBACZQ,KAAK,EACHsD,KAAK,CAAChF,OAAO,KAAK,MAAM,GAAG,OAAO,GAClCgF,KAAK,CAAChF,OAAO,KAAK,QAAQ,GAAG,SAAS,GAAG;kBAC1C;oBAAAmB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACF,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACM,CAAC,EACVoD,KAAK,GAAGlH,eAAe,CAACkG,MAAM,GAAG,CAAC,iBAAI3H,OAAA,CAAC1B,OAAO;kBAAA8G,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC;cAAA,GApC/B0D,KAAK,CAAChG,EAAE;gBAAAmC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAqCb,CACjB;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACI;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV,CAAC,eAGPvF,OAAA,CAACvC,IAAI;UAAAwH,QAAA,eACHjF,OAAA,CAACtC,WAAW;YAAAuH,QAAA,gBACVjF,OAAA,CAACrC,UAAU;cAAC6H,OAAO,EAAC,IAAI;cAACC,EAAE,EAAE;gBAAEU,UAAU,EAAE,MAAM;gBAAEF,EAAE,EAAE;cAAE,CAAE;cAAAhB,QAAA,EAAC;YAE5D;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eAEbvF,OAAA,CAAC9B,IAAI;cAAA+G,QAAA,EACF1D,eAAe,CAACsG,GAAG,CAAC,CAACqB,IAAI,EAAEP,KAAK,kBAC/B3I,OAAA,CAAC5C,KAAK,CAACwL,QAAQ;gBAAA3D,QAAA,gBACbjF,OAAA,CAAC7B,QAAQ;kBAACsH,EAAE,EAAE;oBAAEoD,EAAE,EAAE;kBAAE,CAAE;kBAAA5D,QAAA,gBACtBjF,OAAA,CAAC3B,cAAc;oBAAA4G,QAAA,eACbjF,OAAA,CAACpC,MAAM;sBAAC6H,EAAE,EAAE;wBAAE0B,OAAO,EAAE;sBAAe,CAAE;sBAAAlC,QAAA,EACrCiE,IAAI,CAACxF,IAAI,CAACyF,KAAK,CAAC,GAAG,CAAC,CAACtB,GAAG,CAACuB,CAAC,IAAIA,CAAC,CAAC,CAAC,CAAC,CAAC,CAACC,IAAI,CAAC,EAAE;oBAAC;sBAAAjE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACvC;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACK,CAAC,eACjBvF,OAAA,CAAC5B,YAAY;oBACX0K,OAAO,EAAEI,IAAI,CAACxF,IAAK;oBACnBqF,SAAS,eACP/I,OAAA,CAACzC,GAAG;sBAAA0H,QAAA,gBACFjF,OAAA,CAACrC,UAAU;wBAAC6H,OAAO,EAAC,SAAS;wBAACG,KAAK,EAAC,gBAAgB;wBAAAV,QAAA,EACjDiE,IAAI,CAACtF;sBAAc;wBAAAwB,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACV,CAAC,eACbvF,OAAA,CAACzC,GAAG;wBAACsH,OAAO,EAAC,MAAM;wBAACE,UAAU,EAAC,QAAQ;wBAACW,EAAE,EAAE,GAAI;wBAAAT,QAAA,gBAC9CjF,OAAA,CAACrC,UAAU;0BAAC6H,OAAO,EAAC,SAAS;0BAACC,EAAE,EAAE;4BAAE8B,EAAE,EAAE;0BAAE,CAAE;0BAAAtC,QAAA,GAAC,cAC/B,EAACiE,IAAI,CAACvF,gBAAgB,EAAC,GACrC;wBAAA;0BAAAyB,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAY,CAAC,eACbvF,OAAA,CAAClC,cAAc;0BACb0H,OAAO,EAAC,aAAa;0BACrB8B,KAAK,EAAE4B,IAAI,CAACvF,gBAAiB;0BAC7B8B,EAAE,EAAE;4BAAE6D,QAAQ,EAAE,CAAC;4BAAErC,MAAM,EAAE;0BAAE,CAAE;0BAC/BtB,KAAK,EACHuD,IAAI,CAACvF,gBAAgB,IAAI,EAAE,GAAG,SAAS,GACvCuF,IAAI,CAACvF,gBAAgB,IAAI,EAAE,GAAG,SAAS,GAAG;wBAC3C;0BAAAyB,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACF,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACC,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH;kBACN;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACF,CAAC,eACFvF,OAAA,CAACzC,GAAG;oBAAC2H,SAAS,EAAC,QAAQ;oBAAAD,QAAA,gBACrBjF,OAAA,CAACrC,UAAU;sBAAC6H,OAAO,EAAC,SAAS;sBAACG,KAAK,EAAC,gBAAgB;sBAAAV,QAAA,GAAC,UAC3C,EAACiE,IAAI,CAAC3I,YAAY;oBAAA;sBAAA6E,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAChB,CAAC,eACbvF,OAAA,CAACrC,UAAU;sBAAC6H,OAAO,EAAC,SAAS;sBAACX,OAAO,EAAC,OAAO;sBAACc,KAAK,EAAC,gBAAgB;sBAAAV,QAAA,GAAC,QAC7D,EAACiE,IAAI,CAAC1I,eAAe;oBAAA;sBAAA4E,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACjB,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACV,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACE,CAAC,EACVoD,KAAK,GAAGpH,eAAe,CAACoG,MAAM,GAAG,CAAC,iBAAI3H,OAAA,CAAC1B,OAAO;kBAAA8G,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC;cAAA,GAxC/B2D,IAAI,CAACjG,EAAE;gBAAAmC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAyCZ,CACjB;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACI;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGPvF,OAAA,CAACzC,GAAG;MAACkI,EAAE,EAAE;QAAEC,EAAE,EAAE;MAAE,CAAE;MAAAT,QAAA,eACjBjF,OAAA,CAAC/B,KAAK;QACJ4H,QAAQ,EAAE9D,aAAa,CAACE,SAAS,GAAG,SAAS,GAAG,SAAU;QAC1D6D,MAAM,EACJ,CAAC/D,aAAa,CAACE,SAAS,iBACtBjC,OAAA,CAAChC,MAAM;UAAC2H,KAAK,EAAC,SAAS;UAACR,IAAI,EAAC,OAAO;UAACY,OAAO,EAAE5D,iBAAkB;UAAA8C,QAAA,EAAC;QAEjE;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAEX;QAAAN,QAAA,gBAEDjF,OAAA,CAACrC,UAAU;UAAC6H,OAAO,EAAC,OAAO;UAACC,EAAE,EAAE;YAAEU,UAAU,EAAE;UAAO,CAAE;UAAAlB,QAAA,GAAC,kBACtC,EAAClD,aAAa,CAACG,IAAI,KAAK,MAAM,GAAG,WAAW,GAAG,WAAW;QAAA;UAAAkD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChE,CAAC,eACbvF,OAAA,CAACrC,UAAU;UAAC6H,OAAO,EAAC,SAAS;UAAAP,QAAA,EAC1BlD,aAAa,CAACE,SAAS,GACpB,gIAAgI,GAChI;QAA4F;UAAAmD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAEtF,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACR;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACrF,EAAA,CApyBID,SAAmB;EAAA,QACNH,OAAO,EACVd,QAAQ;AAAA;AAAAuK,EAAA,GAFlBtJ,SAAmB;AAsyBzB,eAAeA,SAAS;AAAC,IAAAsJ,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}