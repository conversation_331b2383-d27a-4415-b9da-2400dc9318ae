/**
 * Cases List Component - Comprehensive Enhancement
 * Professional case management interface with advanced features
 */

import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  Paper,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Chip,
  Button,
  CircularProgress,
  Alert,
  TextField,
  InputAdornment,
  IconButton,
  Menu,
  MenuItem,
  Checkbox,
  FormControl,
  InputLabel,
  Select,
  Grid,
  Card,
  CardContent,
  Avatar,
  Tooltip,
  Badge,
  LinearProgress,
  Fab,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TablePagination,
  TableSortLabel,
  Toolbar,
  alpha,
  useTheme,
} from '@mui/material';
import {
  Add,
  Visibility,
  Search,
  FilterList,
  MoreVert,
  Edit,
  Delete,
  Assignment,
  CheckCircle,
  Warning,
  Schedule,
  AttachFile,
  Comment,
  Person,
  Business,
  CalendarToday,
  TrendingUp,
  GetApp,
  Print,
  Share,
  Refresh,
  ViewModule,
  ViewList,
  Sort,
  ArrowUp<PERSON>,
  ArrowDownward,
} from '@mui/icons-material';
import { useNavigate } from 'react-router-dom';
import { useCases } from '../../hooks/api';
import { statusColors, priorityColors } from '../../theme';

// Enhanced interfaces
interface CaseFilters {
  search: string;
  status: string;
  priority: string;
  clinic: string;
  technician: string;
  dateRange: {
    start: string;
    end: string;
  };
  overdue: boolean;
}

interface SortConfig {
  key: string;
  direction: 'asc' | 'desc';
}

type ViewMode = 'table' | 'cards';

function CasesList() {
  const navigate = useNavigate();
  const theme = useTheme();
  const { data: cases, isLoading, error } = useCases();

  console.log('CasesList component rendered', { cases, isLoading, error });
  console.log('Auth token exists:', !!localStorage.getItem('authToken'));

  // Enhanced state management
  const [filteredCases, setFilteredCases] = useState<any[]>([]);
  const [selectedCases, setSelectedCases] = useState<string[]>([]);
  const [viewMode, setViewMode] = useState<ViewMode>('table');
  const [page, setPage] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(25);
  const [sortConfig, setSortConfig] = useState<SortConfig>({ key: 'created_at', direction: 'desc' });

  const [filters, setFilters] = useState<CaseFilters>({
    search: '',
    status: 'all',
    priority: 'all',
    clinic: 'all',
    technician: 'all',
    dateRange: { start: '', end: '' },
    overdue: false,
  });

  // Menu states
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
  const [filterMenuAnchor, setFilterMenuAnchor] = useState<null | HTMLElement>(null);
  const [bulkActionAnchor, setBulkActionAnchor] = useState<null | HTMLElement>(null);

  // Dialog states
  const [bulkActionDialog, setBulkActionDialog] = useState(false);
  const [deleteConfirmDialog, setDeleteConfirmDialog] = useState(false);

  // Enhanced filtering and sorting logic
  useEffect(() => {
    if (!cases) return;

    let filtered = [...cases];

    // Apply search filter
    if (filters.search) {
      const searchTerm = filters.search.toLowerCase();
      filtered = filtered.filter(case_ =>
        case_.patient_name?.toLowerCase().includes(searchTerm) ||
        case_.id?.toLowerCase().includes(searchTerm) ||
        case_.tooth_number?.includes(searchTerm) ||
        case_.clinic?.name?.toLowerCase().includes(searchTerm) ||
        case_.service_type?.toLowerCase().includes(searchTerm)
      );
    }

    // Apply status filter
    if (filters.status !== 'all') {
      filtered = filtered.filter(case_ => case_.status === filters.status);
    }

    // Apply priority filter
    if (filters.priority !== 'all') {
      filtered = filtered.filter(case_ => case_.priority === filters.priority);
    }

    // Apply clinic filter
    if (filters.clinic !== 'all') {
      filtered = filtered.filter(case_ => case_.clinic?.id === filters.clinic);
    }

    // Apply technician filter
    if (filters.technician !== 'all') {
      filtered = filtered.filter(case_ => case_.assigned_technician_id === filters.technician);
    }

    // Apply overdue filter
    if (filters.overdue) {
      filtered = filtered.filter(case_ => case_.is_overdue);
    }

    // Apply date range filter
    if (filters.dateRange.start && filters.dateRange.end) {
      const startDate = new Date(filters.dateRange.start);
      const endDate = new Date(filters.dateRange.end);
      filtered = filtered.filter(case_ => {
        const caseDate = new Date(case_.created_at);
        return caseDate >= startDate && caseDate <= endDate;
      });
    }

    // Apply sorting
    filtered.sort((a, b) => {
      const aValue = (a as any)[sortConfig.key];
      const bValue = (b as any)[sortConfig.key];

      if (aValue < bValue) {
        return sortConfig.direction === 'asc' ? -1 : 1;
      }
      if (aValue > bValue) {
        return sortConfig.direction === 'asc' ? 1 : -1;
      }
      return 0;
    });

    setFilteredCases(filtered);
  }, [cases, filters, sortConfig]);

  // Handler functions
  const handleSort = (key: string) => {
    setSortConfig(prev => ({
      key,
      direction: prev.key === key && prev.direction === 'asc' ? 'desc' : 'asc'
    }));
  };

  const handleSelectAll = (event: React.ChangeEvent<HTMLInputElement>) => {
    if (event.target.checked) {
      const newSelected = filteredCases.map(case_ => case_.id);
      setSelectedCases(newSelected);
    } else {
      setSelectedCases([]);
    }
  };

  const handleSelectCase = (caseId: string) => {
    const selectedIndex = selectedCases.indexOf(caseId);
    let newSelected: string[] = [];

    if (selectedIndex === -1) {
      newSelected = newSelected.concat(selectedCases, caseId);
    } else if (selectedIndex === 0) {
      newSelected = newSelected.concat(selectedCases.slice(1));
    } else if (selectedIndex === selectedCases.length - 1) {
      newSelected = newSelected.concat(selectedCases.slice(0, -1));
    } else if (selectedIndex > 0) {
      newSelected = newSelected.concat(
        selectedCases.slice(0, selectedIndex),
        selectedCases.slice(selectedIndex + 1),
      );
    }

    setSelectedCases(newSelected);
  };

  const handleFilterChange = (key: keyof CaseFilters, value: any) => {
    setFilters(prev => ({ ...prev, [key]: value }));
    setPage(0); // Reset to first page when filtering
  };

  const clearFilters = () => {
    setFilters({
      search: '',
      status: 'all',
      priority: 'all',
      clinic: 'all',
      technician: 'all',
      dateRange: { start: '', end: '' },
      overdue: false,
    });
  };

  // Utility functions
  const getStatusIcon = (status: string) => {
    const icons: Record<string, React.ReactNode> = {
      'received': <Schedule color="primary" />,
      'design': <Edit color="warning" />,
      'milling': <Assignment color="info" />,
      'sintering': <Warning color="error" />,
      'qc': <CheckCircle color="success" />,
      'shipped': <TrendingUp color="success" />,
      'delivered': <CheckCircle color="success" />,
      'cancelled': <Warning color="disabled" />,
    };
    return icons[status] || <Schedule />;
  };

  const getPriorityIcon = (priority: string) => {
    const icons: Record<string, React.ReactNode> = {
      'low': <ArrowDownward color="success" />,
      'normal': <ArrowUpward color="primary" />,
      'urgent': <Warning color="warning" />,
      'stat': <Warning color="error" />,
    };
    return icons[priority] || <ArrowUpward />;
  };

  const formatDate = (dateString: string) => {
    if (!dateString) return 'N/A';
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  const getDaysUntilDue = (dueDateString: string) => {
    if (!dueDateString) return null;
    const dueDate = new Date(dueDateString);
    const today = new Date();
    const diffTime = dueDate.getTime() - today.getTime();
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    return diffDays;
  };

  // Enhanced loading and error states
  if (isLoading) {
    return (
      <Box sx={{ p: 3 }}>
        <Box display="flex" justifyContent="space-between" alignItems="center" mb={3}>
          <Typography variant="h4" fontWeight="bold">
            Cases Management
          </Typography>
          <Button variant="contained" disabled startIcon={<Add />}>
            New Case
          </Button>
        </Box>
        <Paper sx={{ p: 4, textAlign: 'center' }}>
          <CircularProgress size={60} />
          <Typography variant="h6" sx={{ mt: 2, color: 'text.secondary' }}>
            Loading cases...
          </Typography>
        </Paper>
      </Box>
    );
  }

  if (error) {
    return (
      <Box sx={{ p: 3 }}>
        <Alert
          severity="error"
          action={
            <Box>
              <Button
                color="inherit"
                size="small"
                onClick={() => {
                  localStorage.removeItem('authToken');
                  navigate('/login');
                }}
                sx={{ mr: 1 }}
              >
                Login Again
              </Button>
              <Button color="inherit" size="small" onClick={() => window.location.reload()}>
                Retry
              </Button>
            </Box>
          }
        >
          Failed to load cases. This might be due to an authentication issue. Please try logging in again.
        </Alert>
      </Box>
    );
  }

  return (
    <Box sx={{ p: 3, backgroundColor: '#f5f5f5', minHeight: '100vh' }}>
      {/* Enhanced Header */}
      <Box sx={{ mb: 4 }}>
        <Box display="flex" justifyContent="space-between" alignItems="center" mb={3}>
          <Box>
            <Typography variant="h3" component="h1" sx={{
              color: 'primary.main',
              fontWeight: 'bold',
              display: 'flex',
              alignItems: 'center',
              gap: 1
            }}>
              📋 Cases Management
            </Typography>
            <Typography variant="h6" color="text.secondary" sx={{ mt: 1 }}>
              Manage all dental laboratory cases and workflows
            </Typography>
          </Box>
          <Box display="flex" alignItems="center" gap={2}>
            <Tooltip title="Refresh Cases">
              <IconButton onClick={() => window.location.reload()} color="primary">
                <Refresh />
              </IconButton>
            </Tooltip>
            <Button
              variant="contained"
              startIcon={<Add />}
              onClick={() => navigate('/cases/new')}
              size="large"
            >
              New Case
            </Button>
          </Box>
        </Box>

        {/* Statistics Cards */}
        <Grid container spacing={3} sx={{ mb: 3 }}>
          <Grid item xs={12} sm={6} md={3}>
            <Card>
              <CardContent>
                <Box display="flex" alignItems="center" justifyContent="space-between">
                  <Box>
                    <Typography variant="h4" sx={{ fontWeight: 'bold', color: 'primary.main' }}>
                      {filteredCases.length}
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      Total Cases
                    </Typography>
                  </Box>
                  <Avatar sx={{ bgcolor: 'primary.main' }}>
                    <Assignment />
                  </Avatar>
                </Box>
              </CardContent>
            </Card>
          </Grid>
          <Grid item xs={12} sm={6} md={3}>
            <Card>
              <CardContent>
                <Box display="flex" alignItems="center" justifyContent="space-between">
                  <Box>
                    <Typography variant="h4" sx={{ fontWeight: 'bold', color: 'warning.main' }}>
                      {filteredCases.filter(c => c.is_overdue).length}
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      Overdue Cases
                    </Typography>
                  </Box>
                  <Avatar sx={{ bgcolor: 'warning.main' }}>
                    <Warning />
                  </Avatar>
                </Box>
              </CardContent>
            </Card>
          </Grid>
          <Grid item xs={12} sm={6} md={3}>
            <Card>
              <CardContent>
                <Box display="flex" alignItems="center" justifyContent="space-between">
                  <Box>
                    <Typography variant="h4" sx={{ fontWeight: 'bold', color: 'success.main' }}>
                      {filteredCases.filter(c => c.status === 'delivered').length}
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      Completed
                    </Typography>
                  </Box>
                  <Avatar sx={{ bgcolor: 'success.main' }}>
                    <CheckCircle />
                  </Avatar>
                </Box>
              </CardContent>
            </Card>
          </Grid>
          <Grid item xs={12} sm={6} md={3}>
            <Card>
              <CardContent>
                <Box display="flex" alignItems="center" justifyContent="space-between">
                  <Box>
                    <Typography variant="h4" sx={{ fontWeight: 'bold', color: 'info.main' }}>
                      {selectedCases.length}
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      Selected
                    </Typography>
                  </Box>
                  <Avatar sx={{ bgcolor: 'info.main' }}>
                    <CheckCircle />
                  </Avatar>
                </Box>
              </CardContent>
            </Card>
          </Grid>
        </Grid>

        {/* Enhanced Filters */}
        <Paper sx={{ p: 3, mb: 3 }}>
          <Grid container spacing={2} alignItems="center">
            <Grid item xs={12} md={4}>
              <TextField
                fullWidth
                placeholder="Search cases, patients, clinics..."
                value={filters.search}
                onChange={(e) => handleFilterChange('search', e.target.value)}
                InputProps={{
                  startAdornment: (
                    <InputAdornment position="start">
                      <Search />
                    </InputAdornment>
                  ),
                }}
              />
            </Grid>
            <Grid item xs={12} sm={6} md={2}>
              <FormControl fullWidth>
                <InputLabel>Status</InputLabel>
                <Select
                  value={filters.status}
                  label="Status"
                  onChange={(e) => handleFilterChange('status', e.target.value)}
                >
                  <MenuItem value="all">All Statuses</MenuItem>
                  <MenuItem value="received">Received</MenuItem>
                  <MenuItem value="design">Design</MenuItem>
                  <MenuItem value="milling">Milling</MenuItem>
                  <MenuItem value="sintering">Sintering</MenuItem>
                  <MenuItem value="qc">Quality Control</MenuItem>
                  <MenuItem value="shipped">Shipped</MenuItem>
                  <MenuItem value="delivered">Delivered</MenuItem>
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={12} sm={6} md={2}>
              <FormControl fullWidth>
                <InputLabel>Priority</InputLabel>
                <Select
                  value={filters.priority}
                  label="Priority"
                  onChange={(e) => handleFilterChange('priority', e.target.value)}
                >
                  <MenuItem value="all">All Priorities</MenuItem>
                  <MenuItem value="low">Low</MenuItem>
                  <MenuItem value="normal">Normal</MenuItem>
                  <MenuItem value="urgent">Urgent</MenuItem>
                  <MenuItem value="stat">STAT</MenuItem>
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={12} sm={6} md={2}>
              <Button
                variant="outlined"
                onClick={clearFilters}
                fullWidth
                startIcon={<FilterList />}
              >
                Clear Filters
              </Button>
            </Grid>
            <Grid item xs={12} sm={6} md={2}>
              <Box display="flex" gap={1}>
                <Tooltip title="Table View">
                  <IconButton
                    onClick={() => setViewMode('table')}
                    color={viewMode === 'table' ? 'primary' : 'default'}
                  >
                    <ViewList />
                  </IconButton>
                </Tooltip>
                <Tooltip title="Card View">
                  <IconButton
                    onClick={() => setViewMode('cards')}
                    color={viewMode === 'cards' ? 'primary' : 'default'}
                  >
                    <ViewModule />
                  </IconButton>
                </Tooltip>
              </Box>
            </Grid>
          </Grid>
        </Paper>
      </Box>

      {/* Enhanced Table View */}
      {viewMode === 'table' && (
        <Paper>
          {/* Bulk Actions Toolbar */}
          {selectedCases.length > 0 && (
            <Toolbar
              sx={{
                pl: { sm: 2 },
                pr: { xs: 1, sm: 1 },
                bgcolor: alpha(theme.palette.primary.main, 0.12),
              }}
            >
              <Typography
                sx={{ flex: '1 1 100%' }}
                color="inherit"
                variant="subtitle1"
                component="div"
              >
                {selectedCases.length} selected
              </Typography>
              <Tooltip title="Bulk Actions">
                <IconButton onClick={(e) => setBulkActionAnchor(e.currentTarget)}>
                  <MoreVert />
                </IconButton>
              </Tooltip>
            </Toolbar>
          )}

          <TableContainer>
            <Table>
              <TableHead>
                <TableRow>
                  <TableCell padding="checkbox">
                    <Checkbox
                      color="primary"
                      indeterminate={selectedCases.length > 0 && selectedCases.length < filteredCases.length}
                      checked={filteredCases.length > 0 && selectedCases.length === filteredCases.length}
                      onChange={handleSelectAll}
                    />
                  </TableCell>
                  <TableCell>
                    <TableSortLabel
                      active={sortConfig.key === 'id'}
                      direction={sortConfig.key === 'id' ? sortConfig.direction : 'asc'}
                      onClick={() => handleSort('id')}
                    >
                      Case ID
                    </TableSortLabel>
                  </TableCell>
                  <TableCell>
                    <TableSortLabel
                      active={sortConfig.key === 'patient_name'}
                      direction={sortConfig.key === 'patient_name' ? sortConfig.direction : 'asc'}
                      onClick={() => handleSort('patient_name')}
                    >
                      Patient
                    </TableSortLabel>
                  </TableCell>
                  <TableCell>Service Type</TableCell>
                  <TableCell>
                    <TableSortLabel
                      active={sortConfig.key === 'priority'}
                      direction={sortConfig.key === 'priority' ? sortConfig.direction : 'asc'}
                      onClick={() => handleSort('priority')}
                    >
                      Priority
                    </TableSortLabel>
                  </TableCell>
                  <TableCell>
                    <TableSortLabel
                      active={sortConfig.key === 'status'}
                      direction={sortConfig.key === 'status' ? sortConfig.direction : 'asc'}
                      onClick={() => handleSort('status')}
                    >
                      Status
                    </TableSortLabel>
                  </TableCell>
                  <TableCell>
                    <TableSortLabel
                      active={sortConfig.key === 'due_date'}
                      direction={sortConfig.key === 'due_date' ? sortConfig.direction : 'asc'}
                      onClick={() => handleSort('due_date')}
                    >
                      Due Date
                    </TableSortLabel>
                  </TableCell>
                  <TableCell>Technician</TableCell>
                  <TableCell>Progress</TableCell>
                  <TableCell>Actions</TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {filteredCases
                  .slice(page * rowsPerPage, page * rowsPerPage + rowsPerPage)
                  .map((case_) => {
                    const isSelected = selectedCases.indexOf(case_.id) !== -1;
                    const daysUntilDue = getDaysUntilDue(case_.due_date);

                    return (
                      <TableRow
                        key={case_.id}
                        hover
                        onClick={() => handleSelectCase(case_.id)}
                        role="checkbox"
                        selected={isSelected}
                        sx={{ cursor: 'pointer' }}
                      >
                        <TableCell padding="checkbox">
                          <Checkbox
                            color="primary"
                            checked={isSelected}
                            onChange={() => handleSelectCase(case_.id)}
                          />
                        </TableCell>
                        <TableCell>
                          <Box display="flex" alignItems="center" gap={1}>
                            <Typography variant="body2" fontWeight="bold">
                              #{case_.id}
                            </Typography>
                            {case_.is_overdue && (
                              <Chip label="OVERDUE" size="small" color="error" />
                            )}
                          </Box>
                        </TableCell>
                        <TableCell>
                          <Box>
                            <Typography variant="body2" fontWeight="bold">
                              {case_.patient_name}
                            </Typography>
                            <Typography variant="caption" color="text.secondary">
                              {case_.clinic?.name}
                            </Typography>
                          </Box>
                        </TableCell>
                        <TableCell>
                          <Chip
                            label={case_.service_type}
                            size="small"
                            variant="outlined"
                            icon={<Business />}
                          />
                        </TableCell>
                        <TableCell>
                          <Chip
                            label={case_.priority}
                            size="small"
                            sx={{
                              bgcolor: priorityColors[case_.priority as keyof typeof priorityColors],
                              color: 'white',
                            }}
                          />
                        </TableCell>
                        <TableCell>
                          <Chip
                            label={case_.current_stage?.name || case_.status}
                            size="small"
                            sx={{
                              bgcolor: statusColors[case_.status as keyof typeof statusColors],
                              color: 'white',
                            }}
                          />
                        </TableCell>
                        <TableCell>
                          <Box>
                            <Typography
                              variant="body2"
                              color={case_.is_overdue ? 'error.main' : 'text.primary'}
                              fontWeight={case_.is_overdue ? 'bold' : 'normal'}
                            >
                              {formatDate(case_.due_date)}
                            </Typography>
                            {daysUntilDue !== null && (
                              <Typography
                                variant="caption"
                                color={daysUntilDue < 0 ? 'error.main' : daysUntilDue <= 2 ? 'warning.main' : 'text.secondary'}
                              >
                                {daysUntilDue < 0
                                  ? `${Math.abs(daysUntilDue)} days overdue`
                                  : daysUntilDue === 0
                                    ? 'Due today'
                                    : `${daysUntilDue} days left`
                                }
                              </Typography>
                            )}
                          </Box>
                        </TableCell>
                        <TableCell>
                          {case_.assigned_technician_id ? (
                            <Box display="flex" alignItems="center" gap={1}>
                              <Avatar sx={{ width: 24, height: 24, fontSize: '0.75rem' }}>
                                {case_.assigned_technician_id.charAt(0)}
                              </Avatar>
                              <Typography variant="caption">
                                {case_.assigned_technician_id}
                              </Typography>
                            </Box>
                          ) : (
                            <Chip label="Unassigned" size="small" variant="outlined" />
                          )}
                        </TableCell>
                        <TableCell>
                          <Box sx={{ width: '100%' }}>
                            <LinearProgress
                              variant="determinate"
                              value={case_.progress_percentage || 0}
                              sx={{ mb: 0.5 }}
                            />
                            <Typography variant="caption" color="text.secondary">
                              {case_.progress_percentage || 0}%
                            </Typography>
                          </Box>
                        </TableCell>
                        <TableCell>
                          <Box display="flex" gap={0.5}>
                            <Tooltip title="View Details">
                              <IconButton
                                size="small"
                                onClick={(e) => {
                                  e.stopPropagation();
                                  navigate(`/cases/${case_.id}`);
                                }}
                              >
                                <Visibility />
                              </IconButton>
                            </Tooltip>
                            <Tooltip title="More Actions">
                              <IconButton
                                size="small"
                                onClick={(e) => {
                                  e.stopPropagation();
                                  setAnchorEl(e.currentTarget);
                                }}
                              >
                                <MoreVert />
                              </IconButton>
                            </Tooltip>
                          </Box>
                        </TableCell>
                      </TableRow>
                    );
                  })}
              </TableBody>
            </Table>
          </TableContainer>

          {/* Pagination */}
          <TablePagination
            rowsPerPageOptions={[10, 25, 50, 100]}
            component="div"
            count={filteredCases.length}
            rowsPerPage={rowsPerPage}
            page={page}
            onPageChange={(_, newPage) => setPage(newPage)}
            onRowsPerPageChange={(e) => {
              setRowsPerPage(parseInt(e.target.value, 10));
              setPage(0);
            }}
          />
        </Paper>
      )}

      {/* Enhanced Card View */}
      {viewMode === 'cards' && (
        <Grid container spacing={3}>
          {filteredCases
            .slice(page * rowsPerPage, page * rowsPerPage + rowsPerPage)
            .map((case_) => {
              const isSelected = selectedCases.indexOf(case_.id) !== -1;
              const daysUntilDue = getDaysUntilDue(case_.due_date);

              return (
                <Grid item xs={12} sm={6} md={4} lg={3} key={case_.id}>
                  <Card
                    sx={{
                      height: '100%',
                      cursor: 'pointer',
                      border: isSelected ? 2 : 1,
                      borderColor: isSelected ? 'primary.main' : 'divider',
                      '&:hover': {
                        boxShadow: 4,
                        transform: 'translateY(-2px)',
                        transition: 'all 0.2s ease-in-out'
                      }
                    }}
                    onClick={() => handleSelectCase(case_.id)}
                  >
                    <CardContent>
                      {/* Card Header */}
                      <Box display="flex" justifyContent="space-between" alignItems="flex-start" mb={2}>
                        <Box>
                          <Typography variant="h6" fontWeight="bold">
                            #{case_.id}
                          </Typography>
                          <Typography variant="body2" color="text.secondary">
                            {case_.patient_name}
                          </Typography>
                        </Box>
                        <Box display="flex" alignItems="center" gap={0.5}>
                          <Checkbox
                            size="small"
                            checked={isSelected}
                            onChange={() => handleSelectCase(case_.id)}
                          />
                          <IconButton
                            size="small"
                            onClick={(e) => {
                              e.stopPropagation();
                              setAnchorEl(e.currentTarget);
                            }}
                          >
                            <MoreVert />
                          </IconButton>
                        </Box>
                      </Box>

                      {/* Service Type and Priority */}
                      <Box display="flex" gap={1} mb={2}>
                        <Chip
                          label={case_.service_type}
                          size="small"
                          variant="outlined"
                        />
                        <Chip
                          label={case_.priority}
                          size="small"
                          sx={{
                            bgcolor: priorityColors[case_.priority as keyof typeof priorityColors],
                            color: 'white',
                          }}
                        />
                      </Box>

                      {/* Status */}
                      <Box mb={2}>
                        <Chip
                          label={case_.current_stage?.name || case_.status}
                          sx={{
                            bgcolor: statusColors[case_.status as keyof typeof statusColors],
                            color: 'white',
                            width: '100%',
                            justifyContent: 'flex-start'
                          }}
                        />
                      </Box>

                      {/* Progress */}
                      <Box mb={2}>
                        <Box display="flex" justifyContent="space-between" alignItems="center" mb={0.5}>
                          <Typography variant="caption" color="text.secondary">
                            Progress
                          </Typography>
                          <Typography variant="caption" color="text.secondary">
                            {case_.progress_percentage || 0}%
                          </Typography>
                        </Box>
                        <LinearProgress
                          variant="determinate"
                          value={case_.progress_percentage || 0}
                        />
                      </Box>

                      {/* Due Date */}
                      <Box display="flex" alignItems="center" gap={1} mb={2}>
                        <CalendarToday sx={{ fontSize: 16, color: 'text.secondary' }} />
                        <Typography
                          variant="body2"
                          color={case_.is_overdue ? 'error.main' : 'text.primary'}
                        >
                          {formatDate(case_.due_date)}
                        </Typography>
                        {case_.is_overdue && (
                          <Chip label="OVERDUE" size="small" color="error" />
                        )}
                      </Box>

                      {/* Technician */}
                      <Box display="flex" alignItems="center" gap={1} mb={2}>
                        <Person sx={{ fontSize: 16, color: 'text.secondary' }} />
                        {case_.assigned_technician_id ? (
                          <Typography variant="body2">
                            {case_.assigned_technician_id}
                          </Typography>
                        ) : (
                          <Typography variant="body2" color="text.secondary">
                            Unassigned
                          </Typography>
                        )}
                      </Box>

                      {/* Actions */}
                      <Box display="flex" gap={1} mt={2}>
                        <Button
                          size="small"
                          variant="outlined"
                          startIcon={<Visibility />}
                          onClick={(e) => {
                            e.stopPropagation();
                            navigate(`/cases/${case_.id}`);
                          }}
                          fullWidth
                        >
                          View
                        </Button>
                      </Box>
                    </CardContent>
                  </Card>
                </Grid>
              );
            })}
        </Grid>
      )}

      {/* Pagination for Card View */}
      {viewMode === 'cards' && (
        <Box display="flex" justifyContent="center" mt={4}>
          <TablePagination
            rowsPerPageOptions={[12, 24, 48]}
            component="div"
            count={filteredCases.length}
            rowsPerPage={rowsPerPage}
            page={page}
            onPageChange={(_, newPage) => setPage(newPage)}
            onRowsPerPageChange={(e) => {
              setRowsPerPage(parseInt(e.target.value, 10));
              setPage(0);
            }}
          />
        </Box>
      )}

      {/* Context Menus */}
      <Menu
        anchorEl={anchorEl}
        open={Boolean(anchorEl)}
        onClose={() => setAnchorEl(null)}
      >
        <MenuItem onClick={() => setAnchorEl(null)}>
          <Edit sx={{ mr: 1 }} /> Edit Case
        </MenuItem>
        <MenuItem onClick={() => setAnchorEl(null)}>
          <Assignment sx={{ mr: 1 }} /> Assign Technician
        </MenuItem>
        <MenuItem onClick={() => setAnchorEl(null)}>
          <AttachFile sx={{ mr: 1 }} /> View Files
        </MenuItem>
        <MenuItem onClick={() => setAnchorEl(null)}>
          <Comment sx={{ mr: 1 }} /> Add Comment
        </MenuItem>
        <MenuItem onClick={() => setAnchorEl(null)} sx={{ color: 'error.main' }}>
          <Delete sx={{ mr: 1 }} /> Delete Case
        </MenuItem>
      </Menu>

      <Menu
        anchorEl={bulkActionAnchor}
        open={Boolean(bulkActionAnchor)}
        onClose={() => setBulkActionAnchor(null)}
      >
        <MenuItem onClick={() => setBulkActionAnchor(null)}>
          <Assignment sx={{ mr: 1 }} /> Bulk Assign
        </MenuItem>
        <MenuItem onClick={() => setBulkActionAnchor(null)}>
          <CheckCircle sx={{ mr: 1 }} /> Update Status
        </MenuItem>
        <MenuItem onClick={() => setBulkActionAnchor(null)}>
          <GetApp sx={{ mr: 1 }} /> Export Selected
        </MenuItem>
        <MenuItem onClick={() => setBulkActionAnchor(null)} sx={{ color: 'error.main' }}>
          <Delete sx={{ mr: 1 }} /> Delete Selected
        </MenuItem>
      </Menu>

      {/* Floating Action Button */}
      <Fab
        color="primary"
        aria-label="add"
        sx={{
          position: 'fixed',
          bottom: 16,
          right: 16,
        }}
        onClick={() => navigate('/cases/new')}
      >
        <Add />
      </Fab>
    </Box>
  );
}

export default CasesList;
