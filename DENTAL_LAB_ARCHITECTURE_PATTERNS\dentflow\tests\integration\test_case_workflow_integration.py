"""
DentFlow Integration Tests - Complete Case Lifecycle
Tests the entire workflow from case creation to delivery
"""

import pytest
from django.test import TransactionTestCase
from django.urls import reverse
from rest_framework.test import APIClient
from rest_framework import status
from datetime import datetime, timedelta
from unittest.mock import patch, MagicMock

from backend.domain.model import Case, CaseId, ToothNumber, Priority, CaseStatus
from backend.domain.commands import CreateCase, AdvanceCase, AssignTechnician
from backend.services.handlers import MessageBus
from backend.services.unit_of_work import SqlAlchemyUnitOfWork
from backend.apps.cases.models import CaseModel, TechnicianModel, ClinicModel


class TestCaseWorkflowIntegration(TransactionTestCase):
    """
    Integration tests covering the complete case management workflow
    Tests both API endpoints and business logic integration
    """
    
    def setUp(self):
        """Set up test data and clients"""
        self.client = APIClient()
        self.messagebus = MessageBus()
        
        # Create test data
        self.clinic = ClinicModel.objects.create(
            name="Test Dental Clinic",
            email="<EMAIL>",
            phone="555-0123"
        )
        
        self.technician = TechnicianModel.objects.create(
            name="<PERSON>",
            department="Design",
            skill_level="Senior",
            email="<EMAIL>"
        )
        
        # Authenticate client
        self._authenticate_client()
    
    def _authenticate_client(self):
        """Authenticate API client for testing"""
        from django.contrib.auth.models import User
        from rest_framework_simplejwt.tokens import RefreshToken
        
        user = User.objects.create_user(
            username='testuser',
            email='<EMAIL>',
            password='testpass123'
        )
        
        refresh = RefreshToken.for_user(user)
        self.client.credentials(HTTP_AUTHORIZATION=f'Bearer {refresh.access_token}')
    
    def test_complete_case_lifecycle_via_api(self):
        """Test complete case workflow through API endpoints"""
        
        # 1. Create new case via API
        case_data = {
            'clinic_id': str(self.clinic.id),
            'patient_name': 'Jane Doe',
            'tooth_number': '14',
            'service_type': 'Crown',
            'priority': 'normal',
            'due_date': (datetime.now() + timedelta(days=7)).isoformat(),
            'notes': 'Patient prefers ceramic material'
        }
        
        response = self.client.post(
            reverse('api:cases-list'),
            data=case_data,
            format='json'
        )
        
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        case_id = response.data['case_id']
        
        # Verify case was created correctly
        self.assertEqual(response.data['patient_name'], 'Jane Doe')
        self.assertEqual(response.data['status'], 'received')
        self.assertEqual(response.data['tooth_number'], '14')
        
        # 2. Assign workflow template
        workflow_data = {
            'template_id': 'standard_crown_workflow',
            'estimated_completion': 5  # days
        }
        
        response = self.client.post(
            reverse('api:cases-assign-workflow', kwargs={'pk': case_id}),
            data=workflow_data,
            format='json'
        )
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        
        # 3. Assign technician to current stage
        assignment_data = {
            'technician_id': str(self.technician.id),
            'stage': 'design'
        }
        
        response = self.client.post(
            reverse('api:cases-assign-technician', kwargs={'pk': case_id}),
            data=assignment_data,
            format='json'
        )
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        
        # 4. Advance case through workflow stages
        stages = ['design', 'milling', 'sintering', 'qc']
        
        for stage in stages:
            advance_data = {
                'notes': f'Completed {stage} stage successfully',
                'quality_check_passed': True
            }
            
            response = self.client.post(
                reverse('api:cases-advance', kwargs={'pk': case_id}),
                data=advance_data,
                format='json'
            )
            
            self.assertEqual(response.status_code, status.HTTP_200_OK)
            
            # Verify status change
            updated_case = self.client.get(
                reverse('api:cases-detail', kwargs={'pk': case_id})
            )
            
            if stage != 'qc':  # QC is the final stage before shipped
                expected_status = stages[stages.index(stage) + 1] if stage != stages[-1] else 'qc'
            else:
                expected_status = 'qc'
                
        # 5. Final quality approval and shipping
        shipping_data = {
            'quality_approved': True,
            'shipping_method': 'courier',
            'tracking_number': 'TRK123456789'
        }
        
        response = self.client.post(
            reverse('api:cases-ship', kwargs={'pk': case_id}),
            data=shipping_data,
            format='json'
        )
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        
        # Verify final status
        final_case = self.client.get(
            reverse('api:cases-detail', kwargs={'pk': case_id})
        )
        
        self.assertEqual(final_case.data['status'], 'shipped')
        self.assertEqual(final_case.data['tracking_number'], 'TRK123456789')
    
    @patch('backend.services.email_service.EmailService.send_notification')
    def test_case_workflow_with_notifications(self, mock_email):
        """Test that appropriate notifications are sent during workflow"""
        
        # Create case
        case_data = {
            'clinic_id': str(self.clinic.id),
            'patient_name': 'John Doe',
            'tooth_number': '21',
            'service_type': 'Bridge',
            'priority': 'urgent'
        }
        
        response = self.client.post(
            reverse('api:cases-list'),
            data=case_data,
            format='json'
        )
        
        case_id = response.data['case_id']
        
        # Verify creation notification was sent
        mock_email.assert_called()
        call_args = mock_email.call_args
        self.assertIn('case_created', call_args[0][0])
        
        # Reset mock for next test
        mock_email.reset_mock()
        
        # Advance case and verify status change notification
        self.client.post(
            reverse('api:cases-advance', kwargs={'pk': case_id}),
            data={'notes': 'Moving to milling stage'},
            format='json'
        )
        
        # Verify status change notification
        mock_email.assert_called()
        call_args = mock_email.call_args
        self.assertIn('status_changed', call_args[0][0])
    
    def test_case_workflow_error_handling(self):
        """Test error handling in case workflow"""
        
        # Try to advance non-existent case
        response = self.client.post(
            reverse('api:cases-advance', kwargs={'pk': 'nonexistent'}),
            data={'notes': 'Test note'},
            format='json'
        )
        
        self.assertEqual(response.status_code, status.HTTP_404_NOT_FOUND)
        
        # Create case for further testing
        case_data = {
            'clinic_id': str(self.clinic.id),
            'patient_name': 'Error Test',
            'tooth_number': '11',
            'service_type': 'Veneer',
            'priority': 'normal'
        }
        
        response = self.client.post(
            reverse('api:cases-list'),
            data=case_data,
            format='json'
        )
        
        case_id = response.data['case_id']
        
        # Try to assign non-existent technician
        response = self.client.post(
            reverse('api:cases-assign-technician', kwargs={'pk': case_id}),
            data={'technician_id': 'nonexistent'},
            format='json'
        )
        
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        
        # Try to advance case without proper progression
        # (e.g., skipping stages)
        response = self.client.post(
            reverse('api:cases-ship', kwargs={'pk': case_id}),
            data={'quality_approved': True},
            format='json'
        )
        
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertIn('Cannot ship case', response.data['error'])
    
    def test_concurrent_case_operations(self):
        """Test handling of concurrent operations on the same case"""
        from threading import Thread
        import time
        
        # Create case
        case_data = {
            'clinic_id': str(self.clinic.id),
            'patient_name': 'Concurrent Test',
            'tooth_number': '24',
            'service_type': 'Inlay',
            'priority': 'normal'
        }
        
        response = self.client.post(
            reverse('api:cases-list'),
            data=case_data,
            format='json'
        )
        
        case_id = response.data['case_id']
        
        # Define concurrent operations
        results = []
        
        def advance_case():
            client = APIClient()
            client.credentials(HTTP_AUTHORIZATION=self.client._credentials['HTTP_AUTHORIZATION'])
            response = client.post(
                reverse('api:cases-advance', kwargs={'pk': case_id}),
                data={'notes': 'Concurrent advance'},
                format='json'
            )
            results.append(response.status_code)
        
        def assign_technician():
            client = APIClient()
            client.credentials(HTTP_AUTHORIZATION=self.client._credentials['HTTP_AUTHORIZATION'])
            response = client.post(
                reverse('api:cases-assign-technician', kwargs={'pk': case_id}),
                data={'technician_id': str(self.technician.id)},
                format='json'
            )
            results.append(response.status_code)
        
        # Execute concurrent operations
        threads = [
            Thread(target=advance_case),
            Thread(target=assign_technician)
        ]
        
        for thread in threads:
            thread.start()
        
        for thread in threads:
            thread.join()
        
        # Verify that operations completed successfully
        # (exact behavior depends on business rules)
        self.assertTrue(any(code == 200 for code in results))
    
    def test_performance_benchmarks(self):
        """Test performance of critical operations"""
        import time
        
        # Benchmark case creation
        start_time = time.time()
        
        for i in range(10):
            case_data = {
                'clinic_id': str(self.clinic.id),
                'patient_name': f'Performance Test {i}',
                'tooth_number': '15',
                'service_type': 'Crown',
                'priority': 'normal'
            }
            
            response = self.client.post(
                reverse('api:cases-list'),
                data=case_data,
                format='json'
            )
            
            self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        
        elapsed_time = time.time() - start_time
        
        # Assert that 10 case creations take less than 2 seconds
        self.assertLess(elapsed_time, 2.0, 
                       f"Case creation too slow: {elapsed_time:.2f}s for 10 cases")
        
        # Benchmark case list retrieval
        start_time = time.time()
        
        response = self.client.get(reverse('api:cases-list'))
        
        elapsed_time = time.time() - start_time
        
        # Assert that case list loads in under 500ms
        self.assertLess(elapsed_time, 0.5,
                       f"Case list too slow: {elapsed_time:.2f}s")
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertGreaterEqual(len(response.data['results']), 10)