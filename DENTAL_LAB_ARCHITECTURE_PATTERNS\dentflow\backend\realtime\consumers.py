"""
DentFlow Real-time Communication System
WebSocket implementation for live collaboration and notifications
"""

import json
import asyncio
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional
from channels.generic.websocket import AsyncWebsocketConsumer
from channels.db import database_sync_to_async
from django.contrib.auth.models import User
from django.core.serializers.json import DjangoJSONEncoder
import structlog

logger = structlog.get_logger(__name__)


class DentFlowWebSocketConsumer(AsyncWebsocketConsumer):
    """
    Main WebSocket consumer for DentFlow real-time features
    Handles case updates, notifications, and collaboration
    """
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.tenant_id = None
        self.user_id = None
        self.user_groups = []
        self.room_groups = []
        
    async def connect(self):
        """Handle WebSocket connection"""
        
        # Authenticate user
        user = await self._get_user_from_scope()
        if not user or not user.is_authenticated:
            await self.close(code=4001)  # Unauthorized
            return
        
        self.user_id = user.id
        self.tenant_id = await self._get_user_tenant(user)
        
        if not self.tenant_id:
            await self.close(code=4003)  # Forbidden - no tenant
            return
        
        # Join tenant group
        self.tenant_group_name = f'tenant_{self.tenant_id}'
        await self.channel_layer.group_add(
            self.tenant_group_name,
            self.channel_name
        )
        
        # Join user-specific group
        self.user_group_name = f'user_{self.user_id}'
        await self.channel_layer.group_add(
            self.user_group_name,
            self.channel_name
        )
        
        # Join role-based groups
        user_roles = await self._get_user_roles(user)
        for role in user_roles:
            role_group_name = f'role_{role}_{self.tenant_id}'
            self.user_groups.append(role_group_name)
            await self.channel_layer.group_add(
                role_group_name,
                self.channel_name
            )
        
        await self.accept()
        
        # Send connection confirmation
        await self.send(text_data=json.dumps({
            'type': 'connection_established',
            'user_id': self.user_id,
            'tenant_id': self.tenant_id,
            'timestamp': datetime.utcnow().isoformat(),
            'server_time': datetime.utcnow().isoformat()
        }))
        
        # Send any pending notifications
        await self._send_pending_notifications()
        
        logger.info(
            "WebSocket connection established",
            user_id=self.user_id,
            tenant_id=self.tenant_id,
            channel_name=self.channel_name
        )
    
    async def disconnect(self, close_code):
        """Handle WebSocket disconnection"""
        
        # Leave all groups
        if hasattr(self, 'tenant_group_name'):
            await self.channel_layer.group_discard(
                self.tenant_group_name,
                self.channel_name
            )
        
        if hasattr(self, 'user_group_name'):
            await self.channel_layer.group_discard(
                self.user_group_name,
                self.channel_name
            )
        
        for group_name in self.user_groups:
            await self.channel_layer.group_discard(
                group_name,
                self.channel_name
            )
        
        for group_name in self.room_groups:
            await self.channel_layer.group_discard(
                group_name,
                self.channel_name
            )
        
        logger.info(
            "WebSocket connection closed",
            user_id=self.user_id,
            tenant_id=self.tenant_id,
            close_code=close_code
        )
    
    async def receive(self, text_data):
        """Handle incoming WebSocket messages"""
        
        try:
            data = json.loads(text_data)
            message_type = data.get('type')
            
            if message_type == 'ping':
                await self._handle_ping(data)
            elif message_type == 'subscribe_case':
                await self._handle_case_subscription(data)
            elif message_type == 'unsubscribe_case':
                await self._handle_case_unsubscription(data)
            elif message_type == 'case_comment':
                await self._handle_case_comment(data)
            elif message_type == 'technician_status':
                await self._handle_technician_status(data)
            elif message_type == 'request_notifications':
                await self._send_pending_notifications()
            else:
                await self.send(text_data=json.dumps({
                    'type': 'error',
                    'message': f'Unknown message type: {message_type}'
                }))
        
        except json.JSONDecodeError:
            await self.send(text_data=json.dumps({
                'type': 'error',
                'message': 'Invalid JSON format'
            }))
        except Exception as e:
            logger.error(
                "Error handling WebSocket message",
                error=str(e),
                user_id=self.user_id,
                message_data=text_data[:100]  # Truncate for privacy
            )
            await self.send(text_data=json.dumps({
                'type': 'error',
                'message': 'Internal server error'
            }))
    
    async def _handle_ping(self, data):
        """Handle ping messages for keepalive"""
        await self.send(text_data=json.dumps({
            'type': 'pong',
            'timestamp': datetime.utcnow().isoformat()
        }))
    
    async def _handle_case_subscription(self, data):
        """Subscribe to real-time updates for a specific case"""
        case_id = data.get('case_id')
        
        if not case_id:
            await self.send(text_data=json.dumps({
                'type': 'error',
                'message': 'case_id is required for subscription'
            }))
            return
        
        # Verify user has access to this case
        has_access = await self._verify_case_access(case_id)
        if not has_access:
            await self.send(text_data=json.dumps({
                'type': 'error',
                'message': 'Access denied for this case'
            }))
            return
        
        # Join case-specific group
        case_group_name = f'case_{case_id}'
        self.room_groups.append(case_group_name)
        await self.channel_layer.group_add(
            case_group_name,
            self.channel_name
        )
        
        await self.send(text_data=json.dumps({
            'type': 'case_subscribed',
            'case_id': case_id,
            'message': 'Successfully subscribed to case updates'
        }))
        
        logger.info(
            "User subscribed to case updates",
            user_id=self.user_id,
            case_id=case_id
        )
    
    async def _handle_case_unsubscription(self, data):
        """Unsubscribe from case updates"""
        case_id = data.get('case_id')
        
        if case_id:
            case_group_name = f'case_{case_id}'
            if case_group_name in self.room_groups:
                self.room_groups.remove(case_group_name)
                await self.channel_layer.group_discard(
                    case_group_name,
                    self.channel_name
                )
                
                await self.send(text_data=json.dumps({
                    'type': 'case_unsubscribed',
                    'case_id': case_id
                }))
    
    async def _handle_case_comment(self, data):
        """Handle real-time case comments"""
        case_id = data.get('case_id')
        comment = data.get('comment', '').strip()
        
        if not case_id or not comment:
            await self.send(text_data=json.dumps({
                'type': 'error',
                'message': 'case_id and comment are required'
            }))
            return
        
        # Verify access and save comment
        has_access = await self._verify_case_access(case_id)
        if not has_access:
            return
        
        # Save comment to database
        comment_data = await self._save_case_comment(case_id, comment)
        
        # Broadcast to all case subscribers
        case_group_name = f'case_{case_id}'
        await self.channel_layer.group_send(
            case_group_name,
            {
                'type': 'case_comment_notification',
                'case_id': case_id,
                'comment': comment_data
            }
        )
    
    async def _handle_technician_status(self, data):
        """Handle technician status updates (online/offline, current task)"""
        status = data.get('status', 'online')  # online, offline, busy
        current_task = data.get('current_task', '')
        
        # Update technician status in cache/database
        await self._update_technician_status(status, current_task)
        
        # Broadcast to tenant group
        await self.channel_layer.group_send(
            self.tenant_group_name,
            {
                'type': 'technician_status_update',
                'user_id': self.user_id,
                'status': status,
                'current_task': current_task,
                'timestamp': datetime.utcnow().isoformat()
            }
        )
    
    # Channel layer event handlers
    async def case_status_changed(self, event):
        """Handle case status change notifications"""
        await self.send(text_data=json.dumps({
            'type': 'case_status_changed',
            'case_id': event['case_id'],
            'old_status': event['old_status'],
            'new_status': event['new_status'],
            'changed_by': event['changed_by'],
            'timestamp': event['timestamp'],
            'message': f"Case {event['case_id']} status changed to {event['new_status']}"
        }))
    
    async def case_assigned(self, event):
        """Handle case assignment notifications"""
        await self.send(text_data=json.dumps({
            'type': 'case_assigned',
            'case_id': event['case_id'],
            'technician_id': event['technician_id'],
            'technician_name': event['technician_name'],
            'assigned_by': event['assigned_by'],
            'timestamp': event['timestamp'],
            'message': f"Case {event['case_id']} assigned to {event['technician_name']}"
        }))
    
    async def case_comment_notification(self, event):
        """Handle case comment notifications"""
        await self.send(text_data=json.dumps({
            'type': 'case_comment',
            'case_id': event['case_id'],
            'comment': event['comment']
        }))
    
    async def technician_status_update(self, event):
        """Handle technician status updates"""
        await self.send(text_data=json.dumps({
            'type': 'technician_status',
            'user_id': event['user_id'],
            'status': event['status'],
            'current_task': event['current_task'],
            'timestamp': event['timestamp']
        }))
    
    async def urgent_notification(self, event):
        """Handle urgent notifications (STAT orders, overdue cases, etc.)"""
        await self.send(text_data=json.dumps({
            'type': 'urgent_notification',
            'notification_id': event['notification_id'],
            'title': event['title'],
            'message': event['message'],
            'priority': event['priority'],
            'action_required': event.get('action_required', False),
            'action_url': event.get('action_url', ''),
            'timestamp': event['timestamp']
        }))
    
    async def system_announcement(self, event):
        """Handle system-wide announcements"""
        await self.send(text_data=json.dumps({
            'type': 'system_announcement',
            'title': event['title'],
            'message': event['message'],
            'severity': event['severity'],
            'timestamp': event['timestamp']
        }))
    
    # Helper methods
    @database_sync_to_async
    def _get_user_from_scope(self):
        """Get user from WebSocket scope"""
        return self.scope.get('user')
    
    @database_sync_to_async
    def _get_user_tenant(self, user):
        """Get user's tenant ID"""
        # Implement based on your tenant model
        try:
            profile = user.userprofile
            return profile.tenant_id
        except:
            return None
    
    @database_sync_to_async
    def _get_user_roles(self, user):
        """Get user's roles for group membership"""
        # Implement based on your role system
        roles = []
        if user.groups.filter(name='technicians').exists():
            roles.append('technician')
        if user.groups.filter(name='managers').exists():
            roles.append('manager')
        if user.groups.filter(name='admins').exists():
            roles.append('admin')
        return roles
    
    @database_sync_to_async
    def _verify_case_access(self, case_id):
        """Verify user has access to specific case"""
        try:
            from backend.apps.cases.models import CaseModel
            case = CaseModel.objects.get(id=case_id, tenant_id=self.tenant_id)
            return True
        except CaseModel.DoesNotExist:
            return False
    
    @database_sync_to_async
    def _save_case_comment(self, case_id, comment):
        """Save case comment to database"""
        try:
            from backend.apps.cases.models import CaseComment
            from django.contrib.auth.models import User
            
            user = User.objects.get(id=self.user_id)
            
            comment_obj = CaseComment.objects.create(
                case_id=case_id,
                user=user,
                comment=comment,
                timestamp=datetime.utcnow()
            )
            
            return {
                'id': comment_obj.id,
                'user_id': user.id,
                'user_name': f"{user.first_name} {user.last_name}".strip() or user.username,
                'comment': comment,
                'timestamp': comment_obj.timestamp.isoformat()
            }
        except Exception as e:
            logger.error("Failed to save case comment", error=str(e))
            return None
    
    @database_sync_to_async
    def _update_technician_status(self, status, current_task):
        """Update technician online status"""
        try:
            from django.core.cache import cache
            
            cache_key = f"technician_status:{self.user_id}"
            status_data = {
                'status': status,
                'current_task': current_task,
                'last_seen': datetime.utcnow().isoformat(),
                'tenant_id': self.tenant_id
            }
            
            # Cache for 5 minutes
            cache.set(cache_key, status_data, 300)
            
        except Exception as e:
            logger.error("Failed to update technician status", error=str(e))
    
    async def _send_pending_notifications(self):
        """Send any pending notifications for the user"""
        try:
            notifications = await self._get_pending_notifications()
            
            for notification in notifications:
                await self.send(text_data=json.dumps({
                    'type': 'pending_notification',
                    **notification
                }))
        
        except Exception as e:
            logger.error("Failed to send pending notifications", error=str(e))
    
    @database_sync_to_async
    def _get_pending_notifications(self):
        """Get pending notifications for user"""
        try:
            from backend.apps.notifications.models import UserNotification
            
            notifications = UserNotification.objects.filter(
                user_id=self.user_id,
                read=False,
                created_at__gte=datetime.utcnow() - timedelta(days=7)
            ).order_by('-created_at')[:20]
            
            return [
                {
                    'id': notif.id,
                    'title': notif.title,
                    'message': notif.message,
                    'type': notif.notification_type,
                    'priority': notif.priority,
                    'timestamp': notif.created_at.isoformat(),
                    'action_url': notif.action_url
                }
                for notif in notifications
            ]
        except Exception:
            return []


class RealTimeNotificationService:
    """
    Service for sending real-time notifications through WebSocket
    """
    
    def __init__(self, channel_layer):
        self.channel_layer = channel_layer
        self.logger = structlog.get_logger(__name__)
    
    async def notify_case_status_change(self, case_id: str, old_status: str, 
                                      new_status: str, changed_by: str, 
                                      tenant_id: str):
        """Send case status change notification"""
        
        # Send to case subscribers
        await self.channel_layer.group_send(
            f'case_{case_id}',
            {
                'type': 'case_status_changed',
                'case_id': case_id,
                'old_status': old_status,
                'new_status': new_status,
                'changed_by': changed_by,
                'timestamp': datetime.utcnow().isoformat()
            }
        )
        
        # Send to tenant group
        await self.channel_layer.group_send(
            f'tenant_{tenant_id}',
            {
                'type': 'case_status_changed',
                'case_id': case_id,
                'old_status': old_status,
                'new_status': new_status,
                'changed_by': changed_by,
                'timestamp': datetime.utcnow().isoformat()
            }
        )
        
        self.logger.info(
            "Case status change notification sent",
            case_id=case_id,
            old_status=old_status,
            new_status=new_status,
            tenant_id=tenant_id
        )
    
    async def notify_case_assignment(self, case_id: str, technician_id: str,
                                   technician_name: str, assigned_by: str,
                                   tenant_id: str):
        """Send case assignment notification"""
        
        # Send to assigned technician
        await self.channel_layer.group_send(
            f'user_{technician_id}',
            {
                'type': 'case_assigned',
                'case_id': case_id,
                'technician_id': technician_id,
                'technician_name': technician_name,
                'assigned_by': assigned_by,
                'timestamp': datetime.utcnow().isoformat()
            }
        )
        
        # Send to case subscribers
        await self.channel_layer.group_send(
            f'case_{case_id}',
            {
                'type': 'case_assigned',
                'case_id': case_id,
                'technician_id': technician_id,
                'technician_name': technician_name,
                'assigned_by': assigned_by,
                'timestamp': datetime.utcnow().isoformat()
            }
        )
    
    async def send_urgent_notification(self, recipient_ids: List[str], 
                                     title: str, message: str, 
                                     priority: str = 'high',
                                     action_required: bool = False,
                                     action_url: str = ''):
        """Send urgent notification to specific users"""
        
        notification_id = f"urgent_{datetime.utcnow().timestamp()}"
        
        for user_id in recipient_ids:
            await self.channel_layer.group_send(
                f'user_{user_id}',
                {
                    'type': 'urgent_notification',
                    'notification_id': notification_id,
                    'title': title,
                    'message': message,
                    'priority': priority,
                    'action_required': action_required,
                    'action_url': action_url,
                    'timestamp': datetime.utcnow().isoformat()
                }
            )
    
    async def broadcast_system_announcement(self, tenant_id: str, title: str,
                                          message: str, severity: str = 'info'):
        """Broadcast system announcement to all users in tenant"""
        
        await self.channel_layer.group_send(
            f'tenant_{tenant_id}',
            {
                'type': 'system_announcement',
                'title': title,
                'message': message,
                'severity': severity,
                'timestamp': datetime.utcnow().isoformat()
            }
        )


# Utility functions for integration with Django views
def get_realtime_service():
    """Get real-time notification service instance"""
    from channels.layers import get_channel_layer
    channel_layer = get_channel_layer()
    return RealTimeNotificationService(channel_layer)


async def notify_case_update(case_id: str, update_type: str, **kwargs):
    """Convenience function to send case updates"""
    service = get_realtime_service()
    
    if update_type == 'status_change':
        await service.notify_case_status_change(
            case_id=case_id,
            old_status=kwargs['old_status'],
            new_status=kwargs['new_status'],
            changed_by=kwargs['changed_by'],
            tenant_id=kwargs['tenant_id']
        )
    elif update_type == 'assignment':
        await service.notify_case_assignment(
            case_id=case_id,
            technician_id=kwargs['technician_id'],
            technician_name=kwargs['technician_name'],
            assigned_by=kwargs['assigned_by'],
            tenant_id=kwargs['tenant_id']
        )