"""
DentFlow Performance Monitoring & Metrics
Comprehensive monitoring for production excellence
"""

import time
import logging
from typing import Dict, Any, Optional
from functools import wraps
from datetime import datetime, timedelta

from prometheus_client import Counter, Histogram, Gauge, Info
from django.conf import settings
from django.db import connection
from django.core.cache import cache
import structlog

# Prometheus Metrics Definitions
CASE_OPERATIONS = Counter(
    'dentflow_case_operations_total',
    'Total case operations performed',
    ['operation', 'status', 'tenant_id']
)

CASE_PROCESSING_TIME = Histogram(
    'dentflow_case_processing_seconds',
    'Time spent processing cases',
    ['stage', 'service_type', 'priority'],
    buckets=[0.1, 0.5, 1.0, 2.5, 5.0, 10.0, 30.0, 60.0, 120.0, 300.0]
)

ACTIVE_CASES = Gauge(
    'dentflow_active_cases_total',
    'Number of active cases by status',
    ['status', 'tenant_id']
)

API_REQUEST_DURATION = Histogram(
    'dentflow_api_request_duration_seconds',
    'HTTP request duration',
    ['method', 'endpoint', 'status_code'],
    buckets=[0.01, 0.05, 0.1, 0.2, 0.5, 1.0, 2.0, 5.0, 10.0]
)

DATABASE_QUERY_DURATION = Histogram(
    'dentflow_database_query_duration_seconds',
    'Database query execution time',
    ['query_type', 'table'],
    buckets=[0.001, 0.005, 0.01, 0.05, 0.1, 0.5, 1.0, 2.0]
)

CACHE_OPERATIONS = Counter(
    'dentflow_cache_operations_total',
    'Cache operations performed',
    ['operation', 'result', 'key_type']
)

TECHNICIAN_WORKLOAD = Gauge(
    'dentflow_technician_workload',
    'Current workload per technician',
    ['technician_id', 'department']
)

SYSTEM_INFO = Info(
    'dentflow_system_info',
    'System information and version'
)

# Set system info
SYSTEM_INFO.info({
    'version': getattr(settings, 'VERSION', '1.0.0'),
    'environment': getattr(settings, 'ENVIRONMENT', 'development'),
    'django_version': '4.2.7',
    'python_version': '3.11'
})


class PerformanceMonitor:
    """
    Centralized performance monitoring and metrics collection
    """
    
    def __init__(self):
        self.logger = structlog.get_logger(__name__)
        self._setup_structured_logging()
    
    def _setup_structured_logging(self):
        """Configure structured logging for better observability"""
        structlog.configure(
            processors=[
                structlog.stdlib.filter_by_level,
                structlog.stdlib.add_logger_name,
                structlog.stdlib.add_log_level,
                structlog.stdlib.PositionalArgumentsFormatter(),
                structlog.processors.TimeStamper(fmt="iso"),
                structlog.processors.StackInfoRenderer(),
                structlog.processors.format_exc_info,
                structlog.processors.UnicodeDecoder(),
                structlog.processors.JSONRenderer()
            ],
            context_class=dict,
            logger_factory=structlog.stdlib.LoggerFactory(),
            wrapper_class=structlog.stdlib.BoundLogger,
            cache_logger_on_first_use=True,
        )
    
    def track_case_operation(self, operation: str, status: str, tenant_id: str, 
                           duration: float = None, metadata: Dict[str, Any] = None):
        """Track case-related operations with metrics and logging"""
        
        # Update Prometheus metrics
        CASE_OPERATIONS.labels(
            operation=operation,
            status=status,
            tenant_id=tenant_id
        ).inc()
        
        # Structured logging
        log_data = {
            'event': 'case_operation',
            'operation': operation,
            'status': status,
            'tenant_id': tenant_id,
            'duration': duration
        }
        
        if metadata:
            log_data.update(metadata)
        
        if status == 'success':
            self.logger.info("Case operation completed", **log_data)
        else:
            self.logger.error("Case operation failed", **log_data)
    
    def track_case_processing_time(self, stage: str, service_type: str, 
                                 priority: str, duration: float):
        """Track time spent in each case processing stage"""
        
        CASE_PROCESSING_TIME.labels(
            stage=stage,
            service_type=service_type,
            priority=priority
        ).observe(duration)
        
        self.logger.info(
            "Case processing stage completed",
            stage=stage,
            service_type=service_type,
            priority=priority,
            duration=duration
        )
    
    def update_active_cases_count(self, tenant_id: str):
        """Update active cases count per status"""
        from backend.apps.cases.models import CaseModel
        
        try:
            cases_by_status = CaseModel.objects.filter(
                tenant_id=tenant_id
            ).values('status').annotate(
                count=models.Count('id')
            )
            
            for status_data in cases_by_status:
                ACTIVE_CASES.labels(
                    status=status_data['status'],
                    tenant_id=tenant_id
                ).set(status_data['count'])
                
        except Exception as e:
            self.logger.error(
                "Failed to update active cases count",
                error=str(e),
                tenant_id=tenant_id
            )
    
    def track_database_query(self, query_type: str, table: str, duration: float):
        """Track database query performance"""
        
        DATABASE_QUERY_DURATION.labels(
            query_type=query_type,
            table=table
        ).observe(duration)
        
        # Log slow queries (>100ms)
        if duration > 0.1:
            self.logger.warning(
                "Slow database query detected",
                query_type=query_type,
                table=table,
                duration=duration
            )
    
    def track_cache_operation(self, operation: str, result: str, key_type: str, 
                            key: str = None, duration: float = None):
        """Track cache operations and performance"""
        
        CACHE_OPERATIONS.labels(
            operation=operation,
            result=result,
            key_type=key_type
        ).inc()
        
        log_data = {
            'event': 'cache_operation',
            'operation': operation,
            'result': result,
            'key_type': key_type,
            'duration': duration
        }
        
        if key:
            log_data['cache_key'] = key
        
        self.logger.info("Cache operation", **log_data)
    
    def update_technician_workload(self, technician_id: str, department: str, 
                                 active_cases: int):
        """Update current technician workload metrics"""
        
        TECHNICIAN_WORKLOAD.labels(
            technician_id=technician_id,
            department=department
        ).set(active_cases)
    
    def get_performance_summary(self, tenant_id: str) -> Dict[str, Any]:
        """Get comprehensive performance summary for dashboard"""
        
        try:
            from backend.apps.cases.models import CaseModel
            from django.db.models import Count, Avg, Q
            from django.utils import timezone
            
            now = timezone.now()
            last_24h = now - timedelta(hours=24)
            last_7d = now - timedelta(days=7)
            
            # Case processing metrics
            recent_cases = CaseModel.objects.filter(
                tenant_id=tenant_id,
                created_at__gte=last_24h
            )
            
            weekly_cases = CaseModel.objects.filter(
                tenant_id=tenant_id,
                created_at__gte=last_7d
            )
            
            # Calculate average processing times
            avg_processing_times = weekly_cases.aggregate(
                avg_design_time=Avg('design_completion_time'),
                avg_milling_time=Avg('milling_completion_time'),
                avg_total_time=Avg('total_processing_time')
            )
            
            # Active cases by status
            active_cases_by_status = CaseModel.objects.filter(
                tenant_id=tenant_id
            ).exclude(
                status__in=['delivered', 'cancelled']
            ).values('status').annotate(count=Count('id'))
            
            # Overdue cases
            overdue_cases = CaseModel.objects.filter(
                tenant_id=tenant_id,
                due_date__lt=now
            ).exclude(
                status__in=['delivered', 'cancelled']
            ).count()
            
            # Cache hit rates
            cache_stats = self._get_cache_statistics()
            
            summary = {
                'timestamp': now.isoformat(),
                'tenant_id': tenant_id,
                'cases': {
                    'created_24h': recent_cases.count(),
                    'created_7d': weekly_cases.count(),
                    'active_total': sum(s['count'] for s in active_cases_by_status),
                    'overdue': overdue_cases,
                    'by_status': {s['status']: s['count'] for s in active_cases_by_status}
                },
                'performance': {
                    'avg_design_time_hours': avg_processing_times['avg_design_time'] or 0,
                    'avg_milling_time_hours': avg_processing_times['avg_milling_time'] or 0,
                    'avg_total_time_hours': avg_processing_times['avg_total_time'] or 0
                },
                'cache': cache_stats,
                'database': {
                    'query_count': len(connection.queries),
                    'connection_count': self._get_db_connection_count()
                }
            }
            
            self.logger.info("Performance summary generated", **summary)
            return summary
            
        except Exception as e:
            self.logger.error(
                "Failed to generate performance summary",
                error=str(e),
                tenant_id=tenant_id
            )
            return {'error': str(e)}
    
    def _get_cache_statistics(self) -> Dict[str, Any]:
        """Get cache performance statistics"""
        try:
            # This would depend on your Redis/cache implementation
            # Basic implementation for Django cache
            cache_info = {
                'status': 'available',
                'hit_rate': 'unknown',  # Would need Redis INFO command
                'memory_usage': 'unknown'
            }
            
            # Test cache connectivity
            test_key = 'health_check'
            cache.set(test_key, 'ok', 60)
            if cache.get(test_key) == 'ok':
                cache_info['status'] = 'healthy'
                cache.delete(test_key)
            
            return cache_info
            
        except Exception as e:
            return {
                'status': 'error',
                'error': str(e)
            }
    
    def _get_db_connection_count(self) -> int:
        """Get current database connection count"""
        try:
            # This would need to be implemented based on your DB
            # PostgreSQL example:
            with connection.cursor() as cursor:
                cursor.execute(
                    "SELECT count(*) FROM pg_stat_activity WHERE state = 'active'"
                )
                return cursor.fetchone()[0]
        except Exception:
            return 0


# Global monitor instance
monitor = PerformanceMonitor()


def track_performance(operation: str, include_db_queries: bool = True):
    """
    Decorator to track performance of functions/methods
    """
    def decorator(func):
        @wraps(func)
        def wrapper(*args, **kwargs):
            start_time = time.time()
            start_queries = len(connection.queries) if include_db_queries else 0
            
            try:
                result = func(*args, **kwargs)
                status = 'success'
                return result
                
            except Exception as e:
                status = 'error'
                monitor.logger.error(
                    f"Function {func.__name__} failed",
                    operation=operation,
                    error=str(e),
                    args=str(args)[:100],  # Truncate for privacy
                    kwargs=str(kwargs)[:100]
                )
                raise
                
            finally:
                duration = time.time() - start_time
                end_queries = len(connection.queries) if include_db_queries else 0
                query_count = end_queries - start_queries
                
                # Track in Prometheus
                API_REQUEST_DURATION.labels(
                    method=operation,
                    endpoint=func.__name__,
                    status_code=status
                ).observe(duration)
                
                # Log performance data
                monitor.logger.info(
                    f"Function {func.__name__} completed",
                    operation=operation,
                    status=status,
                    duration=duration,
                    db_queries=query_count
                )
        
        return wrapper
    return decorator


class HealthChecker:
    """
    Comprehensive health checking for all system components
    """
    
    def __init__(self):
        self.logger = structlog.get_logger(__name__)
    
    def check_system_health(self) -> Dict[str, Any]:
        """Comprehensive system health check"""
        
        health_status = {
            'timestamp': datetime.utcnow().isoformat(),
            'status': 'healthy',
            'checks': {}
        }
        
        # Database health
        db_health = self._check_database()
        health_status['checks']['database'] = db_health
        
        # Cache health
        cache_health = self._check_cache()
        health_status['checks']['cache'] = cache_health
        
        # Application health
        app_health = self._check_application()
        health_status['checks']['application'] = app_health
        
        # External dependencies
        external_health = self._check_external_dependencies()
        health_status['checks']['external'] = external_health
        
        # Determine overall status
        all_checks = [db_health, cache_health, app_health, external_health]
        if any(check['status'] == 'critical' for check in all_checks):
            health_status['status'] = 'critical'
        elif any(check['status'] == 'warning' for check in all_checks):
            health_status['status'] = 'warning'
        
        return health_status
    
    def _check_database(self) -> Dict[str, Any]:
        """Check database connectivity and performance"""
        try:
            start_time = time.time()
            
            with connection.cursor() as cursor:
                cursor.execute("SELECT 1")
                cursor.fetchone()
            
            response_time = time.time() - start_time
            
            return {
                'status': 'healthy' if response_time < 0.1 else 'warning',
                'response_time': response_time,
                'message': 'Database responsive'
            }
            
        except Exception as e:
            return {
                'status': 'critical',
                'error': str(e),
                'message': 'Database connection failed'
            }
    
    def _check_cache(self) -> Dict[str, Any]:
        """Check cache connectivity and performance"""
        try:
            start_time = time.time()
            
            test_key = 'health_check_cache'
            test_value = str(time.time())
            
            cache.set(test_key, test_value, 60)
            retrieved_value = cache.get(test_key)
            cache.delete(test_key)
            
            response_time = time.time() - start_time
            
            if retrieved_value == test_value:
                return {
                    'status': 'healthy',
                    'response_time': response_time,
                    'message': 'Cache responsive'
                }
            else:
                return {
                    'status': 'warning',
                    'message': 'Cache data integrity issue'
                }
                
        except Exception as e:
            return {
                'status': 'critical',
                'error': str(e),
                'message': 'Cache connection failed'
            }
    
    def _check_application(self) -> Dict[str, Any]:
        """Check application-specific health indicators"""
        try:
            from backend.apps.cases.models import CaseModel
            
            # Check if we can query main models
            case_count = CaseModel.objects.count()
            
            return {
                'status': 'healthy',
                'total_cases': case_count,
                'message': 'Application models accessible'
            }
            
        except Exception as e:
            return {
                'status': 'critical',
                'error': str(e),
                'message': 'Application models inaccessible'
            }
    
    def _check_external_dependencies(self) -> Dict[str, Any]:
        """Check external service dependencies"""
        # Placeholder for external service checks
        # (email service, payment processor, file storage, etc.)
        
        return {
            'status': 'healthy',
            'message': 'No external dependencies configured'
        }


# Global health checker instance
health_checker = HealthChecker()


# Performance monitoring middleware
class PerformanceMiddleware:
    """
    Middleware to track API request performance
    """
    
    def __init__(self, get_response):
        self.get_response = get_response
    
    def __call__(self, request):
        start_time = time.time()
        start_queries = len(connection.queries)
        
        response = self.get_response(request)
        
        duration = time.time() - start_time
        query_count = len(connection.queries) - start_queries
        
        # Track in Prometheus
        API_REQUEST_DURATION.labels(
            method=request.method,
            endpoint=request.path,
            status_code=response.status_code
        ).observe(duration)
        
        # Log slow requests (>1 second)
        if duration > 1.0:
            monitor.logger.warning(
                "Slow API request detected",
                method=request.method,
                path=request.path,
                duration=duration,
                db_queries=query_count,
                status_code=response.status_code
            )
        
        # Add performance headers
        response['X-Response-Time'] = f"{duration:.3f}s"
        response['X-DB-Queries'] = str(query_count)
        
        return response