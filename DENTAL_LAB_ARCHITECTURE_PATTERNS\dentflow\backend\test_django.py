#!/usr/bin/env python3
"""
Test Django configuration
"""

import os
import sys

# Set Django settings module
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'dentflow_project.settings')

try:
    import django
    print("✅ Django imported successfully")
    
    django.setup()
    print("✅ Django setup completed")
    
    from django.conf import settings
    print(f"✅ Settings loaded - DEBUG: {settings.DEBUG}")
    print(f"✅ ALLOWED_HOSTS: {settings.ALLOWED_HOSTS}")
    
    # Test database connection
    from django.db import connection
    cursor = connection.cursor()
    print("✅ Database connection successful")
    
    # Test apps
    print(f"✅ Installed apps: {len(settings.INSTALLED_APPS)}")
    
    # Test management command
    from django.core.management import execute_from_command_line
    print("✅ Management commands available")
    
    print("\n🎉 Django configuration is working!")
    
except Exception as e:
    print(f"❌ Django configuration failed: {e}")
    import traceback
    traceback.print_exc()
    sys.exit(1)
