# Generated by Django 4.2.7 on 2025-07-25 23:52

from decimal import Decimal
import django.core.validators
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("inventory", "0001_initial"),
    ]

    operations = [
        migrations.AlterField(
            model_name="itemmaterialcomposition",
            name="quantity",
            field=models.DecimalField(
                decimal_places=4,
                help_text="Quantity of material needed",
                max_digits=10,
                validators=[
                    django.core.validators.MinValueValidator(Decimal("0.0001"))
                ],
            ),
        ),
    ]
