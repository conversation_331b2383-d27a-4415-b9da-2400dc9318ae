"""
Pytest configuration for DentFlow
Provides fixtures and test setup for domain-driven testing
"""

import pytest
from django.test import TestCase
from django.conf import settings
from django.core.management import call_command
from django.db import transaction
import tempfile
import os

# Configure Django settings for testing
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'dentflow_project.settings')

import django
django.setup()

from apps.tenants.models import Tenant
from apps.cases.models import Clinic, Case as DjangoCase
from apps.users.models import User
from domain.model import Case, CaseId, ToothNumber, Priority, WorkflowStage
from services.unit_of_work import FakeUnitOfWork, DjangoUnitOfWork
from bootstrap import clear_bootstrap_cache


@pytest.fixture(scope='session')
def django_db_setup():
    """Setup test database"""
    settings.DATABASES['default'] = {
        'ENGINE': 'django.db.backends.sqlite3',
        'NAME': ':memory:',
    }


@pytest.fixture(autouse=True)
def enable_db_access_for_all_tests(db):
    """Enable database access for all tests"""
    pass


@pytest.fixture(autouse=True)
def clear_bootstrap():
    """Clear bootstrap cache between tests"""
    clear_bootstrap_cache()
    yield
    clear_bootstrap_cache()


@pytest.fixture
def tenant():
    """Create a test tenant"""
    return Tenant.objects.create(
        name='Test Lab',
        subdomain='test',
        contact_email='<EMAIL>',
        is_active=True
    )


@pytest.fixture
def clinic(tenant):
    """Create a test clinic"""
    return Clinic.objects.create(
        tenant=tenant,
        name='Test Clinic',
        email='<EMAIL>',
        phone='555-0123',
        address='123 Test St'
    )


@pytest.fixture
def admin_user(tenant):
    """Create a test admin user"""
    return User.objects.create_user(
        username='admin',
        email='<EMAIL>',
        password='testpass123',
        role='admin',
        tenant=tenant
    )


@pytest.fixture
def dentist_user(tenant, clinic):
    """Create a test dentist user"""
    return User.objects.create_user(
        username='dentist',
        email='<EMAIL>',
        password='testpass123',
        role='dentist',
        tenant=tenant,
        default_clinic=clinic
    )


@pytest.fixture
def technician_user(tenant):
    """Create a test technician user"""
    return User.objects.create_user(
        username='technician',
        email='<EMAIL>',
        password='testpass123',
        role='technician',
        tenant=tenant
    )


@pytest.fixture
def fake_uow():
    """Create a fake unit of work for fast testing"""
    return FakeUnitOfWork(tenant_id='test-tenant')


@pytest.fixture
def django_uow(tenant):
    """Create a Django unit of work for integration testing"""
    return DjangoUnitOfWork(tenant_id=str(tenant.id))


@pytest.fixture
def domain_case():
    """Create a domain case for testing"""
    case_id = CaseId.generate()
    tooth_number = ToothNumber("11")
    
    case = Case(
        case_id=case_id,
        clinic_id="test-clinic",
        patient_name="John Doe",
        tooth_number=tooth_number,
        service_type="crown",
        tenant_id="test-tenant",
        priority=Priority.NORMAL
    )
    
    # Assign workflow
    workflow = [
        WorkflowStage("Design", "CAD", 45),
        WorkflowStage("Milling", "CAM", 15),
        WorkflowStage("Sintering", "Furnace", 480),
    ]
    case.assign_workflow(workflow)
    
    return case


@pytest.fixture
def django_case(tenant, clinic):
    """Create a Django case for testing"""
    return DjangoCase.objects.create(
        id="TEST-2025-01-01-0001",
        tenant=tenant,
        clinic=clinic,
        patient_name="Jane Doe",
        tooth_number="12",
        service_type="crown",
        priority="normal",
        status="received"
    )


@pytest.fixture
def api_client():
    """Create an API client for testing"""
    from rest_framework.test import APIClient
    return APIClient()


@pytest.fixture
def authenticated_client(api_client, admin_user):
    """Create an authenticated API client"""
    from rest_framework_simplejwt.tokens import RefreshToken
    
    refresh = RefreshToken.for_user(admin_user)
    api_client.credentials(HTTP_AUTHORIZATION=f'Bearer {refresh.access_token}')
    api_client.user = admin_user
    
    return api_client


# Pytest configuration
def pytest_configure(config):
    """Configure pytest"""
    import django
    from django.conf import settings
    
    settings.configure(
        DATABASES={
            'default': {
                'ENGINE': 'django.db.backends.sqlite3',
                'NAME': ':memory:',
            }
        },
        INSTALLED_APPS=[
            'django.contrib.auth',
            'django.contrib.contenttypes',
            'django.contrib.sessions',
            'rest_framework',
            'apps.users',
            'apps.cases',
            'apps.tenants',
        ],
        SECRET_KEY='test-secret-key',
        USE_TZ=True,
        TESTING=True,
        CELERY_TASK_ALWAYS_EAGER=True,
        CELERY_TASK_EAGER_PROPAGATES=True,
    )
    
    django.setup()


# Test markers
pytest_plugins = [
    'pytest_django',
]

# Pytest options
def pytest_addoption(parser):
    """Add custom pytest options"""
    parser.addoption(
        "--integration",
        action="store_true",
        default=False,
        help="Run integration tests"
    )
    parser.addoption(
        "--e2e",
        action="store_true", 
        default=False,
        help="Run end-to-end tests"
    )


def pytest_collection_modifyitems(config, items):
    """Modify test collection based on options"""
    if not config.getoption("--integration"):
        skip_integration = pytest.mark.skip(reason="need --integration option to run")
        for item in items:
            if "integration" in item.keywords:
                item.add_marker(skip_integration)
    
    if not config.getoption("--e2e"):
        skip_e2e = pytest.mark.skip(reason="need --e2e option to run")
        for item in items:
            if "e2e" in item.keywords:
                item.add_marker(skip_e2e)