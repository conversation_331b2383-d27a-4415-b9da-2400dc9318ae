#!/usr/bin/env python
"""
Test login endpoint
"""

import os
import django
import requests
import json

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'dentflow_project.settings')
django.setup()

from django.contrib.auth import get_user_model

User = get_user_model()

# Check users
print("=== Users in database ===")
users = User.objects.all()
print(f"Total users: {users.count()}")
for user in users:
    print(f"- {user.email} (active: {user.is_active}, staff: {user.is_staff})")

# Test login endpoint
print("\n=== Testing login endpoint ===")
url = "http://localhost:8001/api/v1/auth/login/"
data = {
    "email": "<EMAIL>",
    "password": "admin123"
}

try:
    response = requests.post(url, json=data, headers={'Content-Type': 'application/json'})
    print(f"Status: {response.status_code}")
    print(f"Response: {response.text}")
    
    if response.status_code == 200:
        token_data = response.json()
        print(f"✅ Login successful!")
        print(f"Token: {token_data.get('access', 'N/A')[:50]}...")
    else:
        print(f"❌ Login failed: {response.status_code}")
        
except Exception as e:
    print(f"❌ Error: {e}")
