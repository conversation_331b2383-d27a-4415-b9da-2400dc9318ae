{"ast": null, "code": "var _jsxFileName = \"C:\\\\GPT4_PROJECTS\\\\DENTAL_LAB_ARCHITECTURE_PATTERNS\\\\dentflow\\\\frontend\\\\src\\\\components\\\\Dashboard.tsx\",\n  _s = $RefreshSig$();\n/**\n * DentFlow Dashboard Component - Enhanced Version\n * Comprehensive dashboard with advanced widgets, charts, and real-time data visualization\n */\n\nimport React, { useState, useEffect } from 'react';\nimport { Box, Typography, Button, Alert, CircularProgress, useTheme } from '@mui/material';\nimport { CasesService } from '../api/casesService';\nimport { useAuth } from '../context/AuthContext';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Dashboard = () => {\n  _s();\n  const {\n    user\n  } = useAuth();\n  const theme = useTheme();\n  const [stats, setStats] = useState({\n    active_cases: 0,\n    completed_today: 0,\n    pending_qc: 0,\n    revenue_today: 0,\n    overdue_cases: 0,\n    total_cases_this_month: 0,\n    revenue_this_month: 0,\n    average_completion_time: 0,\n    customer_satisfaction: 0,\n    inventory_alerts: 0,\n    technician_utilization: 0,\n    pending_invoices: 0\n  });\n  const [recentCases, setRecentCases] = useState([]);\n  const [recentActivities, setRecentActivities] = useState([]);\n  const [technicianStats, setTechnicianStats] = useState([]);\n  const [inventoryAlerts, setInventoryAlerts] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState('');\n  const [backendStatus, setBackendStatus] = useState({\n    available: false,\n    mode: 'mock'\n  });\n  useEffect(() => {\n    loadDashboardData();\n  }, []);\n  const loadDashboardData = async () => {\n    try {\n      setLoading(true);\n      setError('');\n\n      // Check backend health first\n      await CasesService.checkBackendHealth();\n      const [dashboardStats, cases] = await Promise.all([CasesService.getDashboardStats(), CasesService.getCases()]);\n\n      // Enhanced stats with mock data for demo\n      const enhancedStats = {\n        ...dashboardStats,\n        total_cases_this_month: dashboardStats.active_cases + dashboardStats.completed_today + 45,\n        revenue_this_month: dashboardStats.revenue_today * 22,\n        average_completion_time: 3.2,\n        customer_satisfaction: 4.7,\n        inventory_alerts: 3,\n        technician_utilization: 87,\n        pending_invoices: 12\n      };\n      setStats(enhancedStats);\n      setRecentCases(cases.slice(0, 8)); // Show more cases\n      setBackendStatus(CasesService.getBackendStatus());\n\n      // Load mock data for enhanced features\n      loadMockEnhancedData();\n    } catch (err) {\n      console.error('Error loading dashboard:', err);\n      setError('Failed to load some dashboard data');\n      setBackendStatus(CasesService.getBackendStatus());\n      loadMockEnhancedData(); // Load mock data even on error\n    } finally {\n      setLoading(false);\n    }\n  };\n  const loadMockEnhancedData = () => {\n    // Mock recent activities\n    setRecentActivities([{\n      id: '1',\n      type: 'case_completed',\n      title: 'Crown Case #1234 Completed',\n      description: 'Zirconia crown for patient John Doe completed by Dr. Smith',\n      timestamp: new Date(Date.now() - 1000 * 60 * 15).toISOString(),\n      user: 'Dr. Smith',\n      priority: 'medium'\n    }, {\n      id: '2',\n      type: 'inventory_low',\n      title: 'Low Inventory Alert',\n      description: 'Zirconia blocks running low (5 units remaining)',\n      timestamp: new Date(Date.now() - 1000 * 60 * 45).toISOString(),\n      priority: 'high'\n    }, {\n      id: '3',\n      type: 'payment_received',\n      title: 'Payment Received',\n      description: '$2,450 payment received from Dental Clinic ABC',\n      timestamp: new Date(Date.now() - 1000 * 60 * 120).toISOString(),\n      priority: 'low'\n    }, {\n      id: '4',\n      type: 'case_created',\n      title: 'New Bridge Case',\n      description: '3-unit bridge case received from Dr. Johnson',\n      timestamp: new Date(Date.now() - 1000 * 60 * 180).toISOString(),\n      user: 'Dr. Johnson',\n      priority: 'medium'\n    }]);\n\n    // Mock technician stats\n    setTechnicianStats([{\n      id: '1',\n      name: 'Mike Rodriguez',\n      active_cases: 8,\n      completed_today: 3,\n      efficiency_score: 94,\n      specialization: 'Crowns & Bridges'\n    }, {\n      id: '2',\n      name: 'Sarah Chen',\n      active_cases: 6,\n      completed_today: 4,\n      efficiency_score: 97,\n      specialization: 'Implants'\n    }, {\n      id: '3',\n      name: 'David Kim',\n      active_cases: 5,\n      completed_today: 2,\n      efficiency_score: 89,\n      specialization: 'Orthodontics'\n    }]);\n\n    // Mock inventory alerts\n    setInventoryAlerts([{\n      id: '1',\n      material_name: 'Zirconia Blocks',\n      current_stock: 5,\n      minimum_stock: 10,\n      unit: 'blocks',\n      urgency: 'high'\n    }, {\n      id: '2',\n      material_name: 'Titanium Abutments',\n      current_stock: 8,\n      minimum_stock: 15,\n      unit: 'pieces',\n      urgency: 'medium'\n    }, {\n      id: '3',\n      material_name: 'Ceramic Stain',\n      current_stock: 12,\n      minimum_stock: 20,\n      unit: 'ml',\n      urgency: 'low'\n    }]);\n  };\n  const formatCurrency = amount => {\n    return new Intl.NumberFormat('en-US', {\n      style: 'currency',\n      currency: 'USD'\n    }).format(amount);\n  };\n  const getStatusColor = status => {\n    const colors = {\n      'received': '#2196f3',\n      'design': '#ff9800',\n      'milling': '#9c27b0',\n      'sintering': '#e91e63',\n      'qc': '#ff5722',\n      'shipped': '#4caf50',\n      'delivered': '#8bc34a',\n      'cancelled': '#757575'\n    };\n    return colors[status] || '#666';\n  };\n  const getPriorityColor = priority => {\n    const colors = {\n      'low': '#4caf50',\n      'normal': '#2196f3',\n      'urgent': '#ff9800',\n      'stat': '#f44336'\n    };\n    return colors[priority] || '#666';\n  };\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(Box, {\n      display: \"flex\",\n      justifyContent: \"center\",\n      alignItems: \"center\",\n      minHeight: \"60vh\",\n      children: /*#__PURE__*/_jsxDEV(Box, {\n        textAlign: \"center\",\n        children: [/*#__PURE__*/_jsxDEV(CircularProgress, {\n          size: 60\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 308,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"h6\",\n          sx: {\n            mt: 2,\n            color: 'text.secondary'\n          },\n          children: \"Loading dashboard...\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 309,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 307,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 306,\n      columnNumber: 7\n    }, this);\n  }\n  if (error) {\n    return /*#__PURE__*/_jsxDEV(Box, {\n      p: 3,\n      children: /*#__PURE__*/_jsxDEV(Alert, {\n        severity: \"error\",\n        action: /*#__PURE__*/_jsxDEV(Button, {\n          color: \"inherit\",\n          size: \"small\",\n          onClick: loadDashboardData,\n          children: \"Retry\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 323,\n          columnNumber: 13\n        }, this),\n        children: error\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 320,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 319,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    style: {\n      padding: '2rem'\n    },\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        marginBottom: '2rem'\n      },\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          display: 'flex',\n          justifyContent: 'space-between',\n          alignItems: 'center'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n            style: {\n              margin: 0,\n              color: '#1976d2'\n            },\n            children: \"\\uD83E\\uDDB7 DentFlow Dashboard\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 339,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            style: {\n              margin: '0.5rem 0',\n              color: '#666'\n            },\n            children: [\"Welcome back, \", user === null || user === void 0 ? void 0 : user.first_name, \" \", user === null || user === void 0 ? void 0 : user.last_name, \" (\", user === null || user === void 0 ? void 0 : user.role, \")\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 342,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 338,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            background: backendStatus.available ? '#e8f5e8' : '#fff3cd',\n            color: backendStatus.available ? '#2e7d32' : '#856404',\n            padding: '0.5rem 1rem',\n            borderRadius: '20px',\n            fontSize: '0.9rem',\n            fontWeight: 'bold',\n            border: backendStatus.available ? '1px solid #4caf50' : '1px solid #ffc107'\n          },\n          children: backendStatus.available ? '🟢 Live Data' : '🟡 Demo Data'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 346,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 337,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 336,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        display: 'grid',\n        gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))',\n        gap: '1rem',\n        marginBottom: '2rem'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        style: cardStyle,\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          style: {\n            margin: '0 0 0.5rem 0',\n            color: '#666'\n          },\n          children: \"Active Cases\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 368,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            fontSize: '2rem',\n            fontWeight: 'bold',\n            color: '#1976d2'\n          },\n          children: stats.active_cases\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 369,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"small\", {\n          style: {\n            color: '#666'\n          },\n          children: \"In production\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 372,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 367,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: cardStyle,\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          style: {\n            margin: '0 0 0.5rem 0',\n            color: '#666'\n          },\n          children: \"Completed Today\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 376,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            fontSize: '2rem',\n            fontWeight: 'bold',\n            color: '#4caf50'\n          },\n          children: stats.completed_today\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 377,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"small\", {\n          style: {\n            color: '#666'\n          },\n          children: \"Cases finished\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 380,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 375,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: cardStyle,\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          style: {\n            margin: '0 0 0.5rem 0',\n            color: '#666'\n          },\n          children: \"Pending QC\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 384,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            fontSize: '2rem',\n            fontWeight: 'bold',\n            color: '#ff9800'\n          },\n          children: stats.pending_qc\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 385,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"small\", {\n          style: {\n            color: '#666'\n          },\n          children: \"Quality control needed\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 388,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 383,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: cardStyle,\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          style: {\n            margin: '0 0 0.5rem 0',\n            color: '#666'\n          },\n          children: \"Revenue Today\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 392,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            fontSize: '2rem',\n            fontWeight: 'bold',\n            color: '#2196f3'\n          },\n          children: formatCurrency(stats.revenue_today)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 393,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"small\", {\n          style: {\n            color: '#666'\n          },\n          children: \"Daily earnings\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 396,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 391,\n        columnNumber: 9\n      }, this), stats.overdue_cases > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          ...cardStyle,\n          borderLeft: '4px solid #f44336'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          style: {\n            margin: '0 0 0.5rem 0',\n            color: '#666'\n          },\n          children: \"Overdue Cases\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 401,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            fontSize: '2rem',\n            fontWeight: 'bold',\n            color: '#f44336'\n          },\n          children: stats.overdue_cases\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 402,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"small\", {\n          style: {\n            color: '#666'\n          },\n          children: \"Immediate attention needed\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 405,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 400,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 361,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n        style: {\n          marginBottom: '1rem',\n          color: '#333'\n        },\n        children: \"Recent Cases\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 411,\n        columnNumber: 9\n      }, this), recentCases.length === 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n        style: cardStyle,\n        children: /*#__PURE__*/_jsxDEV(\"p\", {\n          style: {\n            margin: 0,\n            color: '#666',\n            textAlign: 'center'\n          },\n          children: \"No cases found. Create your first case to get started!\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 414,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 413,\n        columnNumber: 11\n      }, this) : recentCases.map(case_ => /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          ...cardStyle,\n          marginBottom: '1rem',\n          borderLeft: `4px solid ${getStatusColor(case_.status)}`\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            display: 'flex',\n            justifyContent: 'space-between',\n            alignItems: 'flex-start',\n            marginBottom: '0.5rem'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n              style: {\n                margin: '0 0 0.25rem 0',\n                color: '#333'\n              },\n              children: [\"Case \", case_.id]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 432,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              style: {\n                margin: 0,\n                color: '#666'\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                children: \"Patient:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 436,\n                columnNumber: 21\n              }, this), \" \", case_.patient_name]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 435,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 431,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              textAlign: 'right'\n            },\n            children: /*#__PURE__*/_jsxDEV(\"span\", {\n              style: {\n                background: getPriorityColor(case_.priority),\n                color: 'white',\n                padding: '0.25rem 0.5rem',\n                borderRadius: '4px',\n                fontSize: '0.8rem',\n                textTransform: 'uppercase'\n              },\n              children: case_.priority\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 440,\n              columnNumber: 19\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 439,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 425,\n          columnNumber: 15\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            display: 'grid',\n            gridTemplateColumns: 'repeat(auto-fit, minmax(150px, 1fr))',\n            gap: '1rem',\n            color: '#666',\n            fontSize: '0.9rem'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n              children: \"Type:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 461,\n              columnNumber: 19\n            }, this), \" \", case_.service_type]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 460,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n              children: \"Tooth:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 464,\n              columnNumber: 19\n            }, this), \" #\", case_.tooth_number]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 463,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n              children: \"Status:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 467,\n              columnNumber: 19\n            }, this), ' ', /*#__PURE__*/_jsxDEV(\"span\", {\n              style: {\n                color: getStatusColor(case_.status)\n              },\n              children: case_.current_stage\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 468,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 466,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n              children: \"Due:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 473,\n              columnNumber: 19\n            }, this), ' ', case_.due_date ? /*#__PURE__*/_jsxDEV(\"span\", {\n              style: {\n                color: case_.is_overdue ? '#f44336' : '#666'\n              },\n              children: case_.days_until_due !== null && case_.days_until_due !== undefined ? case_.days_until_due >= 0 ? `${case_.days_until_due} days` : `${Math.abs(case_.days_until_due)} days overdue` : 'Today'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 475,\n              columnNumber: 21\n            }, this) : 'Not set']\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 472,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 453,\n          columnNumber: 15\n        }, this)]\n      }, case_.id, true, {\n        fileName: _jsxFileName,\n        lineNumber: 420,\n        columnNumber: 13\n      }, this))]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 410,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        marginTop: '2rem',\n        padding: '1rem',\n        background: backendStatus.available ? '#e8f5e8' : '#fff3cd',\n        borderRadius: '4px',\n        border: backendStatus.available ? '1px solid #4caf50' : '1px solid #ffc107'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n        style: {\n          color: backendStatus.available ? '#2e7d32' : '#856404',\n          margin: '0 0 0.5rem 0'\n        },\n        children: [backendStatus.available ? '✅' : '⚠️', \" Backend Status: \", backendStatus.mode === 'live' ? 'Connected' : 'Mock Data']\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 501,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        style: {\n          margin: 0,\n          color: backendStatus.available ? '#2e7d32' : '#856404'\n        },\n        children: backendStatus.available ? 'React frontend successfully connected to Django REST API backend. Real-time data loading from DentFlow case management system.' : 'Backend unavailable. Using demonstration data. All features remain functional for testing.'\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 507,\n        columnNumber: 9\n      }, this), !backendStatus.available && /*#__PURE__*/_jsxDEV(\"button\", {\n        onClick: loadDashboardData,\n        style: {\n          marginTop: '0.5rem',\n          background: '#1976d2',\n          color: 'white',\n          border: 'none',\n          padding: '0.5rem 1rem',\n          borderRadius: '4px',\n          cursor: 'pointer'\n        },\n        children: \"\\uD83D\\uDD04 Try Reconnect\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 517,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 494,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 334,\n    columnNumber: 5\n  }, this);\n};\n\n// Styles\n_s(Dashboard, \"TpSWFmBzfBX/bnEMSRexAAm8j9w=\", false, function () {\n  return [useAuth, useTheme];\n});\n_c = Dashboard;\nconst cardStyle = {\n  background: '#fff',\n  padding: '1.5rem',\n  borderRadius: '8px',\n  boxShadow: '0 2px 8px rgba(0,0,0,0.1)',\n  border: '1px solid #e0e0e0'\n};\nexport default Dashboard;\nvar _c;\n$RefreshReg$(_c, \"Dashboard\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Box", "Typography", "<PERSON><PERSON>", "<PERSON><PERSON>", "CircularProgress", "useTheme", "CasesService", "useAuth", "jsxDEV", "_jsxDEV", "Dashboard", "_s", "user", "theme", "stats", "setStats", "active_cases", "completed_today", "pending_qc", "revenue_today", "overdue_cases", "total_cases_this_month", "revenue_this_month", "average_completion_time", "customer_satisfaction", "inventory_alerts", "technician_utilization", "pending_invoices", "recentCases", "setRecentCases", "recentActivities", "setRecentActivities", "technicianStats", "setTechnicianStats", "inventoryAlerts", "setInventoryAlerts", "loading", "setLoading", "error", "setError", "backendStatus", "setBackendStatus", "available", "mode", "loadDashboardData", "checkBackendHealth", "dashboardStats", "cases", "Promise", "all", "getDashboardStats", "getCases", "enhancedStats", "slice", "getBackendStatus", "loadMockEnhancedData", "err", "console", "id", "type", "title", "description", "timestamp", "Date", "now", "toISOString", "priority", "name", "efficiency_score", "specialization", "material_name", "current_stock", "minimum_stock", "unit", "urgency", "formatCurrency", "amount", "Intl", "NumberFormat", "style", "currency", "format", "getStatusColor", "status", "colors", "getPriorityColor", "display", "justifyContent", "alignItems", "minHeight", "children", "textAlign", "size", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "variant", "sx", "mt", "color", "p", "severity", "action", "onClick", "padding", "marginBottom", "margin", "first_name", "last_name", "role", "background", "borderRadius", "fontSize", "fontWeight", "border", "gridTemplateColumns", "gap", "cardStyle", "borderLeft", "length", "map", "case_", "patient_name", "textTransform", "service_type", "tooth_number", "current_stage", "due_date", "is_overdue", "days_until_due", "undefined", "Math", "abs", "marginTop", "cursor", "_c", "boxShadow", "$RefreshReg$"], "sources": ["C:/GPT4_PROJECTS/DENTAL_LAB_ARCHITECTURE_PATTERNS/dentflow/frontend/src/components/Dashboard.tsx"], "sourcesContent": ["/**\n * DentFlow Dashboard Component - Enhanced Version\n * Comprehensive dashboard with advanced widgets, charts, and real-time data visualization\n */\n\nimport React, { useState, useEffect } from 'react';\nimport {\n  Box,\n  Grid,\n  Card,\n  CardContent,\n  Typography,\n  Avatar,\n  Chip,\n  LinearProgress,\n  IconButton,\n  Button,\n  Alert,\n  Paper,\n  List,\n  ListItem,\n  ListItemText,\n  ListItemAvatar,\n  Divider,\n  Badge,\n  Tooltip,\n  CircularProgress,\n  Table,\n  TableBody,\n  TableCell,\n  TableContainer,\n  TableHead,\n  TableRow,\n  useTheme,\n  alpha,\n} from '@mui/material';\nimport {\n  TrendingUp,\n  TrendingDown,\n  Assignment,\n  CheckCircle,\n  Warning,\n  Schedule,\n  AttachMoney,\n  People,\n  Inventory,\n  Analytics,\n  Refresh,\n  Notifications,\n  CalendarToday,\n  Build,\n  LocalShipping,\n  QualityControl,\n} from '@mui/icons-material';\nimport { CasesService } from '../api/casesService';\nimport { Case } from '../api/types';\nimport { useAuth } from '../context/AuthContext';\n\ninterface DashboardStats {\n  active_cases: number;\n  completed_today: number;\n  pending_qc: number;\n  revenue_today: number;\n  overdue_cases: number;\n  total_cases_this_month: number;\n  revenue_this_month: number;\n  average_completion_time: number;\n  customer_satisfaction: number;\n  inventory_alerts: number;\n  technician_utilization: number;\n  pending_invoices: number;\n}\n\ninterface ActivityItem {\n  id: string;\n  type: 'case_created' | 'case_completed' | 'payment_received' | 'inventory_low' | 'qc_failed';\n  title: string;\n  description: string;\n  timestamp: string;\n  user?: string;\n  priority?: 'low' | 'medium' | 'high';\n}\n\ninterface TechnicianStats {\n  id: string;\n  name: string;\n  active_cases: number;\n  completed_today: number;\n  efficiency_score: number;\n  specialization: string;\n}\n\ninterface InventoryAlert {\n  id: string;\n  material_name: string;\n  current_stock: number;\n  minimum_stock: number;\n  unit: string;\n  urgency: 'low' | 'medium' | 'high';\n}\n\nconst Dashboard: React.FC = () => {\n  const { user } = useAuth();\n  const theme = useTheme();\n\n  const [stats, setStats] = useState<DashboardStats>({\n    active_cases: 0,\n    completed_today: 0,\n    pending_qc: 0,\n    revenue_today: 0,\n    overdue_cases: 0,\n    total_cases_this_month: 0,\n    revenue_this_month: 0,\n    average_completion_time: 0,\n    customer_satisfaction: 0,\n    inventory_alerts: 0,\n    technician_utilization: 0,\n    pending_invoices: 0,\n  });\n\n  const [recentCases, setRecentCases] = useState<Case[]>([]);\n  const [recentActivities, setRecentActivities] = useState<ActivityItem[]>([]);\n  const [technicianStats, setTechnicianStats] = useState<TechnicianStats[]>([]);\n  const [inventoryAlerts, setInventoryAlerts] = useState<InventoryAlert[]>([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState<string>('');\n  const [backendStatus, setBackendStatus] = useState<{available: boolean; mode: string}>({\n    available: false,\n    mode: 'mock'\n  });\n\n  useEffect(() => {\n    loadDashboardData();\n  }, []);\n\n  const loadDashboardData = async () => {\n    try {\n      setLoading(true);\n      setError('');\n\n      // Check backend health first\n      await CasesService.checkBackendHealth();\n\n      const [dashboardStats, cases] = await Promise.all([\n        CasesService.getDashboardStats(),\n        CasesService.getCases(),\n      ]);\n\n      // Enhanced stats with mock data for demo\n      const enhancedStats = {\n        ...dashboardStats,\n        total_cases_this_month: dashboardStats.active_cases + dashboardStats.completed_today + 45,\n        revenue_this_month: dashboardStats.revenue_today * 22,\n        average_completion_time: 3.2,\n        customer_satisfaction: 4.7,\n        inventory_alerts: 3,\n        technician_utilization: 87,\n        pending_invoices: 12,\n      };\n\n      setStats(enhancedStats);\n      setRecentCases(cases.slice(0, 8)); // Show more cases\n      setBackendStatus(CasesService.getBackendStatus());\n\n      // Load mock data for enhanced features\n      loadMockEnhancedData();\n\n    } catch (err: any) {\n      console.error('Error loading dashboard:', err);\n      setError('Failed to load some dashboard data');\n      setBackendStatus(CasesService.getBackendStatus());\n      loadMockEnhancedData(); // Load mock data even on error\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const loadMockEnhancedData = () => {\n    // Mock recent activities\n    setRecentActivities([\n      {\n        id: '1',\n        type: 'case_completed',\n        title: 'Crown Case #1234 Completed',\n        description: 'Zirconia crown for patient John Doe completed by Dr. Smith',\n        timestamp: new Date(Date.now() - 1000 * 60 * 15).toISOString(),\n        user: 'Dr. Smith',\n        priority: 'medium'\n      },\n      {\n        id: '2',\n        type: 'inventory_low',\n        title: 'Low Inventory Alert',\n        description: 'Zirconia blocks running low (5 units remaining)',\n        timestamp: new Date(Date.now() - 1000 * 60 * 45).toISOString(),\n        priority: 'high'\n      },\n      {\n        id: '3',\n        type: 'payment_received',\n        title: 'Payment Received',\n        description: '$2,450 payment received from Dental Clinic ABC',\n        timestamp: new Date(Date.now() - 1000 * 60 * 120).toISOString(),\n        priority: 'low'\n      },\n      {\n        id: '4',\n        type: 'case_created',\n        title: 'New Bridge Case',\n        description: '3-unit bridge case received from Dr. Johnson',\n        timestamp: new Date(Date.now() - 1000 * 60 * 180).toISOString(),\n        user: 'Dr. Johnson',\n        priority: 'medium'\n      }\n    ]);\n\n    // Mock technician stats\n    setTechnicianStats([\n      {\n        id: '1',\n        name: 'Mike Rodriguez',\n        active_cases: 8,\n        completed_today: 3,\n        efficiency_score: 94,\n        specialization: 'Crowns & Bridges'\n      },\n      {\n        id: '2',\n        name: 'Sarah Chen',\n        active_cases: 6,\n        completed_today: 4,\n        efficiency_score: 97,\n        specialization: 'Implants'\n      },\n      {\n        id: '3',\n        name: 'David Kim',\n        active_cases: 5,\n        completed_today: 2,\n        efficiency_score: 89,\n        specialization: 'Orthodontics'\n      }\n    ]);\n\n    // Mock inventory alerts\n    setInventoryAlerts([\n      {\n        id: '1',\n        material_name: 'Zirconia Blocks',\n        current_stock: 5,\n        minimum_stock: 10,\n        unit: 'blocks',\n        urgency: 'high'\n      },\n      {\n        id: '2',\n        material_name: 'Titanium Abutments',\n        current_stock: 8,\n        minimum_stock: 15,\n        unit: 'pieces',\n        urgency: 'medium'\n      },\n      {\n        id: '3',\n        material_name: 'Ceramic Stain',\n        current_stock: 12,\n        minimum_stock: 20,\n        unit: 'ml',\n        urgency: 'low'\n      }\n    ]);\n  };\n  const formatCurrency = (amount: number) => {\n    return new Intl.NumberFormat('en-US', {\n      style: 'currency',\n      currency: 'USD',\n    }).format(amount);\n  };\n\n  const getStatusColor = (status: string) => {\n    const colors: Record<string, string> = {\n      'received': '#2196f3',\n      'design': '#ff9800',\n      'milling': '#9c27b0',\n      'sintering': '#e91e63',\n      'qc': '#ff5722',\n      'shipped': '#4caf50',\n      'delivered': '#8bc34a',\n      'cancelled': '#757575',\n    };\n    return colors[status] || '#666';\n  };\n\n  const getPriorityColor = (priority: string) => {\n    const colors: Record<string, string> = {\n      'low': '#4caf50',\n      'normal': '#2196f3',\n      'urgent': '#ff9800',\n      'stat': '#f44336',\n    };\n    return colors[priority] || '#666';\n  };\n\n  if (loading) {\n    return (\n      <Box display=\"flex\" justifyContent=\"center\" alignItems=\"center\" minHeight=\"60vh\">\n        <Box textAlign=\"center\">\n          <CircularProgress size={60} />\n          <Typography variant=\"h6\" sx={{ mt: 2, color: 'text.secondary' }}>\n            Loading dashboard...\n          </Typography>\n        </Box>\n      </Box>\n    );\n  }\n\n  if (error) {\n    return (\n      <Box p={3}>\n        <Alert\n          severity=\"error\"\n          action={\n            <Button color=\"inherit\" size=\"small\" onClick={loadDashboardData}>\n              Retry\n            </Button>\n          }\n        >\n          {error}\n        </Alert>\n      </Box>\n    );\n  }\n  return (\n    <div style={{ padding: '2rem' }}>\n      {/* Header */}\n      <div style={{ marginBottom: '2rem' }}>\n        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>\n          <div>\n            <h1 style={{ margin: 0, color: '#1976d2' }}>\n              🦷 DentFlow Dashboard\n            </h1>\n            <p style={{ margin: '0.5rem 0', color: '#666' }}>\n              Welcome back, {user?.first_name} {user?.last_name} ({user?.role})\n            </p>\n          </div>\n          <div style={{\n            background: backendStatus.available ? '#e8f5e8' : '#fff3cd',\n            color: backendStatus.available ? '#2e7d32' : '#856404',\n            padding: '0.5rem 1rem',\n            borderRadius: '20px',\n            fontSize: '0.9rem',\n            fontWeight: 'bold',\n            border: backendStatus.available ? '1px solid #4caf50' : '1px solid #ffc107'\n          }}>\n            {backendStatus.available ? '🟢 Live Data' : '🟡 Demo Data'}\n          </div>\n        </div>\n      </div>\n\n      {/* Stats Grid */}\n      <div style={{\n        display: 'grid',\n        gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))',\n        gap: '1rem',\n        marginBottom: '2rem',\n      }}>\n        <div style={cardStyle}>\n          <h3 style={{ margin: '0 0 0.5rem 0', color: '#666' }}>Active Cases</h3>\n          <div style={{ fontSize: '2rem', fontWeight: 'bold', color: '#1976d2' }}>\n            {stats.active_cases}\n          </div>\n          <small style={{ color: '#666' }}>In production</small>\n        </div>\n\n        <div style={cardStyle}>\n          <h3 style={{ margin: '0 0 0.5rem 0', color: '#666' }}>Completed Today</h3>\n          <div style={{ fontSize: '2rem', fontWeight: 'bold', color: '#4caf50' }}>\n            {stats.completed_today}\n          </div>\n          <small style={{ color: '#666' }}>Cases finished</small>\n        </div>\n\n        <div style={cardStyle}>\n          <h3 style={{ margin: '0 0 0.5rem 0', color: '#666' }}>Pending QC</h3>\n          <div style={{ fontSize: '2rem', fontWeight: 'bold', color: '#ff9800' }}>\n            {stats.pending_qc}\n          </div>\n          <small style={{ color: '#666' }}>Quality control needed</small>\n        </div>\n\n        <div style={cardStyle}>\n          <h3 style={{ margin: '0 0 0.5rem 0', color: '#666' }}>Revenue Today</h3>\n          <div style={{ fontSize: '2rem', fontWeight: 'bold', color: '#2196f3' }}>\n            {formatCurrency(stats.revenue_today)}\n          </div>\n          <small style={{ color: '#666' }}>Daily earnings</small>\n        </div>\n\n        {stats.overdue_cases > 0 && (\n          <div style={{ ...cardStyle, borderLeft: '4px solid #f44336' }}>\n            <h3 style={{ margin: '0 0 0.5rem 0', color: '#666' }}>Overdue Cases</h3>\n            <div style={{ fontSize: '2rem', fontWeight: 'bold', color: '#f44336' }}>\n              {stats.overdue_cases}\n            </div>\n            <small style={{ color: '#666' }}>Immediate attention needed</small>\n          </div>\n        )}\n      </div>\n      {/* Recent Cases */}\n      <div>\n        <h2 style={{ marginBottom: '1rem', color: '#333' }}>Recent Cases</h2>\n        {recentCases.length === 0 ? (\n          <div style={cardStyle}>\n            <p style={{ margin: 0, color: '#666', textAlign: 'center' }}>\n              No cases found. Create your first case to get started!\n            </p>\n          </div>\n        ) : (\n          recentCases.map((case_) => (\n            <div key={case_.id} style={{\n              ...cardStyle,\n              marginBottom: '1rem',\n              borderLeft: `4px solid ${getStatusColor(case_.status)}`,\n            }}>\n              <div style={{\n                display: 'flex',\n                justifyContent: 'space-between',\n                alignItems: 'flex-start',\n                marginBottom: '0.5rem',\n              }}>\n                <div>\n                  <h4 style={{ margin: '0 0 0.25rem 0', color: '#333' }}>\n                    Case {case_.id}\n                  </h4>\n                  <p style={{ margin: 0, color: '#666' }}>\n                    <strong>Patient:</strong> {case_.patient_name}\n                  </p>\n                </div>\n                <div style={{ textAlign: 'right' }}>\n                  <span style={{\n                    background: getPriorityColor(case_.priority),\n                    color: 'white',\n                    padding: '0.25rem 0.5rem',\n                    borderRadius: '4px',\n                    fontSize: '0.8rem',\n                    textTransform: 'uppercase',\n                  }}>\n                    {case_.priority}\n                  </span>\n                </div>\n              </div>\n              \n              <div style={{\n                display: 'grid',\n                gridTemplateColumns: 'repeat(auto-fit, minmax(150px, 1fr))',\n                gap: '1rem',\n                color: '#666',\n                fontSize: '0.9rem',\n              }}>\n                <div>\n                  <strong>Type:</strong> {case_.service_type}\n                </div>\n                <div>\n                  <strong>Tooth:</strong> #{case_.tooth_number}\n                </div>\n                <div>\n                  <strong>Status:</strong>{' '}\n                  <span style={{ color: getStatusColor(case_.status) }}>\n                    {case_.current_stage}\n                  </span>\n                </div>\n                <div>\n                  <strong>Due:</strong>{' '}\n                  {case_.due_date ? (\n                    <span style={{ \n                      color: case_.is_overdue ? '#f44336' : '#666' \n                    }}>\n                      {case_.days_until_due !== null && case_.days_until_due !== undefined ? (\n                        case_.days_until_due >= 0 ?\n                          `${case_.days_until_due} days` :\n                          `${Math.abs(case_.days_until_due)} days overdue`\n                      ) : 'Today'}\n                    </span>\n                  ) : (\n                    'Not set'\n                  )}\n                </div>\n              </div>\n            </div>\n          ))\n        )}\n      </div>\n\n      <div style={{\n        marginTop: '2rem',\n        padding: '1rem',\n        background: backendStatus.available ? '#e8f5e8' : '#fff3cd',\n        borderRadius: '4px',\n        border: backendStatus.available ? '1px solid #4caf50' : '1px solid #ffc107',\n      }}>\n        <h4 style={{ \n          color: backendStatus.available ? '#2e7d32' : '#856404', \n          margin: '0 0 0.5rem 0' \n        }}>\n          {backendStatus.available ? '✅' : '⚠️'} Backend Status: {backendStatus.mode === 'live' ? 'Connected' : 'Mock Data'}\n        </h4>\n        <p style={{ \n          margin: 0, \n          color: backendStatus.available ? '#2e7d32' : '#856404' \n        }}>\n          {backendStatus.available \n            ? 'React frontend successfully connected to Django REST API backend. Real-time data loading from DentFlow case management system.'\n            : 'Backend unavailable. Using demonstration data. All features remain functional for testing.'\n          }\n        </p>\n        {!backendStatus.available && (\n          <button\n            onClick={loadDashboardData}\n            style={{\n              marginTop: '0.5rem',\n              background: '#1976d2',\n              color: 'white',\n              border: 'none',\n              padding: '0.5rem 1rem',\n              borderRadius: '4px',\n              cursor: 'pointer',\n            }}\n          >\n            🔄 Try Reconnect\n          </button>\n        )}\n      </div>\n    </div>\n  );\n};\n\n// Styles\nconst cardStyle: React.CSSProperties = {\n  background: '#fff',\n  padding: '1.5rem',\n  borderRadius: '8px',\n  boxShadow: '0 2px 8px rgba(0,0,0,0.1)',\n  border: '1px solid #e0e0e0',\n};\n\nexport default Dashboard;"], "mappings": ";;AAAA;AACA;AACA;AACA;;AAEA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SACEC,GAAG,EAIHC,UAAU,EAKVC,MAAM,EACNC,KAAK,EASLC,gBAAgB,EAOhBC,QAAQ,QAEH,eAAe;AAmBtB,SAASC,YAAY,QAAQ,qBAAqB;AAElD,SAASC,OAAO,QAAQ,wBAAwB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AA6CjD,MAAMC,SAAmB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAChC,MAAM;IAAEC;EAAK,CAAC,GAAGL,OAAO,CAAC,CAAC;EAC1B,MAAMM,KAAK,GAAGR,QAAQ,CAAC,CAAC;EAExB,MAAM,CAACS,KAAK,EAAEC,QAAQ,CAAC,GAAGjB,QAAQ,CAAiB;IACjDkB,YAAY,EAAE,CAAC;IACfC,eAAe,EAAE,CAAC;IAClBC,UAAU,EAAE,CAAC;IACbC,aAAa,EAAE,CAAC;IAChBC,aAAa,EAAE,CAAC;IAChBC,sBAAsB,EAAE,CAAC;IACzBC,kBAAkB,EAAE,CAAC;IACrBC,uBAAuB,EAAE,CAAC;IAC1BC,qBAAqB,EAAE,CAAC;IACxBC,gBAAgB,EAAE,CAAC;IACnBC,sBAAsB,EAAE,CAAC;IACzBC,gBAAgB,EAAE;EACpB,CAAC,CAAC;EAEF,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAG/B,QAAQ,CAAS,EAAE,CAAC;EAC1D,MAAM,CAACgC,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGjC,QAAQ,CAAiB,EAAE,CAAC;EAC5E,MAAM,CAACkC,eAAe,EAAEC,kBAAkB,CAAC,GAAGnC,QAAQ,CAAoB,EAAE,CAAC;EAC7E,MAAM,CAACoC,eAAe,EAAEC,kBAAkB,CAAC,GAAGrC,QAAQ,CAAmB,EAAE,CAAC;EAC5E,MAAM,CAACsC,OAAO,EAAEC,UAAU,CAAC,GAAGvC,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACwC,KAAK,EAAEC,QAAQ,CAAC,GAAGzC,QAAQ,CAAS,EAAE,CAAC;EAC9C,MAAM,CAAC0C,aAAa,EAAEC,gBAAgB,CAAC,GAAG3C,QAAQ,CAAqC;IACrF4C,SAAS,EAAE,KAAK;IAChBC,IAAI,EAAE;EACR,CAAC,CAAC;EAEF5C,SAAS,CAAC,MAAM;IACd6C,iBAAiB,CAAC,CAAC;EACrB,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMA,iBAAiB,GAAG,MAAAA,CAAA,KAAY;IACpC,IAAI;MACFP,UAAU,CAAC,IAAI,CAAC;MAChBE,QAAQ,CAAC,EAAE,CAAC;;MAEZ;MACA,MAAMjC,YAAY,CAACuC,kBAAkB,CAAC,CAAC;MAEvC,MAAM,CAACC,cAAc,EAAEC,KAAK,CAAC,GAAG,MAAMC,OAAO,CAACC,GAAG,CAAC,CAChD3C,YAAY,CAAC4C,iBAAiB,CAAC,CAAC,EAChC5C,YAAY,CAAC6C,QAAQ,CAAC,CAAC,CACxB,CAAC;;MAEF;MACA,MAAMC,aAAa,GAAG;QACpB,GAAGN,cAAc;QACjBzB,sBAAsB,EAAEyB,cAAc,CAAC9B,YAAY,GAAG8B,cAAc,CAAC7B,eAAe,GAAG,EAAE;QACzFK,kBAAkB,EAAEwB,cAAc,CAAC3B,aAAa,GAAG,EAAE;QACrDI,uBAAuB,EAAE,GAAG;QAC5BC,qBAAqB,EAAE,GAAG;QAC1BC,gBAAgB,EAAE,CAAC;QACnBC,sBAAsB,EAAE,EAAE;QAC1BC,gBAAgB,EAAE;MACpB,CAAC;MAEDZ,QAAQ,CAACqC,aAAa,CAAC;MACvBvB,cAAc,CAACkB,KAAK,CAACM,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;MACnCZ,gBAAgB,CAACnC,YAAY,CAACgD,gBAAgB,CAAC,CAAC,CAAC;;MAEjD;MACAC,oBAAoB,CAAC,CAAC;IAExB,CAAC,CAAC,OAAOC,GAAQ,EAAE;MACjBC,OAAO,CAACnB,KAAK,CAAC,0BAA0B,EAAEkB,GAAG,CAAC;MAC9CjB,QAAQ,CAAC,oCAAoC,CAAC;MAC9CE,gBAAgB,CAACnC,YAAY,CAACgD,gBAAgB,CAAC,CAAC,CAAC;MACjDC,oBAAoB,CAAC,CAAC,CAAC,CAAC;IAC1B,CAAC,SAAS;MACRlB,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAMkB,oBAAoB,GAAGA,CAAA,KAAM;IACjC;IACAxB,mBAAmB,CAAC,CAClB;MACE2B,EAAE,EAAE,GAAG;MACPC,IAAI,EAAE,gBAAgB;MACtBC,KAAK,EAAE,4BAA4B;MACnCC,WAAW,EAAE,4DAA4D;MACzEC,SAAS,EAAE,IAAIC,IAAI,CAACA,IAAI,CAACC,GAAG,CAAC,CAAC,GAAG,IAAI,GAAG,EAAE,GAAG,EAAE,CAAC,CAACC,WAAW,CAAC,CAAC;MAC9DrD,IAAI,EAAE,WAAW;MACjBsD,QAAQ,EAAE;IACZ,CAAC,EACD;MACER,EAAE,EAAE,GAAG;MACPC,IAAI,EAAE,eAAe;MACrBC,KAAK,EAAE,qBAAqB;MAC5BC,WAAW,EAAE,iDAAiD;MAC9DC,SAAS,EAAE,IAAIC,IAAI,CAACA,IAAI,CAACC,GAAG,CAAC,CAAC,GAAG,IAAI,GAAG,EAAE,GAAG,EAAE,CAAC,CAACC,WAAW,CAAC,CAAC;MAC9DC,QAAQ,EAAE;IACZ,CAAC,EACD;MACER,EAAE,EAAE,GAAG;MACPC,IAAI,EAAE,kBAAkB;MACxBC,KAAK,EAAE,kBAAkB;MACzBC,WAAW,EAAE,gDAAgD;MAC7DC,SAAS,EAAE,IAAIC,IAAI,CAACA,IAAI,CAACC,GAAG,CAAC,CAAC,GAAG,IAAI,GAAG,EAAE,GAAG,GAAG,CAAC,CAACC,WAAW,CAAC,CAAC;MAC/DC,QAAQ,EAAE;IACZ,CAAC,EACD;MACER,EAAE,EAAE,GAAG;MACPC,IAAI,EAAE,cAAc;MACpBC,KAAK,EAAE,iBAAiB;MACxBC,WAAW,EAAE,8CAA8C;MAC3DC,SAAS,EAAE,IAAIC,IAAI,CAACA,IAAI,CAACC,GAAG,CAAC,CAAC,GAAG,IAAI,GAAG,EAAE,GAAG,GAAG,CAAC,CAACC,WAAW,CAAC,CAAC;MAC/DrD,IAAI,EAAE,aAAa;MACnBsD,QAAQ,EAAE;IACZ,CAAC,CACF,CAAC;;IAEF;IACAjC,kBAAkB,CAAC,CACjB;MACEyB,EAAE,EAAE,GAAG;MACPS,IAAI,EAAE,gBAAgB;MACtBnD,YAAY,EAAE,CAAC;MACfC,eAAe,EAAE,CAAC;MAClBmD,gBAAgB,EAAE,EAAE;MACpBC,cAAc,EAAE;IAClB,CAAC,EACD;MACEX,EAAE,EAAE,GAAG;MACPS,IAAI,EAAE,YAAY;MAClBnD,YAAY,EAAE,CAAC;MACfC,eAAe,EAAE,CAAC;MAClBmD,gBAAgB,EAAE,EAAE;MACpBC,cAAc,EAAE;IAClB,CAAC,EACD;MACEX,EAAE,EAAE,GAAG;MACPS,IAAI,EAAE,WAAW;MACjBnD,YAAY,EAAE,CAAC;MACfC,eAAe,EAAE,CAAC;MAClBmD,gBAAgB,EAAE,EAAE;MACpBC,cAAc,EAAE;IAClB,CAAC,CACF,CAAC;;IAEF;IACAlC,kBAAkB,CAAC,CACjB;MACEuB,EAAE,EAAE,GAAG;MACPY,aAAa,EAAE,iBAAiB;MAChCC,aAAa,EAAE,CAAC;MAChBC,aAAa,EAAE,EAAE;MACjBC,IAAI,EAAE,QAAQ;MACdC,OAAO,EAAE;IACX,CAAC,EACD;MACEhB,EAAE,EAAE,GAAG;MACPY,aAAa,EAAE,oBAAoB;MACnCC,aAAa,EAAE,CAAC;MAChBC,aAAa,EAAE,EAAE;MACjBC,IAAI,EAAE,QAAQ;MACdC,OAAO,EAAE;IACX,CAAC,EACD;MACEhB,EAAE,EAAE,GAAG;MACPY,aAAa,EAAE,eAAe;MAC9BC,aAAa,EAAE,EAAE;MACjBC,aAAa,EAAE,EAAE;MACjBC,IAAI,EAAE,IAAI;MACVC,OAAO,EAAE;IACX,CAAC,CACF,CAAC;EACJ,CAAC;EACD,MAAMC,cAAc,GAAIC,MAAc,IAAK;IACzC,OAAO,IAAIC,IAAI,CAACC,YAAY,CAAC,OAAO,EAAE;MACpCC,KAAK,EAAE,UAAU;MACjBC,QAAQ,EAAE;IACZ,CAAC,CAAC,CAACC,MAAM,CAACL,MAAM,CAAC;EACnB,CAAC;EAED,MAAMM,cAAc,GAAIC,MAAc,IAAK;IACzC,MAAMC,MAA8B,GAAG;MACrC,UAAU,EAAE,SAAS;MACrB,QAAQ,EAAE,SAAS;MACnB,SAAS,EAAE,SAAS;MACpB,WAAW,EAAE,SAAS;MACtB,IAAI,EAAE,SAAS;MACf,SAAS,EAAE,SAAS;MACpB,WAAW,EAAE,SAAS;MACtB,WAAW,EAAE;IACf,CAAC;IACD,OAAOA,MAAM,CAACD,MAAM,CAAC,IAAI,MAAM;EACjC,CAAC;EAED,MAAME,gBAAgB,GAAInB,QAAgB,IAAK;IAC7C,MAAMkB,MAA8B,GAAG;MACrC,KAAK,EAAE,SAAS;MAChB,QAAQ,EAAE,SAAS;MACnB,QAAQ,EAAE,SAAS;MACnB,MAAM,EAAE;IACV,CAAC;IACD,OAAOA,MAAM,CAAClB,QAAQ,CAAC,IAAI,MAAM;EACnC,CAAC;EAED,IAAI9B,OAAO,EAAE;IACX,oBACE3B,OAAA,CAACT,GAAG;MAACsF,OAAO,EAAC,MAAM;MAACC,cAAc,EAAC,QAAQ;MAACC,UAAU,EAAC,QAAQ;MAACC,SAAS,EAAC,MAAM;MAAAC,QAAA,eAC9EjF,OAAA,CAACT,GAAG;QAAC2F,SAAS,EAAC,QAAQ;QAAAD,QAAA,gBACrBjF,OAAA,CAACL,gBAAgB;UAACwF,IAAI,EAAE;QAAG;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAC9BvF,OAAA,CAACR,UAAU;UAACgG,OAAO,EAAC,IAAI;UAACC,EAAE,EAAE;YAAEC,EAAE,EAAE,CAAC;YAAEC,KAAK,EAAE;UAAiB,CAAE;UAAAV,QAAA,EAAC;QAEjE;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;EAEA,IAAI1D,KAAK,EAAE;IACT,oBACE7B,OAAA,CAACT,GAAG;MAACqG,CAAC,EAAE,CAAE;MAAAX,QAAA,eACRjF,OAAA,CAACN,KAAK;QACJmG,QAAQ,EAAC,OAAO;QAChBC,MAAM,eACJ9F,OAAA,CAACP,MAAM;UAACkG,KAAK,EAAC,SAAS;UAACR,IAAI,EAAC,OAAO;UAACY,OAAO,EAAE5D,iBAAkB;UAAA8C,QAAA,EAAC;QAEjE;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CACT;QAAAN,QAAA,EAEApD;MAAK;QAAAuD,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC;EAEV;EACA,oBACEvF,OAAA;IAAKsE,KAAK,EAAE;MAAE0B,OAAO,EAAE;IAAO,CAAE;IAAAf,QAAA,gBAE9BjF,OAAA;MAAKsE,KAAK,EAAE;QAAE2B,YAAY,EAAE;MAAO,CAAE;MAAAhB,QAAA,eACnCjF,OAAA;QAAKsE,KAAK,EAAE;UAAEO,OAAO,EAAE,MAAM;UAAEC,cAAc,EAAE,eAAe;UAAEC,UAAU,EAAE;QAAS,CAAE;QAAAE,QAAA,gBACrFjF,OAAA;UAAAiF,QAAA,gBACEjF,OAAA;YAAIsE,KAAK,EAAE;cAAE4B,MAAM,EAAE,CAAC;cAAEP,KAAK,EAAE;YAAU,CAAE;YAAAV,QAAA,EAAC;UAE5C;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACLvF,OAAA;YAAGsE,KAAK,EAAE;cAAE4B,MAAM,EAAE,UAAU;cAAEP,KAAK,EAAE;YAAO,CAAE;YAAAV,QAAA,GAAC,gBACjC,EAAC9E,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEgG,UAAU,EAAC,GAAC,EAAChG,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEiG,SAAS,EAAC,IAAE,EAACjG,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEkG,IAAI,EAAC,GAClE;UAAA;YAAAjB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC,eACNvF,OAAA;UAAKsE,KAAK,EAAE;YACVgC,UAAU,EAAEvE,aAAa,CAACE,SAAS,GAAG,SAAS,GAAG,SAAS;YAC3D0D,KAAK,EAAE5D,aAAa,CAACE,SAAS,GAAG,SAAS,GAAG,SAAS;YACtD+D,OAAO,EAAE,aAAa;YACtBO,YAAY,EAAE,MAAM;YACpBC,QAAQ,EAAE,QAAQ;YAClBC,UAAU,EAAE,MAAM;YAClBC,MAAM,EAAE3E,aAAa,CAACE,SAAS,GAAG,mBAAmB,GAAG;UAC1D,CAAE;UAAAgD,QAAA,EACClD,aAAa,CAACE,SAAS,GAAG,cAAc,GAAG;QAAc;UAAAmD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACvD,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNvF,OAAA;MAAKsE,KAAK,EAAE;QACVO,OAAO,EAAE,MAAM;QACf8B,mBAAmB,EAAE,sCAAsC;QAC3DC,GAAG,EAAE,MAAM;QACXX,YAAY,EAAE;MAChB,CAAE;MAAAhB,QAAA,gBACAjF,OAAA;QAAKsE,KAAK,EAAEuC,SAAU;QAAA5B,QAAA,gBACpBjF,OAAA;UAAIsE,KAAK,EAAE;YAAE4B,MAAM,EAAE,cAAc;YAAEP,KAAK,EAAE;UAAO,CAAE;UAAAV,QAAA,EAAC;QAAY;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACvEvF,OAAA;UAAKsE,KAAK,EAAE;YAAEkC,QAAQ,EAAE,MAAM;YAAEC,UAAU,EAAE,MAAM;YAAEd,KAAK,EAAE;UAAU,CAAE;UAAAV,QAAA,EACpE5E,KAAK,CAACE;QAAY;UAAA6E,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChB,CAAC,eACNvF,OAAA;UAAOsE,KAAK,EAAE;YAAEqB,KAAK,EAAE;UAAO,CAAE;UAAAV,QAAA,EAAC;QAAa;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACnD,CAAC,eAENvF,OAAA;QAAKsE,KAAK,EAAEuC,SAAU;QAAA5B,QAAA,gBACpBjF,OAAA;UAAIsE,KAAK,EAAE;YAAE4B,MAAM,EAAE,cAAc;YAAEP,KAAK,EAAE;UAAO,CAAE;UAAAV,QAAA,EAAC;QAAe;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC1EvF,OAAA;UAAKsE,KAAK,EAAE;YAAEkC,QAAQ,EAAE,MAAM;YAAEC,UAAU,EAAE,MAAM;YAAEd,KAAK,EAAE;UAAU,CAAE;UAAAV,QAAA,EACpE5E,KAAK,CAACG;QAAe;UAAA4E,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnB,CAAC,eACNvF,OAAA;UAAOsE,KAAK,EAAE;YAAEqB,KAAK,EAAE;UAAO,CAAE;UAAAV,QAAA,EAAC;QAAc;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACpD,CAAC,eAENvF,OAAA;QAAKsE,KAAK,EAAEuC,SAAU;QAAA5B,QAAA,gBACpBjF,OAAA;UAAIsE,KAAK,EAAE;YAAE4B,MAAM,EAAE,cAAc;YAAEP,KAAK,EAAE;UAAO,CAAE;UAAAV,QAAA,EAAC;QAAU;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACrEvF,OAAA;UAAKsE,KAAK,EAAE;YAAEkC,QAAQ,EAAE,MAAM;YAAEC,UAAU,EAAE,MAAM;YAAEd,KAAK,EAAE;UAAU,CAAE;UAAAV,QAAA,EACpE5E,KAAK,CAACI;QAAU;UAAA2E,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACd,CAAC,eACNvF,OAAA;UAAOsE,KAAK,EAAE;YAAEqB,KAAK,EAAE;UAAO,CAAE;UAAAV,QAAA,EAAC;QAAsB;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC5D,CAAC,eAENvF,OAAA;QAAKsE,KAAK,EAAEuC,SAAU;QAAA5B,QAAA,gBACpBjF,OAAA;UAAIsE,KAAK,EAAE;YAAE4B,MAAM,EAAE,cAAc;YAAEP,KAAK,EAAE;UAAO,CAAE;UAAAV,QAAA,EAAC;QAAa;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACxEvF,OAAA;UAAKsE,KAAK,EAAE;YAAEkC,QAAQ,EAAE,MAAM;YAAEC,UAAU,EAAE,MAAM;YAAEd,KAAK,EAAE;UAAU,CAAE;UAAAV,QAAA,EACpEf,cAAc,CAAC7D,KAAK,CAACK,aAAa;QAAC;UAAA0E,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjC,CAAC,eACNvF,OAAA;UAAOsE,KAAK,EAAE;YAAEqB,KAAK,EAAE;UAAO,CAAE;UAAAV,QAAA,EAAC;QAAc;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACpD,CAAC,EAELlF,KAAK,CAACM,aAAa,GAAG,CAAC,iBACtBX,OAAA;QAAKsE,KAAK,EAAE;UAAE,GAAGuC,SAAS;UAAEC,UAAU,EAAE;QAAoB,CAAE;QAAA7B,QAAA,gBAC5DjF,OAAA;UAAIsE,KAAK,EAAE;YAAE4B,MAAM,EAAE,cAAc;YAAEP,KAAK,EAAE;UAAO,CAAE;UAAAV,QAAA,EAAC;QAAa;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACxEvF,OAAA;UAAKsE,KAAK,EAAE;YAAEkC,QAAQ,EAAE,MAAM;YAAEC,UAAU,EAAE,MAAM;YAAEd,KAAK,EAAE;UAAU,CAAE;UAAAV,QAAA,EACpE5E,KAAK,CAACM;QAAa;UAAAyE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjB,CAAC,eACNvF,OAAA;UAAOsE,KAAK,EAAE;YAAEqB,KAAK,EAAE;UAAO,CAAE;UAAAV,QAAA,EAAC;QAA0B;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAChE,CACN;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC,eAENvF,OAAA;MAAAiF,QAAA,gBACEjF,OAAA;QAAIsE,KAAK,EAAE;UAAE2B,YAAY,EAAE,MAAM;UAAEN,KAAK,EAAE;QAAO,CAAE;QAAAV,QAAA,EAAC;MAAY;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,EACpEpE,WAAW,CAAC4F,MAAM,KAAK,CAAC,gBACvB/G,OAAA;QAAKsE,KAAK,EAAEuC,SAAU;QAAA5B,QAAA,eACpBjF,OAAA;UAAGsE,KAAK,EAAE;YAAE4B,MAAM,EAAE,CAAC;YAAEP,KAAK,EAAE,MAAM;YAAET,SAAS,EAAE;UAAS,CAAE;UAAAD,QAAA,EAAC;QAE7D;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC,GAENpE,WAAW,CAAC6F,GAAG,CAAEC,KAAK,iBACpBjH,OAAA;QAAoBsE,KAAK,EAAE;UACzB,GAAGuC,SAAS;UACZZ,YAAY,EAAE,MAAM;UACpBa,UAAU,EAAE,aAAarC,cAAc,CAACwC,KAAK,CAACvC,MAAM,CAAC;QACvD,CAAE;QAAAO,QAAA,gBACAjF,OAAA;UAAKsE,KAAK,EAAE;YACVO,OAAO,EAAE,MAAM;YACfC,cAAc,EAAE,eAAe;YAC/BC,UAAU,EAAE,YAAY;YACxBkB,YAAY,EAAE;UAChB,CAAE;UAAAhB,QAAA,gBACAjF,OAAA;YAAAiF,QAAA,gBACEjF,OAAA;cAAIsE,KAAK,EAAE;gBAAE4B,MAAM,EAAE,eAAe;gBAAEP,KAAK,EAAE;cAAO,CAAE;cAAAV,QAAA,GAAC,OAChD,EAACgC,KAAK,CAAChE,EAAE;YAAA;cAAAmC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACZ,CAAC,eACLvF,OAAA;cAAGsE,KAAK,EAAE;gBAAE4B,MAAM,EAAE,CAAC;gBAAEP,KAAK,EAAE;cAAO,CAAE;cAAAV,QAAA,gBACrCjF,OAAA;gBAAAiF,QAAA,EAAQ;cAAQ;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,KAAC,EAAC0B,KAAK,CAACC,YAAY;YAAA;cAAA9B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC5C,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CAAC,eACNvF,OAAA;YAAKsE,KAAK,EAAE;cAAEY,SAAS,EAAE;YAAQ,CAAE;YAAAD,QAAA,eACjCjF,OAAA;cAAMsE,KAAK,EAAE;gBACXgC,UAAU,EAAE1B,gBAAgB,CAACqC,KAAK,CAACxD,QAAQ,CAAC;gBAC5CkC,KAAK,EAAE,OAAO;gBACdK,OAAO,EAAE,gBAAgB;gBACzBO,YAAY,EAAE,KAAK;gBACnBC,QAAQ,EAAE,QAAQ;gBAClBW,aAAa,EAAE;cACjB,CAAE;cAAAlC,QAAA,EACCgC,KAAK,CAACxD;YAAQ;cAAA2B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACX;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAENvF,OAAA;UAAKsE,KAAK,EAAE;YACVO,OAAO,EAAE,MAAM;YACf8B,mBAAmB,EAAE,sCAAsC;YAC3DC,GAAG,EAAE,MAAM;YACXjB,KAAK,EAAE,MAAM;YACba,QAAQ,EAAE;UACZ,CAAE;UAAAvB,QAAA,gBACAjF,OAAA;YAAAiF,QAAA,gBACEjF,OAAA;cAAAiF,QAAA,EAAQ;YAAK;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,KAAC,EAAC0B,KAAK,CAACG,YAAY;UAAA;YAAAhC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACvC,CAAC,eACNvF,OAAA;YAAAiF,QAAA,gBACEjF,OAAA;cAAAiF,QAAA,EAAQ;YAAM;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,MAAE,EAAC0B,KAAK,CAACI,YAAY;UAAA;YAAAjC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACzC,CAAC,eACNvF,OAAA;YAAAiF,QAAA,gBACEjF,OAAA;cAAAiF,QAAA,EAAQ;YAAO;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,EAAC,GAAG,eAC5BvF,OAAA;cAAMsE,KAAK,EAAE;gBAAEqB,KAAK,EAAElB,cAAc,CAACwC,KAAK,CAACvC,MAAM;cAAE,CAAE;cAAAO,QAAA,EAClDgC,KAAK,CAACK;YAAa;cAAAlC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChB,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ,CAAC,eACNvF,OAAA;YAAAiF,QAAA,gBACEjF,OAAA;cAAAiF,QAAA,EAAQ;YAAI;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,EAAC,GAAG,EACxB0B,KAAK,CAACM,QAAQ,gBACbvH,OAAA;cAAMsE,KAAK,EAAE;gBACXqB,KAAK,EAAEsB,KAAK,CAACO,UAAU,GAAG,SAAS,GAAG;cACxC,CAAE;cAAAvC,QAAA,EACCgC,KAAK,CAACQ,cAAc,KAAK,IAAI,IAAIR,KAAK,CAACQ,cAAc,KAAKC,SAAS,GAClET,KAAK,CAACQ,cAAc,IAAI,CAAC,GACvB,GAAGR,KAAK,CAACQ,cAAc,OAAO,GAC9B,GAAGE,IAAI,CAACC,GAAG,CAACX,KAAK,CAACQ,cAAc,CAAC,eAAe,GAChD;YAAO;cAAArC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACP,CAAC,GAEP,SACD;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA,GApEE0B,KAAK,CAAChE,EAAE;QAAAmC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAqEb,CACN,CACF;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC,eAENvF,OAAA;MAAKsE,KAAK,EAAE;QACVuD,SAAS,EAAE,MAAM;QACjB7B,OAAO,EAAE,MAAM;QACfM,UAAU,EAAEvE,aAAa,CAACE,SAAS,GAAG,SAAS,GAAG,SAAS;QAC3DsE,YAAY,EAAE,KAAK;QACnBG,MAAM,EAAE3E,aAAa,CAACE,SAAS,GAAG,mBAAmB,GAAG;MAC1D,CAAE;MAAAgD,QAAA,gBACAjF,OAAA;QAAIsE,KAAK,EAAE;UACTqB,KAAK,EAAE5D,aAAa,CAACE,SAAS,GAAG,SAAS,GAAG,SAAS;UACtDiE,MAAM,EAAE;QACV,CAAE;QAAAjB,QAAA,GACClD,aAAa,CAACE,SAAS,GAAG,GAAG,GAAG,IAAI,EAAC,mBAAiB,EAACF,aAAa,CAACG,IAAI,KAAK,MAAM,GAAG,WAAW,GAAG,WAAW;MAAA;QAAAkD,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC/G,CAAC,eACLvF,OAAA;QAAGsE,KAAK,EAAE;UACR4B,MAAM,EAAE,CAAC;UACTP,KAAK,EAAE5D,aAAa,CAACE,SAAS,GAAG,SAAS,GAAG;QAC/C,CAAE;QAAAgD,QAAA,EACClD,aAAa,CAACE,SAAS,GACpB,gIAAgI,GAChI;MAA4F;QAAAmD,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAE/F,CAAC,EACH,CAACxD,aAAa,CAACE,SAAS,iBACvBjC,OAAA;QACE+F,OAAO,EAAE5D,iBAAkB;QAC3BmC,KAAK,EAAE;UACLuD,SAAS,EAAE,QAAQ;UACnBvB,UAAU,EAAE,SAAS;UACrBX,KAAK,EAAE,OAAO;UACde,MAAM,EAAE,MAAM;UACdV,OAAO,EAAE,aAAa;UACtBO,YAAY,EAAE,KAAK;UACnBuB,MAAM,EAAE;QACV,CAAE;QAAA7C,QAAA,EACH;MAED;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CACT;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;;AAED;AAAArF,EAAA,CAnbMD,SAAmB;EAAA,QACNH,OAAO,EACVF,QAAQ;AAAA;AAAAmI,EAAA,GAFlB9H,SAAmB;AAobzB,MAAM4G,SAA8B,GAAG;EACrCP,UAAU,EAAE,MAAM;EAClBN,OAAO,EAAE,QAAQ;EACjBO,YAAY,EAAE,KAAK;EACnByB,SAAS,EAAE,2BAA2B;EACtCtB,MAAM,EAAE;AACV,CAAC;AAED,eAAezG,SAAS;AAAC,IAAA8H,EAAA;AAAAE,YAAA,CAAAF,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}