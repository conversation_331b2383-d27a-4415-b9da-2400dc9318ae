#!/usr/bin/env python
"""Check what's happening with DEBUG setting."""
import os
import sys

print("=== Environment Check ===")
print(f"DJANGO_SETTINGS_MODULE: {os.environ.get('DJANGO_SETTINGS_MODULE', 'Not set')}")
print(f"DEBUG env var: {os.environ.get('DEBUG', 'Not set')}")

# Set the Django settings module
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'dentflow_project.settings')

print(f"After setdefault - DJANGO_SETTINGS_MODULE: {os.environ.get('DJANGO_SETTINGS_MODULE')}")

# Import the settings module directly
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

try:
    from dentflow_project import settings as settings_module
    print(f"Direct import - DEBUG: {getattr(settings_module, 'DEBUG', 'Not found')}")
    print(f"Direct import - ALLOWED_HOSTS: {getattr(settings_module, 'ALLOWED_HOSTS', 'Not found')}")
except Exception as e:
    print(f"Error importing settings module directly: {e}")
    import traceback
    traceback.print_exc()

print("\n=== Django Setup ===")
try:
    import django
    django.setup()
    
    from django.conf import settings
    print(f"Django settings - DEBUG: {settings.DEBUG}")
    print(f"Django settings - ALLOWED_HOSTS: {settings.ALLOWED_HOSTS}")
    
except Exception as e:
    print(f"Error with Django setup: {e}")
    import traceback
    traceback.print_exc()
