"""
Django management command to initialize DentFlow system
Sets up default data, workflows, and configurations
"""

from django.core.management.base import BaseCommand
from django.db import transaction
from apps.tenants.models import Tenant
from apps.cases.models import Clinic
from apps.users.models import User
import uuid


class Command(BaseCommand):
    help = 'Initialize DentFlow system with default data'
    
    def add_arguments(self, parser):
        parser.add_argument(
            '--tenant-name',
            type=str,
            default='Demo Lab',
            help='Name of the demo tenant to create'
        )
        parser.add_argument(
            '--subdomain',
            type=str,
            default='demo',
            help='Subdomain for the tenant'
        )
        parser.add_argument(
            '--admin-email',
            type=str,
            default='<EMAIL>',
            help='Email for the admin user'
        )
        parser.add_argument(
            '--admin-password',
            type=str,
            default='admin123',
            help='Password for the admin user'
        )
    
    def handle(self, *args, **options):
        """Initialize system with demo data"""
        
        try:
            with transaction.atomic():
                # Create demo tenant
                tenant = self._create_tenant(
                    options['tenant_name'],
                    options['subdomain']
                )
                
                # Create admin user
                admin_user = self._create_admin_user(
                    tenant,
                    options['admin_email'],
                    options['admin_password']
                )
                
                # Create demo clinics
                clinics = self._create_demo_clinics(tenant)
                
                # Create demo users
                demo_users = self._create_demo_users(tenant, clinics)
                
                self.stdout.write(
                    self.style.SUCCESS(
                        f'Successfully initialized DentFlow with:\n'
                        f'- Tenant: {tenant.name}\n'
                        f'- Admin: {admin_user.email}\n'
                        f'- Clinics: {len(clinics)}\n'
                        f'- Users: {len(demo_users) + 1}'
                    )
                )
                
        except Exception as e:
            self.stdout.write(
                self.style.ERROR(f'Failed to initialize system: {e}')
            )
            raise
    
    def _create_tenant(self, name, subdomain):
        """Create demo tenant"""
        tenant, created = Tenant.objects.get_or_create(
            subdomain=subdomain,
            defaults={
                'name': name,
                'is_active': True
            }
        )
        
        if created:
            self.stdout.write(f'Created tenant: {name}')
        else:
            self.stdout.write(f'Using existing tenant: {name}')
        
        return tenant
    
    def _create_admin_user(self, tenant, email, password):
        """Create admin user"""
        admin_user, created = User.objects.get_or_create(
            email=email,
            defaults={
                'username': email,
                'role': 'admin',
                'tenant': tenant,
                'is_staff': True,
                'is_superuser': True,
                'first_name': 'Admin',
                'last_name': 'User'
            }
        )
        
        if created:
            admin_user.set_password(password)
            admin_user.save()
            self.stdout.write(f'Created admin user: {email}')
        else:
            self.stdout.write(f'Using existing admin user: {email}')
        
        return admin_user
    
    def _create_demo_clinics(self, tenant):
        """Create demo clinics"""
        clinics_data = [
            {
                'name': 'Smile Dental Clinic',
                'email': '<EMAIL>',
                'phone': '******-0101',
                'address': '123 Main St, Dental City, DC 12345'
            },
            {
                'name': 'Perfect Teeth Center',
                'email': '<EMAIL>',
                'phone': '******-0102',
                'address': '456 Oak Ave, Dental City, DC 12346'
            },
            {
                'name': 'Family Dental Practice',
                'email': '<EMAIL>',
                'phone': '******-0103',
                'address': '789 Pine Rd, Dental City, DC 12347'
            }
        ]
        
        clinics = []
        for clinic_data in clinics_data:
            clinic, created = Clinic.objects.get_or_create(
                tenant=tenant,
                name=clinic_data['name'],
                defaults=clinic_data
            )
            clinics.append(clinic)
            
            if created:
                self.stdout.write(f'Created clinic: {clinic_data["name"]}')
        
        return clinics
    
    def _create_demo_users(self, tenant, clinics):
        """Create demo users"""
        users_data = [
            {
                'email': '<EMAIL>',
                'username': 'receptionist',
                'role': 'receptionist',
                'first_name': 'Sarah',
                'last_name': 'Johnson',
                'password': 'receptionist123'
            },
            {
                'email': '<EMAIL>',
                'username': 'tech1',
                'role': 'technician',
                'first_name': 'Mike',
                'last_name': 'Smith',
                'password': 'tech123'
            },
            {
                'email': '<EMAIL>',
                'username': 'tech2',
                'role': 'technician',
                'first_name': 'Lisa',
                'last_name': 'Davis',
                'password': 'tech123'
            },
            {
                'email': '<EMAIL>',
                'username': 'dentist1',
                'role': 'dentist',
                'first_name': 'Dr. John',
                'last_name': 'Wilson',
                'password': 'dentist123',
                'default_clinic': clinics[0] if clinics else None
            },
            {
                'email': '<EMAIL>',
                'username': 'dentist2',
                'role': 'dentist',
                'first_name': 'Dr. Maria',
                'last_name': 'Garcia',
                'password': 'dentist123',
                'default_clinic': clinics[1] if len(clinics) > 1 else None
            }
        ]
        
        created_users = []
        for user_data in users_data:
            default_clinic = user_data.pop('default_clinic', None)
            password = user_data.pop('password')
            
            user, created = User.objects.get_or_create(
                email=user_data['email'],
                defaults={
                    **user_data,
                    'tenant': tenant,
                    'default_clinic': default_clinic
                }
            )
            
            if created:
                user.set_password(password)
                user.save()
                created_users.append(user)
                self.stdout.write(f'Created user: {user_data["email"]} ({user_data["role"]})')
        
        return created_users