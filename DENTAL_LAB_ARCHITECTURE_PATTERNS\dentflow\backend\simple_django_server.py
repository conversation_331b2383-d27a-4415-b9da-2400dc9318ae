#!/usr/bin/env python3
"""
Simple Django server with basic API endpoints
"""

import os
import sys
import json
from http.server import HTTPServer, BaseHTTPRequestHandler
from urllib.parse import urlparse, parse_qs
import jwt
from datetime import datetime, timedelta

# In-memory storage for development
users = {
    '<EMAIL>': {
        'id': 1,
        'username': 'admin',
        'email': '<EMAIL>',
        'password': 'admin123',  # In real app, this would be hashed
        'first_name': 'Admin',
        'last_name': 'User',
        'phone': '+**********',
        'profile_picture': None,
        'created_at': '2025-01-01T00:00:00Z',
        'updated_at': '2025-01-01T00:00:00Z',
        'last_login_ip': '127.0.0.1'
    }
}

cases = [
    {
        'id': 1,
        'case_number': 'LAB-2025-001',
        'patient_name': '<PERSON>',
        'clinic_name': 'Dental Clinic A',
        'case_type': 'Crown',
        'priority': 'normal',
        'status': 'in_progress',
        'due_date': '2025-02-01',
        'created_at': '2025-01-15T10:00:00Z',
        'updated_at': '2025-01-15T10:00:00Z'
    },
    {
        'id': 2,
        'case_number': 'LAB-2025-002',
        'patient_name': 'Jane Smith',
        'clinic_name': 'Dental Clinic B',
        'case_type': 'Bridge',
        'priority': 'high',
        'status': 'pending',
        'due_date': '2025-01-30',
        'created_at': '2025-01-16T14:30:00Z',
        'updated_at': '2025-01-16T14:30:00Z'
    }
]

SECRET_KEY = 'dev-secret-key-change-in-production-django-dentflow-2025'

class DentFlowHandler(BaseHTTPRequestHandler):
    def do_OPTIONS(self):
        """Handle CORS preflight requests"""
        self.send_response(200)
        self.send_header('Access-Control-Allow-Origin', '*')
        self.send_header('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS')
        self.send_header('Access-Control-Allow-Headers', 'Content-Type, Authorization')
        self.send_header('Access-Control-Max-Age', '86400')
        self.end_headers()

    def do_GET(self):
        """Handle GET requests"""
        parsed_path = urlparse(self.path)
        path = parsed_path.path
        
        # CORS headers
        self.send_header('Access-Control-Allow-Origin', '*')
        self.send_header('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS')
        self.send_header('Access-Control-Allow-Headers', 'Content-Type, Authorization')
        
        if path == '/health/':
            self.send_response(200)
            self.send_header('Content-Type', 'application/json')
            self.end_headers()
            response = {'status': 'healthy', 'service': 'DentFlow Django API', 'timestamp': datetime.now().isoformat()}
            self.wfile.write(json.dumps(response).encode())
            
        elif path == '/api/v1/cases/':
            # Get JWT token from Authorization header
            auth_header = self.headers.get('Authorization')
            if not auth_header or not auth_header.startswith('Bearer '):
                self.send_response(401)
                self.send_header('Content-Type', 'application/json')
                self.end_headers()
                self.wfile.write(json.dumps({'error': 'Authentication required'}).encode())
                return
            
            self.send_response(200)
            self.send_header('Content-Type', 'application/json')
            self.end_headers()
            self.wfile.write(json.dumps(cases).encode())
            
        else:
            self.send_response(404)
            self.send_header('Content-Type', 'application/json')
            self.end_headers()
            self.wfile.write(json.dumps({'error': 'Not found'}).encode())

    def do_POST(self):
        """Handle POST requests"""
        parsed_path = urlparse(self.path)
        path = parsed_path.path
        
        # CORS headers
        self.send_header('Access-Control-Allow-Origin', '*')
        self.send_header('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS')
        self.send_header('Access-Control-Allow-Headers', 'Content-Type, Authorization')
        
        if path == '/api/auth/login/':
            content_length = int(self.headers['Content-Length'])
            post_data = self.rfile.read(content_length)
            
            try:
                data = json.loads(post_data.decode())
                email = data.get('email')
                password = data.get('password')
                
                # Check credentials
                user = users.get(email)
                if user and user['password'] == password:
                    # Generate JWT token
                    payload = {
                        'user_id': user['id'],
                        'email': user['email'],
                        'exp': datetime.utcnow() + timedelta(hours=24)
                    }
                    token = jwt.encode(payload, SECRET_KEY, algorithm='HS256')
                    
                    self.send_response(200)
                    self.send_header('Content-Type', 'application/json')
                    self.end_headers()
                    
                    response = {
                        'access': token,
                        'refresh': token,  # Simplified for development
                        'user': {
                            'id': user['id'],
                            'username': user['username'],
                            'email': user['email'],
                            'first_name': user['first_name'],
                            'last_name': user['last_name'],
                            'phone': user['phone'],
                            'profile_picture': user['profile_picture'],
                            'created_at': user['created_at'],
                            'updated_at': user['updated_at'],
                            'last_login_ip': user['last_login_ip']
                        }
                    }
                    self.wfile.write(json.dumps(response).encode())
                else:
                    self.send_response(401)
                    self.send_header('Content-Type', 'application/json')
                    self.end_headers()
                    self.wfile.write(json.dumps({'error': 'Invalid credentials'}).encode())
                    
            except json.JSONDecodeError:
                self.send_response(400)
                self.send_header('Content-Type', 'application/json')
                self.end_headers()
                self.wfile.write(json.dumps({'error': 'Invalid JSON'}).encode())
        else:
            self.send_response(404)
            self.send_header('Content-Type', 'application/json')
            self.end_headers()
            self.wfile.write(json.dumps({'error': 'Not found'}).encode())

def run_server():
    server_address = ('127.0.0.1', 9000)
    httpd = HTTPServer(server_address, DentFlowHandler)
    print(f"🚀 DentFlow API Server running on http://127.0.0.1:9000")
    print("📋 Available endpoints:")
    print("  - GET  /health/")
    print("  - POST /api/auth/login/")
    print("  - GET  /api/v1/cases/")
    print("🔑 Login credentials: <EMAIL> / admin123")
    print("⏹️  Press Ctrl+C to stop")
    
    try:
        httpd.serve_forever()
    except KeyboardInterrupt:
        print("\n🛑 Server stopped")
        httpd.server_close()

if __name__ == "__main__":
    run_server()
