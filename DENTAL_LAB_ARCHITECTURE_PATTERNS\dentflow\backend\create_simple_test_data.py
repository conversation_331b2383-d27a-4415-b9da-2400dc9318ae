#!/usr/bin/env python
"""
Simple test data creation script for DentFlow
"""
import os
import sys
import django
from datetime import datetime, timedelta
from django.utils import timezone

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'dentflow_project.settings')
django.setup()

from apps.tenants.models import Tenant
from apps.cases.models import Clinic, Case

def create_test_data():
    print("🚀 Creating simple test data...")
    
    # Create tenant
    tenant, created = Tenant.objects.get_or_create(
        subdomain='demo',
        defaults={
            'name': 'Demo Dental Laboratory',
            'contact_email': '<EMAIL>',
            'phone': '******-0123',
            'address': '123 Dental Street, Lab City, LC 12345',
            'plan': 'professional',
            'max_users': 10,
            'max_cases_per_month': 100,
            'is_active': True
        }
    )
    print(f"✅ Tenant: {'created' if created else 'verified'}")
    
    # Create clinics
    clinics_data = [
        {
            'name': 'Smile Dental Clinic',
            'email': '<EMAIL>',
            'phone': '******-0123',
            'address': '123 Main St, Downtown, NY 10001'
        },
        {
            'name': 'City Dental Care',
            'email': '<EMAIL>',
            'phone': '******-0456',
            'address': '456 Oak Ave, Midtown, NY 10002'
        },
        {
            'name': 'Family Dentistry Plus',
            'email': '<EMAIL>',
            'phone': '******-0789',
            'address': '789 Pine St, Uptown, NY 10003'
        }
    ]
    
    clinics = []
    for clinic_data in clinics_data:
        clinic, created = Clinic.objects.get_or_create(
            tenant=tenant,
            name=clinic_data['name'],
            defaults=clinic_data
        )
        clinics.append(clinic)
        print(f"✅ Clinic {clinic.name}: {'created' if created else 'verified'}")
    
    # Create test cases
    print("📋 Creating test cases...")
    
    test_cases = [
        {
            'clinic': clinics[0],
            'patient_name': 'John Doe',
            'tooth_number': '11',
            'service_type': 'crown',
            'priority': 'normal',
            'status': 'received'
        },
        {
            'clinic': clinics[1],
            'patient_name': 'Jane Smith',
            'tooth_number': '16',
            'service_type': 'crown',
            'priority': 'urgent',
            'status': 'design'
        },
        {
            'clinic': clinics[0],
            'patient_name': 'Bob Johnson',
            'tooth_number': '14-16',
            'service_type': 'bridge',
            'priority': 'normal',
            'status': 'milling'
        },
        {
            'clinic': clinics[2],
            'patient_name': 'Alice Brown',
            'tooth_number': '21-23',
            'service_type': 'veneer',
            'priority': 'stat',
            'status': 'qc'
        },
        {
            'clinic': clinics[1],
            'patient_name': 'Charlie Wilson',
            'tooth_number': '36',
            'service_type': 'implant',
            'priority': 'normal',
            'status': 'shipped'
        }
    ]
    
    created_cases = []
    for i, case_data in enumerate(test_cases):
        try:
            case_id = f"LAB-2024-{str(i+1).zfill(4)}"
            
            case, created = Case.objects.get_or_create(
                id=case_id,
                defaults={
                    'tenant': tenant,
                    'clinic': case_data['clinic'],
                    'patient_name': case_data['patient_name'],
                    'tooth_number': case_data['tooth_number'],
                    'service_type': case_data['service_type'],
                    'priority': case_data['priority'],
                    'status': case_data['status'],
                    'current_stage_index': 0,
                    'workflow_stages': [],
                    'notes': [],
                    'files': [],
                    'due_date': timezone.now() + timedelta(days=7)
                }
            )
            
            if created:
                created_cases.append(case_id)
                print(f"✅ Case {case_id}: {case_data['patient_name']} - {case_data['service_type']}")
            else:
                print(f"ℹ️ Case {case_id} already exists")
            
        except Exception as e:
            print(f"❌ Error creating case for {case_data['patient_name']}: {e}")
    
    print(f"\n🎉 Test data creation complete!")
    print(f"   - Tenant: {tenant.name}")
    print(f"   - Clinics: {len(clinics)}")
    print(f"   - Cases: {len(created_cases)}")
    
    return tenant, clinics, created_cases

if __name__ == '__main__':
    create_test_data()
