/**
 * Authentication Context Provider
 * Manages authentication state and user data
 */

import React, { createContext, useContext, useEffect, ReactNode } from 'react';
import { useCurrentUser, useLogin, useLogout } from '../hooks/api';
import { AuthUser } from '../api/types';
import { DentFlowAPI } from '../api';

interface AuthContextValue {
  user: AuthUser | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  login: (email: string, password: string) => Promise<void>;
  logout: () => Promise<void>;
  refetchUser: () => void;
}

const AuthContext = createContext<AuthContextValue | undefined>(undefined);

interface AuthProviderProps {
  children: ReactNode;
}

export function AuthProvider({ children }: AuthProviderProps) {
  const [initialCheckDone, setInitialCheckDone] = React.useState(false);

  const {
    data: user,
    isLoading: userLoading,
    refetch: refetchUser,
    error: userError,
  } = useCurrentUser({
    retry: false,
    refetchOnWindowFocus: false,
    enabled: DentFlowAPI.auth.isAuthenticated(),
  });

  const loginMutation = useLogin({
    onSuccess: () => {
      refetchUser();
    },
    onError: (error) => {
      console.error('Login failed:', error);
    },
  });

  const logoutMutation = useLogout({
    onSuccess: () => {
      // Redirect will be handled by the logout function
    },
  });

  // Check initial authentication state
  React.useEffect(() => {
    const checkInitialAuth = () => {
      // If no token exists, we're not authenticated and not loading
      if (!DentFlowAPI.auth.isAuthenticated()) {
        setInitialCheckDone(true);
      }
    };

    checkInitialAuth();
  }, []);

  // Set initial check done when user query completes (success or error)
  React.useEffect(() => {
    if (!userLoading && (user !== undefined || userError)) {
      setInitialCheckDone(true);
    }
  }, [userLoading, user, userError]);

  const isAuthenticated = DentFlowAPI.auth.isAuthenticated() && !!user && !userError;
  const isLoading = !initialCheckDone || (DentFlowAPI.auth.isAuthenticated() && userLoading);

  // Debug logging
  console.log('AuthProvider state:', {
    initialCheckDone,
    hasToken: DentFlowAPI.auth.isAuthenticated(),
    user: !!user,
    userError: !!userError,
    userLoading,
    isAuthenticated,
    isLoading
  });

  const login = async (email: string, password: string) => {
    try {
      await loginMutation.mutateAsync({ email, password });
    } catch (error: any) {
      throw new Error(error.message || 'Login failed');
    }
  };

  const logout = async () => {
    try {
      await logoutMutation.mutateAsync();
    } catch (error) {
      console.warn('Logout API call failed, but clearing local state');
    }
  };

  // Initialize API when component mounts
  useEffect(() => {
    DentFlowAPI.initialize();
  }, []);

  const value: AuthContextValue = {
    user: (user && Object.keys(user).length > 0) ? user as AuthUser : null,
    isAuthenticated,
    isLoading: isLoading || loginMutation.isPending || logoutMutation.isPending,
    login,
    logout,
    refetchUser,
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
}

export function useAuth() {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
}

// HOC for protecting routes
export function withAuth<P extends object>(Component: React.ComponentType<P>) {
  return function AuthenticatedComponent(props: P) {
    const { isAuthenticated, isLoading } = useAuth();

    if (isLoading) {
      return <div>Loading...</div>; // Replace with your loading component
    }

    if (!isAuthenticated) {
      // Redirect to login will be handled by route protection
      return null;
    }

    return <Component {...props} />;
  };
}
