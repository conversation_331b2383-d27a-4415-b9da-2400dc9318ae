name: DentFlow CI/CD Pipeline

on:
  push:
    branches: [main, develop]
  pull_request:
    branches: [main]
  release:
    types: [published]

env:
  REGISTRY: ghcr.io
  IMAGE_NAME: dentflow

jobs:
  # Security and Quality Checks
  security-scan:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Run Trivy vulnerability scanner
        uses: aquasecurity/trivy-action@master
        with:
          scan-type: 'fs'
          scan-ref: '.'
          format: 'sarif'
          output: 'trivy-results.sarif'

      - name: Upload Trivy scan results
        uses: github/codeql-action/upload-sarif@v2
        with:
          sarif_file: 'trivy-results.sarif'

      - name: Setup Python for security scan
        uses: actions/setup-python@v4
        with:
          python-version: '3.11'

      - name: Install security tools
        run: |
          pip install bandit safety semgrep

      - name: Run Bandit security scan
        run: |
          bandit -r backend/ -f json -o bandit-report.json
        continue-on-error: true

      - name: Run Safety dependency check
        run: |
          cd backend && safety check --json --output safety-report.json
        continue-on-error: true

      - name: Run Semgrep security scan
        run: |
          semgrep --config=auto backend/ --json --output=semgrep-report.json
        continue-on-error: true

  # Backend Testing
  backend-tests:
    runs-on: ubuntu-latest
    services:
      postgres:
        image: postgres:15
        env:
          POSTGRES_USER: dentflow_test
          POSTGRES_PASSWORD: test_password
          POSTGRES_DB: dentflow_test
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 5432:5432

      redis:
        image: redis:7
        options: >-
          --health-cmd "redis-cli ping"
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 6379:6379

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Python
        uses: actions/setup-python@v4
        with:
          python-version: '3.11'
          cache: 'pip'

      - name: Install backend dependencies
        run: |
          cd backend
          pip install -r requirements.txt
          pip install -r requirements-dev.txt
          pip install coverage pytest pytest-django pytest-xdist

      - name: Run Django system checks
        run: |
          cd backend
          python manage.py check --settings=dentflow_project.settings.test

      - name: Run database migrations
        run: |
          cd backend
          python manage.py migrate --settings=dentflow_project.settings.test

      - name: Run unit tests with coverage
        run: |
          cd backend
          coverage run --source='.' manage.py test --settings=dentflow_project.settings.test
          coverage xml

      - name: Run integration tests
        run: |
          cd backend
          python -m pytest tests/integration/ -v --tb=short

      - name: Upload coverage to Codecov
        uses: codecov/codecov-action@v3
        with:
          file: ./backend/coverage.xml
          flags: backend
          name: backend-coverage
  # Frontend Testing
  frontend-tests:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '18'
          cache: 'npm'
          cache-dependency-path: frontend/package-lock.json

      - name: Install frontend dependencies
        run: |
          cd frontend
          npm ci

      - name: Run ESLint
        run: |
          cd frontend
          npm run lint

      - name: Run TypeScript type checking
        run: |
          cd frontend
          npm run type-check

      - name: Run unit tests with coverage
        run: |
          cd frontend
          npm test -- --coverage --watchAll=false --testResultsProcessor=jest-sonar-reporter

      - name: Upload frontend coverage
        uses: codecov/codecov-action@v3
        with:
          directory: ./frontend/coverage
          flags: frontend
          name: frontend-coverage

      - name: Build frontend
        run: |
          cd frontend
          npm run build

      - name: Upload build artifacts
        uses: actions/upload-artifact@v3
        with:
          name: frontend-build
          path: frontend/build/

  # End-to-End Testing
  e2e-tests:
    runs-on: ubuntu-latest
    needs: [backend-tests, frontend-tests]
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Docker Compose
        run: |
          docker-compose -f docker-compose.test.yml up -d
          sleep 30  # Wait for services to be ready

      - name: Run database migrations
        run: |
          docker-compose -f docker-compose.test.yml exec -T backend python manage.py migrate

      - name: Create test data
        run: |
          docker-compose -f docker-compose.test.yml exec -T backend python manage.py create_test_data

      - name: Setup Node.js for E2E tests
        uses: actions/setup-node@v4
        with:
          node-version: '18'

      - name: Install Playwright
        run: |
          cd e2e-tests
          npm ci
          npx playwright install

      - name: Run E2E tests
        run: |
          cd e2e-tests
          npx playwright test

      - name: Upload E2E test results
        uses: actions/upload-artifact@v3
        if: always()
        with:
          name: e2e-test-results
          path: e2e-tests/test-results/

      - name: Cleanup
        if: always()
        run: |
          docker-compose -f docker-compose.test.yml down -v

  # Performance Testing
  performance-tests:
    runs-on: ubuntu-latest
    needs: [e2e-tests]
    if: github.ref == 'refs/heads/main' || github.event_name == 'release'
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup K6
        run: |
          sudo apt-key adv --keyserver hkp://keyserver.ubuntu.com:80 --recv-keys C5AD17C747E3415A3642D57D77C6C491D6AC1D69
          echo "deb https://dl.k6.io/deb stable main" | sudo tee /etc/apt/sources.list.d/k6.list
          sudo apt-get update
          sudo apt-get install k6

      - name: Start test environment
        run: |
          docker-compose -f docker-compose.test.yml up -d
          sleep 60  # Wait for full startup

      - name: Run load tests
        run: |
          cd performance-tests
          k6 run load-test.js

      - name: Upload performance results
        uses: actions/upload-artifact@v3
        with:
          name: performance-results
          path: performance-tests/results/

  # Build and Push Docker Images
  build-images:
    runs-on: ubuntu-latest
    needs: [security-scan, backend-tests, frontend-tests]
    permissions:
      contents: read
      packages: write
    outputs:
      backend-image: ${{ steps.meta-backend.outputs.tags }}
      frontend-image: ${{ steps.meta-frontend.outputs.tags }}
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Log in to Container Registry
        uses: docker/login-action@v2
        with:
          registry: ${{ env.REGISTRY }}
          username: ${{ github.actor }}
          password: ${{ secrets.GITHUB_TOKEN }}

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v2

      # Backend Image
      - name: Extract backend metadata
        id: meta-backend
        uses: docker/metadata-action@v4
        with:
          images: ${{ env.REGISTRY }}/${{ github.repository }}/backend
          tags: |
            type=ref,event=branch
            type=ref,event=pr
            type=semver,pattern={{version}}
            type=semver,pattern={{major}}.{{minor}}

      - name: Build and push backend image
        uses: docker/build-push-action@v4
        with:
          context: ./backend
          file: ./backend/Dockerfile.production
          push: true
          tags: ${{ steps.meta-backend.outputs.tags }}
          labels: ${{ steps.meta-backend.outputs.labels }}
          cache-from: type=gha
          cache-to: type=gha,mode=max
          target: production

      # Frontend Image
      - name: Extract frontend metadata
        id: meta-frontend
        uses: docker/metadata-action@v4
        with:
          images: ${{ env.REGISTRY }}/${{ github.repository }}/frontend
          tags: |
            type=ref,event=branch
            type=ref,event=pr
            type=semver,pattern={{version}}
            type=semver,pattern={{major}}.{{minor}}

      - name: Build and push frontend image
        uses: docker/build-push-action@v4
        with:
          context: ./frontend
          file: ./frontend/Dockerfile.production
          push: true
          tags: ${{ steps.meta-frontend.outputs.tags }}
          labels: ${{ steps.meta-frontend.outputs.labels }}
          cache-from: type=gha
          cache-to: type=gha,mode=max
          target: production
  # Deploy to Staging
  deploy-staging:
    runs-on: ubuntu-latest
    needs: [build-images, e2e-tests]
    if: github.ref == 'refs/heads/develop'
    environment:
      name: staging
      url: https://staging.dentflow.com
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Deploy to staging
        run: |
          echo "Deploying to staging environment..."
          # Add your staging deployment script here
          # Example: kubectl apply -f k8s/staging/

      - name: Run smoke tests
        run: |
          sleep 60  # Wait for deployment
          curl -f https://staging.dentflow.com/health || exit 1

      - name: Notify deployment
        uses: 8398a7/action-slack@v3
        with:
          status: ${{ job.status }}
          channel: '#deployments'
          webhook_url: ${{ secrets.SLACK_WEBHOOK }}

  # Deploy to Production
  deploy-production:
    runs-on: ubuntu-latest
    needs: [build-images, performance-tests]
    if: github.ref == 'refs/heads/main' || github.event_name == 'release'
    environment:
      name: production
      url: https://dentflow.com
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Deploy to production
        run: |
          echo "Deploying to production environment..."
          # Add your production deployment script here
          # Example: kubectl apply -f k8s/production/

      - name: Run health checks
        run: |
          sleep 120  # Wait for deployment
          curl -f https://dentflow.com/health || exit 1
          curl -f https://api.dentflow.com/health || exit 1

      - name: Update deployment status
        run: |
          echo "Production deployment successful"

      - name: Notify successful deployment
        uses: 8398a7/action-slack@v3
        with:
          status: success
          channel: '#deployments'
          webhook_url: ${{ secrets.SLACK_WEBHOOK }}
          text: |
            :rocket: DentFlow deployed to production successfully!
            Version: ${{ github.sha }}
            Environment: https://dentflow.com

  # Rollback (Manual trigger)
  rollback:
    runs-on: ubuntu-latest
    if: github.event_name == 'workflow_dispatch'
    environment:
      name: production
    steps:
      - name: Rollback production
        run: |
          echo "Rolling back production deployment..."
          # Add rollback logic here

      - name: Verify rollback
        run: |
          curl -f https://dentflow.com/health || exit 1

      - name: Notify rollback
        uses: 8398a7/action-slack@v3
        with:
          status: warning
          channel: '#deployments'
          webhook_url: ${{ secrets.SLACK_WEBHOOK }}
          text: |
            :warning: DentFlow production rollback completed
            Triggered by: ${{ github.actor }}

  # Post-deployment monitoring
  post-deployment-monitoring:
    runs-on: ubuntu-latest
    needs: [deploy-production]
    if: github.ref == 'refs/heads/main' || github.event_name == 'release'
    steps:
      - name: Wait for deployment stabilization
        run: sleep 300  # 5 minutes

      - name: Check application metrics
        run: |
          # Check key metrics after deployment
          echo "Checking application health metrics..."
          
          # Response time check
          RESPONSE_TIME=$(curl -o /dev/null -s -w "%{time_total}" https://dentflow.com)
          if (( $(echo "$RESPONSE_TIME > 2.0" | bc -l) )); then
            echo "Response time too high: $RESPONSE_TIME"
            exit 1
          fi
          
          # Database connectivity
          curl -f https://api.dentflow.com/health/db || exit 1
          
          # Cache connectivity  
          curl -f https://api.dentflow.com/health/cache || exit 1

      - name: Check error rates
        run: |
          # Monitor error rates for 5 minutes post-deployment
          echo "Monitoring error rates..."
          # Add your monitoring checks here

      - name: Alert if issues detected
        if: failure()
        uses: 8398a7/action-slack@v3
        with:
          status: failure
          channel: '#alerts'
          webhook_url: ${{ secrets.SLACK_WEBHOOK }}
          text: |
            :rotating_light: DentFlow production deployment has issues!
            Please investigate immediately.
            Deployment: ${{ github.sha }}

# Workflow dispatch for manual operations
workflow_dispatch:
  inputs:
    environment:
      description: 'Environment to deploy to'
      required: true
      default: 'staging'
      type: choice
      options:
        - staging
        - production
    rollback:
      description: 'Perform rollback'
      required: false
      default: false
      type: boolean