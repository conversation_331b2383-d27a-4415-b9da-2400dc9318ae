# Generated by Django 4.2.7 on 2025-07-25 19:47

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ("tenants", "__first__"),
        ("cases", "0001_initial"),
    ]

    operations = [
        migrations.AlterField(
            model_name="case",
            name="tenant",
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.CASCADE, to="tenants.tenant"
            ),
        ),
        migrations.AlterField(
            model_name="clinic",
            name="tenant",
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.CASCADE, to="tenants.tenant"
            ),
        ),
        migrations.DeleteModel(
            name="Tenant",
        ),
    ]
