"""
DentFlow Security Framework
Comprehensive security implementation for healthcare compliance
"""

import hashlib
import hmac
import time
import json
import logging
from typing import Dict, Any, Optional, List
from datetime import datetime, timedelta
from functools import wraps

from django.core.cache import cache
from django.contrib.auth.models import User
from django.http import JsonResponse
from django.conf import settings
from django.utils import timezone
from django.db import models
from rest_framework import status
from rest_framework.response import Response
import structlog

logger = structlog.get_logger(__name__)


class SecurityAuditLog(models.Model):
    """
    Comprehensive audit logging for security events
    HIPAA/GDPR compliant audit trail
    """
    
    EVENT_TYPES = [
        ('login', 'User Login'),
        ('logout', 'User Logout'),
        ('failed_login', 'Failed Login Attempt'),
        ('password_change', 'Password Changed'),
        ('data_access', 'Patient Data Access'),
        ('data_modification', 'Data Modification'),
        ('api_access', 'API Access'),
        ('permission_change', 'Permission Change'),
        ('export_data', 'Data Export'),
        ('system_admin', 'System Administration'),
        ('security_violation', 'Security Violation'),
    ]
    
    SEVERITY_LEVELS = [
        ('low', 'Low'),
        ('medium', 'Medium'),
        ('high', 'High'),
        ('critical', 'Critical'),
    ]
    
    user = models.ForeignKey(User, on_delete=models.CASCADE, null=True, blank=True)
    event_type = models.CharField(max_length=50, choices=EVENT_TYPES)
    severity = models.CharField(max_length=20, choices=SEVERITY_LEVELS, default='medium')
    timestamp = models.DateTimeField(auto_now_add=True)
    
    # Request details
    ip_address = models.GenericIPAddressField()
    user_agent = models.TextField()
    session_key = models.CharField(max_length=100, blank=True)
    
    # Action details
    resource_type = models.CharField(max_length=50, blank=True)
    resource_id = models.CharField(max_length=100, blank=True)
    action = models.CharField(max_length=100)
    
    # Context data (encrypted)
    context_data = models.JSONField(default=dict)
    
    # HIPAA compliance fields
    patient_id = models.CharField(max_length=100, blank=True)  # If applicable
    phi_accessed = models.BooleanField(default=False)  # Protected Health Information
    
    # Technical details
    request_path = models.CharField(max_length=500)
    http_method = models.CharField(max_length=10)
    response_status = models.IntegerField(null=True, blank=True)
    
    class Meta:
        indexes = [
            models.Index(fields=['user', 'timestamp']),
            models.Index(fields=['event_type', 'timestamp']),
            models.Index(fields=['severity', 'timestamp']),
            models.Index(fields=['ip_address', 'timestamp']),
            models.Index(fields=['patient_id', 'timestamp']),
        ]
        
        # Retention policy: 7 years for HIPAA compliance
        db_table = 'security_audit_log'


class RateLimiter:
    """
    Advanced rate limiting with multiple strategies
    """
    
    def __init__(self):
        self.cache = cache
        
    def is_rate_limited(self, key: str, limit: int, window: int) -> bool:
        """
        Check if rate limit is exceeded using sliding window
        
        Args:
            key: Identifier for rate limiting (IP, user, etc.)
            limit: Maximum requests allowed
            window: Time window in seconds
        
        Returns:
            True if rate limited, False otherwise
        """
        now = time.time()
        cache_key = f"rate_limit:{key}"
        
        # Get current request timestamps
        requests = self.cache.get(cache_key, [])
        
        # Remove old requests outside the window
        requests = [req_time for req_time in requests if req_time > now - window]
        
        if len(requests) >= limit:
            return True
        
        # Add current request
        requests.append(now)
        self.cache.set(cache_key, requests, window + 60)  # Cache a bit longer than window
        
        return False
    
    def get_rate_limit_status(self, key: str, limit: int, window: int) -> Dict[str, Any]:
        """Get current rate limit status"""
        now = time.time()
        cache_key = f"rate_limit:{key}"
        
        requests = self.cache.get(cache_key, [])
        requests = [req_time for req_time in requests if req_time > now - window]
        
        remaining = max(0, limit - len(requests))
        reset_time = min(requests) + window if requests else now
        
        return {
            'limit': limit,
            'remaining': remaining,
            'reset': int(reset_time),
            'window': window
        }


class SecurityValidator:
    """
    Input validation and security checks
    """
    
    @staticmethod
    def validate_password_strength(password: str) -> Dict[str, Any]:
        """Validate password meets security requirements"""
        errors = []
        
        if len(password) < 12:
            errors.append("Password must be at least 12 characters long")
        
        if not any(c.isupper() for c in password):
            errors.append("Password must contain at least one uppercase letter")
        
        if not any(c.islower() for c in password):
            errors.append("Password must contain at least one lowercase letter")
        
        if not any(c.isdigit() for c in password):
            errors.append("Password must contain at least one number")
        
        if not any(c in "!@#$%^&*()_+-=[]{}|;:,.<>?" for c in password):
            errors.append("Password must contain at least one special character")
        
        # Check against common passwords
        common_passwords = [
            'password', '123456', 'admin', 'dental', 'dentist',
            'password123', 'admin123', 'qwerty'
        ]
        
        if password.lower() in common_passwords:
            errors.append("Password is too common")
        
        return {
            'valid': len(errors) == 0,
            'errors': errors,
            'score': max(0, 100 - (len(errors) * 20))  # Simple scoring
        }
    
    @staticmethod
    def validate_hipaa_compliance(data: Dict[str, Any]) -> Dict[str, Any]:
        """Validate data access for HIPAA compliance"""
        
        # Check for PHI (Protected Health Information)
        phi_fields = [
            'patient_name', 'ssn', 'phone', 'email', 'address',
            'birth_date', 'medical_record', 'insurance'
        ]
        
        phi_present = any(field in data for field in phi_fields)
        
        compliance_checks = {
            'phi_present': phi_present,
            'requires_audit': phi_present,
            'requires_encryption': phi_present,
            'minimum_access_level': 'authenticated' if phi_present else 'basic'
        }
        
        return compliance_checks
    
    @staticmethod
    def sanitize_input(data: Any) -> Any:
        """Sanitize input data to prevent injection attacks"""
        if isinstance(data, str):
            # Remove potentially dangerous characters
            dangerous_chars = ['<', '>', '"', "'", '&', ';', '(', ')', '|', '`']
            for char in dangerous_chars:
                data = data.replace(char, '')
            return data.strip()
        
        elif isinstance(data, dict):
            return {key: SecurityValidator.sanitize_input(value) 
                   for key, value in data.items()}
        
        elif isinstance(data, list):
            return [SecurityValidator.sanitize_input(item) for item in data]
        
        return data


class SecurityAuditor:
    """
    Security event auditing and monitoring
    """
    
    def __init__(self):
        self.logger = structlog.get_logger(__name__)
        self.rate_limiter = RateLimiter()
    
    def log_security_event(self, event_type: str, request, user=None, 
                          severity='medium', **kwargs):
        """Log security event with comprehensive details"""
        
        try:
            # Extract request details
            ip_address = self._get_client_ip(request)
            user_agent = request.META.get('HTTP_USER_AGENT', '')[:500]
            
            # Create audit log entry
            audit_log = SecurityAuditLog.objects.create(
                user=user,
                event_type=event_type,
                severity=severity,
                ip_address=ip_address,
                user_agent=user_agent,
                session_key=request.session.session_key or '',
                action=kwargs.get('action', ''),
                resource_type=kwargs.get('resource_type', ''),
                resource_id=kwargs.get('resource_id', ''),
                request_path=request.path,
                http_method=request.method,
                context_data=kwargs.get('context_data', {}),
                patient_id=kwargs.get('patient_id', ''),
                phi_accessed=kwargs.get('phi_accessed', False)
            )
            
            # Structured logging
            log_data = {
                'event': 'security_audit',
                'audit_id': audit_log.id,
                'event_type': event_type,
                'severity': severity,
                'user_id': user.id if user else None,
                'ip_address': ip_address,
                'path': request.path,
                'method': request.method
            }
            
            if severity in ['high', 'critical']:
                self.logger.error("Security event", **log_data)
                self._handle_critical_event(audit_log)
            else:
                self.logger.info("Security event", **log_data)
            
            return audit_log
            
        except Exception as e:
            self.logger.error(
                "Failed to log security event",
                error=str(e),
                event_type=event_type
            )
    
    def _get_client_ip(self, request) -> str:
        """Get client IP address from request"""
        x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
        if x_forwarded_for:
            ip = x_forwarded_for.split(',')[0]
        else:
            ip = request.META.get('REMOTE_ADDR')
        return ip or '127.0.0.1'
    
    def _handle_critical_event(self, audit_log: SecurityAuditLog):
        """Handle critical security events"""
        
        # Immediate actions for critical events
        if audit_log.event_type == 'security_violation':
            self._temporarily_block_ip(audit_log.ip_address)
        
        # Send alerts (implement based on your alerting system)
        self._send_security_alert(audit_log)
    
    def _temporarily_block_ip(self, ip_address: str, duration: int = 3600):
        """Temporarily block an IP address"""
        cache_key = f"blocked_ip:{ip_address}"
        cache.set(cache_key, True, duration)
        
        self.logger.warning(
            "IP address temporarily blocked",
            ip_address=ip_address,
            duration=duration
        )
    
    def _send_security_alert(self, audit_log: SecurityAuditLog):
        """Send security alert to administrators"""
        # Placeholder for alert system integration
        # Could integrate with Slack, email, PagerDuty, etc.
        pass
    
    def check_suspicious_activity(self, request, user=None) -> Dict[str, Any]:
        """Check for suspicious activity patterns"""
        
        ip_address = self._get_client_ip(request)
        
        suspicious_indicators = {
            'multiple_failed_logins': self._check_failed_logins(ip_address),
            'unusual_access_pattern': self._check_access_pattern(user),
            'blocked_ip': self._is_ip_blocked(ip_address),
            'rate_limited': self.rate_limiter.is_rate_limited(
                f"ip:{ip_address}", 100, 3600  # 100 requests per hour
            )
        }
        
        risk_score = sum(suspicious_indicators.values())
        
        return {
            'risk_score': risk_score,
            'indicators': suspicious_indicators,
            'action_required': risk_score >= 2
        }
    
    def _check_failed_logins(self, ip_address: str) -> bool:
        """Check for multiple failed login attempts"""
        recent_failures = SecurityAuditLog.objects.filter(
            event_type='failed_login',
            ip_address=ip_address,
            timestamp__gte=timezone.now() - timedelta(hours=1)
        ).count()
        
        return recent_failures >= 5
    
    def _check_access_pattern(self, user) -> bool:
        """Check for unusual access patterns"""
        if not user:
            return False
        
        # Check for access outside normal hours
        current_hour = timezone.now().hour
        if current_hour < 6 or current_hour > 22:  # Outside 6 AM - 10 PM
            return True
        
        return False
    
    def _is_ip_blocked(self, ip_address: str) -> bool:
        """Check if IP address is blocked"""
        cache_key = f"blocked_ip:{ip_address}"
        return cache.get(cache_key, False)


class SecureAPIMiddleware:
    """
    Comprehensive security middleware for API endpoints
    """
    
    def __init__(self, get_response):
        self.get_response = get_response
        self.auditor = SecurityAuditor()
        self.rate_limiter = RateLimiter()
    
    def __call__(self, request):
        # Pre-request security checks
        security_check = self._perform_security_checks(request)
        
        if security_check['blocked']:
            return JsonResponse(
                {
                    'error': 'Access denied',
                    'reason': security_check['reason']
                },
                status=403
            )
        
        # Process request
        response = self.get_response(request)
        
        # Post-request security logging
        self._log_request(request, response)
        
        # Add security headers
        self._add_security_headers(response)
        
        return response
    
    def _perform_security_checks(self, request) -> Dict[str, Any]:
        """Perform comprehensive security checks"""
        
        ip_address = self.auditor._get_client_ip(request)
        
        # Check if IP is blocked
        if self.auditor._is_ip_blocked(ip_address):
            return {
                'blocked': True,
                'reason': 'IP address temporarily blocked'
            }
        
        # Rate limiting
        if self.rate_limiter.is_rate_limited(f"ip:{ip_address}", 100, 3600):
            self.auditor.log_security_event(
                'security_violation',
                request,
                severity='medium',
                action='rate_limit_exceeded'
            )
            return {
                'blocked': True,
                'reason': 'Rate limit exceeded'
            }
        
        # Check for suspicious activity
        suspicion_check = self.auditor.check_suspicious_activity(request)
        if suspicion_check['action_required']:
            self.auditor.log_security_event(
                'security_violation',
                request,
                severity='high',
                action='suspicious_activity_detected',
                context_data=suspicion_check
            )
        
        return {'blocked': False}
    
    def _log_request(self, request, response):
        """Log API request for audit purposes"""
        
        # Log based on sensitivity
        if self._is_sensitive_endpoint(request.path):
            self.auditor.log_security_event(
                'api_access',
                request,
                user=getattr(request, 'user', None),
                severity='medium',
                action='sensitive_api_access',
                resource_type='api',
                resource_id=request.path,
                context_data={
                    'status_code': response.status_code
                }
            )
    
    def _is_sensitive_endpoint(self, path: str) -> bool:
        """Check if endpoint handles sensitive data"""
        sensitive_patterns = [
            '/api/cases/',
            '/api/patients/',
            '/api/users/',
            '/admin/',
            '/api/reports/'
        ]
        
        return any(pattern in path for pattern in sensitive_patterns)
    
    def _add_security_headers(self, response):
        """Add security headers to response"""
        
        # HIPAA and general security headers
        security_headers = {
            'X-Content-Type-Options': 'nosniff',
            'X-Frame-Options': 'DENY',
            'X-XSS-Protection': '1; mode=block',
            'Strict-Transport-Security': 'max-age=31536000; includeSubDomains',
            'Content-Security-Policy': "default-src 'self'",
            'Referrer-Policy': 'strict-origin-when-cross-origin',
            'Permissions-Policy': 'geolocation=(), microphone=(), camera=()',
            'Cache-Control': 'no-store, no-cache, must-revalidate, max-age=0',
            'Pragma': 'no-cache'
        }
        
        for header, value in security_headers.items():
            response[header] = value


def require_mfa(view_func):
    """
    Decorator to require multi-factor authentication for sensitive operations
    """
    @wraps(view_func)
    def wrapper(request, *args, **kwargs):
        user = getattr(request, 'user', None)
        
        if not user or not user.is_authenticated:
            return JsonResponse({'error': 'Authentication required'}, status=401)
        
        # Check if MFA is required and verified
        mfa_required = getattr(settings, 'MFA_REQUIRED_FOR_SENSITIVE_OPS', True)
        
        if mfa_required:
            mfa_verified = request.session.get('mfa_verified', False)
            mfa_timestamp = request.session.get('mfa_timestamp', 0)
            
            # MFA expires after 30 minutes
            if not mfa_verified or time.time() - mfa_timestamp > 1800:
                return JsonResponse(
                    {
                        'error': 'Multi-factor authentication required',
                        'mfa_required': True
                    },
                    status=403
                )
        
        return view_func(request, *args, **kwargs)
    
    return wrapper


def audit_data_access(resource_type: str, phi_access: bool = False):
    """
    Decorator to audit data access for compliance
    """
    def decorator(view_func):
        @wraps(view_func)
        def wrapper(request, *args, **kwargs):
            auditor = SecurityAuditor()
            
            # Log data access
            auditor.log_security_event(
                'data_access',
                request,
                user=getattr(request, 'user', None),
                severity='medium' if phi_access else 'low',
                action='data_access',
                resource_type=resource_type,
                resource_id=kwargs.get('pk', ''),
                phi_accessed=phi_access
            )
            
            return view_func(request, *args, **kwargs)
        
        return wrapper
    return decorator


# Global security auditor instance
security_auditor = SecurityAuditor()
rate_limiter = RateLimiter()