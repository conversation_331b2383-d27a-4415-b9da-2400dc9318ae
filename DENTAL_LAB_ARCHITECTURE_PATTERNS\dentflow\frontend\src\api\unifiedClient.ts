/**
 * Unified API Service Layer for DentFlow
 * Connects React frontend to Django backend with fallback to mock data
 */

import axios, { AxiosInstance, AxiosResponse } from 'axios';

// API Configuration
const API_BASE_URL = process.env.REACT_APP_API_URL || 'http://localhost:8001/api/v1';

export interface ApiResponse<T> {
  data: T;
  message?: string;
  errors?: Record<string, string[]>;
}

class UnifiedApiClient {
  private client: AxiosInstance;
  private isBackendAvailable = true;

  constructor() {
    this.client = axios.create({
      baseURL: API_BASE_URL,
      timeout: 10000,
      headers: {
        'Content-Type': 'application/json',
      },
    });

    // Request interceptor for auth token
    this.client.interceptors.request.use(
      (config) => {
        const token = localStorage.getItem('dentflow_token');
        if (token) {
          config.headers.Authorization = `Bearer ${token}`;
        }
        return config;
      },
      (error) => Promise.reject(error)
    );

    // Response interceptor for error handling
    this.client.interceptors.response.use(
      (response) => response,
      (error) => {
        if (error.response?.status === 401) {
          localStorage.removeItem('dentflow_token');
          window.location.href = '/login';
        }
        return Promise.reject(error);
      }
    );
  }

  async get<T>(url: string, config?: any): Promise<T> {
    try {
      const response: AxiosResponse<T> = await this.client.get(url, config);
      this.isBackendAvailable = true;
      return response.data;
    } catch (error) {
      this.isBackendAvailable = false;
      throw error;
    }
  }

  async getBlob(url: string): Promise<Blob> {
    try {
      const response: AxiosResponse<Blob> = await this.client.get(url, { responseType: 'blob' });
      this.isBackendAvailable = true;
      return response.data;
    } catch (error) {
      this.isBackendAvailable = false;
      throw error;
    }
  }

  async post<T>(url: string, data?: any): Promise<T> {
    try {
      const response: AxiosResponse<T> = await this.client.post(url, data);
      this.isBackendAvailable = true;
      return response.data;
    } catch (error) {
      this.isBackendAvailable = false;
      throw error;
    }
  }

  async put<T>(url: string, data?: any): Promise<T> {
    try {
      const response: AxiosResponse<T> = await this.client.put(url, data);
      this.isBackendAvailable = true;
      return response.data;
    } catch (error) {
      this.isBackendAvailable = false;
      throw error;
    }
  }

  async patch<T>(url: string, data?: any): Promise<T> {
    try {
      const response: AxiosResponse<T> = await this.client.patch(url, data);
      this.isBackendAvailable = true;
      return response.data;
    } catch (error) {
      this.isBackendAvailable = false;
      throw error;
    }
  }

  async delete<T>(url: string): Promise<T> {
    try {
      const response: AxiosResponse<T> = await this.client.delete(url);
      this.isBackendAvailable = true;
      return response.data;
    } catch (error) {
      this.isBackendAvailable = false;
      throw error;
    }
  }

  // Authentication methods
  async login(email: string, password: string): Promise<{ access: string; refresh: string; user: any }> {
    try {
      const response = await this.client.post('/auth/login/', {
        email,
        password
      });

      // Store tokens for future requests
      if (response.data.access) {
        localStorage.setItem('dentflow_token', response.data.access);
        localStorage.setItem('dentflow_refresh_token', response.data.refresh);
        this.client.defaults.headers.common['Authorization'] = `Bearer ${response.data.access}`;
      }

      return response.data;
    } catch (error: any) {
      console.error('Login failed:', error.response?.data || error.message);
      throw new Error(error.response?.data?.detail || 'Login failed');
    }
  }

  async logout(): Promise<void> {
    localStorage.removeItem('dentflow_token');
    localStorage.removeItem('dentflow_refresh_token');
    delete this.client.defaults.headers.common['Authorization'];
  }

  getBackendStatus() {
    return {
      available: this.isBackendAvailable,
      mode: this.isBackendAvailable ? 'live' : 'mock'
    };
  }
}

export const apiClient = new UnifiedApiClient();
