#!/usr/bin/env python3
"""
DentFlow Frontend Test Demo
Comprehensive test to demonstrate frontend and backend integration
"""

import os
import sys
import django
import requests
import json
from decimal import Decimal
from datetime import datetime, timedelta

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'dentflow_project.settings')
sys.path.append('backend')
django.setup()

# Import models
from apps.tenants.models import Tenant
from apps.cases.models import Case, CaseStatus
from apps.inventory.models import Material, MaterialCategory, Item, ItemCategory, ItemMaterialComposition
from apps.billing.models import Invoice, InvoiceItem, PriceList, ServicePrice, ItemPrice
from django.contrib.auth.models import User

def test_backend_api():
    """Test backend API endpoints"""
    print("🔧 Testing Backend API...")
    
    base_url = "http://localhost:8001/api/v1"
    
    # Test API health
    try:
        response = requests.get(f"{base_url}/health/", timeout=5)
        if response.status_code == 200:
            print("✅ Backend API is responding")
        else:
            print(f"⚠️ Backend API returned status {response.status_code}")
    except requests.exceptions.RequestException as e:
        print(f"❌ Backend API not accessible: {e}")
        return False
    
    return True

def create_test_data():
    """Create comprehensive test data for frontend demo"""
    print("📊 Creating test data for frontend demo...")
    
    # Create tenant
    tenant, created = Tenant.objects.get_or_create(
        subdomain='demo-lab',
        defaults={
            'name': 'Demo Dental Laboratory',
            'email': '<EMAIL>',
            'phone': '******-0123',
            'address': '123 Dental Street, Lab City, LC 12345',
            'plan': 'professional',
            'max_users': 10,
            'max_cases_per_month': 100,
            'is_active': True
        }
    )
    print(f"{'✅ Created' if created else '✅ Found'} tenant: {tenant.name}")
    
    # Create admin user
    admin_user, created = User.objects.get_or_create(
        username='admin',
        defaults={
            'email': '<EMAIL>',
            'first_name': 'Admin',
            'last_name': 'User',
            'is_staff': True,
            'is_superuser': True
        }
    )
    if created:
        admin_user.set_password('admin123')
        admin_user.save()
    print(f"{'✅ Created' if created else '✅ Found'} admin user")
    
    # Create material categories
    ceramic_cat, _ = MaterialCategory.objects.get_or_create(
        name='Ceramics',
        defaults={'description': 'Ceramic materials for dental restorations'}
    )
    
    metal_cat, _ = MaterialCategory.objects.get_or_create(
        name='Metals',
        defaults={'description': 'Metal alloys for dental work'}
    )
    
    # Create materials
    zirconia, _ = Material.objects.get_or_create(
        name='Zirconia Block',
        defaults={
            'category': ceramic_cat,
            'unit': 'piece',
            'standard_cost': Decimal('45.00'),
            'current_stock': Decimal('25.0000'),
            'minimum_stock': Decimal('5.0000'),
            'description': 'High-strength zirconia block for crowns and bridges'
        }
    )
    
    titanium, _ = Material.objects.get_or_create(
        name='Titanium Alloy',
        defaults={
            'category': metal_cat,
            'unit': 'gram',
            'standard_cost': Decimal('2.50'),
            'current_stock': Decimal('500.0000'),
            'minimum_stock': Decimal('100.0000'),
            'description': 'Grade 2 titanium alloy for implant components'
        }
    )
    
    # Create item categories
    crown_cat, _ = ItemCategory.objects.get_or_create(
        name='Crowns',
        defaults={'description': 'All types of dental crowns'}
    )
    
    bridge_cat, _ = ItemCategory.objects.get_or_create(
        name='Bridges',
        defaults={'description': 'Fixed dental bridges'}
    )
    
    # Create items
    zirconia_crown, _ = Item.objects.get_or_create(
        name='Zirconia Crown',
        defaults={
            'category': crown_cat,
            'description': 'Full zirconia crown restoration',
            'estimated_production_time_minutes': 180,
            'is_active': True
        }
    )
    
    titanium_bridge, _ = Item.objects.get_or_create(
        name='Titanium Bridge (3-unit)',
        defaults={
            'category': bridge_cat,
            'description': '3-unit titanium bridge',
            'estimated_production_time_minutes': 360,
            'is_active': True
        }
    )
    
    # Create material compositions
    ItemMaterialComposition.objects.get_or_create(
        item=zirconia_crown,
        material=zirconia,
        defaults={
            'quantity': Decimal('0.5000'),
            'waste_factor': Decimal('10.00'),
            'notes': 'Half block needed for single crown'
        }
    )
    
    ItemMaterialComposition.objects.get_or_create(
        item=titanium_bridge,
        material=titanium,
        defaults={
            'quantity': Decimal('15.0000'),
            'waste_factor': Decimal('5.00'),
            'notes': '15g titanium for 3-unit bridge'
        }
    )
    
    # Create price list
    price_list, _ = PriceList.objects.get_or_create(
        name='Standard Pricing 2024',
        defaults={
            'description': 'Standard laboratory pricing for 2024',
            'is_active': True,
            'effective_date': datetime.now().date()
        }
    )
    
    # Create item prices
    ItemPrice.objects.get_or_create(
        price_list=price_list,
        item=zirconia_crown,
        defaults={
            'base_price': Decimal('150.00'),
            'labor_cost': Decimal('75.00'),
            'markup_percentage': Decimal('35.00')
        }
    )
    
    ItemPrice.objects.get_or_create(
        price_list=price_list,
        item=titanium_bridge,
        defaults={
            'base_price': Decimal('450.00'),
            'labor_cost': Decimal('200.00'),
            'markup_percentage': Decimal('40.00')
        }
    )
    
    # Create sample cases
    case_statuses = ['received', 'in_progress', 'quality_check', 'completed', 'shipped']
    
    for i in range(5):
        case, created = Case.objects.get_or_create(
            case_number=f'LAB-2024-{str(i+1).zfill(3)}',
            defaults={
                'tenant': tenant,
                'patient_name': f'Patient {i+1}',
                'dentist_name': f'Dr. Smith {i+1}',
                'service_type': 'crown' if i % 2 == 0 else 'bridge',
                'priority': 'normal',
                'due_date': datetime.now().date() + timedelta(days=7+i),
                'notes': f'Test case {i+1} for frontend demo',
                'created_by': admin_user
            }
        )
        
        if created:
            # Create case status
            CaseStatus.objects.create(
                case=case,
                status=case_statuses[i % len(case_statuses)],
                notes=f'Case {i+1} status update',
                created_by=admin_user
            )
    
    print("✅ Test data created successfully")
    return True

def test_frontend_features():
    """Test frontend features and API integration"""
    print("🌐 Testing Frontend Features...")
    
    frontend_url = "http://localhost:3001"
    
    # Test if frontend is accessible
    try:
        response = requests.get(frontend_url, timeout=5)
        if response.status_code == 200:
            print("✅ Frontend is accessible")
        else:
            print(f"⚠️ Frontend returned status {response.status_code}")
    except requests.exceptions.RequestException as e:
        print(f"❌ Frontend not accessible: {e}")
        return False
    
    print("✅ Frontend test completed")
    return True

def display_demo_info():
    """Display demo information and instructions"""
    print("\n" + "="*60)
    print("🦷 DENTFLOW FRONTEND DEMO - READY!")
    print("="*60)
    
    print("\n📊 DEMO DATA CREATED:")
    print("• 2 Material Categories (Ceramics, Metals)")
    print("• 2 Materials (Zirconia Block, Titanium Alloy)")
    print("• 2 Item Categories (Crowns, Bridges)")
    print("• 2 Items with material compositions")
    print("• 1 Price List with item pricing")
    print("• 5 Sample Cases with different statuses")
    print("• Admin user (username: admin, password: admin123)")
    
    print("\n🌐 ACCESS POINTS:")
    print("• Frontend Application: http://localhost:3001")
    print("• Backend API: http://localhost:8001/api/v1")
    print("• Django Admin: http://localhost:8001/admin")
    
    print("\n🔐 LOGIN CREDENTIALS:")
    print("• Username: admin")
    print("• Password: admin123")
    
    print("\n🧪 TESTING FEATURES:")
    print("1. 📊 Dashboard - View KPIs and recent activity")
    print("2. 📋 Cases - Browse, create, and manage cases")
    print("3. 💰 Billing - View invoices and financial data")
    print("4. ⚙️ Workflow - Manage production workflows")
    print("5. 📅 Schedule - View and manage appointments")
    print("6. 📈 Reports - Generate business reports")
    
    print("\n🎯 BILLING SYSTEM HIGHLIGHTS:")
    print("• Material cost calculation from inventory")
    print("• Labor cost tracking")
    print("• Profit margin analysis")
    print("• Invoice generation with cost breakdown")
    print("• Real-time inventory integration")
    
    print("\n✨ NEXT STEPS:")
    print("1. Open http://localhost:3001 in your browser")
    print("2. Login with admin/admin123")
    print("3. Explore the billing module to see the new inventory integration")
    print("4. Check the Django admin at http://localhost:8001/admin")
    print("5. Test creating new cases and invoices")
    
    print("\n" + "="*60)

def main():
    """Main demo function"""
    print("🚀 Starting DentFlow Frontend Demo...")
    
    # Test backend
    if not test_backend_api():
        print("❌ Backend not available. Please start the Django server first.")
        return
    
    # Create test data
    if not create_test_data():
        print("❌ Failed to create test data")
        return
    
    # Test frontend
    if not test_frontend_features():
        print("❌ Frontend not available. Please start the React server first.")
        return
    
    # Display demo info
    display_demo_info()
    
    print("\n🎉 Demo setup complete! Ready for testing.")

if __name__ == '__main__':
    main()
