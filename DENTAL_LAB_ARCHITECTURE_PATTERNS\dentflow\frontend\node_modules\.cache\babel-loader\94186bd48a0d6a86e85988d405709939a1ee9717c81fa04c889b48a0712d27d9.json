{"ast": null, "code": "var _s = $RefreshSig$(),\n  _s2 = $RefreshSig$(),\n  _s3 = $RefreshSig$(),\n  _s4 = $RefreshSig$(),\n  _s5 = $RefreshSig$(),\n  _s6 = $RefreshSig$(),\n  _s7 = $RefreshSig$(),\n  _s8 = $RefreshSig$(),\n  _s9 = $RefreshSig$(),\n  _s0 = $RefreshSig$(),\n  _s1 = $RefreshSig$(),\n  _s10 = $RefreshSig$(),\n  _s11 = $RefreshSig$(),\n  _s12 = $RefreshSig$(),\n  _s13 = $RefreshSig$(),\n  _s14 = $RefreshSig$(),\n  _s15 = $RefreshSig$(),\n  _s16 = $RefreshSig$(),\n  _s17 = $RefreshSig$(),\n  _s18 = $RefreshSig$(),\n  _s19 = $RefreshSig$(),\n  _s20 = $RefreshSig$(),\n  _s21 = $RefreshSig$(),\n  _s22 = $RefreshSig$(),\n  _s23 = $RefreshSig$(),\n  _s24 = $RefreshSig$();\n/**\n * React Query Hooks for DentFlow API\n * Provides data fetching, caching, and mutation hooks\n */\n\nimport { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';\nimport { DentFlowAPI } from '../api';\nimport { casesService } from '../services/api';\n\n// Query keys for React Query\nexport const queryKeys = {\n  cases: ['cases'],\n  case: id => ['cases', id],\n  casesByStatus: status => ['cases', 'status', status],\n  overdueCases: ['cases', 'overdue'],\n  dashboardStats: ['dashboard', 'stats'],\n  tasks: ['tasks'],\n  technicians: ['technicians'],\n  workflowStages: ['workflow', 'stages'],\n  invoices: ['invoices'],\n  invoice: id => ['invoices', id],\n  appointments: ['appointments'],\n  appointment: id => ['appointments', id],\n  reports: ['reports'],\n  currentUser: ['auth', 'current-user']\n};\n\n// ===== AUTHENTICATION HOOKS =====\n\nexport const useCurrentUser = options => {\n  _s();\n  return useQuery({\n    queryKey: queryKeys.currentUser,\n    queryFn: () => DentFlowAPI.auth.getCurrentUser(),\n    enabled: DentFlowAPI.auth.isAuthenticated(),\n    staleTime: 5 * 60 * 1000,\n    // 5 minutes\n    ...options\n  });\n};\n_s(useCurrentUser, \"4ZpngI1uv+Uo3WQHEZmTQ5FNM+k=\", false, function () {\n  return [useQuery];\n});\nexport const useLogin = options => {\n  _s2();\n  const queryClient = useQueryClient();\n  return useMutation({\n    mutationFn: ({\n      email,\n      password\n    }) => DentFlowAPI.auth.login({\n      email,\n      password\n    }),\n    onSuccess: data => {\n      queryClient.setQueryData(queryKeys.currentUser, data.user);\n      queryClient.invalidateQueries({\n        queryKey: ['auth']\n      });\n    },\n    ...options\n  });\n};\n_s2(useLogin, \"YK0wzM21ECnncaq5SECwU+/SVdQ=\", false, function () {\n  return [useQueryClient, useMutation];\n});\nexport const useLogout = options => {\n  _s3();\n  const queryClient = useQueryClient();\n  return useMutation({\n    mutationFn: () => DentFlowAPI.auth.logout(),\n    onSuccess: () => {\n      queryClient.clear();\n    },\n    ...options\n  });\n};\n\n// ===== CASES HOOKS =====\n_s3(useLogout, \"YK0wzM21ECnncaq5SECwU+/SVdQ=\", false, function () {\n  return [useQueryClient, useMutation];\n});\nexport const useCases = options => {\n  _s4();\n  return useQuery({\n    queryKey: queryKeys.cases,\n    queryFn: async () => {\n      const response = await casesService.getCases();\n      return response.results || [];\n    },\n    staleTime: 2 * 60 * 1000,\n    // 2 minutes\n    ...options\n  });\n};\n_s4(useCases, \"4ZpngI1uv+Uo3WQHEZmTQ5FNM+k=\", false, function () {\n  return [useQuery];\n});\nexport const useCase = (id, options) => {\n  _s5();\n  return useQuery({\n    queryKey: queryKeys.case(id),\n    queryFn: () => casesService.getCaseById(id),\n    enabled: !!id,\n    staleTime: 1 * 60 * 1000,\n    // 1 minute\n    ...options\n  });\n};\n_s5(useCase, \"4ZpngI1uv+Uo3WQHEZmTQ5FNM+k=\", false, function () {\n  return [useQuery];\n});\nexport const useCasesByStatus = (status, options) => {\n  _s6();\n  return useQuery({\n    queryKey: queryKeys.casesByStatus(status),\n    queryFn: () => DentFlowAPI.cases.getCasesByStatus(status),\n    enabled: !!status,\n    staleTime: 1 * 60 * 1000,\n    ...options\n  });\n};\n_s6(useCasesByStatus, \"4ZpngI1uv+Uo3WQHEZmTQ5FNM+k=\", false, function () {\n  return [useQuery];\n});\nexport const useOverdueCases = options => {\n  _s7();\n  return useQuery({\n    queryKey: queryKeys.overdueCases,\n    queryFn: () => DentFlowAPI.cases.getOverdueCases(),\n    staleTime: 30 * 1000,\n    // 30 seconds (more critical data)\n    ...options\n  });\n};\n_s7(useOverdueCases, \"4ZpngI1uv+Uo3WQHEZmTQ5FNM+k=\", false, function () {\n  return [useQuery];\n});\nexport const useCreateCase = options => {\n  _s8();\n  const queryClient = useQueryClient();\n  return useMutation({\n    mutationFn: data => DentFlowAPI.cases.createCase(data),\n    onSuccess: () => {\n      queryClient.invalidateQueries({\n        queryKey: queryKeys.cases\n      });\n      queryClient.invalidateQueries({\n        queryKey: queryKeys.dashboardStats\n      });\n    },\n    ...options\n  });\n};\n_s8(useCreateCase, \"YK0wzM21ECnncaq5SECwU+/SVdQ=\", false, function () {\n  return [useQueryClient, useMutation];\n});\nexport const useUpdateCase = options => {\n  _s9();\n  const queryClient = useQueryClient();\n  return useMutation({\n    mutationFn: ({\n      id,\n      data\n    }) => DentFlowAPI.cases.updateCase(id, data),\n    onSuccess: updatedCase => {\n      queryClient.setQueryData(queryKeys.case(updatedCase.id), updatedCase);\n      queryClient.invalidateQueries({\n        queryKey: queryKeys.cases\n      });\n      queryClient.invalidateQueries({\n        queryKey: queryKeys.dashboardStats\n      });\n    },\n    ...options\n  });\n};\n_s9(useUpdateCase, \"YK0wzM21ECnncaq5SECwU+/SVdQ=\", false, function () {\n  return [useQueryClient, useMutation];\n});\nexport const useAdvanceCase = options => {\n  _s0();\n  const queryClient = useQueryClient();\n  return useMutation({\n    mutationFn: ({\n      id,\n      notes\n    }) => DentFlowAPI.cases.advanceCase(id, {\n      notes\n    }),\n    onSuccess: updatedCase => {\n      queryClient.setQueryData(queryKeys.case(updatedCase.id), updatedCase);\n      queryClient.invalidateQueries({\n        queryKey: queryKeys.cases\n      });\n      queryClient.invalidateQueries({\n        queryKey: queryKeys.tasks\n      });\n    },\n    ...options\n  });\n};\n_s0(useAdvanceCase, \"YK0wzM21ECnncaq5SECwU+/SVdQ=\", false, function () {\n  return [useQueryClient, useMutation];\n});\nexport const useAssignTechnician = options => {\n  _s1();\n  const queryClient = useQueryClient();\n  return useMutation({\n    mutationFn: ({\n      caseId,\n      technicianId\n    }) => DentFlowAPI.cases.assignTechnician(caseId, {\n      technician_id: technicianId\n    }),\n    onSuccess: updatedCase => {\n      queryClient.setQueryData(queryKeys.case(updatedCase.id), updatedCase);\n      queryClient.invalidateQueries({\n        queryKey: queryKeys.cases\n      });\n      queryClient.invalidateQueries({\n        queryKey: queryKeys.tasks\n      });\n    },\n    ...options\n  });\n};\n\n// ===== DASHBOARD HOOKS =====\n_s1(useAssignTechnician, \"YK0wzM21ECnncaq5SECwU+/SVdQ=\", false, function () {\n  return [useQueryClient, useMutation];\n});\nexport const useDashboardStats = options => {\n  _s10();\n  return useQuery({\n    queryKey: queryKeys.dashboardStats,\n    queryFn: () => DentFlowAPI.cases.getDashboardStats(),\n    staleTime: 30 * 1000,\n    // 30 seconds\n    refetchInterval: 60 * 1000,\n    // Refresh every minute\n    ...options\n  });\n};\n\n// ===== WORKFLOW HOOKS =====\n_s10(useDashboardStats, \"4ZpngI1uv+Uo3WQHEZmTQ5FNM+k=\", false, function () {\n  return [useQuery];\n});\nexport const useTasks = (filters, options) => {\n  _s11();\n  return useQuery({\n    queryKey: [...queryKeys.tasks, filters],\n    queryFn: () => DentFlowAPI.workflow.getTasks(filters),\n    staleTime: 1 * 60 * 1000,\n    ...options\n  });\n};\n_s11(useTasks, \"4ZpngI1uv+Uo3WQHEZmTQ5FNM+k=\", false, function () {\n  return [useQuery];\n});\nexport const useTechnicians = options => {\n  _s12();\n  return useQuery({\n    queryKey: queryKeys.technicians,\n    queryFn: () => DentFlowAPI.workflow.getTechnicians(),\n    staleTime: 5 * 60 * 1000,\n    // 5 minutes (technician data changes less frequently)\n    ...options\n  });\n};\n_s12(useTechnicians, \"4ZpngI1uv+Uo3WQHEZmTQ5FNM+k=\", false, function () {\n  return [useQuery];\n});\nexport const useWorkflowStages = options => {\n  _s13();\n  return useQuery({\n    queryKey: queryKeys.workflowStages,\n    queryFn: () => DentFlowAPI.workflow.getWorkflowStages(),\n    staleTime: 10 * 60 * 1000,\n    // 10 minutes (rarely changes)\n    ...options\n  });\n};\n_s13(useWorkflowStages, \"4ZpngI1uv+Uo3WQHEZmTQ5FNM+k=\", false, function () {\n  return [useQuery];\n});\nexport const useStartTask = options => {\n  _s14();\n  const queryClient = useQueryClient();\n  return useMutation({\n    mutationFn: taskId => DentFlowAPI.workflow.startTask(taskId),\n    onSuccess: () => {\n      queryClient.invalidateQueries({\n        queryKey: queryKeys.tasks\n      });\n    },\n    ...options\n  });\n};\n_s14(useStartTask, \"YK0wzM21ECnncaq5SECwU+/SVdQ=\", false, function () {\n  return [useQueryClient, useMutation];\n});\nexport const useCompleteTask = options => {\n  _s15();\n  const queryClient = useQueryClient();\n  return useMutation({\n    mutationFn: ({\n      taskId,\n      notes\n    }) => DentFlowAPI.workflow.completeTask(taskId, notes),\n    onSuccess: () => {\n      queryClient.invalidateQueries({\n        queryKey: queryKeys.tasks\n      });\n      queryClient.invalidateQueries({\n        queryKey: queryKeys.cases\n      });\n    },\n    ...options\n  });\n};\n\n// ===== BILLING HOOKS =====\n_s15(useCompleteTask, \"YK0wzM21ECnncaq5SECwU+/SVdQ=\", false, function () {\n  return [useQueryClient, useMutation];\n});\nexport const useInvoices = (filters, options) => {\n  _s16();\n  return useQuery({\n    queryKey: [...queryKeys.invoices, filters],\n    queryFn: () => DentFlowAPI.billing.getInvoices(filters),\n    staleTime: 2 * 60 * 1000,\n    ...options\n  });\n};\n_s16(useInvoices, \"4ZpngI1uv+Uo3WQHEZmTQ5FNM+k=\", false, function () {\n  return [useQuery];\n});\nexport const useInvoice = (id, options) => {\n  _s17();\n  return useQuery({\n    queryKey: queryKeys.invoice(id),\n    queryFn: () => DentFlowAPI.billing.getInvoiceById(id),\n    enabled: !!id,\n    staleTime: 1 * 60 * 1000,\n    ...options\n  });\n};\n_s17(useInvoice, \"4ZpngI1uv+Uo3WQHEZmTQ5FNM+k=\", false, function () {\n  return [useQuery];\n});\nexport const useCreateInvoice = options => {\n  _s18();\n  const queryClient = useQueryClient();\n  return useMutation({\n    mutationFn: data => DentFlowAPI.billing.createInvoice(data),\n    onSuccess: () => {\n      queryClient.invalidateQueries({\n        queryKey: queryKeys.invoices\n      });\n    },\n    ...options\n  });\n};\n\n// ===== SCHEDULE HOOKS =====\n_s18(useCreateInvoice, \"YK0wzM21ECnncaq5SECwU+/SVdQ=\", false, function () {\n  return [useQueryClient, useMutation];\n});\nexport const useAppointments = (filters, options) => {\n  _s19();\n  return useQuery({\n    queryKey: [...queryKeys.appointments, filters],\n    queryFn: () => DentFlowAPI.schedule.getAppointments(filters),\n    staleTime: 1 * 60 * 1000,\n    ...options\n  });\n};\n_s19(useAppointments, \"4ZpngI1uv+Uo3WQHEZmTQ5FNM+k=\", false, function () {\n  return [useQuery];\n});\nexport const useCreateAppointment = options => {\n  _s20();\n  const queryClient = useQueryClient();\n  return useMutation({\n    mutationFn: data => DentFlowAPI.schedule.createAppointment(data),\n    onSuccess: () => {\n      queryClient.invalidateQueries({\n        queryKey: queryKeys.appointments\n      });\n    },\n    ...options\n  });\n};\n\n// ===== REPORTS HOOKS =====\n_s20(useCreateAppointment, \"YK0wzM21ECnncaq5SECwU+/SVdQ=\", false, function () {\n  return [useQueryClient, useMutation];\n});\nexport const useReports = options => {\n  _s21();\n  return useQuery({\n    queryKey: queryKeys.reports,\n    queryFn: () => DentFlowAPI.reports.getReports(),\n    staleTime: 5 * 60 * 1000,\n    ...options\n  });\n};\n_s21(useReports, \"4ZpngI1uv+Uo3WQHEZmTQ5FNM+k=\", false, function () {\n  return [useQuery];\n});\nexport const useGenerateReport = options => {\n  _s22();\n  const queryClient = useQueryClient();\n  return useMutation({\n    mutationFn: data => DentFlowAPI.reports.generateReport(data),\n    onSuccess: () => {\n      queryClient.invalidateQueries({\n        queryKey: queryKeys.reports\n      });\n    },\n    ...options\n  });\n};\n\n// ===== UTILITY HOOKS =====\n_s22(useGenerateReport, \"YK0wzM21ECnncaq5SECwU+/SVdQ=\", false, function () {\n  return [useQueryClient, useMutation];\n});\nexport const useApiHealth = () => {\n  _s23();\n  return useQuery({\n    queryKey: ['api', 'health'],\n    queryFn: () => DentFlowAPI.healthCheck(),\n    staleTime: 30 * 1000,\n    refetchInterval: 60 * 1000\n  });\n};\n\n// Custom hook for optimistic updates\n_s23(useApiHealth, \"4ZpngI1uv+Uo3WQHEZmTQ5FNM+k=\", false, function () {\n  return [useQuery];\n});\nexport const useOptimisticUpdate = (queryKey, updateFn) => {\n  _s24();\n  const queryClient = useQueryClient();\n  const updateOptimistically = updateData => {\n    queryClient.setQueryData(queryKey, old => old ? updateFn(old) : old);\n  };\n  const revert = () => {\n    queryClient.invalidateQueries({\n      queryKey\n    });\n  };\n  return {\n    updateOptimistically,\n    revert\n  };\n};\n_s24(useOptimisticUpdate, \"4R+oYVB2Uc11P7bp1KcuhpkfaTw=\", false, function () {\n  return [useQueryClient];\n});", "map": {"version": 3, "names": ["useQuery", "useMutation", "useQueryClient", "DentFlowAPI", "casesService", "query<PERSON>eys", "cases", "case", "id", "casesByStatus", "status", "overdueCases", "dashboardStats", "tasks", "technicians", "workflowStages", "invoices", "invoice", "appointments", "appointment", "reports", "currentUser", "useCurrentUser", "options", "_s", "query<PERSON><PERSON>", "queryFn", "auth", "getCurrentUser", "enabled", "isAuthenticated", "staleTime", "useLogin", "_s2", "queryClient", "mutationFn", "email", "password", "login", "onSuccess", "data", "setQueryData", "user", "invalidateQueries", "useLogout", "_s3", "logout", "clear", "useCases", "_s4", "response", "getCases", "results", "useCase", "_s5", "getCaseById", "useCasesByStatus", "_s6", "getCasesByStatus", "useOverdueCases", "_s7", "getOverdueCases", "useCreateCase", "_s8", "createCase", "useUpdateCase", "_s9", "updateCase", "updatedCase", "useAdvanceCase", "_s0", "notes", "advanceCase", "useAssignTechnician", "_s1", "caseId", "technicianId", "assignTechnician", "technician_id", "useDashboardStats", "_s10", "getDashboardStats", "refetchInterval", "useTasks", "filters", "_s11", "workflow", "getTasks", "useTechnicians", "_s12", "getTechnicians", "useWorkflowStages", "_s13", "getWorkflowStages", "useStartTask", "_s14", "taskId", "startTask", "useCompleteTask", "_s15", "completeTask", "useInvoices", "_s16", "billing", "getInvoices", "useInvoice", "_s17", "getInvoiceById", "useCreateInvoice", "_s18", "createInvoice", "useAppointments", "_s19", "schedule", "getAppointments", "useCreateAppointment", "_s20", "createAppointment", "useReports", "_s21", "getReports", "useGenerateReport", "_s22", "generateReport", "useApiHealth", "_s23", "healthCheck", "useOptimisticUpdate", "updateFn", "_s24", "updateOptimistically", "updateData", "old", "revert"], "sources": ["C:/GPT4_PROJECTS/DENTAL_LAB_ARCHITECTURE_PATTERNS/dentflow/frontend/src/hooks/api.ts"], "sourcesContent": ["/**\n * React Query Hooks for DentFlow API\n * Provides data fetching, caching, and mutation hooks\n */\n\nimport { useQuery, useMutation, useQueryClient, UseQueryOptions, UseMutationOptions } from '@tanstack/react-query';\nimport { DentFlowAPI, Case, CreateCaseRequest, DashboardStats, Task, Technician, Invoice, Appointment } from '../api';\nimport { casesService, Case as ApiCase, CreateCaseRequest as ApiCreateCaseRequest } from '../services/api';\n\n// Query keys for React Query\nexport const queryKeys = {\n  cases: ['cases'] as const,\n  case: (id: string) => ['cases', id] as const,\n  casesByStatus: (status: string) => ['cases', 'status', status] as const,\n  overdueCases: ['cases', 'overdue'] as const,\n  dashboardStats: ['dashboard', 'stats'] as const,\n  tasks: ['tasks'] as const,\n  technicians: ['technicians'] as const,\n  workflowStages: ['workflow', 'stages'] as const,\n  invoices: ['invoices'] as const,\n  invoice: (id: string) => ['invoices', id] as const,\n  appointments: ['appointments'] as const,\n  appointment: (id: string) => ['appointments', id] as const,\n  reports: ['reports'] as const,\n  currentUser: ['auth', 'current-user'] as const,\n};\n\n// ===== AUTHENTICATION HOOKS =====\n\nexport const useCurrentUser = (options?: UseQueryOptions) => {\n  return useQuery({\n    queryKey: queryKeys.currentUser,\n    queryFn: () => DentFlowAPI.auth.getCurrentUser(),\n    enabled: DentFlowAPI.auth.isAuthenticated(),\n    staleTime: 5 * 60 * 1000, // 5 minutes\n    ...options,\n  });\n};\n\nexport const useLogin = (options?: UseMutationOptions<any, Error, { email: string; password: string }>) => {\n  const queryClient = useQueryClient();\n  \n  return useMutation({\n    mutationFn: ({ email, password }) => DentFlowAPI.auth.login({ email, password }),\n    onSuccess: (data) => {\n      queryClient.setQueryData(queryKeys.currentUser, data.user);\n      queryClient.invalidateQueries({ queryKey: ['auth'] });\n    },\n    ...options,\n  });\n};\n\nexport const useLogout = (options?: UseMutationOptions) => {\n  const queryClient = useQueryClient();\n  \n  return useMutation({\n    mutationFn: () => DentFlowAPI.auth.logout(),\n    onSuccess: () => {\n      queryClient.clear();\n    },\n    ...options,\n  });\n};\n\n// ===== CASES HOOKS =====\n\nexport const useCases = (options?: UseQueryOptions<ApiCase[]>) => {\n  return useQuery({\n    queryKey: queryKeys.cases,\n    queryFn: async () => {\n      const response = await casesService.getCases();\n      return response.results || [];\n    },\n    staleTime: 2 * 60 * 1000, // 2 minutes\n    ...options,\n  });\n};\n\nexport const useCase = (id: string, options?: UseQueryOptions<ApiCase>) => {\n  return useQuery({\n    queryKey: queryKeys.case(id),\n    queryFn: () => casesService.getCaseById(id),\n    enabled: !!id,\n    staleTime: 1 * 60 * 1000, // 1 minute\n    ...options,\n  });\n};\n\nexport const useCasesByStatus = (status: string, options?: UseQueryOptions<Case[]>) => {\n  return useQuery({\n    queryKey: queryKeys.casesByStatus(status),\n    queryFn: () => DentFlowAPI.cases.getCasesByStatus(status),\n    enabled: !!status,\n    staleTime: 1 * 60 * 1000,\n    ...options,\n  });\n};\n\nexport const useOverdueCases = (options?: UseQueryOptions<Case[]>) => {\n  return useQuery({\n    queryKey: queryKeys.overdueCases,\n    queryFn: () => DentFlowAPI.cases.getOverdueCases(),\n    staleTime: 30 * 1000, // 30 seconds (more critical data)\n    ...options,\n  });\n};\n\nexport const useCreateCase = (options?: UseMutationOptions<Case, Error, CreateCaseRequest>) => {\n  const queryClient = useQueryClient();\n  \n  return useMutation({\n    mutationFn: (data: CreateCaseRequest) => DentFlowAPI.cases.createCase(data),\n    onSuccess: () => {\n      queryClient.invalidateQueries({ queryKey: queryKeys.cases });\n      queryClient.invalidateQueries({ queryKey: queryKeys.dashboardStats });\n    },\n    ...options,\n  });\n};\n\nexport const useUpdateCase = (options?: UseMutationOptions<Case, Error, { id: string; data: Partial<Case> }>) => {\n  const queryClient = useQueryClient();\n  \n  return useMutation({\n    mutationFn: ({ id, data }) => DentFlowAPI.cases.updateCase(id, data),\n    onSuccess: (updatedCase) => {\n      queryClient.setQueryData(queryKeys.case(updatedCase.id), updatedCase);\n      queryClient.invalidateQueries({ queryKey: queryKeys.cases });\n      queryClient.invalidateQueries({ queryKey: queryKeys.dashboardStats });\n    },\n    ...options,\n  });\n};\n\nexport const useAdvanceCase = (options?: UseMutationOptions<Case, Error, { id: string; notes?: string }>) => {\n  const queryClient = useQueryClient();\n  \n  return useMutation({\n    mutationFn: ({ id, notes }) => DentFlowAPI.cases.advanceCase(id, { notes }),\n    onSuccess: (updatedCase) => {\n      queryClient.setQueryData(queryKeys.case(updatedCase.id), updatedCase);\n      queryClient.invalidateQueries({ queryKey: queryKeys.cases });\n      queryClient.invalidateQueries({ queryKey: queryKeys.tasks });\n    },\n    ...options,\n  });\n};\n\nexport const useAssignTechnician = (options?: UseMutationOptions<Case, Error, { caseId: string; technicianId: string }>) => {\n  const queryClient = useQueryClient();\n  \n  return useMutation({\n    mutationFn: ({ caseId, technicianId }) => DentFlowAPI.cases.assignTechnician(caseId, { technician_id: technicianId }),\n    onSuccess: (updatedCase) => {\n      queryClient.setQueryData(queryKeys.case(updatedCase.id), updatedCase);\n      queryClient.invalidateQueries({ queryKey: queryKeys.cases });\n      queryClient.invalidateQueries({ queryKey: queryKeys.tasks });\n    },\n    ...options,\n  });\n};\n\n// ===== DASHBOARD HOOKS =====\n\nexport const useDashboardStats = (options?: UseQueryOptions<DashboardStats>) => {\n  return useQuery({\n    queryKey: queryKeys.dashboardStats,\n    queryFn: () => DentFlowAPI.cases.getDashboardStats(),\n    staleTime: 30 * 1000, // 30 seconds\n    refetchInterval: 60 * 1000, // Refresh every minute\n    ...options,\n  });\n};\n\n// ===== WORKFLOW HOOKS =====\n\nexport const useTasks = (filters?: any, options?: UseQueryOptions<Task[]>) => {\n  return useQuery({\n    queryKey: [...queryKeys.tasks, filters],\n    queryFn: () => DentFlowAPI.workflow.getTasks(filters),\n    staleTime: 1 * 60 * 1000,\n    ...options,\n  });\n};\n\nexport const useTechnicians = (options?: UseQueryOptions<Technician[]>) => {\n  return useQuery({\n    queryKey: queryKeys.technicians,\n    queryFn: () => DentFlowAPI.workflow.getTechnicians(),\n    staleTime: 5 * 60 * 1000, // 5 minutes (technician data changes less frequently)\n    ...options,\n  });\n};\n\nexport const useWorkflowStages = (options?: UseQueryOptions) => {\n  return useQuery({\n    queryKey: queryKeys.workflowStages,\n    queryFn: () => DentFlowAPI.workflow.getWorkflowStages(),\n    staleTime: 10 * 60 * 1000, // 10 minutes (rarely changes)\n    ...options,\n  });\n};\n\nexport const useStartTask = (options?: UseMutationOptions<Task, Error, string>) => {\n  const queryClient = useQueryClient();\n  \n  return useMutation({\n    mutationFn: (taskId: string) => DentFlowAPI.workflow.startTask(taskId),\n    onSuccess: () => {\n      queryClient.invalidateQueries({ queryKey: queryKeys.tasks });\n    },\n    ...options,\n  });\n};\n\nexport const useCompleteTask = (options?: UseMutationOptions<Task, Error, { taskId: string; notes?: string }>) => {\n  const queryClient = useQueryClient();\n  \n  return useMutation({\n    mutationFn: ({ taskId, notes }) => DentFlowAPI.workflow.completeTask(taskId, notes),\n    onSuccess: () => {\n      queryClient.invalidateQueries({ queryKey: queryKeys.tasks });\n      queryClient.invalidateQueries({ queryKey: queryKeys.cases });\n    },\n    ...options,\n  });\n};\n\n// ===== BILLING HOOKS =====\n\nexport const useInvoices = (filters?: any, options?: UseQueryOptions<Invoice[]>) => {\n  return useQuery({\n    queryKey: [...queryKeys.invoices, filters],\n    queryFn: () => DentFlowAPI.billing.getInvoices(filters),\n    staleTime: 2 * 60 * 1000,\n    ...options,\n  });\n};\n\nexport const useInvoice = (id: string, options?: UseQueryOptions<Invoice>) => {\n  return useQuery({\n    queryKey: queryKeys.invoice(id),\n    queryFn: () => DentFlowAPI.billing.getInvoiceById(id),\n    enabled: !!id,\n    staleTime: 1 * 60 * 1000,\n    ...options,\n  });\n};\n\nexport const useCreateInvoice = (options?: UseMutationOptions<Invoice, Error, any>) => {\n  const queryClient = useQueryClient();\n  \n  return useMutation({\n    mutationFn: (data) => DentFlowAPI.billing.createInvoice(data),\n    onSuccess: () => {\n      queryClient.invalidateQueries({ queryKey: queryKeys.invoices });\n    },\n    ...options,\n  });\n};\n\n// ===== SCHEDULE HOOKS =====\n\nexport const useAppointments = (filters?: any, options?: UseQueryOptions<Appointment[]>) => {\n  return useQuery({\n    queryKey: [...queryKeys.appointments, filters],\n    queryFn: () => DentFlowAPI.schedule.getAppointments(filters),\n    staleTime: 1 * 60 * 1000,\n    ...options,\n  });\n};\n\nexport const useCreateAppointment = (options?: UseMutationOptions<Appointment, Error, any>) => {\n  const queryClient = useQueryClient();\n  \n  return useMutation({\n    mutationFn: (data) => DentFlowAPI.schedule.createAppointment(data),\n    onSuccess: () => {\n      queryClient.invalidateQueries({ queryKey: queryKeys.appointments });\n    },\n    ...options,\n  });\n};\n\n// ===== REPORTS HOOKS =====\n\nexport const useReports = (options?: UseQueryOptions) => {\n  return useQuery({\n    queryKey: queryKeys.reports,\n    queryFn: () => DentFlowAPI.reports.getReports(),\n    staleTime: 5 * 60 * 1000,\n    ...options,\n  });\n};\n\nexport const useGenerateReport = (options?: UseMutationOptions) => {\n  const queryClient = useQueryClient();\n  \n  return useMutation({\n    mutationFn: (data: any) => DentFlowAPI.reports.generateReport(data),\n    onSuccess: () => {\n      queryClient.invalidateQueries({ queryKey: queryKeys.reports });\n    },\n    ...options,\n  });\n};\n\n// ===== UTILITY HOOKS =====\n\nexport const useApiHealth = () => {\n  return useQuery({\n    queryKey: ['api', 'health'],\n    queryFn: () => DentFlowAPI.healthCheck(),\n    staleTime: 30 * 1000,\n    refetchInterval: 60 * 1000,\n  });\n};\n\n// Custom hook for optimistic updates\nexport const useOptimisticUpdate = <T>(queryKey: any[], updateFn: (old: T) => T) => {\n  const queryClient = useQueryClient();\n  \n  const updateOptimistically = (updateData: any) => {\n    queryClient.setQueryData(queryKey, (old: T | undefined) => old ? updateFn(old) : old);\n  };\n  \n  const revert = () => {\n    queryClient.invalidateQueries({ queryKey });\n  };\n  \n  return { updateOptimistically, revert };\n};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;AACA;AACA;AACA;;AAEA,SAASA,QAAQ,EAAEC,WAAW,EAAEC,cAAc,QAA6C,uBAAuB;AAClH,SAASC,WAAW,QAAyF,QAAQ;AACrH,SAASC,YAAY,QAAoE,iBAAiB;;AAE1G;AACA,OAAO,MAAMC,SAAS,GAAG;EACvBC,KAAK,EAAE,CAAC,OAAO,CAAU;EACzBC,IAAI,EAAGC,EAAU,IAAK,CAAC,OAAO,EAAEA,EAAE,CAAU;EAC5CC,aAAa,EAAGC,MAAc,IAAK,CAAC,OAAO,EAAE,QAAQ,EAAEA,MAAM,CAAU;EACvEC,YAAY,EAAE,CAAC,OAAO,EAAE,SAAS,CAAU;EAC3CC,cAAc,EAAE,CAAC,WAAW,EAAE,OAAO,CAAU;EAC/CC,KAAK,EAAE,CAAC,OAAO,CAAU;EACzBC,WAAW,EAAE,CAAC,aAAa,CAAU;EACrCC,cAAc,EAAE,CAAC,UAAU,EAAE,QAAQ,CAAU;EAC/CC,QAAQ,EAAE,CAAC,UAAU,CAAU;EAC/BC,OAAO,EAAGT,EAAU,IAAK,CAAC,UAAU,EAAEA,EAAE,CAAU;EAClDU,YAAY,EAAE,CAAC,cAAc,CAAU;EACvCC,WAAW,EAAGX,EAAU,IAAK,CAAC,cAAc,EAAEA,EAAE,CAAU;EAC1DY,OAAO,EAAE,CAAC,SAAS,CAAU;EAC7BC,WAAW,EAAE,CAAC,MAAM,EAAE,cAAc;AACtC,CAAC;;AAED;;AAEA,OAAO,MAAMC,cAAc,GAAIC,OAAyB,IAAK;EAAAC,EAAA;EAC3D,OAAOxB,QAAQ,CAAC;IACdyB,QAAQ,EAAEpB,SAAS,CAACgB,WAAW;IAC/BK,OAAO,EAAEA,CAAA,KAAMvB,WAAW,CAACwB,IAAI,CAACC,cAAc,CAAC,CAAC;IAChDC,OAAO,EAAE1B,WAAW,CAACwB,IAAI,CAACG,eAAe,CAAC,CAAC;IAC3CC,SAAS,EAAE,CAAC,GAAG,EAAE,GAAG,IAAI;IAAE;IAC1B,GAAGR;EACL,CAAC,CAAC;AACJ,CAAC;AAACC,EAAA,CARWF,cAAc;EAAA,QAClBtB,QAAQ;AAAA;AASjB,OAAO,MAAMgC,QAAQ,GAAIT,OAA6E,IAAK;EAAAU,GAAA;EACzG,MAAMC,WAAW,GAAGhC,cAAc,CAAC,CAAC;EAEpC,OAAOD,WAAW,CAAC;IACjBkC,UAAU,EAAEA,CAAC;MAAEC,KAAK;MAAEC;IAAS,CAAC,KAAKlC,WAAW,CAACwB,IAAI,CAACW,KAAK,CAAC;MAAEF,KAAK;MAAEC;IAAS,CAAC,CAAC;IAChFE,SAAS,EAAGC,IAAI,IAAK;MACnBN,WAAW,CAACO,YAAY,CAACpC,SAAS,CAACgB,WAAW,EAAEmB,IAAI,CAACE,IAAI,CAAC;MAC1DR,WAAW,CAACS,iBAAiB,CAAC;QAAElB,QAAQ,EAAE,CAAC,MAAM;MAAE,CAAC,CAAC;IACvD,CAAC;IACD,GAAGF;EACL,CAAC,CAAC;AACJ,CAAC;AAACU,GAAA,CAXWD,QAAQ;EAAA,QACC9B,cAAc,EAE3BD,WAAW;AAAA;AAUpB,OAAO,MAAM2C,SAAS,GAAIrB,OAA4B,IAAK;EAAAsB,GAAA;EACzD,MAAMX,WAAW,GAAGhC,cAAc,CAAC,CAAC;EAEpC,OAAOD,WAAW,CAAC;IACjBkC,UAAU,EAAEA,CAAA,KAAMhC,WAAW,CAACwB,IAAI,CAACmB,MAAM,CAAC,CAAC;IAC3CP,SAAS,EAAEA,CAAA,KAAM;MACfL,WAAW,CAACa,KAAK,CAAC,CAAC;IACrB,CAAC;IACD,GAAGxB;EACL,CAAC,CAAC;AACJ,CAAC;;AAED;AAAAsB,GAAA,CAZaD,SAAS;EAAA,QACA1C,cAAc,EAE3BD,WAAW;AAAA;AAWpB,OAAO,MAAM+C,QAAQ,GAAIzB,OAAoC,IAAK;EAAA0B,GAAA;EAChE,OAAOjD,QAAQ,CAAC;IACdyB,QAAQ,EAAEpB,SAAS,CAACC,KAAK;IACzBoB,OAAO,EAAE,MAAAA,CAAA,KAAY;MACnB,MAAMwB,QAAQ,GAAG,MAAM9C,YAAY,CAAC+C,QAAQ,CAAC,CAAC;MAC9C,OAAOD,QAAQ,CAACE,OAAO,IAAI,EAAE;IAC/B,CAAC;IACDrB,SAAS,EAAE,CAAC,GAAG,EAAE,GAAG,IAAI;IAAE;IAC1B,GAAGR;EACL,CAAC,CAAC;AACJ,CAAC;AAAC0B,GAAA,CAVWD,QAAQ;EAAA,QACZhD,QAAQ;AAAA;AAWjB,OAAO,MAAMqD,OAAO,GAAGA,CAAC7C,EAAU,EAAEe,OAAkC,KAAK;EAAA+B,GAAA;EACzE,OAAOtD,QAAQ,CAAC;IACdyB,QAAQ,EAAEpB,SAAS,CAACE,IAAI,CAACC,EAAE,CAAC;IAC5BkB,OAAO,EAAEA,CAAA,KAAMtB,YAAY,CAACmD,WAAW,CAAC/C,EAAE,CAAC;IAC3CqB,OAAO,EAAE,CAAC,CAACrB,EAAE;IACbuB,SAAS,EAAE,CAAC,GAAG,EAAE,GAAG,IAAI;IAAE;IAC1B,GAAGR;EACL,CAAC,CAAC;AACJ,CAAC;AAAC+B,GAAA,CARWD,OAAO;EAAA,QACXrD,QAAQ;AAAA;AASjB,OAAO,MAAMwD,gBAAgB,GAAGA,CAAC9C,MAAc,EAAEa,OAAiC,KAAK;EAAAkC,GAAA;EACrF,OAAOzD,QAAQ,CAAC;IACdyB,QAAQ,EAAEpB,SAAS,CAACI,aAAa,CAACC,MAAM,CAAC;IACzCgB,OAAO,EAAEA,CAAA,KAAMvB,WAAW,CAACG,KAAK,CAACoD,gBAAgB,CAAChD,MAAM,CAAC;IACzDmB,OAAO,EAAE,CAAC,CAACnB,MAAM;IACjBqB,SAAS,EAAE,CAAC,GAAG,EAAE,GAAG,IAAI;IACxB,GAAGR;EACL,CAAC,CAAC;AACJ,CAAC;AAACkC,GAAA,CARWD,gBAAgB;EAAA,QACpBxD,QAAQ;AAAA;AASjB,OAAO,MAAM2D,eAAe,GAAIpC,OAAiC,IAAK;EAAAqC,GAAA;EACpE,OAAO5D,QAAQ,CAAC;IACdyB,QAAQ,EAAEpB,SAAS,CAACM,YAAY;IAChCe,OAAO,EAAEA,CAAA,KAAMvB,WAAW,CAACG,KAAK,CAACuD,eAAe,CAAC,CAAC;IAClD9B,SAAS,EAAE,EAAE,GAAG,IAAI;IAAE;IACtB,GAAGR;EACL,CAAC,CAAC;AACJ,CAAC;AAACqC,GAAA,CAPWD,eAAe;EAAA,QACnB3D,QAAQ;AAAA;AAQjB,OAAO,MAAM8D,aAAa,GAAIvC,OAA4D,IAAK;EAAAwC,GAAA;EAC7F,MAAM7B,WAAW,GAAGhC,cAAc,CAAC,CAAC;EAEpC,OAAOD,WAAW,CAAC;IACjBkC,UAAU,EAAGK,IAAuB,IAAKrC,WAAW,CAACG,KAAK,CAAC0D,UAAU,CAACxB,IAAI,CAAC;IAC3ED,SAAS,EAAEA,CAAA,KAAM;MACfL,WAAW,CAACS,iBAAiB,CAAC;QAAElB,QAAQ,EAAEpB,SAAS,CAACC;MAAM,CAAC,CAAC;MAC5D4B,WAAW,CAACS,iBAAiB,CAAC;QAAElB,QAAQ,EAAEpB,SAAS,CAACO;MAAe,CAAC,CAAC;IACvE,CAAC;IACD,GAAGW;EACL,CAAC,CAAC;AACJ,CAAC;AAACwC,GAAA,CAXWD,aAAa;EAAA,QACJ5D,cAAc,EAE3BD,WAAW;AAAA;AAUpB,OAAO,MAAMgE,aAAa,GAAI1C,OAA8E,IAAK;EAAA2C,GAAA;EAC/G,MAAMhC,WAAW,GAAGhC,cAAc,CAAC,CAAC;EAEpC,OAAOD,WAAW,CAAC;IACjBkC,UAAU,EAAEA,CAAC;MAAE3B,EAAE;MAAEgC;IAAK,CAAC,KAAKrC,WAAW,CAACG,KAAK,CAAC6D,UAAU,CAAC3D,EAAE,EAAEgC,IAAI,CAAC;IACpED,SAAS,EAAG6B,WAAW,IAAK;MAC1BlC,WAAW,CAACO,YAAY,CAACpC,SAAS,CAACE,IAAI,CAAC6D,WAAW,CAAC5D,EAAE,CAAC,EAAE4D,WAAW,CAAC;MACrElC,WAAW,CAACS,iBAAiB,CAAC;QAAElB,QAAQ,EAAEpB,SAAS,CAACC;MAAM,CAAC,CAAC;MAC5D4B,WAAW,CAACS,iBAAiB,CAAC;QAAElB,QAAQ,EAAEpB,SAAS,CAACO;MAAe,CAAC,CAAC;IACvE,CAAC;IACD,GAAGW;EACL,CAAC,CAAC;AACJ,CAAC;AAAC2C,GAAA,CAZWD,aAAa;EAAA,QACJ/D,cAAc,EAE3BD,WAAW;AAAA;AAWpB,OAAO,MAAMoE,cAAc,GAAI9C,OAAyE,IAAK;EAAA+C,GAAA;EAC3G,MAAMpC,WAAW,GAAGhC,cAAc,CAAC,CAAC;EAEpC,OAAOD,WAAW,CAAC;IACjBkC,UAAU,EAAEA,CAAC;MAAE3B,EAAE;MAAE+D;IAAM,CAAC,KAAKpE,WAAW,CAACG,KAAK,CAACkE,WAAW,CAAChE,EAAE,EAAE;MAAE+D;IAAM,CAAC,CAAC;IAC3EhC,SAAS,EAAG6B,WAAW,IAAK;MAC1BlC,WAAW,CAACO,YAAY,CAACpC,SAAS,CAACE,IAAI,CAAC6D,WAAW,CAAC5D,EAAE,CAAC,EAAE4D,WAAW,CAAC;MACrElC,WAAW,CAACS,iBAAiB,CAAC;QAAElB,QAAQ,EAAEpB,SAAS,CAACC;MAAM,CAAC,CAAC;MAC5D4B,WAAW,CAACS,iBAAiB,CAAC;QAAElB,QAAQ,EAAEpB,SAAS,CAACQ;MAAM,CAAC,CAAC;IAC9D,CAAC;IACD,GAAGU;EACL,CAAC,CAAC;AACJ,CAAC;AAAC+C,GAAA,CAZWD,cAAc;EAAA,QACLnE,cAAc,EAE3BD,WAAW;AAAA;AAWpB,OAAO,MAAMwE,mBAAmB,GAAIlD,OAAmF,IAAK;EAAAmD,GAAA;EAC1H,MAAMxC,WAAW,GAAGhC,cAAc,CAAC,CAAC;EAEpC,OAAOD,WAAW,CAAC;IACjBkC,UAAU,EAAEA,CAAC;MAAEwC,MAAM;MAAEC;IAAa,CAAC,KAAKzE,WAAW,CAACG,KAAK,CAACuE,gBAAgB,CAACF,MAAM,EAAE;MAAEG,aAAa,EAAEF;IAAa,CAAC,CAAC;IACrHrC,SAAS,EAAG6B,WAAW,IAAK;MAC1BlC,WAAW,CAACO,YAAY,CAACpC,SAAS,CAACE,IAAI,CAAC6D,WAAW,CAAC5D,EAAE,CAAC,EAAE4D,WAAW,CAAC;MACrElC,WAAW,CAACS,iBAAiB,CAAC;QAAElB,QAAQ,EAAEpB,SAAS,CAACC;MAAM,CAAC,CAAC;MAC5D4B,WAAW,CAACS,iBAAiB,CAAC;QAAElB,QAAQ,EAAEpB,SAAS,CAACQ;MAAM,CAAC,CAAC;IAC9D,CAAC;IACD,GAAGU;EACL,CAAC,CAAC;AACJ,CAAC;;AAED;AAAAmD,GAAA,CAdaD,mBAAmB;EAAA,QACVvE,cAAc,EAE3BD,WAAW;AAAA;AAapB,OAAO,MAAM8E,iBAAiB,GAAIxD,OAAyC,IAAK;EAAAyD,IAAA;EAC9E,OAAOhF,QAAQ,CAAC;IACdyB,QAAQ,EAAEpB,SAAS,CAACO,cAAc;IAClCc,OAAO,EAAEA,CAAA,KAAMvB,WAAW,CAACG,KAAK,CAAC2E,iBAAiB,CAAC,CAAC;IACpDlD,SAAS,EAAE,EAAE,GAAG,IAAI;IAAE;IACtBmD,eAAe,EAAE,EAAE,GAAG,IAAI;IAAE;IAC5B,GAAG3D;EACL,CAAC,CAAC;AACJ,CAAC;;AAED;AAAAyD,IAAA,CAVaD,iBAAiB;EAAA,QACrB/E,QAAQ;AAAA;AAWjB,OAAO,MAAMmF,QAAQ,GAAGA,CAACC,OAAa,EAAE7D,OAAiC,KAAK;EAAA8D,IAAA;EAC5E,OAAOrF,QAAQ,CAAC;IACdyB,QAAQ,EAAE,CAAC,GAAGpB,SAAS,CAACQ,KAAK,EAAEuE,OAAO,CAAC;IACvC1D,OAAO,EAAEA,CAAA,KAAMvB,WAAW,CAACmF,QAAQ,CAACC,QAAQ,CAACH,OAAO,CAAC;IACrDrD,SAAS,EAAE,CAAC,GAAG,EAAE,GAAG,IAAI;IACxB,GAAGR;EACL,CAAC,CAAC;AACJ,CAAC;AAAC8D,IAAA,CAPWF,QAAQ;EAAA,QACZnF,QAAQ;AAAA;AAQjB,OAAO,MAAMwF,cAAc,GAAIjE,OAAuC,IAAK;EAAAkE,IAAA;EACzE,OAAOzF,QAAQ,CAAC;IACdyB,QAAQ,EAAEpB,SAAS,CAACS,WAAW;IAC/BY,OAAO,EAAEA,CAAA,KAAMvB,WAAW,CAACmF,QAAQ,CAACI,cAAc,CAAC,CAAC;IACpD3D,SAAS,EAAE,CAAC,GAAG,EAAE,GAAG,IAAI;IAAE;IAC1B,GAAGR;EACL,CAAC,CAAC;AACJ,CAAC;AAACkE,IAAA,CAPWD,cAAc;EAAA,QAClBxF,QAAQ;AAAA;AAQjB,OAAO,MAAM2F,iBAAiB,GAAIpE,OAAyB,IAAK;EAAAqE,IAAA;EAC9D,OAAO5F,QAAQ,CAAC;IACdyB,QAAQ,EAAEpB,SAAS,CAACU,cAAc;IAClCW,OAAO,EAAEA,CAAA,KAAMvB,WAAW,CAACmF,QAAQ,CAACO,iBAAiB,CAAC,CAAC;IACvD9D,SAAS,EAAE,EAAE,GAAG,EAAE,GAAG,IAAI;IAAE;IAC3B,GAAGR;EACL,CAAC,CAAC;AACJ,CAAC;AAACqE,IAAA,CAPWD,iBAAiB;EAAA,QACrB3F,QAAQ;AAAA;AAQjB,OAAO,MAAM8F,YAAY,GAAIvE,OAAiD,IAAK;EAAAwE,IAAA;EACjF,MAAM7D,WAAW,GAAGhC,cAAc,CAAC,CAAC;EAEpC,OAAOD,WAAW,CAAC;IACjBkC,UAAU,EAAG6D,MAAc,IAAK7F,WAAW,CAACmF,QAAQ,CAACW,SAAS,CAACD,MAAM,CAAC;IACtEzD,SAAS,EAAEA,CAAA,KAAM;MACfL,WAAW,CAACS,iBAAiB,CAAC;QAAElB,QAAQ,EAAEpB,SAAS,CAACQ;MAAM,CAAC,CAAC;IAC9D,CAAC;IACD,GAAGU;EACL,CAAC,CAAC;AACJ,CAAC;AAACwE,IAAA,CAVWD,YAAY;EAAA,QACH5F,cAAc,EAE3BD,WAAW;AAAA;AASpB,OAAO,MAAMiG,eAAe,GAAI3E,OAA6E,IAAK;EAAA4E,IAAA;EAChH,MAAMjE,WAAW,GAAGhC,cAAc,CAAC,CAAC;EAEpC,OAAOD,WAAW,CAAC;IACjBkC,UAAU,EAAEA,CAAC;MAAE6D,MAAM;MAAEzB;IAAM,CAAC,KAAKpE,WAAW,CAACmF,QAAQ,CAACc,YAAY,CAACJ,MAAM,EAAEzB,KAAK,CAAC;IACnFhC,SAAS,EAAEA,CAAA,KAAM;MACfL,WAAW,CAACS,iBAAiB,CAAC;QAAElB,QAAQ,EAAEpB,SAAS,CAACQ;MAAM,CAAC,CAAC;MAC5DqB,WAAW,CAACS,iBAAiB,CAAC;QAAElB,QAAQ,EAAEpB,SAAS,CAACC;MAAM,CAAC,CAAC;IAC9D,CAAC;IACD,GAAGiB;EACL,CAAC,CAAC;AACJ,CAAC;;AAED;AAAA4E,IAAA,CAbaD,eAAe;EAAA,QACNhG,cAAc,EAE3BD,WAAW;AAAA;AAYpB,OAAO,MAAMoG,WAAW,GAAGA,CAACjB,OAAa,EAAE7D,OAAoC,KAAK;EAAA+E,IAAA;EAClF,OAAOtG,QAAQ,CAAC;IACdyB,QAAQ,EAAE,CAAC,GAAGpB,SAAS,CAACW,QAAQ,EAAEoE,OAAO,CAAC;IAC1C1D,OAAO,EAAEA,CAAA,KAAMvB,WAAW,CAACoG,OAAO,CAACC,WAAW,CAACpB,OAAO,CAAC;IACvDrD,SAAS,EAAE,CAAC,GAAG,EAAE,GAAG,IAAI;IACxB,GAAGR;EACL,CAAC,CAAC;AACJ,CAAC;AAAC+E,IAAA,CAPWD,WAAW;EAAA,QACfrG,QAAQ;AAAA;AAQjB,OAAO,MAAMyG,UAAU,GAAGA,CAACjG,EAAU,EAAEe,OAAkC,KAAK;EAAAmF,IAAA;EAC5E,OAAO1G,QAAQ,CAAC;IACdyB,QAAQ,EAAEpB,SAAS,CAACY,OAAO,CAACT,EAAE,CAAC;IAC/BkB,OAAO,EAAEA,CAAA,KAAMvB,WAAW,CAACoG,OAAO,CAACI,cAAc,CAACnG,EAAE,CAAC;IACrDqB,OAAO,EAAE,CAAC,CAACrB,EAAE;IACbuB,SAAS,EAAE,CAAC,GAAG,EAAE,GAAG,IAAI;IACxB,GAAGR;EACL,CAAC,CAAC;AACJ,CAAC;AAACmF,IAAA,CARWD,UAAU;EAAA,QACdzG,QAAQ;AAAA;AASjB,OAAO,MAAM4G,gBAAgB,GAAIrF,OAAiD,IAAK;EAAAsF,IAAA;EACrF,MAAM3E,WAAW,GAAGhC,cAAc,CAAC,CAAC;EAEpC,OAAOD,WAAW,CAAC;IACjBkC,UAAU,EAAGK,IAAI,IAAKrC,WAAW,CAACoG,OAAO,CAACO,aAAa,CAACtE,IAAI,CAAC;IAC7DD,SAAS,EAAEA,CAAA,KAAM;MACfL,WAAW,CAACS,iBAAiB,CAAC;QAAElB,QAAQ,EAAEpB,SAAS,CAACW;MAAS,CAAC,CAAC;IACjE,CAAC;IACD,GAAGO;EACL,CAAC,CAAC;AACJ,CAAC;;AAED;AAAAsF,IAAA,CAZaD,gBAAgB;EAAA,QACP1G,cAAc,EAE3BD,WAAW;AAAA;AAWpB,OAAO,MAAM8G,eAAe,GAAGA,CAAC3B,OAAa,EAAE7D,OAAwC,KAAK;EAAAyF,IAAA;EAC1F,OAAOhH,QAAQ,CAAC;IACdyB,QAAQ,EAAE,CAAC,GAAGpB,SAAS,CAACa,YAAY,EAAEkE,OAAO,CAAC;IAC9C1D,OAAO,EAAEA,CAAA,KAAMvB,WAAW,CAAC8G,QAAQ,CAACC,eAAe,CAAC9B,OAAO,CAAC;IAC5DrD,SAAS,EAAE,CAAC,GAAG,EAAE,GAAG,IAAI;IACxB,GAAGR;EACL,CAAC,CAAC;AACJ,CAAC;AAACyF,IAAA,CAPWD,eAAe;EAAA,QACnB/G,QAAQ;AAAA;AAQjB,OAAO,MAAMmH,oBAAoB,GAAI5F,OAAqD,IAAK;EAAA6F,IAAA;EAC7F,MAAMlF,WAAW,GAAGhC,cAAc,CAAC,CAAC;EAEpC,OAAOD,WAAW,CAAC;IACjBkC,UAAU,EAAGK,IAAI,IAAKrC,WAAW,CAAC8G,QAAQ,CAACI,iBAAiB,CAAC7E,IAAI,CAAC;IAClED,SAAS,EAAEA,CAAA,KAAM;MACfL,WAAW,CAACS,iBAAiB,CAAC;QAAElB,QAAQ,EAAEpB,SAAS,CAACa;MAAa,CAAC,CAAC;IACrE,CAAC;IACD,GAAGK;EACL,CAAC,CAAC;AACJ,CAAC;;AAED;AAAA6F,IAAA,CAZaD,oBAAoB;EAAA,QACXjH,cAAc,EAE3BD,WAAW;AAAA;AAWpB,OAAO,MAAMqH,UAAU,GAAI/F,OAAyB,IAAK;EAAAgG,IAAA;EACvD,OAAOvH,QAAQ,CAAC;IACdyB,QAAQ,EAAEpB,SAAS,CAACe,OAAO;IAC3BM,OAAO,EAAEA,CAAA,KAAMvB,WAAW,CAACiB,OAAO,CAACoG,UAAU,CAAC,CAAC;IAC/CzF,SAAS,EAAE,CAAC,GAAG,EAAE,GAAG,IAAI;IACxB,GAAGR;EACL,CAAC,CAAC;AACJ,CAAC;AAACgG,IAAA,CAPWD,UAAU;EAAA,QACdtH,QAAQ;AAAA;AAQjB,OAAO,MAAMyH,iBAAiB,GAAIlG,OAA4B,IAAK;EAAAmG,IAAA;EACjE,MAAMxF,WAAW,GAAGhC,cAAc,CAAC,CAAC;EAEpC,OAAOD,WAAW,CAAC;IACjBkC,UAAU,EAAGK,IAAS,IAAKrC,WAAW,CAACiB,OAAO,CAACuG,cAAc,CAACnF,IAAI,CAAC;IACnED,SAAS,EAAEA,CAAA,KAAM;MACfL,WAAW,CAACS,iBAAiB,CAAC;QAAElB,QAAQ,EAAEpB,SAAS,CAACe;MAAQ,CAAC,CAAC;IAChE,CAAC;IACD,GAAGG;EACL,CAAC,CAAC;AACJ,CAAC;;AAED;AAAAmG,IAAA,CAZaD,iBAAiB;EAAA,QACRvH,cAAc,EAE3BD,WAAW;AAAA;AAWpB,OAAO,MAAM2H,YAAY,GAAGA,CAAA,KAAM;EAAAC,IAAA;EAChC,OAAO7H,QAAQ,CAAC;IACdyB,QAAQ,EAAE,CAAC,KAAK,EAAE,QAAQ,CAAC;IAC3BC,OAAO,EAAEA,CAAA,KAAMvB,WAAW,CAAC2H,WAAW,CAAC,CAAC;IACxC/F,SAAS,EAAE,EAAE,GAAG,IAAI;IACpBmD,eAAe,EAAE,EAAE,GAAG;EACxB,CAAC,CAAC;AACJ,CAAC;;AAED;AAAA2C,IAAA,CATaD,YAAY;EAAA,QAChB5H,QAAQ;AAAA;AASjB,OAAO,MAAM+H,mBAAmB,GAAGA,CAAItG,QAAe,EAAEuG,QAAuB,KAAK;EAAAC,IAAA;EAClF,MAAM/F,WAAW,GAAGhC,cAAc,CAAC,CAAC;EAEpC,MAAMgI,oBAAoB,GAAIC,UAAe,IAAK;IAChDjG,WAAW,CAACO,YAAY,CAAChB,QAAQ,EAAG2G,GAAkB,IAAKA,GAAG,GAAGJ,QAAQ,CAACI,GAAG,CAAC,GAAGA,GAAG,CAAC;EACvF,CAAC;EAED,MAAMC,MAAM,GAAGA,CAAA,KAAM;IACnBnG,WAAW,CAACS,iBAAiB,CAAC;MAAElB;IAAS,CAAC,CAAC;EAC7C,CAAC;EAED,OAAO;IAAEyG,oBAAoB;IAAEG;EAAO,CAAC;AACzC,CAAC;AAACJ,IAAA,CAZWF,mBAAmB;EAAA,QACV7H,cAAc;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}