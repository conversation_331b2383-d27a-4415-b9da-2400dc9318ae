#!/usr/bin/env python3
"""
Simple test data creation script
"""

import os
import django
from decimal import Decimal
from datetime import datetime, timedelta

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'dentflow_project.settings')
django.setup()

from django.contrib.auth.models import User
from apps.tenants.models import Tenant
from apps.inventory.models import MaterialCategory, Material, ItemCategory, Item, ItemMaterialComposition
from apps.billing.models import PriceList, ItemPrice
from apps.cases.models import Case, CaseStatus

def create_test_data():
    print("🚀 Creating test data...")
    
    # Create admin user
    admin_user, created = User.objects.get_or_create(
        username='admin',
        defaults={
            'email': '<EMAIL>',
            'first_name': 'Admin',
            'last_name': 'User',
            'is_staff': True,
            'is_superuser': True
        }
    )
    if created or not admin_user.check_password('admin123'):
        admin_user.set_password('admin123')
        admin_user.save()
    print(f"✅ Admin user: {'created' if created else 'updated'}")
    
    # Create tenant
    tenant, created = Tenant.objects.get_or_create(
        subdomain='demo-lab',
        defaults={
            'name': 'Demo Dental Laboratory',
            'email': '<EMAIL>',
            'phone': '******-0123',
            'address': '123 Dental Street, Lab City, LC 12345',
            'plan': 'professional',
            'max_users': 10,
            'max_cases_per_month': 100,
            'is_active': True
        }
    )
    print(f"✅ Tenant: {'created' if created else 'found'}")
    
    # Create material categories
    ceramic_cat, _ = MaterialCategory.objects.get_or_create(
        name='Ceramics',
        defaults={'description': 'Ceramic materials for dental restorations'}
    )
    metal_cat, _ = MaterialCategory.objects.get_or_create(
        name='Metals',
        defaults={'description': 'Metal alloys for dental work'}
    )
    print("✅ Material categories created")
    
    # Create materials
    zirconia, _ = Material.objects.get_or_create(
        name='Zirconia Block',
        defaults={
            'category': ceramic_cat,
            'unit': 'piece',
            'standard_cost': Decimal('52.00'),
            'current_stock': Decimal('30.0000'),
            'minimum_stock': Decimal('5.0000'),
            'description': 'High-strength zirconia block'
        }
    )
    
    titanium, _ = Material.objects.get_or_create(
        name='Titanium Alloy',
        defaults={
            'category': metal_cat,
            'unit': 'gram',
            'standard_cost': Decimal('3.20'),
            'current_stock': Decimal('800.0000'),
            'minimum_stock': Decimal('100.0000'),
            'description': 'Grade 2 titanium alloy'
        }
    )
    
    porcelain, _ = Material.objects.get_or_create(
        name='Porcelain Powder',
        defaults={
            'category': ceramic_cat,
            'unit': 'gram',
            'standard_cost': Decimal('0.85'),
            'current_stock': Decimal('2500.0000'),
            'minimum_stock': Decimal('500.0000'),
            'description': 'Feldspathic porcelain powder'
        }
    )
    print("✅ Materials created")
    
    # Create item categories
    crown_cat, _ = ItemCategory.objects.get_or_create(
        name='Crowns',
        defaults={'description': 'All types of dental crowns'}
    )
    bridge_cat, _ = ItemCategory.objects.get_or_create(
        name='Bridges',
        defaults={'description': 'Fixed dental bridges'}
    )
    print("✅ Item categories created")
    
    # Create items
    zirconia_crown, _ = Item.objects.get_or_create(
        name='Zirconia Crown',
        defaults={
            'category': crown_cat,
            'description': 'High-translucency zirconia crown',
            'estimated_production_time_minutes': 180,
            'is_active': True
        }
    )
    
    titanium_bridge, _ = Item.objects.get_or_create(
        name='Titanium Bridge (3-unit)',
        defaults={
            'category': bridge_cat,
            'description': '3-unit titanium bridge',
            'estimated_production_time_minutes': 360,
            'is_active': True
        }
    )
    
    pfm_crown, _ = Item.objects.get_or_create(
        name='PFM Crown',
        defaults={
            'category': crown_cat,
            'description': 'Porcelain fused to metal crown',
            'estimated_production_time_minutes': 240,
            'is_active': True
        }
    )
    print("✅ Items created")
    
    # Create material compositions
    ItemMaterialComposition.objects.get_or_create(
        item=zirconia_crown,
        material=zirconia,
        defaults={
            'quantity': Decimal('0.6000'),
            'waste_factor': Decimal('12.00'),
            'notes': 'Zirconia block for crown'
        }
    )
    
    ItemMaterialComposition.objects.get_or_create(
        item=zirconia_crown,
        material=porcelain,
        defaults={
            'quantity': Decimal('2.5000'),
            'waste_factor': Decimal('8.00'),
            'notes': 'Porcelain for layering'
        }
    )
    
    ItemMaterialComposition.objects.get_or_create(
        item=titanium_bridge,
        material=titanium,
        defaults={
            'quantity': Decimal('25.0000'),
            'waste_factor': Decimal('8.00'),
            'notes': 'Titanium for 3-unit bridge'
        }
    )
    
    ItemMaterialComposition.objects.get_or_create(
        item=pfm_crown,
        material=titanium,
        defaults={
            'quantity': Decimal('3.2000'),
            'waste_factor': Decimal('5.00'),
            'notes': 'Titanium substructure'
        }
    )
    
    ItemMaterialComposition.objects.get_or_create(
        item=pfm_crown,
        material=porcelain,
        defaults={
            'quantity': Decimal('1.8000'),
            'waste_factor': Decimal('8.00'),
            'notes': 'Porcelain overlay'
        }
    )
    print("✅ Material compositions created")
    
    # Create price list
    price_list, _ = PriceList.objects.get_or_create(
        name='Standard Pricing 2024',
        defaults={
            'description': 'Standard laboratory pricing',
            'is_active': True,
            'effective_date': datetime.now().date()
        }
    )
    
    # Create item prices
    ItemPrice.objects.get_or_create(
        price_list=price_list,
        item=zirconia_crown,
        defaults={
            'base_price': Decimal('185.00'),
            'labor_cost': Decimal('95.00'),
            'markup_percentage': Decimal('35.00')
        }
    )
    
    ItemPrice.objects.get_or_create(
        price_list=price_list,
        item=titanium_bridge,
        defaults={
            'base_price': Decimal('520.00'),
            'labor_cost': Decimal('280.00'),
            'markup_percentage': Decimal('38.00')
        }
    )
    
    ItemPrice.objects.get_or_create(
        price_list=price_list,
        item=pfm_crown,
        defaults={
            'base_price': Decimal('280.00'),
            'labor_cost': Decimal('140.00'),
            'markup_percentage': Decimal('30.00')
        }
    )
    print("✅ Item prices created")
    
    # Create sample cases
    for i in range(5):
        case, created = Case.objects.get_or_create(
            case_number=f'TEST-2024-{str(i+1).zfill(3)}',
            defaults={
                'tenant': tenant,
                'patient_name': f'Test Patient {i+1}',
                'dentist_name': f'Dr. Test {i+1}',
                'service_type': 'crown' if i % 2 == 0 else 'bridge',
                'priority': 'normal',
                'due_date': datetime.now().date() + timedelta(days=7+i),
                'notes': f'Test case {i+1}',
                'created_by': admin_user
            }
        )
        
        if created:
            CaseStatus.objects.create(
                case=case,
                status=['received', 'in_progress', 'quality_check', 'completed', 'shipped'][i],
                notes=f'Test case {i+1} status',
                created_by=admin_user
            )
    print("✅ Sample cases created")
    
    print("\n🎉 Test data creation completed!")
    print("📊 Summary:")
    print(f"   Materials: {Material.objects.count()}")
    print(f"   Items: {Item.objects.count()}")
    print(f"   Compositions: {ItemMaterialComposition.objects.count()}")
    print(f"   Cases: {Case.objects.count()}")
    print(f"   Item Prices: {ItemPrice.objects.count()}")
    
    print("\n🌐 Access Points:")
    print("   Frontend: http://localhost:3001")
    print("   Admin: http://localhost:8001/admin")
    print("   Login: admin / admin123")

if __name__ == '__main__':
    create_test_data()
