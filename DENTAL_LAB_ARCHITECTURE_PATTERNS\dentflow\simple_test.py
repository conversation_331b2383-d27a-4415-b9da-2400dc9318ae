#!/usr/bin/env python3
"""
Simple test to verify DentFlow system is working
"""

import requests
import sys

def test_system():
    print("🧪 DentFlow System Test")
    print("="*40)
    
    # Test backend
    try:
        response = requests.get("http://localhost:8001/admin/", timeout=5)
        backend_status = "✅ RUNNING" if response.status_code in [200, 302] else "❌ ERROR"
        print(f"Backend Server: {backend_status}")
    except Exception as e:
        print(f"Backend Server: ❌ NOT RUNNING ({e})")
        return False
    
    # Test frontend
    try:
        response = requests.get("http://localhost:3001", timeout=5)
        frontend_status = "✅ RUNNING" if response.status_code == 200 else "❌ ERROR"
        print(f"Frontend Server: {frontend_status}")
    except Exception as e:
        print(f"Frontend Server: ❌ NOT RUNNING ({e})")
    
    print("\n🎯 READY FOR TESTING!")
    print("📊 Access Points:")
    print("• Frontend: http://localhost:3001")
    print("• Admin: http://localhost:8001/admin")
    print("• Login: admin / admin123")
    
    return True

if __name__ == '__main__':
    test_system()
