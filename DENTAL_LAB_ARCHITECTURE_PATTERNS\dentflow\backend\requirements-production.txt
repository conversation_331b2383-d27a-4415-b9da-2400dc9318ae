# Production requirements for DentFlow Backend
# Base Django requirements
Django==4.2.7
djangorestframework==3.14.0
django-cors-headers==4.3.1

# Authentication & Security
djangorestframework-simplejwt==5.3.0
django-oauth-toolkit==2.2.0
cryptography==41.0.7

# Database & Caching
psycopg2-binary==2.9.7
redis==5.0.1
django-redis==5.4.0

# Background Tasks
celery==5.3.4
django-celery-beat==2.5.0
flower==2.0.1

# File Storage & Processing
boto3==1.34.0  # AWS S3 integration
Pillow==10.1.0  # Image processing
python-magic==0.4.27  # File type detection

# Monitoring & Logging
sentry-sdk[django]==1.38.0
structlog==23.2.0
django-extensions==3.2.3
prometheus-client==0.19.0

# Production Server
gunicorn==21.2.0
whitenoise==6.6.0  # Static file serving

# Environment & Configuration
python-decouple==3.8
dj-database-url==2.1.0

# API Documentation
drf-spectacular==0.26.5

# Testing & Quality (for CI/CD)
coverage==7.3.2
factory-boy==3.3.0
faker==20.1.0

# Security & Compliance
django-ratelimit==4.1.0
django-defender==0.9.7
django-csp==3.7