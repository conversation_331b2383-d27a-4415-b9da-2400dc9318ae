"""
DentFlow Advanced Analytics & Machine Learning
Predictive analytics for lab optimization and business intelligence
"""

import numpy as np
import pandas as pd
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass
from sklearn.ensemble import <PERSON>ForestRegressor, GradientBoostingRegressor
from sklearn.linear_model import LinearRegression
from sklearn.preprocessing import StandardScaler, LabelEncoder
from sklearn.model_selection import train_test_split
from sklearn.metrics import mean_absolute_error, mean_squared_error, r2_score
import joblib
import structlog
from django.db.models import Avg, Count, Sum, Q
from django.utils import timezone

logger = structlog.get_logger(__name__)


@dataclass
class PredictionResult:
    """Result container for predictions"""
    predicted_value: float
    confidence_interval: Tuple[float, float]
    confidence_score: float
    model_version: str
    prediction_timestamp: datetime
    features_used: List[str]
    
    def to_dict(self) -> Dict[str, Any]:
        return {
            'predicted_value': self.predicted_value,
            'confidence_interval': self.confidence_interval,
            'confidence_score': self.confidence_score,
            'model_version': self.model_version,
            'prediction_timestamp': self.prediction_timestamp.isoformat(),
            'features_used': self.features_used
        }


class CaseTimePredictor:
    """
    Machine learning model for predicting case completion times
    """
    
    def __init__(self):
        self.model = None
        self.scaler = StandardScaler()
        self.label_encoders = {}
        self.feature_columns = []
        self.model_version = "1.0.0"
        self.last_trained = None
        self.model_metrics = {}
    
    def prepare_training_data(self, tenant_id: str, 
                            days_back: int = 180) -> pd.DataFrame:
        """Prepare training data from completed cases"""
        
        try:
            from backend.apps.cases.models import CaseModel
            
            # Get completed cases from last N days
            cutoff_date = timezone.now() - timedelta(days=days_back)
            completed_cases = CaseModel.objects.filter(
                tenant_id=tenant_id,
                status__in=['delivered', 'cancelled'],
                created_at__gte=cutoff_date,
                completion_time__isnull=False
            ).values(
                'id', 'service_type', 'priority', 'tooth_number',
                'complexity_score', 'technician_id', 'created_at',
                'due_date', 'completion_time', 'total_processing_time',
                'design_time', 'milling_time', 'sintering_time',
                'qc_time', 'revision_count', 'file_count'
            )
            
            if not completed_cases:
                logger.warning(
                    "No completed cases found for training",
                    tenant_id=tenant_id,
                    days_back=days_back
                )
                return pd.DataFrame()
            
            df = pd.DataFrame(completed_cases)
            
            # Feature engineering
            df = self._engineer_features(df)
            
            logger.info(
                "Training data prepared",
                tenant_id=tenant_id,
                cases_count=len(df),
                features_count=len(df.columns)
            )
            
            return df
            
        except Exception as e:
            logger.error(
                "Failed to prepare training data",
                tenant_id=tenant_id,
                error=str(e)
            )
            return pd.DataFrame()
    
    def _engineer_features(self, df: pd.DataFrame) -> pd.DataFrame:
        """Create features for machine learning"""
        
        # Time-based features
        df['created_hour'] = pd.to_datetime(df['created_at']).dt.hour
        df['created_day_of_week'] = pd.to_datetime(df['created_at']).dt.dayofweek
        df['created_month'] = pd.to_datetime(df['created_at']).dt.month
        
        # Priority encoding
        priority_mapping = {'low': 1, 'normal': 2, 'urgent': 3, 'stat': 4}
        df['priority_numeric'] = df['priority'].map(priority_mapping).fillna(2)
        
        # Service type encoding (will use label encoder)
        df['service_type_encoded'] = df['service_type']
        
        # Tooth position features
        df['tooth_number_numeric'] = pd.to_numeric(df['tooth_number'], errors='coerce').fillna(0)
        df['is_anterior'] = (df['tooth_number_numeric'] >= 11) & (df['tooth_number_numeric'] <= 23)
        df['is_posterior'] = (df['tooth_number_numeric'] >= 24) & (df['tooth_number_numeric'] <= 48)
        
        # Complexity indicators
        df['high_complexity'] = (df['complexity_score'] > 7).astype(int)
        df['has_revisions'] = (df['revision_count'] > 0).astype(int)
        df['many_files'] = (df['file_count'] > 5).astype(int)
        
        # Target variable (what we want to predict)
        df['target_hours'] = df['total_processing_time'] / 60.0  # Convert to hours
        
        return df
    
    def train_model(self, df: pd.DataFrame) -> Dict[str, Any]:
        """Train the machine learning model"""
        
        if df.empty:
            return {'error': 'No training data available'}
        
        try:
            # Prepare features
            feature_columns = [
                'priority_numeric', 'created_hour', 'created_day_of_week',
                'created_month', 'tooth_number_numeric', 'is_anterior',
                'is_posterior', 'complexity_score', 'high_complexity',
                'has_revisions', 'many_files', 'file_count', 'revision_count'
            ]
            
            # Handle categorical variables
            categorical_columns = ['service_type_encoded']
            
            # Encode categorical variables
            for col in categorical_columns:
                if col not in self.label_encoders:
                    self.label_encoders[col] = LabelEncoder()
                df[f'{col}_le'] = self.label_encoders[col].fit_transform(df[col].astype(str))
                feature_columns.append(f'{col}_le')
            
            # Prepare training data
            X = df[feature_columns].fillna(0)
            y = df['target_hours']
            
            # Remove outliers (cases taking more than 100 hours)
            mask = y < 100
            X = X[mask]
            y = y[mask]
            
            if len(X) < 10:
                return {'error': 'Insufficient training data after cleaning'}
            
            # Split data
            X_train, X_test, y_train, y_test = train_test_split(
                X, y, test_size=0.2, random_state=42
            )
            
            # Scale features
            X_train_scaled = self.scaler.fit_transform(X_train)
            X_test_scaled = self.scaler.transform(X_test)
            
            # Train ensemble model
            models = {
                'random_forest': RandomForestRegressor(
                    n_estimators=100, random_state=42, n_jobs=-1
                ),
                'gradient_boosting': GradientBoostingRegressor(
                    n_estimators=100, random_state=42
                ),
                'linear_regression': LinearRegression()
            }
            
            best_model = None
            best_score = float('-inf')
            model_results = {}
            
            for name, model in models.items():
                if name == 'linear_regression':
                    model.fit(X_train_scaled, y_train)
                    y_pred = model.predict(X_test_scaled)
                else:
                    model.fit(X_train, y_train)
                    y_pred = model.predict(X_test)
                
                # Calculate metrics
                mae = mean_absolute_error(y_test, y_pred)
                mse = mean_squared_error(y_test, y_pred)
                r2 = r2_score(y_test, y_pred)
                
                model_results[name] = {
                    'mae': mae,
                    'mse': mse,
                    'r2': r2,
                    'rmse': np.sqrt(mse)
                }
                
                if r2 > best_score:
                    best_score = r2
                    best_model = model
            
            # Save the best model
            self.model = best_model
            self.feature_columns = feature_columns
            self.last_trained = datetime.utcnow()
            self.model_metrics = model_results
            
            # Save model to disk
            self._save_model()
            
            logger.info(
                "Model training completed",
                best_r2_score=best_score,
                training_samples=len(X_train),
                test_samples=len(X_test)
            )
            
            return {
                'success': True,
                'model_metrics': model_results,
                'best_model': type(best_model).__name__,
                'best_r2_score': best_score,
                'training_samples': len(X_train),
                'feature_count': len(feature_columns)
            }
            
        except Exception as e:
            logger.error("Model training failed", error=str(e))
            return {'error': str(e)}
    
    def predict_completion_time(self, case_data: Dict[str, Any]) -> PredictionResult:
        """Predict completion time for a new case"""
        
        if self.model is None:
            self._load_model()
        
        if self.model is None:
            raise ValueError("No trained model available")
        
        try:
            # Create DataFrame from case data
            df = pd.DataFrame([case_data])
            
            # Engineer features
            df = self._engineer_features(df)
            
            # Encode categorical variables
            for col in ['service_type_encoded']:
                if f'{col}_le' in self.feature_columns:
                    if col in self.label_encoders:
                        try:
                            df[f'{col}_le'] = self.label_encoders[col].transform(df[col].astype(str))
                        except ValueError:
                            # Handle unseen categories
                            df[f'{col}_le'] = 0
                    else:
                        df[f'{col}_le'] = 0
            
            # Prepare features
            X = df[self.feature_columns].fillna(0)
            
            # Scale if needed (for linear regression)
            if isinstance(self.model, LinearRegression):
                X = self.scaler.transform(X)
            
            # Make prediction
            prediction = self.model.predict(X)[0]
            
            # Calculate confidence interval (simplified approach)
            if hasattr(self.model, 'estimators_'):
                # For ensemble models, use prediction variance
                predictions = [estimator.predict(X)[0] for estimator in self.model.estimators_]
                std_pred = np.std(predictions)
                confidence_interval = (
                    prediction - 1.96 * std_pred,
                    prediction + 1.96 * std_pred
                )
                confidence_score = max(0, 1 - (std_pred / prediction)) if prediction > 0 else 0.5
            else:
                # For single models, use a simple approach
                confidence_interval = (prediction * 0.8, prediction * 1.2)
                confidence_score = 0.7  # Default confidence
            
            return PredictionResult(
                predicted_value=max(0.1, prediction),  # Minimum 0.1 hours
                confidence_interval=confidence_interval,
                confidence_score=confidence_score,
                model_version=self.model_version,
                prediction_timestamp=datetime.utcnow(),
                features_used=self.feature_columns
            )
            
        except Exception as e:
            logger.error("Prediction failed", error=str(e), case_data=case_data)
            raise
    
    def _save_model(self):
        """Save trained model to disk"""
        try:
            model_data = {
                'model': self.model,
                'scaler': self.scaler,
                'label_encoders': self.label_encoders,
                'feature_columns': self.feature_columns,
                'model_version': self.model_version,
                'last_trained': self.last_trained,
                'model_metrics': self.model_metrics
            }
            
            joblib.dump(model_data, 'models/case_time_predictor.pkl')
            logger.info("Model saved successfully")
            
        except Exception as e:
            logger.error("Failed to save model", error=str(e))
    
    def _load_model(self):
        """Load trained model from disk"""
        try:
            model_data = joblib.load('models/case_time_predictor.pkl')
            
            self.model = model_data['model']
            self.scaler = model_data['scaler']
            self.label_encoders = model_data['label_encoders']
            self.feature_columns = model_data['feature_columns']
            self.model_version = model_data['model_version']
            self.last_trained = model_data['last_trained']
            self.model_metrics = model_data['model_metrics']
            
            logger.info("Model loaded successfully")
            
        except FileNotFoundError:
            logger.warning("No saved model found")
        except Exception as e:
            logger.error("Failed to load model", error=str(e))


class ResourceOptimizer:
    """
    Optimize resource allocation and technician scheduling
    """
    
    def __init__(self):
        self.logger = structlog.get_logger(__name__)
    
    def optimize_technician_assignment(self, case_data: Dict, 
                                     available_technicians: List[Dict]) -> Dict[str, Any]:
        """Find optimal technician assignment for a case"""
        
        try:
            scores = []
            
            for tech in available_technicians:
                score = self._calculate_assignment_score(case_data, tech)
                scores.append({
                    'technician_id': tech['id'],
                    'technician_name': tech['name'],
                    'score': score,
                    'reasoning': self._get_assignment_reasoning(case_data, tech, score)
                })
            
            # Sort by score (highest first)
            scores.sort(key=lambda x: x['score'], reverse=True)
            
            return {
                'recommended_assignments': scores[:3],  # Top 3 recommendations
                'optimization_timestamp': datetime.utcnow().isoformat()
            }
            
        except Exception as e:
            self.logger.error("Technician assignment optimization failed", error=str(e))
            return {'error': str(e)}
    
    def _calculate_assignment_score(self, case_data: Dict, technician: Dict) -> float:
        """Calculate assignment score based on multiple factors"""
        
        score = 0.0
        
        # Skill match (40% weight)
        service_type = case_data.get('service_type', '')
        tech_skills = technician.get('skills', [])
        
        if service_type.lower() in [skill.lower() for skill in tech_skills]:
            score += 40.0
        
        # Experience level (25% weight)
        experience = technician.get('experience_years', 0)
        case_complexity = case_data.get('complexity_score', 5)
        
        if experience >= case_complexity:
            score += 25.0
        elif experience >= case_complexity * 0.7:
            score += 15.0
        
        # Current workload (20% weight)
        current_cases = technician.get('active_cases', 0)
        max_capacity = technician.get('max_capacity', 10)
        
        workload_ratio = current_cases / max_capacity
        if workload_ratio < 0.7:
            score += 20.0
        elif workload_ratio < 0.9:
            score += 10.0
        
        # Priority handling (10% weight)
        case_priority = case_data.get('priority', 'normal')
        tech_can_handle_urgent = technician.get('can_handle_urgent', False)
        
        if case_priority in ['urgent', 'stat'] and tech_can_handle_urgent:
            score += 10.0
        elif case_priority in ['normal', 'low']:
            score += 5.0
        
        # Availability (5% weight)
        is_available = technician.get('is_available', True)
        if is_available:
            score += 5.0
        
        return min(100.0, score)  # Cap at 100
    
    def _get_assignment_reasoning(self, case_data: Dict, technician: Dict, 
                                score: float) -> str:
        """Generate human-readable reasoning for assignment score"""
        
        reasons = []
        
        service_type = case_data.get('service_type', '')
        tech_skills = technician.get('skills', [])
        
        if service_type.lower() in [skill.lower() for skill in tech_skills]:
            reasons.append(f"Expert in {service_type}")
        
        experience = technician.get('experience_years', 0)
        if experience > 5:
            reasons.append(f"{experience} years experience")
        
        current_cases = technician.get('active_cases', 0)
        if current_cases < 5:
            reasons.append("Low current workload")
        elif current_cases > 8:
            reasons.append("High current workload")
        
        if score > 80:
            return "Excellent match: " + ", ".join(reasons)
        elif score > 60:
            return "Good match: " + ", ".join(reasons)
        else:
            return "Possible match: " + ", ".join(reasons)


class BusinessIntelligence:
    """
    Advanced business intelligence and analytics
    """
    
    def __init__(self):
        self.logger = structlog.get_logger(__name__)
    
    def generate_lab_performance_report(self, tenant_id: str, 
                                      start_date: datetime,
                                      end_date: datetime) -> Dict[str, Any]:
        """Generate comprehensive lab performance report"""
        
        try:
            from backend.apps.cases.models import CaseModel
            
            # Base query
            cases = CaseModel.objects.filter(
                tenant_id=tenant_id,
                created_at__range=(start_date, end_date)
            )
            
            # Overall metrics
            total_cases = cases.count()
            completed_cases = cases.filter(status='delivered').count()
            cancelled_cases = cases.filter(status='cancelled').count()
            
            completion_rate = (completed_cases / total_cases * 100) if total_cases > 0 else 0
            
            # Performance by service type
            service_performance = cases.values('service_type').annotate(
                count=Count('id'),
                avg_time=Avg('total_processing_time'),
                completed=Count('id', filter=Q(status='delivered'))
            ).order_by('-count')
            
            # Performance by technician
            technician_performance = cases.values(
                'assigned_technician_id',
                'assigned_technician__first_name',
                'assigned_technician__last_name'
            ).annotate(
                cases_assigned=Count('id'),
                cases_completed=Count('id', filter=Q(status='delivered')),
                avg_completion_time=Avg('total_processing_time')
            ).order_by('-cases_completed')
            
            # Quality metrics
            revision_rate = cases.aggregate(
                avg_revisions=Avg('revision_count')
            )['avg_revisions'] or 0
            
            # Time-based analysis
            hourly_distribution = self._analyze_case_creation_patterns(cases)
            
            # Revenue analysis (if pricing available)
            revenue_data = self._calculate_revenue_metrics(cases)
            
            # Trends and predictions
            trends = self._analyze_trends(tenant_id, start_date, end_date)
            
            report = {
                'report_period': {
                    'start_date': start_date.isoformat(),
                    'end_date': end_date.isoformat(),
                    'days': (end_date - start_date).days
                },
                'overall_metrics': {
                    'total_cases': total_cases,
                    'completed_cases': completed_cases,
                    'cancelled_cases': cancelled_cases,
                    'completion_rate': round(completion_rate, 2),
                    'average_revision_rate': round(revision_rate, 2)
                },
                'service_performance': list(service_performance),
                'technician_performance': list(technician_performance),
                'time_analysis': hourly_distribution,
                'revenue_analysis': revenue_data,
                'trends': trends,
                'generated_at': datetime.utcnow().isoformat()
            }
            
            self.logger.info(
                "Performance report generated",
                tenant_id=tenant_id,
                total_cases=total_cases,
                completion_rate=completion_rate
            )
            
            return report
            
        except Exception as e:
            self.logger.error(
                "Failed to generate performance report",
                tenant_id=tenant_id,
                error=str(e)
            )
            return {'error': str(e)}
    
    def _analyze_case_creation_patterns(self, cases_queryset) -> Dict[str, Any]:
        """Analyze when cases are typically created"""
        
        # This would be implemented with proper database aggregation
        # For now, returning a placeholder structure
        
        return {
            'peak_hours': [9, 10, 11, 14, 15],
            'peak_days': ['Monday', 'Tuesday', 'Wednesday'],
            'busiest_hour': 10,
            'slowest_hour': 17
        }
    
    def _calculate_revenue_metrics(self, cases_queryset) -> Dict[str, Any]:
        """Calculate revenue-related metrics"""
        
        # Placeholder for revenue calculation
        # Would integrate with pricing and billing systems
        
        return {
            'total_revenue': 0,
            'average_case_value': 0,
            'revenue_by_service': {},
            'revenue_trend': 'stable'
        }
    
    def _analyze_trends(self, tenant_id: str, start_date: datetime, 
                       end_date: datetime) -> Dict[str, Any]:
        """Analyze trends and patterns"""
        
        # Placeholder for trend analysis
        # Would use time series analysis
        
        return {
            'case_volume_trend': 'increasing',
            'completion_time_trend': 'improving',
            'quality_trend': 'stable',
            'predictions': {
                'next_month_cases': 0,
                'capacity_utilization': 0.75
            }
        }


# Global instances
case_time_predictor = CaseTimePredictor()
resource_optimizer = ResourceOptimizer()
business_intelligence = BusinessIntelligence()


# Utility functions
def predict_case_completion(case_data: Dict[str, Any]) -> PredictionResult:
    """Convenience function for case time prediction"""
    return case_time_predictor.predict_completion_time(case_data)


def optimize_technician_assignment(case_data: Dict, 
                                 available_technicians: List[Dict]) -> Dict[str, Any]:
    """Convenience function for technician optimization"""
    return resource_optimizer.optimize_technician_assignment(case_data, available_technicians)


def generate_performance_report(tenant_id: str, days_back: int = 30) -> Dict[str, Any]:
    """Convenience function for performance reporting"""
    end_date = datetime.utcnow()
    start_date = end_date - timedelta(days=days_back)
    
    return business_intelligence.generate_lab_performance_report(
        tenant_id, start_date, end_date
    )