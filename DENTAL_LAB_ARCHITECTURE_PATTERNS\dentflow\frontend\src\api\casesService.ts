/**
 * Cases API Service - Django REST API integration with Mock Fallback
 */

import axios from 'axios';
import { apiClient } from './client';
import { Case, CreateCaseRequest, AdvanceCaseRequest, AssignTechnicianRequest } from './types';
import { mockCases, mockDashboardStats, getMockCasesByStatus, getMockOverdueCases } from './mockData';

export class CasesService {
  private static isBackendAvailable = true;
  
  /**
   * Get all cases with fallback to mock data
   */
  static async getCases(): Promise<Case[]> {
    try {
      if (!this.isBackendAvailable) {
        console.log('🔄 Using mock data (backend unavailable)');
        return mockCases;
      }
      
      const cases = await apiClient.get<Case[]>('/cases/');
      console.log('✅ Backend data loaded successfully');
      this.isBackendAvailable = true;
      return cases;
    } catch (err: any) {
      console.warn('⚠️ Backend unavailable, using mock data:', err.message);
      this.isBackendAvailable = false;
      return mockCases;
    }
  }

  /**
   * Get case by ID with fallback
   */
  static async getCase(caseId: string): Promise<Case> {
    try {
      if (!this.isBackendAvailable) {
        const mockCase = mockCases.find((c: Case) => c.id === caseId);
        if (!mockCase) throw new Error('Case not found');
        return mockCase;
      }
      
      const case_ = await apiClient.get<Case>(`/cases/${caseId}/`);
      this.isBackendAvailable = true;
      return case_;
    } catch (err: any) {
      console.warn('⚠️ Backend unavailable for case fetch, using mock data');
      this.isBackendAvailable = false;
      const mockCase = mockCases.find((c: Case) => c.id === caseId);
      if (!mockCase) throw new Error('Case not found');
      return mockCase;
    }
  }

  /**
   * Create new case with fallback
   */
  static async createCase(data: CreateCaseRequest): Promise<Case> {
    try {
      if (!this.isBackendAvailable) {
        // Simulate case creation with mock data
        const newCase: Case = {
          id: `LAB-${new Date().toISOString().split('T')[0]}-${Math.random().toString(36).substr(2, 3).toUpperCase()}`,
          clinic: mockCases[0].clinic, // Use first clinic as default
          patient_name: data.patient_name,
          tooth_number: data.tooth_number,
          service_type: data.service_type as any,
          priority: (data.priority || 'normal') as any,
          status: 'received',
          current_stage_index: 0,
          workflow_stages: [
            {
              name: 'received',
              department: 'intake',
              estimated_duration_minutes: 30,
              auto_assign: true,
              requires_quality_check: false
            }
          ],
          assigned_technician_id: undefined,
          due_date: new Date(Date.now() + 3 * 24 * 60 * 60 * 1000).toISOString(),
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString(),
          notes: data.notes ? [data.notes] : [],
          files: (data.files || []).map((file: File) => ({
            id: `file-${Math.random().toString(36).substr(2, 9)}`,
            name: file.name,
            size: file.size,
            url: URL.createObjectURL(file),
            uploaded_at: new Date().toISOString(),
            uploaded_by: 'current-user'
          })),
          days_until_due: 3,
          is_overdue: false,
          current_stage: 'Received'
        };
        
        console.log('✅ Mock case created:', newCase.id);
        mockCases.unshift(newCase); // Add to beginning of array
        return newCase;
      }
      
      const case_ = await apiClient.post<Case>('/cases/', data);
      this.isBackendAvailable = true;
      return case_;
    } catch (err: any) {
      console.warn('⚠️ Backend unavailable for case creation, using mock creation');
      this.isBackendAvailable = false;
      // Fallback to mock creation (same logic as above)
      return this.createCase(data);
    }
  }
  /**
   * Update case with fallback
   */
  static async updateCase(caseId: string, data: Partial<Case>): Promise<Case> {
    try {
      if (!this.isBackendAvailable) {
        const caseIndex = mockCases.findIndex((c: Case) => c.id === caseId);
        if (caseIndex === -1) throw new Error('Case not found');
        
        mockCases[caseIndex] = { ...mockCases[caseIndex], ...data, updated_at: new Date().toISOString() };
        console.log('✅ Mock case updated:', caseId);
        return mockCases[caseIndex];
      }
      
      const case_ = await apiClient.patch<Case>(`/cases/${caseId}/`, data);
      this.isBackendAvailable = true;
      return case_;
    } catch (err: any) {
      console.warn('⚠️ Backend unavailable for case update, using mock update');
      this.isBackendAvailable = false;
      return this.updateCase(caseId, data);
    }
  }

  /**
   * Delete case with fallback
   */
  static async deleteCase(caseId: string): Promise<void> {
    try {
      if (!this.isBackendAvailable) {
        const caseIndex = mockCases.findIndex((c: Case) => c.id === caseId);
        if (caseIndex === -1) throw new Error('Case not found');
        
        mockCases.splice(caseIndex, 1);
        console.log('✅ Mock case deleted:', caseId);
        return;
      }
      
      await apiClient.delete(`/cases/${caseId}/`);
      this.isBackendAvailable = true;
    } catch (err: any) {
      console.warn('⚠️ Backend unavailable for case deletion, using mock deletion');
      this.isBackendAvailable = false;
      return this.deleteCase(caseId);
    }
  }

  /**
   * Advance case to next stage with fallback
   */
  static async advanceCase(caseId: string, data: AdvanceCaseRequest = {}): Promise<Case> {
    try {
      if (!this.isBackendAvailable) {
        const caseIndex = mockCases.findIndex((c: Case) => c.id === caseId);
        if (caseIndex === -1) throw new Error('Case not found');
        
        const case_ = mockCases[caseIndex];
        const nextStageIndex = case_.current_stage_index + 1;
        
        if (nextStageIndex < case_.workflow_stages.length) {
          const nextStage = case_.workflow_stages[nextStageIndex];
          case_.current_stage_index = nextStageIndex;
          case_.status = nextStage.name as any;
          case_.current_stage = nextStage.name;
          case_.assigned_technician_id = undefined; // Clear assignment
        } else {
          // Move to quality control or delivery
          case_.status = 'qc';
          case_.current_stage = 'Quality Control';
        }
        
        case_.updated_at = new Date().toISOString();
        if (data.notes) {
          case_.notes.push(`${new Date().toISOString()}: ${data.notes}`);
        }
        
        console.log('✅ Mock case advanced:', caseId, 'to', case_.current_stage);
        return case_;
      }
      
      const case_ = await apiClient.post<Case>(`/cases/${caseId}/advance/`, data);
      this.isBackendAvailable = true;
      return case_;
    } catch (err: any) {
      console.warn('⚠️ Backend unavailable for case advance, using mock advance');
      this.isBackendAvailable = false;
      return this.advanceCase(caseId, data);
    }
  }
  /**
   * Assign technician to case with fallback
   */
  static async assignTechnician(caseId: string, data: AssignTechnicianRequest): Promise<Case> {
    try {
      if (!this.isBackendAvailable) {
        const caseIndex = mockCases.findIndex((c: Case) => c.id === caseId);
        if (caseIndex === -1) throw new Error('Case not found');
        
        mockCases[caseIndex].assigned_technician_id = data.technician_id;
        mockCases[caseIndex].updated_at = new Date().toISOString();
        
        console.log('✅ Mock technician assigned:', data.technician_id, 'to case:', caseId);
        return mockCases[caseIndex];
      }
      
      const case_ = await apiClient.post<Case>(`/cases/${caseId}/assign_technician/`, data);
      this.isBackendAvailable = true;
      return case_;
    } catch (err: any) {
      console.warn('⚠️ Backend unavailable for technician assignment, using mock assignment');
      this.isBackendAvailable = false;
      return this.assignTechnician(caseId, data);
    }
  }

  /**
   * Get overdue cases with fallback
   */
  static async getOverdueCases(): Promise<Case[]> {
    try {
      if (!this.isBackendAvailable) {
        return getMockOverdueCases();
      }
      
      const cases = await apiClient.get<Case[]>('/cases/overdue/');
      this.isBackendAvailable = true;
      return cases;
    } catch (err: any) {
      console.warn('⚠️ Backend unavailable for overdue cases, using mock data');
      this.isBackendAvailable = false;
      return getMockOverdueCases();
    }
  }

  /**
   * Get cases by status with fallback
   */
  static async getCasesByStatus(status: string): Promise<Case[]> {
    try {
      if (!this.isBackendAvailable) {
        return getMockCasesByStatus(status);
      }
      
      const cases = await apiClient.get<Case[]>(`/cases/by_status/?status=${status}`);
      this.isBackendAvailable = true;
      return cases;
    } catch (err: any) {
      console.warn('⚠️ Backend unavailable for status filter, using mock data');
      this.isBackendAvailable = false;
      return getMockCasesByStatus(status);
    }
  }

  /**
   * Get dashboard statistics with fallback
   */
  static async getDashboardStats(): Promise<any> {
    try {
      if (!this.isBackendAvailable) {
        console.log('📊 Using mock dashboard stats');
        return mockDashboardStats;
      }
      
      // Try to get real data from backend
      const cases = await this.getCases();
      const today = new Date().toISOString().split('T')[0];
      
      const stats = {
        active_cases: cases.filter(c => !['delivered', 'cancelled'].includes(c.status)).length,
        completed_today: cases.filter(c => 
          c.status === 'delivered' && 
          c.updated_at.startsWith(today)
        ).length,
        pending_qc: cases.filter(c => c.status === 'qc').length,
        revenue_today: 2850, // This would come from billing service
        overdue_cases: cases.filter(c => c.is_overdue).length,
      };
      
      console.log('📊 Real dashboard stats calculated');
      this.isBackendAvailable = true;
      return stats;
    } catch (error) {
      console.warn('⚠️ Backend unavailable for dashboard stats, using mock data');
      this.isBackendAvailable = false;
      return mockDashboardStats;
    }
  }

  /**
   * Check backend health
   */
  static async checkBackendHealth(): Promise<boolean> {
    try {
      // Try a simple health check - health endpoint is at root level, not under /api/v1
      const response = await axios.get('http://localhost:8001/health/');
      this.isBackendAvailable = true;
      console.log('✅ Backend health check passed');
      return true;
    } catch (error: any) {
      // If it's a 503 (service unavailable), the backend is running but some services are down
      // This is acceptable for development - the main API should still work
      if (error.response?.status === 503) {
        this.isBackendAvailable = true;
        console.log('⚠️ Backend is running but some services are unavailable (this is OK for development)');
        return true;
      }
      this.isBackendAvailable = false;
      console.log('❌ Backend health check failed');
      return false;
    }
  }

  /**
   * Get backend status
   */
  static getBackendStatus(): { available: boolean; mode: string } {
    return {
      available: this.isBackendAvailable,
      mode: this.isBackendAvailable ? 'live' : 'mock'
    };
  }
}