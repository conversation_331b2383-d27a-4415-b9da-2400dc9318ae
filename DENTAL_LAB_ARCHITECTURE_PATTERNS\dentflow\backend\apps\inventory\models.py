"""
Inventory Models for DentFlow
Raw materials, stock management, and supplier tracking for dental laboratory
"""

from django.db import models
from django.core.validators import MinValueValidator
from decimal import Decimal
import uuid


class MaterialCategory(models.Model):
    """
    Categories for organizing materials (e.g., Ceramics, Metals, Resins, etc.)
    """

    id = models.UUIDField(primary_key=True, default=uuid.uuid4)
    tenant = models.ForeignKey('tenants.Tenant', on_delete=models.CASCADE, related_name='material_categories')
    name = models.CharField(max_length=100)
    description = models.TextField(blank=True)
    is_active = models.BooleanField(default=True)

    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        indexes = [
            models.Index(fields=['tenant', 'is_active']),
        ]
        unique_together = ['tenant', 'name']
        verbose_name_plural = 'Material Categories'

    def __str__(self):
        return self.name


class Supplier(models.Model):
    """
    Suppliers who provide raw materials to the dental laboratory
    """

    id = models.UUIDField(primary_key=True, default=uuid.uuid4)
    tenant = models.ForeignKey('tenants.Tenant', on_delete=models.CASCADE, related_name='suppliers')

    # Basic information
    name = models.CharField(max_length=255)
    contact_person = models.CharField(max_length=255, blank=True)
    email = models.EmailField(blank=True)
    phone = models.CharField(max_length=20, blank=True)

    # Address
    address = models.TextField(blank=True)
    city = models.CharField(max_length=100, blank=True)
    state = models.CharField(max_length=100, blank=True)
    postal_code = models.CharField(max_length=20, blank=True)
    country = models.CharField(max_length=100, blank=True)

    # Business details
    tax_id = models.CharField(max_length=50, blank=True)
    payment_terms = models.CharField(max_length=100, default='Net 30')
    is_active = models.BooleanField(default=True)

    # Additional information
    notes = models.TextField(blank=True)
    website = models.URLField(blank=True)

    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        indexes = [
            models.Index(fields=['tenant', 'is_active']),
            models.Index(fields=['name']),
        ]
        unique_together = ['tenant', 'name']

    def __str__(self):
        return self.name


class Material(models.Model):
    """
    Raw materials used in dental laboratory production
    """

    UNIT_CHOICES = [
        ('g', 'Grams'),
        ('kg', 'Kilograms'),
        ('ml', 'Milliliters'),
        ('l', 'Liters'),
        ('pcs', 'Pieces'),
        ('mm', 'Millimeters'),
        ('cm', 'Centimeters'),
        ('m', 'Meters'),
        ('oz', 'Ounces'),
        ('lb', 'Pounds'),
    ]

    id = models.UUIDField(primary_key=True, default=uuid.uuid4)
    tenant = models.ForeignKey('tenants.Tenant', on_delete=models.CASCADE, related_name='materials')
    category = models.ForeignKey(MaterialCategory, on_delete=models.CASCADE, related_name='materials')

    # Basic information
    name = models.CharField(max_length=255)
    description = models.TextField(blank=True)
    sku = models.CharField(max_length=100, blank=True)  # Stock Keeping Unit
    barcode = models.CharField(max_length=100, blank=True)

    # Physical properties
    unit_of_measure = models.CharField(max_length=10, choices=UNIT_CHOICES, default='g')
    density = models.DecimalField(max_digits=8, decimal_places=4, null=True, blank=True, help_text="Density in g/cm³")

    # Cost information
    standard_cost = models.DecimalField(max_digits=10, decimal_places=4, validators=[MinValueValidator(Decimal('0.0001'))])
    currency = models.CharField(max_length=3, default='USD')

    # Inventory settings
    minimum_stock_level = models.DecimalField(max_digits=10, decimal_places=2, default=Decimal('0.00'))
    maximum_stock_level = models.DecimalField(max_digits=10, decimal_places=2, null=True, blank=True)
    reorder_point = models.DecimalField(max_digits=10, decimal_places=2, default=Decimal('0.00'))

    # Status
    is_active = models.BooleanField(default=True)
    is_hazardous = models.BooleanField(default=False)

    # Additional information
    manufacturer = models.CharField(max_length=255, blank=True)
    batch_tracking_required = models.BooleanField(default=False)
    expiry_tracking_required = models.BooleanField(default=False)
    notes = models.TextField(blank=True)

    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        indexes = [
            models.Index(fields=['tenant', 'is_active']),
            models.Index(fields=['category', 'is_active']),
            models.Index(fields=['sku']),
            models.Index(fields=['name']),
        ]
        unique_together = [
            ['tenant', 'name'],
            ['tenant', 'sku'],
        ]

    def __str__(self):
        return f"{self.name} ({self.sku})" if self.sku else self.name


class MaterialStock(models.Model):
    """
    Current stock levels for materials in the laboratory
    """

    id = models.UUIDField(primary_key=True, default=uuid.uuid4)
    material = models.OneToOneField(Material, on_delete=models.CASCADE, related_name='stock')

    # Stock levels
    current_quantity = models.DecimalField(max_digits=12, decimal_places=4, default=Decimal('0.0000'))
    reserved_quantity = models.DecimalField(max_digits=12, decimal_places=4, default=Decimal('0.0000'))
    available_quantity = models.DecimalField(max_digits=12, decimal_places=4, default=Decimal('0.0000'))

    # Calculated fields (updated via signals/methods)
    total_value = models.DecimalField(max_digits=15, decimal_places=2, default=Decimal('0.00'))
    average_cost = models.DecimalField(max_digits=10, decimal_places=4, default=Decimal('0.0000'))

    # Status flags
    is_low_stock = models.BooleanField(default=False)
    is_out_of_stock = models.BooleanField(default=False)

    last_updated = models.DateTimeField(auto_now=True)

    class Meta:
        indexes = [
            models.Index(fields=['material']),
            models.Index(fields=['is_low_stock']),
            models.Index(fields=['is_out_of_stock']),
        ]

    def save(self, *args, **kwargs):
        """Auto-calculate derived fields"""
        self.available_quantity = self.current_quantity - self.reserved_quantity
        self.is_out_of_stock = self.available_quantity <= 0
        self.is_low_stock = self.available_quantity <= self.material.minimum_stock_level
        self.total_value = self.current_quantity * self.average_cost
        super().save(*args, **kwargs)

    def __str__(self):
        return f"{self.material.name}: {self.current_quantity} {self.material.unit_of_measure}"


class MaterialTransaction(models.Model):
    """
    All material movements (purchases, usage, adjustments, etc.)
    """

    TRANSACTION_TYPE_CHOICES = [
        ('purchase', 'Purchase'),
        ('usage', 'Usage'),
        ('adjustment', 'Inventory Adjustment'),
        ('transfer', 'Transfer'),
        ('return', 'Return'),
        ('waste', 'Waste/Loss'),
    ]

    id = models.UUIDField(primary_key=True, default=uuid.uuid4)
    material = models.ForeignKey(Material, on_delete=models.CASCADE, related_name='transactions')

    # Transaction details
    transaction_type = models.CharField(max_length=20, choices=TRANSACTION_TYPE_CHOICES)
    quantity = models.DecimalField(max_digits=12, decimal_places=4)
    unit_cost = models.DecimalField(max_digits=10, decimal_places=4, null=True, blank=True)
    total_cost = models.DecimalField(max_digits=15, decimal_places=2, null=True, blank=True)

    # References
    reference_number = models.CharField(max_length=100, blank=True)
    case = models.ForeignKey('cases.Case', on_delete=models.SET_NULL, null=True, blank=True)
    supplier = models.ForeignKey(Supplier, on_delete=models.SET_NULL, null=True, blank=True)

    # Batch and expiry tracking
    batch_number = models.CharField(max_length=100, blank=True)
    expiry_date = models.DateField(null=True, blank=True)

    # Additional information
    notes = models.TextField(blank=True)
    created_by = models.CharField(max_length=100, blank=True)  # User who created the transaction

    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        indexes = [
            models.Index(fields=['material', 'transaction_type']),
            models.Index(fields=['case']),
            models.Index(fields=['supplier']),
            models.Index(fields=['created_at']),
            models.Index(fields=['batch_number']),
        ]
        ordering = ['-created_at']

    def save(self, *args, **kwargs):
        """Auto-calculate total cost"""
        if self.quantity and self.unit_cost:
            self.total_cost = self.quantity * self.unit_cost
        super().save(*args, **kwargs)

    def __str__(self):
        return f"{self.transaction_type.title()}: {self.quantity} {self.material.unit_of_measure} of {self.material.name}"


class PurchaseOrder(models.Model):
    """
    Purchase orders for materials from suppliers
    """

    STATUS_CHOICES = [
        ('draft', 'Draft'),
        ('sent', 'Sent'),
        ('confirmed', 'Confirmed'),
        ('partially_received', 'Partially Received'),
        ('received', 'Received'),
        ('cancelled', 'Cancelled'),
    ]

    id = models.UUIDField(primary_key=True, default=uuid.uuid4)
    tenant = models.ForeignKey('tenants.Tenant', on_delete=models.CASCADE, related_name='purchase_orders')
    supplier = models.ForeignKey(Supplier, on_delete=models.CASCADE, related_name='purchase_orders')

    # Order details
    po_number = models.CharField(max_length=50, unique=True)
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='draft')

    # Financial details
    subtotal = models.DecimalField(max_digits=15, decimal_places=2, default=Decimal('0.00'))
    tax_amount = models.DecimalField(max_digits=15, decimal_places=2, default=Decimal('0.00'))
    shipping_cost = models.DecimalField(max_digits=15, decimal_places=2, default=Decimal('0.00'))
    total_amount = models.DecimalField(max_digits=15, decimal_places=2, default=Decimal('0.00'))
    currency = models.CharField(max_length=3, default='USD')

    # Dates
    order_date = models.DateField()
    expected_delivery_date = models.DateField(null=True, blank=True)
    actual_delivery_date = models.DateField(null=True, blank=True)

    # Additional information
    notes = models.TextField(blank=True)
    created_by = models.CharField(max_length=100, blank=True)

    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        indexes = [
            models.Index(fields=['tenant', 'status']),
            models.Index(fields=['supplier', 'status']),
            models.Index(fields=['po_number']),
            models.Index(fields=['order_date']),
        ]
        ordering = ['-created_at']

    def __str__(self):
        return f"PO {self.po_number} - {self.supplier.name}"


class PurchaseOrderItem(models.Model):
    """
    Individual items within a purchase order
    """

    id = models.UUIDField(primary_key=True, default=uuid.uuid4)
    purchase_order = models.ForeignKey(PurchaseOrder, on_delete=models.CASCADE, related_name='items')
    material = models.ForeignKey(Material, on_delete=models.CASCADE, related_name='purchase_order_items')

    # Order details
    quantity_ordered = models.DecimalField(max_digits=12, decimal_places=4)
    quantity_received = models.DecimalField(max_digits=12, decimal_places=4, default=Decimal('0.0000'))
    unit_price = models.DecimalField(max_digits=10, decimal_places=4)
    total_price = models.DecimalField(max_digits=15, decimal_places=2)

    # Additional information
    notes = models.TextField(blank=True)

    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        indexes = [
            models.Index(fields=['purchase_order']),
            models.Index(fields=['material']),
        ]
        unique_together = ['purchase_order', 'material']

    def save(self, *args, **kwargs):
        """Auto-calculate total price"""
        self.total_price = self.quantity_ordered * self.unit_price
        super().save(*args, **kwargs)

    @property
    def quantity_pending(self):
        """Calculate quantity still pending delivery"""
        return self.quantity_ordered - self.quantity_received

    @property
    def is_fully_received(self):
        """Check if item is fully received"""
        return self.quantity_received >= self.quantity_ordered

    def __str__(self):
        return f"{self.material.name} - {self.quantity_ordered} {self.material.unit_of_measure}"


class ItemCategory(models.Model):
    """
    Categories for finished dental items (e.g., Crowns, Bridges, Dentures, etc.)
    """

    id = models.UUIDField(primary_key=True, default=uuid.uuid4)
    tenant = models.ForeignKey('tenants.Tenant', on_delete=models.CASCADE, related_name='item_categories')
    name = models.CharField(max_length=100)
    description = models.TextField(blank=True)
    is_active = models.BooleanField(default=True)

    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        indexes = [
            models.Index(fields=['tenant', 'is_active']),
        ]
        unique_together = ['tenant', 'name']
        verbose_name_plural = 'Item Categories'

    def __str__(self):
        return self.name


class Item(models.Model):
    """
    Finished dental items/products that can be created by the laboratory
    These are the final products delivered to clinics (crowns, bridges, etc.)
    """

    COMPLEXITY_CHOICES = [
        ('simple', 'Simple'),
        ('moderate', 'Moderate'),
        ('complex', 'Complex'),
        ('highly_complex', 'Highly Complex'),
    ]

    id = models.UUIDField(primary_key=True, default=uuid.uuid4)
    tenant = models.ForeignKey('tenants.Tenant', on_delete=models.CASCADE, related_name='items')
    category = models.ForeignKey(ItemCategory, on_delete=models.CASCADE, related_name='items')

    # Basic information
    name = models.CharField(max_length=255)
    description = models.TextField(blank=True)
    item_code = models.CharField(max_length=100, blank=True)

    # Production details
    complexity = models.CharField(max_length=20, choices=COMPLEXITY_CHOICES, default='simple')
    estimated_production_time_minutes = models.PositiveIntegerField(help_text="Estimated time to produce this item")

    # Pricing
    base_labor_cost = models.DecimalField(max_digits=10, decimal_places=2, help_text="Base labor cost for this item")
    markup_percentage = models.DecimalField(max_digits=5, decimal_places=2, default=Decimal('50.00'), help_text="Markup percentage over material + labor costs")

    # Status
    is_active = models.BooleanField(default=True)
    requires_approval = models.BooleanField(default=False, help_text="Whether this item requires special approval")

    # Additional information
    notes = models.TextField(blank=True)

    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        indexes = [
            models.Index(fields=['tenant', 'is_active']),
            models.Index(fields=['category', 'is_active']),
            models.Index(fields=['item_code']),
            models.Index(fields=['name']),
        ]
        unique_together = [
            ['tenant', 'name'],
            ['tenant', 'item_code'],
        ]

    def calculate_material_cost(self):
        """Calculate total material cost for this item"""
        total_cost = Decimal('0.00')
        for composition in self.material_compositions.all():
            if composition.quantity is not None and composition.material is not None:
                material_cost = composition.get_material_cost()
                total_cost += material_cost
        return total_cost

    def calculate_total_cost(self):
        """Calculate total cost (materials + labor)"""
        material_cost = self.calculate_material_cost()
        return material_cost + self.base_labor_cost

    def calculate_selling_price(self):
        """Calculate selling price with markup"""
        total_cost = self.calculate_total_cost()
        markup_amount = total_cost * (self.markup_percentage / Decimal('100'))
        return total_cost + markup_amount

    def __str__(self):
        return f"{self.name} ({self.item_code})" if self.item_code else self.name


class ItemMaterialComposition(models.Model):
    """
    Defines what materials and quantities are needed to create a specific item
    This is like a "recipe" or "bill of materials" for each dental item
    """

    id = models.UUIDField(primary_key=True, default=uuid.uuid4)
    item = models.ForeignKey(Item, on_delete=models.CASCADE, related_name='material_compositions')
    material = models.ForeignKey(Material, on_delete=models.CASCADE, related_name='item_compositions')

    # Quantity details
    quantity = models.DecimalField(max_digits=10, decimal_places=4, validators=[MinValueValidator(Decimal('0.0001'))], help_text="Quantity of material needed")
    waste_factor = models.DecimalField(max_digits=5, decimal_places=2, default=Decimal('10.00'), help_text="Waste percentage (e.g., 10% = 10.00)")

    # Additional information
    notes = models.TextField(blank=True, help_text="Special instructions for using this material")
    is_optional = models.BooleanField(default=False, help_text="Whether this material is optional")

    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        indexes = [
            models.Index(fields=['item']),
            models.Index(fields=['material']),
        ]
        unique_together = ['item', 'material']
        verbose_name = 'Item Material Composition'
        verbose_name_plural = 'Item Material Compositions'

    def get_total_quantity_needed(self):
        """Calculate total quantity needed including waste factor"""
        if self.quantity is None:
            return Decimal('0.00')
        waste_multiplier = Decimal('1') + (self.waste_factor / Decimal('100'))
        return self.quantity * waste_multiplier

    def get_material_cost(self):
        """Calculate cost for this material in this item"""
        if self.quantity is None or self.material is None:
            return Decimal('0.00')
        total_quantity = self.get_total_quantity_needed()
        return total_quantity * self.material.standard_cost

    def __str__(self):
        return f"{self.item.name} - {self.material.name}: {self.quantity} {self.material.unit_of_measure}"
