INFO 2025-07-24 16:40:49,173 autoreload 3808 34908 Watching for file changes with StatReloader
INFO 2025-07-24 16:42:19,502 autoreload 19824 21380 Watching for file changes with StatReloader
INFO 2025-07-24 16:43:05,402 autoreload 35212 32440 Watching for file changes with StatReloader
INFO 2025-07-24 16:43:30,000 autoreload 5872 3904 Watching for file changes with StatReloader
INFO 2025-07-24 16:44:19,098 autoreload 29884 37732 Watching for file changes with StatReloader
INFO 2025-07-25 21:16:52,198 autoreload 25204 34520 Watching for file changes with StatReloader
INFO 2025-07-25 21:17:04,318 autoreload 34828 14976 Watching for file changes with StatReloader
INFO 2025-07-25 21:17:21,007 autoreload 37328 22908 Watching for file changes with StatReloader
INFO 2025-07-25 21:17:25,637 autoreload 28936 27456 Watching for file changes with StatReloader
INFO 2025-07-25 21:17:49,424 autoreload 34416 27752 Watching for file changes with StatReloader
INFO 2025-07-25 21:18:21,548 autoreload 22476 11540 Watching for file changes with StatReloader
INFO 2025-07-25 21:18:43,072 autoreload 37328 22908 C:\GPT4_PROJECTS\DENTAL_LAB_ARCHITECTURE_PATTERNS\dentflow\backend\apps\users\admin.py changed, reloading.
INFO 2025-07-25 21:18:43,893 autoreload 22476 11540 C:\GPT4_PROJECTS\DENTAL_LAB_ARCHITECTURE_PATTERNS\dentflow\backend\apps\users\admin.py changed, reloading.
INFO 2025-07-25 21:18:43,967 autoreload 32964 27348 Watching for file changes with StatReloader
INFO 2025-07-25 21:18:44,778 autoreload 27256 24980 Watching for file changes with StatReloader
INFO 2025-07-25 21:19:42,432 autoreload 25204 34520 C:\GPT4_PROJECTS\DENTAL_LAB_ARCHITECTURE_PATTERNS\dentflow\backend\apps\files\models.py changed, reloading.
INFO 2025-07-25 21:19:42,754 autoreload 27256 24980 C:\GPT4_PROJECTS\DENTAL_LAB_ARCHITECTURE_PATTERNS\dentflow\backend\apps\files\models.py changed, reloading.
INFO 2025-07-25 21:19:43,047 autoreload 32964 27348 C:\GPT4_PROJECTS\DENTAL_LAB_ARCHITECTURE_PATTERNS\dentflow\backend\apps\files\models.py changed, reloading.
INFO 2025-07-25 21:19:43,342 autoreload 30076 16168 Watching for file changes with StatReloader
INFO 2025-07-25 21:19:43,724 autoreload 7976 26396 Watching for file changes with StatReloader
INFO 2025-07-25 21:19:43,979 autoreload 36480 13916 Watching for file changes with StatReloader
INFO 2025-07-25 21:19:51,392 autoreload 30076 16168 C:\GPT4_PROJECTS\DENTAL_LAB_ARCHITECTURE_PATTERNS\dentflow\backend\apps\files\models.py changed, reloading.
INFO 2025-07-25 21:19:51,683 autoreload 7976 26396 C:\GPT4_PROJECTS\DENTAL_LAB_ARCHITECTURE_PATTERNS\dentflow\backend\apps\files\models.py changed, reloading.
INFO 2025-07-25 21:19:51,886 autoreload 36480 13916 C:\GPT4_PROJECTS\DENTAL_LAB_ARCHITECTURE_PATTERNS\dentflow\backend\apps\files\models.py changed, reloading.
INFO 2025-07-25 21:19:52,275 autoreload 20024 33168 Watching for file changes with StatReloader
INFO 2025-07-25 21:19:52,547 autoreload 17520 17872 Watching for file changes with StatReloader
INFO 2025-07-25 21:19:52,820 autoreload 18284 26388 Watching for file changes with StatReloader
INFO 2025-07-25 21:20:00,332 autoreload 20024 33168 C:\GPT4_PROJECTS\DENTAL_LAB_ARCHITECTURE_PATTERNS\dentflow\backend\apps\files\models.py changed, reloading.
INFO 2025-07-25 21:20:00,490 autoreload 17520 17872 C:\GPT4_PROJECTS\DENTAL_LAB_ARCHITECTURE_PATTERNS\dentflow\backend\apps\files\models.py changed, reloading.
INFO 2025-07-25 21:20:00,777 autoreload 18284 26388 C:\GPT4_PROJECTS\DENTAL_LAB_ARCHITECTURE_PATTERNS\dentflow\backend\apps\files\models.py changed, reloading.
INFO 2025-07-25 21:20:01,259 autoreload 14260 32156 Watching for file changes with StatReloader
INFO 2025-07-25 21:20:01,408 autoreload 17136 19220 Watching for file changes with StatReloader
INFO 2025-07-25 21:20:01,697 autoreload 33720 30092 Watching for file changes with StatReloader
INFO 2025-07-25 21:20:13,446 autoreload 14260 32156 C:\GPT4_PROJECTS\DENTAL_LAB_ARCHITECTURE_PATTERNS\dentflow\backend\apps\notifications\models.py changed, reloading.
INFO 2025-07-25 21:20:13,595 autoreload 17136 19220 C:\GPT4_PROJECTS\DENTAL_LAB_ARCHITECTURE_PATTERNS\dentflow\backend\apps\notifications\models.py changed, reloading.
INFO 2025-07-25 21:20:13,851 autoreload 33720 30092 C:\GPT4_PROJECTS\DENTAL_LAB_ARCHITECTURE_PATTERNS\dentflow\backend\apps\notifications\models.py changed, reloading.
INFO 2025-07-25 21:20:14,388 autoreload 32704 33856 Watching for file changes with StatReloader
INFO 2025-07-25 21:20:14,524 autoreload 37532 6120 Watching for file changes with StatReloader
INFO 2025-07-25 21:20:14,746 autoreload 23468 14644 Watching for file changes with StatReloader
INFO 2025-07-25 21:20:22,377 autoreload 32704 33856 C:\GPT4_PROJECTS\DENTAL_LAB_ARCHITECTURE_PATTERNS\dentflow\backend\apps\notifications\models.py changed, reloading.
INFO 2025-07-25 21:20:22,484 autoreload 37532 6120 C:\GPT4_PROJECTS\DENTAL_LAB_ARCHITECTURE_PATTERNS\dentflow\backend\apps\notifications\models.py changed, reloading.
INFO 2025-07-25 21:20:22,704 autoreload 23468 14644 C:\GPT4_PROJECTS\DENTAL_LAB_ARCHITECTURE_PATTERNS\dentflow\backend\apps\notifications\models.py changed, reloading.
INFO 2025-07-25 21:20:23,380 autoreload 37104 12112 Watching for file changes with StatReloader
INFO 2025-07-25 21:20:23,529 autoreload 27668 15012 Watching for file changes with StatReloader
INFO 2025-07-25 21:20:23,672 autoreload 29040 16444 Watching for file changes with StatReloader
INFO 2025-07-25 21:20:31,537 autoreload 27668 15012 C:\GPT4_PROJECTS\DENTAL_LAB_ARCHITECTURE_PATTERNS\dentflow\backend\apps\notifications\models.py changed, reloading.
INFO 2025-07-25 21:20:31,633 autoreload 29040 16444 C:\GPT4_PROJECTS\DENTAL_LAB_ARCHITECTURE_PATTERNS\dentflow\backend\apps\notifications\models.py changed, reloading.
INFO 2025-07-25 21:20:32,386 autoreload 12192 9096 Watching for file changes with StatReloader
INFO 2025-07-25 21:20:32,468 autoreload 37104 12112 C:\GPT4_PROJECTS\DENTAL_LAB_ARCHITECTURE_PATTERNS\dentflow\backend\apps\notifications\models.py changed, reloading.
INFO 2025-07-25 21:20:32,476 autoreload 6524 33852 Watching for file changes with StatReloader
INFO 2025-07-25 21:20:33,333 autoreload 28260 31588 Watching for file changes with StatReloader
INFO 2025-07-25 21:20:52,851 autoreload 28260 31588 C:\GPT4_PROJECTS\DENTAL_LAB_ARCHITECTURE_PATTERNS\dentflow\backend\apps\files\models.py changed, reloading.
INFO 2025-07-25 21:20:52,977 autoreload 12192 9096 C:\GPT4_PROJECTS\DENTAL_LAB_ARCHITECTURE_PATTERNS\dentflow\backend\apps\files\models.py changed, reloading.
INFO 2025-07-25 21:20:53,119 autoreload 6524 33852 C:\GPT4_PROJECTS\DENTAL_LAB_ARCHITECTURE_PATTERNS\dentflow\backend\apps\files\models.py changed, reloading.
INFO 2025-07-25 21:20:53,894 autoreload 30932 17160 Watching for file changes with StatReloader
INFO 2025-07-25 21:20:53,960 autoreload 35208 17300 Watching for file changes with StatReloader
INFO 2025-07-25 21:20:54,155 autoreload 15568 4448 Watching for file changes with StatReloader
INFO 2025-07-25 21:21:13,524 autoreload 10148 35360 Watching for file changes with StatReloader
INFO 2025-07-25 21:21:33,507 autoreload 30932 17160 C:\GPT4_PROJECTS\DENTAL_LAB_ARCHITECTURE_PATTERNS\dentflow\backend\apps\notifications\models.py changed, reloading.
INFO 2025-07-25 21:21:33,515 autoreload 35208 17300 C:\GPT4_PROJECTS\DENTAL_LAB_ARCHITECTURE_PATTERNS\dentflow\backend\apps\notifications\models.py changed, reloading.
INFO 2025-07-25 21:21:33,619 autoreload 15568 4448 C:\GPT4_PROJECTS\DENTAL_LAB_ARCHITECTURE_PATTERNS\dentflow\backend\apps\notifications\models.py changed, reloading.
INFO 2025-07-25 21:21:34,695 autoreload 36476 12088 Watching for file changes with StatReloader
INFO 2025-07-25 21:21:34,700 autoreload 33544 26224 Watching for file changes with StatReloader
INFO 2025-07-25 21:21:34,772 autoreload 35732 11432 Watching for file changes with StatReloader
INFO 2025-07-25 21:21:47,046 autoreload 36476 12088 C:\GPT4_PROJECTS\DENTAL_LAB_ARCHITECTURE_PATTERNS\dentflow\backend\apps\notifications\models.py changed, reloading.
INFO 2025-07-25 21:21:47,051 autoreload 33544 26224 C:\GPT4_PROJECTS\DENTAL_LAB_ARCHITECTURE_PATTERNS\dentflow\backend\apps\notifications\models.py changed, reloading.
INFO 2025-07-25 21:21:47,058 autoreload 35732 11432 C:\GPT4_PROJECTS\DENTAL_LAB_ARCHITECTURE_PATTERNS\dentflow\backend\apps\notifications\models.py changed, reloading.
INFO 2025-07-25 21:21:48,079 autoreload 14908 24304 Watching for file changes with StatReloader
INFO 2025-07-25 21:21:48,081 autoreload 28616 24048 Watching for file changes with StatReloader
INFO 2025-07-25 21:21:48,085 autoreload 20296 35688 Watching for file changes with StatReloader
INFO 2025-07-25 21:21:56,220 autoreload 20296 35688 C:\GPT4_PROJECTS\DENTAL_LAB_ARCHITECTURE_PATTERNS\dentflow\backend\apps\notifications\models.py changed, reloading.
INFO 2025-07-25 21:21:56,244 autoreload 28616 24048 C:\GPT4_PROJECTS\DENTAL_LAB_ARCHITECTURE_PATTERNS\dentflow\backend\apps\notifications\models.py changed, reloading.
INFO 2025-07-25 21:21:56,268 autoreload 14908 24304 C:\GPT4_PROJECTS\DENTAL_LAB_ARCHITECTURE_PATTERNS\dentflow\backend\apps\notifications\models.py changed, reloading.
INFO 2025-07-25 21:21:57,269 autoreload 33448 28428 Watching for file changes with StatReloader
INFO 2025-07-25 21:21:57,309 autoreload 21972 32692 Watching for file changes with StatReloader
INFO 2025-07-25 21:21:57,329 autoreload 8460 12160 Watching for file changes with StatReloader
INFO 2025-07-25 21:22:05,378 autoreload 21972 32692 C:\GPT4_PROJECTS\DENTAL_LAB_ARCHITECTURE_PATTERNS\dentflow\backend\apps\files\models.py changed, reloading.
INFO 2025-07-25 21:22:05,400 autoreload 33448 28428 C:\GPT4_PROJECTS\DENTAL_LAB_ARCHITECTURE_PATTERNS\dentflow\backend\apps\files\models.py changed, reloading.
INFO 2025-07-25 21:22:05,433 autoreload 8460 12160 C:\GPT4_PROJECTS\DENTAL_LAB_ARCHITECTURE_PATTERNS\dentflow\backend\apps\files\models.py changed, reloading.
INFO 2025-07-25 21:22:06,442 autoreload 5096 35656 Watching for file changes with StatReloader
INFO 2025-07-25 21:22:06,443 autoreload 35736 31364 Watching for file changes with StatReloader
INFO 2025-07-25 21:22:06,445 autoreload 30444 36648 Watching for file changes with StatReloader
INFO 2025-07-25 21:22:15,585 autoreload 35736 31364 C:\GPT4_PROJECTS\DENTAL_LAB_ARCHITECTURE_PATTERNS\dentflow\backend\apps\files\models.py changed, reloading.
INFO 2025-07-25 21:22:15,602 autoreload 5096 35656 C:\GPT4_PROJECTS\DENTAL_LAB_ARCHITECTURE_PATTERNS\dentflow\backend\apps\files\models.py changed, reloading.
INFO 2025-07-25 21:22:15,608 autoreload 30444 36648 C:\GPT4_PROJECTS\DENTAL_LAB_ARCHITECTURE_PATTERNS\dentflow\backend\apps\files\models.py changed, reloading.
INFO 2025-07-25 21:22:16,704 autoreload 15472 36640 Watching for file changes with StatReloader
INFO 2025-07-25 21:22:16,707 autoreload 25996 17352 Watching for file changes with StatReloader
INFO 2025-07-25 21:22:16,709 autoreload 24236 3412 Watching for file changes with StatReloader
INFO 2025-07-25 21:22:25,109 autoreload 24236 3412 C:\GPT4_PROJECTS\DENTAL_LAB_ARCHITECTURE_PATTERNS\dentflow\backend\apps\files\models.py changed, reloading.
INFO 2025-07-25 21:22:25,140 autoreload 15472 36640 C:\GPT4_PROJECTS\DENTAL_LAB_ARCHITECTURE_PATTERNS\dentflow\backend\apps\files\models.py changed, reloading.
INFO 2025-07-25 21:22:25,161 autoreload 25996 17352 C:\GPT4_PROJECTS\DENTAL_LAB_ARCHITECTURE_PATTERNS\dentflow\backend\apps\files\models.py changed, reloading.
INFO 2025-07-25 21:22:26,609 autoreload 2432 12112 Watching for file changes with StatReloader
INFO 2025-07-25 21:22:26,638 autoreload 5544 13240 Watching for file changes with StatReloader
INFO 2025-07-25 21:22:26,675 autoreload 21000 16444 Watching for file changes with StatReloader
INFO 2025-07-25 21:23:14,076 autoreload 21000 16444 C:\GPT4_PROJECTS\DENTAL_LAB_ARCHITECTURE_PATTERNS\dentflow\backend\apps\billing\admin.py changed, reloading.
INFO 2025-07-25 21:23:14,084 autoreload 2432 12112 C:\GPT4_PROJECTS\DENTAL_LAB_ARCHITECTURE_PATTERNS\dentflow\backend\apps\billing\admin.py changed, reloading.
INFO 2025-07-25 21:23:14,115 autoreload 5544 13240 C:\GPT4_PROJECTS\DENTAL_LAB_ARCHITECTURE_PATTERNS\dentflow\backend\apps\billing\admin.py changed, reloading.
INFO 2025-07-25 21:23:15,142 autoreload 21912 23780 Watching for file changes with StatReloader
INFO 2025-07-25 21:23:15,143 autoreload 32260 16448 Watching for file changes with StatReloader
INFO 2025-07-25 21:23:15,152 autoreload 37540 29844 Watching for file changes with StatReloader
INFO 2025-07-25 21:25:07,251 autoreload 32260 16448 C:\GPT4_PROJECTS\DENTAL_LAB_ARCHITECTURE_PATTERNS\dentflow\backend\apps\billing\admin.py changed, reloading.
INFO 2025-07-25 21:25:07,252 autoreload 21912 23780 C:\GPT4_PROJECTS\DENTAL_LAB_ARCHITECTURE_PATTERNS\dentflow\backend\apps\billing\admin.py changed, reloading.
INFO 2025-07-25 21:25:07,256 autoreload 37540 29844 C:\GPT4_PROJECTS\DENTAL_LAB_ARCHITECTURE_PATTERNS\dentflow\backend\apps\billing\admin.py changed, reloading.
INFO 2025-07-25 21:25:08,249 autoreload 12672 2592 Watching for file changes with StatReloader
INFO 2025-07-25 21:25:08,256 autoreload 27440 7888 Watching for file changes with StatReloader
INFO 2025-07-25 21:25:08,265 autoreload 19532 7576 Watching for file changes with StatReloader
INFO 2025-07-25 21:25:22,740 autoreload 27440 7888 C:\GPT4_PROJECTS\DENTAL_LAB_ARCHITECTURE_PATTERNS\dentflow\backend\apps\billing\admin.py changed, reloading.
INFO 2025-07-25 21:25:22,766 autoreload 19532 7576 C:\GPT4_PROJECTS\DENTAL_LAB_ARCHITECTURE_PATTERNS\dentflow\backend\apps\billing\admin.py changed, reloading.
INFO 2025-07-25 21:25:22,775 autoreload 12672 2592 C:\GPT4_PROJECTS\DENTAL_LAB_ARCHITECTURE_PATTERNS\dentflow\backend\apps\billing\admin.py changed, reloading.
INFO 2025-07-25 21:25:23,851 autoreload 13760 12312 Watching for file changes with StatReloader
INFO 2025-07-25 21:25:23,856 autoreload 35108 5568 Watching for file changes with StatReloader
INFO 2025-07-25 21:25:23,860 autoreload 25876 28060 Watching for file changes with StatReloader
INFO 2025-07-25 21:25:35,863 autoreload 25876 28060 C:\GPT4_PROJECTS\DENTAL_LAB_ARCHITECTURE_PATTERNS\dentflow\backend\apps\billing\admin.py changed, reloading.
INFO 2025-07-25 21:25:35,897 autoreload 35108 5568 C:\GPT4_PROJECTS\DENTAL_LAB_ARCHITECTURE_PATTERNS\dentflow\backend\apps\billing\admin.py changed, reloading.
INFO 2025-07-25 21:25:35,898 autoreload 13760 12312 C:\GPT4_PROJECTS\DENTAL_LAB_ARCHITECTURE_PATTERNS\dentflow\backend\apps\billing\admin.py changed, reloading.
INFO 2025-07-25 21:25:36,951 autoreload 16588 12768 Watching for file changes with StatReloader
INFO 2025-07-25 21:25:36,969 autoreload 2612 8708 Watching for file changes with StatReloader
INFO 2025-07-25 21:25:37,003 autoreload 23504 29740 Watching for file changes with StatReloader
INFO 2025-07-25 21:25:48,847 autoreload 24964 17340 Watching for file changes with StatReloader
INFO 2025-07-25 21:26:34,499 autoreload 6240 6468 Watching for file changes with StatReloader
WARNING 2025-07-25 21:26:56,355 log 6240 28220 Not Found: /
WARNING 2025-07-25 21:26:56,356 basehttp 6240 28220 "GET / HTTP/1.1" 404 2914
INFO 2025-07-25 21:31:02,539 autoreload 23016 28208 Watching for file changes with StatReloader
INFO 2025-07-25 21:34:38,387 autoreload 33740 23344 Watching for file changes with StatReloader
INFO 2025-07-25 21:34:46,123 basehttp 33740 18012 "GET /admin/ HTTP/1.1" 302 0
INFO 2025-07-25 21:34:46,212 basehttp 33740 18012 "GET /admin/login/?next=/admin/ HTTP/1.1" 200 4177
WARNING 2025-07-25 21:34:46,410 log 33740 18012 Not Found: /favicon.ico
WARNING 2025-07-25 21:34:46,411 basehttp 33740 18012 "GET /favicon.ico HTTP/1.1" 404 2965
INFO 2025-07-25 21:34:52,242 basehttp 33740 18012 "POST /admin/login/?next=/admin/ HTTP/1.1" 200 4335
INFO 2025-07-25 21:34:58,572 basehttp 33740 18012 "POST /admin/login/?next=/admin/ HTTP/1.1" 200 4335
INFO 2025-07-25 21:36:54,525 autoreload 29440 33840 Watching for file changes with StatReloader
INFO 2025-07-25 21:37:25,217 basehttp 29440 15248 "GET /admin HTTP/1.1" 301 0
INFO 2025-07-25 21:37:25,494 basehttp 29440 2704 "GET /admin/ HTTP/1.1" 302 0
INFO 2025-07-25 21:37:25,527 basehttp 29440 2704 "GET /admin/login/?next=/admin/ HTTP/1.1" 200 4177
WARNING 2025-07-25 21:37:25,578 log 29440 2704 Not Found: /.well-known/appspecific/com.chrome.devtools.json
WARNING 2025-07-25 21:37:25,579 basehttp 29440 2704 "GET /.well-known/appspecific/com.chrome.devtools.json HTTP/1.1" 404 3076
WARNING 2025-07-25 21:37:26,053 log 29440 2704 Not Found: /favicon.ico
WARNING 2025-07-25 21:37:26,054 basehttp 29440 2704 "GET /favicon.ico HTTP/1.1" 404 2965
INFO 2025-07-25 21:37:38,191 basehttp 29440 2704 "POST /admin/login/?next=/admin/ HTTP/1.1" 302 0
INFO 2025-07-25 21:37:38,215 basehttp 29440 2704 "GET /admin/ HTTP/1.1" 200 14382
WARNING 2025-07-25 21:37:38,283 log 29440 2704 Not Found: /.well-known/appspecific/com.chrome.devtools.json
WARNING 2025-07-25 21:37:38,295 basehttp 29440 2704 "GET /.well-known/appspecific/com.chrome.devtools.json HTTP/1.1" 404 3076
INFO 2025-07-25 21:37:38,438 basehttp 29440 2704 "GET /static/admin/img/icon-viewlink.svg HTTP/1.1" 200 581
ERROR 2025-07-25 21:39:23,557 log 29440 2704 Internal Server Error: /admin/tenants/tenant/
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\db\backends\utils.py", line 89, in _execute
    return self.cursor.execute(sql, params)
           ~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\db\backends\sqlite3\base.py", line 328, in execute
    return super().execute(query, params)
           ~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
sqlite3.OperationalError: no such table: tenants_tenant

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\contrib\admin\options.py", line 688, in wrapper
    return self.admin_site.admin_view(view)(*args, **kwargs)
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\utils\decorators.py", line 134, in _wrapper_view
    response = view_func(request, *args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\views\decorators\cache.py", line 62, in _wrapper_view_func
    response = view_func(request, *args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\contrib\admin\sites.py", line 242, in inner
    return view(request, *args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\utils\decorators.py", line 46, in _wrapper
    return bound_method(*args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\utils\decorators.py", line 134, in _wrapper_view
    response = view_func(request, *args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\contrib\admin\options.py", line 1926, in changelist_view
    cl = self.get_changelist_instance(request)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\contrib\admin\options.py", line 836, in get_changelist_instance
    return ChangeList(
        request,
    ...<12 lines>...
        self.search_help_text,
    )
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\contrib\admin\views\main.py", line 123, in __init__
    self.get_results(request)
    ~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\contrib\admin\views\main.py", line 279, in get_results
    result_count = paginator.count
                   ^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\utils\functional.py", line 57, in __get__
    res = instance.__dict__[self.name] = self.func(instance)
                                         ~~~~~~~~~^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\core\paginator.py", line 93, in count
    return c()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\db\models\query.py", line 608, in count
    return self.query.get_count(using=self.db)
           ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\db\models\sql\query.py", line 568, in get_count
    return obj.get_aggregation(using, {"__count": Count("*")})["__count"]
           ~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\db\models\sql\query.py", line 554, in get_aggregation
    result = compiler.execute_sql(SINGLE)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\db\models\sql\compiler.py", line 1562, in execute_sql
    cursor.execute(sql, params)
    ~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\db\backends\utils.py", line 102, in execute
    return super().execute(sql, params)
           ~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\db\backends\utils.py", line 67, in execute
    return self._execute_with_wrappers(
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~^
        sql, params, many=False, executor=self._execute
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\db\backends\utils.py", line 80, in _execute_with_wrappers
    return executor(sql, params, many, context)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\db\backends\utils.py", line 84, in _execute
    with self.db.wrap_database_errors:
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\db\utils.py", line 91, in __exit__
    raise dj_exc_value.with_traceback(traceback) from exc_value
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\db\backends\utils.py", line 89, in _execute
    return self.cursor.execute(sql, params)
           ~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\db\backends\sqlite3\base.py", line 328, in execute
    return super().execute(query, params)
           ~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
django.db.utils.OperationalError: no such table: tenants_tenant
ERROR 2025-07-25 21:39:23,578 basehttp 29440 2704 "GET /admin/tenants/tenant/ HTTP/1.1" 500 199628
ERROR 2025-07-25 21:39:28,727 log 29440 2704 Internal Server Error: /admin/tenants/tenantsettings/
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\db\backends\utils.py", line 89, in _execute
    return self.cursor.execute(sql, params)
           ~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\db\backends\sqlite3\base.py", line 328, in execute
    return super().execute(query, params)
           ~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
sqlite3.OperationalError: no such table: tenants_tenantsettings

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\contrib\admin\options.py", line 688, in wrapper
    return self.admin_site.admin_view(view)(*args, **kwargs)
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\utils\decorators.py", line 134, in _wrapper_view
    response = view_func(request, *args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\views\decorators\cache.py", line 62, in _wrapper_view_func
    response = view_func(request, *args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\contrib\admin\sites.py", line 242, in inner
    return view(request, *args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\utils\decorators.py", line 46, in _wrapper
    return bound_method(*args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\utils\decorators.py", line 134, in _wrapper_view
    response = view_func(request, *args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\contrib\admin\options.py", line 1926, in changelist_view
    cl = self.get_changelist_instance(request)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\contrib\admin\options.py", line 836, in get_changelist_instance
    return ChangeList(
        request,
    ...<12 lines>...
        self.search_help_text,
    )
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\contrib\admin\views\main.py", line 123, in __init__
    self.get_results(request)
    ~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\contrib\admin\views\main.py", line 279, in get_results
    result_count = paginator.count
                   ^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\utils\functional.py", line 57, in __get__
    res = instance.__dict__[self.name] = self.func(instance)
                                         ~~~~~~~~~^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\core\paginator.py", line 93, in count
    return c()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\db\models\query.py", line 608, in count
    return self.query.get_count(using=self.db)
           ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\db\models\sql\query.py", line 568, in get_count
    return obj.get_aggregation(using, {"__count": Count("*")})["__count"]
           ~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\db\models\sql\query.py", line 554, in get_aggregation
    result = compiler.execute_sql(SINGLE)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\db\models\sql\compiler.py", line 1562, in execute_sql
    cursor.execute(sql, params)
    ~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\db\backends\utils.py", line 102, in execute
    return super().execute(sql, params)
           ~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\db\backends\utils.py", line 67, in execute
    return self._execute_with_wrappers(
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~^
        sql, params, many=False, executor=self._execute
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\db\backends\utils.py", line 80, in _execute_with_wrappers
    return executor(sql, params, many, context)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\db\backends\utils.py", line 84, in _execute
    with self.db.wrap_database_errors:
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\db\utils.py", line 91, in __exit__
    raise dj_exc_value.with_traceback(traceback) from exc_value
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\db\backends\utils.py", line 89, in _execute
    return self.cursor.execute(sql, params)
           ~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\db\backends\sqlite3\base.py", line 328, in execute
    return super().execute(query, params)
           ~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
django.db.utils.OperationalError: no such table: tenants_tenantsettings
ERROR 2025-07-25 21:39:28,745 basehttp 29440 2704 "GET /admin/tenants/tenantsettings/ HTTP/1.1" 500 199817
INFO 2025-07-25 21:39:32,178 basehttp 29440 2704 "GET /admin/cases/clinic/ HTTP/1.1" 200 14014
INFO 2025-07-25 21:39:32,223 basehttp 29440 2704 "GET /static/admin/css/changelists.css HTTP/1.1" 200 6566
INFO 2025-07-25 21:39:32,235 basehttp 29440 2704 "GET /admin/jsi18n/ HTTP/1.1" 200 3343
INFO 2025-07-25 21:39:32,244 basehttp 29440 2704 "GET /static/admin/js/vendor/jquery/jquery.js HTTP/1.1" 200 292458
INFO 2025-07-25 21:39:32,248 basehttp 29440 2704 "GET /static/admin/js/jquery.init.js HTTP/1.1" 200 347
INFO 2025-07-25 21:39:32,254 basehttp 29440 2704 "GET /static/admin/js/core.js HTTP/1.1" 200 5682
INFO 2025-07-25 21:39:32,260 basehttp 29440 2704 "GET /static/admin/js/admin/RelatedObjectLookups.js HTTP/1.1" 200 8943
INFO 2025-07-25 21:39:32,264 basehttp 29440 2704 "GET /static/admin/js/actions.js HTTP/1.1" 200 7872
INFO 2025-07-25 21:39:32,270 basehttp 29440 2704 "GET /static/admin/js/urlify.js HTTP/1.1" 200 7887
INFO 2025-07-25 21:39:32,275 basehttp 29440 2704 "GET /static/admin/js/prepopulate.js HTTP/1.1" 200 1531
INFO 2025-07-25 21:39:32,284 basehttp 29440 2704 "GET /static/admin/js/vendor/xregexp/xregexp.js HTTP/1.1" 200 232381
INFO 2025-07-25 21:39:32,290 basehttp 29440 2704 "GET /static/admin/img/search.svg HTTP/1.1" 200 458
INFO 2025-07-25 21:39:32,309 basehttp 29440 2704 "GET /static/admin/js/filters.js HTTP/1.1" 200 978
INFO 2025-07-25 21:39:32,314 basehttp 29440 2704 "GET /static/admin/img/tooltag-add.svg HTTP/1.1" 200 331
ERROR 2025-07-25 21:39:35,947 log 29440 2704 Internal Server Error: /admin/billing/payment/
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\db\backends\utils.py", line 89, in _execute
    return self.cursor.execute(sql, params)
           ~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\db\backends\sqlite3\base.py", line 328, in execute
    return super().execute(query, params)
           ~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
sqlite3.OperationalError: no such table: billing_payment

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\contrib\admin\options.py", line 688, in wrapper
    return self.admin_site.admin_view(view)(*args, **kwargs)
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\utils\decorators.py", line 134, in _wrapper_view
    response = view_func(request, *args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\views\decorators\cache.py", line 62, in _wrapper_view_func
    response = view_func(request, *args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\contrib\admin\sites.py", line 242, in inner
    return view(request, *args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\utils\decorators.py", line 46, in _wrapper
    return bound_method(*args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\utils\decorators.py", line 134, in _wrapper_view
    response = view_func(request, *args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\contrib\admin\options.py", line 1926, in changelist_view
    cl = self.get_changelist_instance(request)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\contrib\admin\options.py", line 836, in get_changelist_instance
    return ChangeList(
        request,
    ...<12 lines>...
        self.search_help_text,
    )
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\contrib\admin\views\main.py", line 123, in __init__
    self.get_results(request)
    ~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\contrib\admin\views\main.py", line 279, in get_results
    result_count = paginator.count
                   ^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\utils\functional.py", line 57, in __get__
    res = instance.__dict__[self.name] = self.func(instance)
                                         ~~~~~~~~~^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\core\paginator.py", line 93, in count
    return c()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\db\models\query.py", line 608, in count
    return self.query.get_count(using=self.db)
           ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\db\models\sql\query.py", line 568, in get_count
    return obj.get_aggregation(using, {"__count": Count("*")})["__count"]
           ~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\db\models\sql\query.py", line 554, in get_aggregation
    result = compiler.execute_sql(SINGLE)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\db\models\sql\compiler.py", line 1562, in execute_sql
    cursor.execute(sql, params)
    ~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\db\backends\utils.py", line 102, in execute
    return super().execute(sql, params)
           ~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\db\backends\utils.py", line 67, in execute
    return self._execute_with_wrappers(
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~^
        sql, params, many=False, executor=self._execute
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\db\backends\utils.py", line 80, in _execute_with_wrappers
    return executor(sql, params, many, context)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\db\backends\utils.py", line 84, in _execute
    with self.db.wrap_database_errors:
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\db\utils.py", line 91, in __exit__
    raise dj_exc_value.with_traceback(traceback) from exc_value
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\db\backends\utils.py", line 89, in _execute
    return self.cursor.execute(sql, params)
           ~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\db\backends\sqlite3\base.py", line 328, in execute
    return super().execute(query, params)
           ~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
django.db.utils.OperationalError: no such table: billing_payment
ERROR 2025-07-25 21:39:35,958 basehttp 29440 2704 "GET /admin/billing/payment/ HTTP/1.1" 500 199774
INFO 2025-07-25 21:39:39,464 basehttp 29440 2704 "GET /admin/cases/clinic/ HTTP/1.1" 200 14014
INFO 2025-07-25 21:46:59,231 autoreload 25888 16544 Watching for file changes with StatReloader
ERROR 2025-07-25 21:47:26,301 log 25888 14768 Internal Server Error: /admin/cases/clinic/
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\db\backends\utils.py", line 89, in _execute
    return self.cursor.execute(sql, params)
           ~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\db\backends\sqlite3\base.py", line 328, in execute
    return super().execute(query, params)
           ~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
sqlite3.OperationalError: no such table: tenants_tenant

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\contrib\admin\options.py", line 688, in wrapper
    return self.admin_site.admin_view(view)(*args, **kwargs)
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\utils\decorators.py", line 134, in _wrapper_view
    response = view_func(request, *args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\views\decorators\cache.py", line 62, in _wrapper_view_func
    response = view_func(request, *args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\contrib\admin\sites.py", line 242, in inner
    return view(request, *args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\utils\decorators.py", line 46, in _wrapper
    return bound_method(*args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\utils\decorators.py", line 134, in _wrapper_view
    response = view_func(request, *args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\contrib\admin\options.py", line 1926, in changelist_view
    cl = self.get_changelist_instance(request)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\contrib\admin\options.py", line 836, in get_changelist_instance
    return ChangeList(
        request,
    ...<12 lines>...
        self.search_help_text,
    )
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\contrib\admin\views\main.py", line 122, in __init__
    self.queryset = self.get_queryset(request)
                    ~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\contrib\admin\views\main.py", line 503, in get_queryset
    ) = self.get_filters(request)
        ~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\contrib\admin\views\main.py", line 182, in get_filters
    spec = field_list_filter_class(
        field,
    ...<4 lines>...
        field_path=field_path,
    )
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\contrib\admin\filters.py", line 175, in create
    return list_filter_class(
        field, request, params, model, model_admin, field_path=field_path
    )
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\contrib\admin\filters.py", line 188, in __init__
    self.lookup_choices = self.field_choices(field, request, model_admin)
                          ~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\contrib\admin\filters.py", line 225, in field_choices
    return field.get_choices(include_blank=False, ordering=ordering)
           ~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\db\models\fields\__init__.py", line 1010, in get_choices
    (choice_func(x), str(x)) for x in qs
                                      ^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\db\models\query.py", line 398, in __iter__
    self._fetch_all()
    ~~~~~~~~~~~~~~~^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\db\models\query.py", line 1881, in _fetch_all
    self._result_cache = list(self._iterable_class(self))
                         ~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\db\models\query.py", line 91, in __iter__
    results = compiler.execute_sql(
        chunked_fetch=self.chunked_fetch, chunk_size=self.chunk_size
    )
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\db\models\sql\compiler.py", line 1562, in execute_sql
    cursor.execute(sql, params)
    ~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\db\backends\utils.py", line 102, in execute
    return super().execute(sql, params)
           ~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\db\backends\utils.py", line 67, in execute
    return self._execute_with_wrappers(
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~^
        sql, params, many=False, executor=self._execute
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\db\backends\utils.py", line 80, in _execute_with_wrappers
    return executor(sql, params, many, context)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\db\backends\utils.py", line 84, in _execute
    with self.db.wrap_database_errors:
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\db\utils.py", line 91, in __exit__
    raise dj_exc_value.with_traceback(traceback) from exc_value
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\db\backends\utils.py", line 89, in _execute
    return self.cursor.execute(sql, params)
           ~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\db\backends\sqlite3\base.py", line 328, in execute
    return super().execute(query, params)
           ~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
django.db.utils.OperationalError: no such table: tenants_tenant
ERROR 2025-07-25 21:47:26,310 basehttp 25888 14768 "GET /admin/cases/clinic/ HTTP/1.1" 500 221123
INFO 2025-07-25 21:47:30,318 basehttp 25888 14768 "GET /admin/ HTTP/1.1" 200 13965
ERROR 2025-07-25 21:47:33,676 log 25888 14768 Internal Server Error: /admin/tenants/tenantsettings/
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\db\backends\utils.py", line 89, in _execute
    return self.cursor.execute(sql, params)
           ~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\db\backends\sqlite3\base.py", line 328, in execute
    return super().execute(query, params)
           ~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
sqlite3.OperationalError: no such table: tenants_tenantsettings

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\contrib\admin\options.py", line 688, in wrapper
    return self.admin_site.admin_view(view)(*args, **kwargs)
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\utils\decorators.py", line 134, in _wrapper_view
    response = view_func(request, *args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\views\decorators\cache.py", line 62, in _wrapper_view_func
    response = view_func(request, *args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\contrib\admin\sites.py", line 242, in inner
    return view(request, *args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\utils\decorators.py", line 46, in _wrapper
    return bound_method(*args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\utils\decorators.py", line 134, in _wrapper_view
    response = view_func(request, *args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\contrib\admin\options.py", line 1926, in changelist_view
    cl = self.get_changelist_instance(request)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\contrib\admin\options.py", line 836, in get_changelist_instance
    return ChangeList(
        request,
    ...<12 lines>...
        self.search_help_text,
    )
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\contrib\admin\views\main.py", line 123, in __init__
    self.get_results(request)
    ~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\contrib\admin\views\main.py", line 279, in get_results
    result_count = paginator.count
                   ^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\utils\functional.py", line 57, in __get__
    res = instance.__dict__[self.name] = self.func(instance)
                                         ~~~~~~~~~^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\core\paginator.py", line 93, in count
    return c()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\db\models\query.py", line 608, in count
    return self.query.get_count(using=self.db)
           ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\db\models\sql\query.py", line 568, in get_count
    return obj.get_aggregation(using, {"__count": Count("*")})["__count"]
           ~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\db\models\sql\query.py", line 554, in get_aggregation
    result = compiler.execute_sql(SINGLE)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\db\models\sql\compiler.py", line 1562, in execute_sql
    cursor.execute(sql, params)
    ~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\db\backends\utils.py", line 102, in execute
    return super().execute(sql, params)
           ~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\db\backends\utils.py", line 67, in execute
    return self._execute_with_wrappers(
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~^
        sql, params, many=False, executor=self._execute
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\db\backends\utils.py", line 80, in _execute_with_wrappers
    return executor(sql, params, many, context)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\db\backends\utils.py", line 84, in _execute
    with self.db.wrap_database_errors:
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\db\utils.py", line 91, in __exit__
    raise dj_exc_value.with_traceback(traceback) from exc_value
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\db\backends\utils.py", line 89, in _execute
    return self.cursor.execute(sql, params)
           ~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\db\backends\sqlite3\base.py", line 328, in execute
    return super().execute(query, params)
           ~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
django.db.utils.OperationalError: no such table: tenants_tenantsettings
ERROR 2025-07-25 21:47:33,693 basehttp 25888 14768 "GET /admin/tenants/tenantsettings/ HTTP/1.1" 500 199817
INFO 2025-07-25 21:47:37,441 basehttp 25888 14768 "GET /admin/ HTTP/1.1" 200 13965
ERROR 2025-07-25 21:47:39,053 log 25888 14768 Internal Server Error: /admin/tenants/tenantsubscription/
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\db\backends\utils.py", line 89, in _execute
    return self.cursor.execute(sql, params)
           ~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\db\backends\sqlite3\base.py", line 328, in execute
    return super().execute(query, params)
           ~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
sqlite3.OperationalError: no such table: tenants_tenantsubscription

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\contrib\admin\options.py", line 688, in wrapper
    return self.admin_site.admin_view(view)(*args, **kwargs)
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\utils\decorators.py", line 134, in _wrapper_view
    response = view_func(request, *args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\views\decorators\cache.py", line 62, in _wrapper_view_func
    response = view_func(request, *args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\contrib\admin\sites.py", line 242, in inner
    return view(request, *args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\utils\decorators.py", line 46, in _wrapper
    return bound_method(*args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\utils\decorators.py", line 134, in _wrapper_view
    response = view_func(request, *args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\contrib\admin\options.py", line 1926, in changelist_view
    cl = self.get_changelist_instance(request)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\contrib\admin\options.py", line 836, in get_changelist_instance
    return ChangeList(
        request,
    ...<12 lines>...
        self.search_help_text,
    )
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\contrib\admin\views\main.py", line 123, in __init__
    self.get_results(request)
    ~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\contrib\admin\views\main.py", line 279, in get_results
    result_count = paginator.count
                   ^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\utils\functional.py", line 57, in __get__
    res = instance.__dict__[self.name] = self.func(instance)
                                         ~~~~~~~~~^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\core\paginator.py", line 93, in count
    return c()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\db\models\query.py", line 608, in count
    return self.query.get_count(using=self.db)
           ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\db\models\sql\query.py", line 568, in get_count
    return obj.get_aggregation(using, {"__count": Count("*")})["__count"]
           ~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\db\models\sql\query.py", line 554, in get_aggregation
    result = compiler.execute_sql(SINGLE)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\db\models\sql\compiler.py", line 1562, in execute_sql
    cursor.execute(sql, params)
    ~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\db\backends\utils.py", line 102, in execute
    return super().execute(sql, params)
           ~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\db\backends\utils.py", line 67, in execute
    return self._execute_with_wrappers(
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~^
        sql, params, many=False, executor=self._execute
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\db\backends\utils.py", line 80, in _execute_with_wrappers
    return executor(sql, params, many, context)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\db\backends\utils.py", line 84, in _execute
    with self.db.wrap_database_errors:
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\db\utils.py", line 91, in __exit__
    raise dj_exc_value.with_traceback(traceback) from exc_value
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\db\backends\utils.py", line 89, in _execute
    return self.cursor.execute(sql, params)
           ~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\db\backends\sqlite3\base.py", line 328, in execute
    return super().execute(query, params)
           ~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
django.db.utils.OperationalError: no such table: tenants_tenantsubscription
ERROR 2025-07-25 21:47:39,075 basehttp 25888 14768 "GET /admin/tenants/tenantsubscription/ HTTP/1.1" 500 200330
INFO 2025-07-25 21:47:40,700 basehttp 25888 14768 "GET /admin/ HTTP/1.1" 200 13965
INFO 2025-07-25 21:51:41,567 autoreload 29648 22532 Watching for file changes with StatReloader
INFO 2025-07-25 21:51:48,955 basehttp 29648 12808 "GET /admin/ HTTP/1.1" 200 13965
INFO 2025-07-25 21:51:51,726 basehttp 29648 12808 "GET /admin/tenants/tenant/ HTTP/1.1" 200 15021
INFO 2025-07-25 21:51:51,772 basehttp 29648 12808 "GET /admin/jsi18n/ HTTP/1.1" 200 3343
INFO 2025-07-25 21:51:56,642 basehttp 29648 12808 "GET /admin/tenants/tenantsubscription/ HTTP/1.1" 200 15293
INFO 2025-07-25 21:51:56,696 basehttp 29648 12808 "GET /admin/jsi18n/ HTTP/1.1" 200 3343
INFO 2025-07-25 21:51:59,885 basehttp 29648 12808 "GET /admin/tenants/tenantsettings/ HTTP/1.1" 200 15187
INFO 2025-07-25 21:51:59,930 basehttp 29648 12808 "GET /admin/jsi18n/ HTTP/1.1" 200 3343
INFO 2025-07-25 21:52:13,361 basehttp 29648 12808 "GET /admin/cases/case/ HTTP/1.1" 200 15746
INFO 2025-07-25 21:52:13,420 basehttp 29648 12808 "GET /admin/jsi18n/ HTTP/1.1" 200 3343
INFO 2025-07-25 21:52:15,448 basehttp 29648 12808 "GET /admin/cases/case/add/ HTTP/1.1" 200 27923
INFO 2025-07-25 21:52:15,496 basehttp 29648 19320 "GET /admin/jsi18n/ HTTP/1.1" 200 3343
INFO 2025-07-25 21:52:15,762 basehttp 29648 19320 "GET /static/admin/js/calendar.js HTTP/1.1" 200 8466
INFO 2025-07-25 21:52:15,762 basehttp 29648 12808 "GET /static/admin/css/forms.css HTTP/1.1" 200 9047
INFO 2025-07-25 21:52:15,768 basehttp 29648 12808 "GET /static/admin/js/collapse.js HTTP/1.1" 200 1803
INFO 2025-07-25 21:52:15,769 basehttp 29648 19320 "GET /static/admin/js/admin/DateTimeShortcuts.js HTTP/1.1" 200 19319
INFO 2025-07-25 21:52:15,773 basehttp 29648 12808 "GET /static/admin/js/prepopulate_init.js HTTP/1.1" 200 586
INFO 2025-07-25 21:52:15,774 basehttp 29648 19320 "GET /static/admin/css/widgets.css HTTP/1.1" 200 11900
INFO 2025-07-25 21:52:15,778 basehttp 29648 19320 "GET /static/admin/js/change_form.js HTTP/1.1" 200 606
INFO 2025-07-25 21:52:15,870 basehttp 29648 12808 "GET /static/admin/img/icon-clock.svg HTTP/1.1" 200 677
INFO 2025-07-25 21:52:15,870 basehttp 29648 19320 "GET /static/admin/img/icon-calendar.svg HTTP/1.1" 200 1086
ERROR 2025-07-25 21:52:45,810 log 29648 19320 Internal Server Error: /admin/billing/invoiceitem/
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\db\backends\utils.py", line 89, in _execute
    return self.cursor.execute(sql, params)
           ~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\db\backends\sqlite3\base.py", line 328, in execute
    return super().execute(query, params)
           ~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
sqlite3.OperationalError: no such table: billing_invoiceitem

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\contrib\admin\options.py", line 688, in wrapper
    return self.admin_site.admin_view(view)(*args, **kwargs)
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\utils\decorators.py", line 134, in _wrapper_view
    response = view_func(request, *args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\views\decorators\cache.py", line 62, in _wrapper_view_func
    response = view_func(request, *args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\contrib\admin\sites.py", line 242, in inner
    return view(request, *args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\utils\decorators.py", line 46, in _wrapper
    return bound_method(*args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\utils\decorators.py", line 134, in _wrapper_view
    response = view_func(request, *args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\contrib\admin\options.py", line 1926, in changelist_view
    cl = self.get_changelist_instance(request)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\contrib\admin\options.py", line 836, in get_changelist_instance
    return ChangeList(
        request,
    ...<12 lines>...
        self.search_help_text,
    )
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\contrib\admin\views\main.py", line 123, in __init__
    self.get_results(request)
    ~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\contrib\admin\views\main.py", line 279, in get_results
    result_count = paginator.count
                   ^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\utils\functional.py", line 57, in __get__
    res = instance.__dict__[self.name] = self.func(instance)
                                         ~~~~~~~~~^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\core\paginator.py", line 93, in count
    return c()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\db\models\query.py", line 608, in count
    return self.query.get_count(using=self.db)
           ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\db\models\sql\query.py", line 568, in get_count
    return obj.get_aggregation(using, {"__count": Count("*")})["__count"]
           ~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\db\models\sql\query.py", line 554, in get_aggregation
    result = compiler.execute_sql(SINGLE)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\db\models\sql\compiler.py", line 1562, in execute_sql
    cursor.execute(sql, params)
    ~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\db\backends\utils.py", line 102, in execute
    return super().execute(sql, params)
           ~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\db\backends\utils.py", line 67, in execute
    return self._execute_with_wrappers(
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~^
        sql, params, many=False, executor=self._execute
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\db\backends\utils.py", line 80, in _execute_with_wrappers
    return executor(sql, params, many, context)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\db\backends\utils.py", line 84, in _execute
    with self.db.wrap_database_errors:
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\db\utils.py", line 91, in __exit__
    raise dj_exc_value.with_traceback(traceback) from exc_value
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\db\backends\utils.py", line 89, in _execute
    return self.cursor.execute(sql, params)
           ~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\db\backends\sqlite3\base.py", line 328, in execute
    return super().execute(query, params)
           ~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
django.db.utils.OperationalError: no such table: billing_invoiceitem
ERROR 2025-07-25 21:52:45,828 basehttp 29648 19320 "GET /admin/billing/invoiceitem/ HTTP/1.1" 500 199928
INFO 2025-07-25 21:52:54,190 basehttp 29648 19320 "GET /admin/cases/case/add/ HTTP/1.1" 200 27923
ERROR 2025-07-25 21:52:56,585 log 29648 19320 Internal Server Error: /admin/billing/invoice/
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\db\backends\utils.py", line 89, in _execute
    return self.cursor.execute(sql, params)
           ~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\db\backends\sqlite3\base.py", line 328, in execute
    return super().execute(query, params)
           ~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
sqlite3.OperationalError: no such table: billing_invoice

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\contrib\admin\options.py", line 688, in wrapper
    return self.admin_site.admin_view(view)(*args, **kwargs)
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\utils\decorators.py", line 134, in _wrapper_view
    response = view_func(request, *args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\views\decorators\cache.py", line 62, in _wrapper_view_func
    response = view_func(request, *args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\contrib\admin\sites.py", line 242, in inner
    return view(request, *args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\utils\decorators.py", line 46, in _wrapper
    return bound_method(*args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\utils\decorators.py", line 134, in _wrapper_view
    response = view_func(request, *args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\contrib\admin\options.py", line 1926, in changelist_view
    cl = self.get_changelist_instance(request)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\contrib\admin\options.py", line 836, in get_changelist_instance
    return ChangeList(
        request,
    ...<12 lines>...
        self.search_help_text,
    )
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\contrib\admin\views\main.py", line 123, in __init__
    self.get_results(request)
    ~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\contrib\admin\views\main.py", line 279, in get_results
    result_count = paginator.count
                   ^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\utils\functional.py", line 57, in __get__
    res = instance.__dict__[self.name] = self.func(instance)
                                         ~~~~~~~~~^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\core\paginator.py", line 93, in count
    return c()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\db\models\query.py", line 608, in count
    return self.query.get_count(using=self.db)
           ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\db\models\sql\query.py", line 568, in get_count
    return obj.get_aggregation(using, {"__count": Count("*")})["__count"]
           ~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\db\models\sql\query.py", line 554, in get_aggregation
    result = compiler.execute_sql(SINGLE)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\db\models\sql\compiler.py", line 1562, in execute_sql
    cursor.execute(sql, params)
    ~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\db\backends\utils.py", line 102, in execute
    return super().execute(sql, params)
           ~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\db\backends\utils.py", line 67, in execute
    return self._execute_with_wrappers(
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~^
        sql, params, many=False, executor=self._execute
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\db\backends\utils.py", line 80, in _execute_with_wrappers
    return executor(sql, params, many, context)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\db\backends\utils.py", line 84, in _execute
    with self.db.wrap_database_errors:
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\db\utils.py", line 91, in __exit__
    raise dj_exc_value.with_traceback(traceback) from exc_value
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\db\backends\utils.py", line 89, in _execute
    return self.cursor.execute(sql, params)
           ~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\db\backends\sqlite3\base.py", line 328, in execute
    return super().execute(query, params)
           ~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
django.db.utils.OperationalError: no such table: billing_invoice
ERROR 2025-07-25 21:52:56,613 basehttp 29648 19320 "GET /admin/billing/invoice/ HTTP/1.1" 500 199880
INFO 2025-07-25 21:53:02,263 basehttp 29648 19320 "GET /admin/cases/case/add/ HTTP/1.1" 200 27923
ERROR 2025-07-25 21:53:06,624 log 29648 19320 Internal Server Error: /admin/billing/pricelist/
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\db\backends\utils.py", line 89, in _execute
    return self.cursor.execute(sql, params)
           ~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\db\backends\sqlite3\base.py", line 328, in execute
    return super().execute(query, params)
           ~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
sqlite3.OperationalError: no such table: billing_pricelist

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\contrib\admin\options.py", line 688, in wrapper
    return self.admin_site.admin_view(view)(*args, **kwargs)
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\utils\decorators.py", line 134, in _wrapper_view
    response = view_func(request, *args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\views\decorators\cache.py", line 62, in _wrapper_view_func
    response = view_func(request, *args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\contrib\admin\sites.py", line 242, in inner
    return view(request, *args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\utils\decorators.py", line 46, in _wrapper
    return bound_method(*args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\utils\decorators.py", line 134, in _wrapper_view
    response = view_func(request, *args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\contrib\admin\options.py", line 1926, in changelist_view
    cl = self.get_changelist_instance(request)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\contrib\admin\options.py", line 836, in get_changelist_instance
    return ChangeList(
        request,
    ...<12 lines>...
        self.search_help_text,
    )
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\contrib\admin\views\main.py", line 123, in __init__
    self.get_results(request)
    ~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\contrib\admin\views\main.py", line 279, in get_results
    result_count = paginator.count
                   ^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\utils\functional.py", line 57, in __get__
    res = instance.__dict__[self.name] = self.func(instance)
                                         ~~~~~~~~~^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\core\paginator.py", line 93, in count
    return c()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\db\models\query.py", line 608, in count
    return self.query.get_count(using=self.db)
           ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\db\models\sql\query.py", line 568, in get_count
    return obj.get_aggregation(using, {"__count": Count("*")})["__count"]
           ~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\db\models\sql\query.py", line 554, in get_aggregation
    result = compiler.execute_sql(SINGLE)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\db\models\sql\compiler.py", line 1562, in execute_sql
    cursor.execute(sql, params)
    ~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\db\backends\utils.py", line 102, in execute
    return super().execute(sql, params)
           ~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\db\backends\utils.py", line 67, in execute
    return self._execute_with_wrappers(
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~^
        sql, params, many=False, executor=self._execute
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\db\backends\utils.py", line 80, in _execute_with_wrappers
    return executor(sql, params, many, context)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\db\backends\utils.py", line 84, in _execute
    with self.db.wrap_database_errors:
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\db\utils.py", line 91, in __exit__
    raise dj_exc_value.with_traceback(traceback) from exc_value
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\db\backends\utils.py", line 89, in _execute
    return self.cursor.execute(sql, params)
           ~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\db\backends\sqlite3\base.py", line 328, in execute
    return super().execute(query, params)
           ~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
django.db.utils.OperationalError: no such table: billing_pricelist
ERROR 2025-07-25 21:53:06,649 basehttp 29648 19320 "GET /admin/billing/pricelist/ HTTP/1.1" 500 199801
INFO 2025-07-25 21:53:08,804 basehttp 29648 19320 "GET /admin/cases/case/add/ HTTP/1.1" 200 27923
ERROR 2025-07-25 21:53:10,775 log 29648 19320 Internal Server Error: /admin/billing/serviceprice/
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\db\backends\utils.py", line 89, in _execute
    return self.cursor.execute(sql, params)
           ~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\db\backends\sqlite3\base.py", line 328, in execute
    return super().execute(query, params)
           ~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
sqlite3.OperationalError: no such table: billing_serviceprice

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\contrib\admin\options.py", line 688, in wrapper
    return self.admin_site.admin_view(view)(*args, **kwargs)
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\utils\decorators.py", line 134, in _wrapper_view
    response = view_func(request, *args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\views\decorators\cache.py", line 62, in _wrapper_view_func
    response = view_func(request, *args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\contrib\admin\sites.py", line 242, in inner
    return view(request, *args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\utils\decorators.py", line 46, in _wrapper
    return bound_method(*args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\utils\decorators.py", line 134, in _wrapper_view
    response = view_func(request, *args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\contrib\admin\options.py", line 1926, in changelist_view
    cl = self.get_changelist_instance(request)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\contrib\admin\options.py", line 836, in get_changelist_instance
    return ChangeList(
        request,
    ...<12 lines>...
        self.search_help_text,
    )
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\contrib\admin\views\main.py", line 123, in __init__
    self.get_results(request)
    ~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\contrib\admin\views\main.py", line 279, in get_results
    result_count = paginator.count
                   ^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\utils\functional.py", line 57, in __get__
    res = instance.__dict__[self.name] = self.func(instance)
                                         ~~~~~~~~~^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\core\paginator.py", line 93, in count
    return c()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\db\models\query.py", line 608, in count
    return self.query.get_count(using=self.db)
           ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\db\models\sql\query.py", line 568, in get_count
    return obj.get_aggregation(using, {"__count": Count("*")})["__count"]
           ~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\db\models\sql\query.py", line 554, in get_aggregation
    result = compiler.execute_sql(SINGLE)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\db\models\sql\compiler.py", line 1562, in execute_sql
    cursor.execute(sql, params)
    ~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\db\backends\utils.py", line 102, in execute
    return super().execute(sql, params)
           ~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\db\backends\utils.py", line 67, in execute
    return self._execute_with_wrappers(
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~^
        sql, params, many=False, executor=self._execute
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\db\backends\utils.py", line 80, in _execute_with_wrappers
    return executor(sql, params, many, context)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\db\backends\utils.py", line 84, in _execute
    with self.db.wrap_database_errors:
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\db\utils.py", line 91, in __exit__
    raise dj_exc_value.with_traceback(traceback) from exc_value
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\db\backends\utils.py", line 89, in _execute
    return self.cursor.execute(sql, params)
           ~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\db\backends\sqlite3\base.py", line 328, in execute
    return super().execute(query, params)
           ~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
django.db.utils.OperationalError: no such table: billing_serviceprice
ERROR 2025-07-25 21:53:10,800 basehttp 29648 19320 "GET /admin/billing/serviceprice/ HTTP/1.1" 500 199980
INFO 2025-07-25 21:53:12,209 basehttp 29648 19320 "GET /admin/cases/case/add/ HTTP/1.1" 200 27923
INFO 2025-07-25 21:53:15,931 basehttp 29648 19320 "GET /admin/cases/caseevent/ HTTP/1.1" 200 14598
INFO 2025-07-25 21:53:15,987 basehttp 29648 19320 "GET /admin/jsi18n/ HTTP/1.1" 200 3343
INFO 2025-07-25 21:53:17,452 basehttp 29648 19320 "GET /admin/cases/clinic/ HTTP/1.1" 200 13726
INFO 2025-07-25 21:53:17,487 basehttp 29648 19320 "GET /admin/jsi18n/ HTTP/1.1" 200 3343
ERROR 2025-07-25 21:53:32,953 log 29648 19320 Internal Server Error: /admin/billing/payment/
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\db\backends\utils.py", line 89, in _execute
    return self.cursor.execute(sql, params)
           ~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\db\backends\sqlite3\base.py", line 328, in execute
    return super().execute(query, params)
           ~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
sqlite3.OperationalError: no such table: billing_payment

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\contrib\admin\options.py", line 688, in wrapper
    return self.admin_site.admin_view(view)(*args, **kwargs)
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\utils\decorators.py", line 134, in _wrapper_view
    response = view_func(request, *args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\views\decorators\cache.py", line 62, in _wrapper_view_func
    response = view_func(request, *args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\contrib\admin\sites.py", line 242, in inner
    return view(request, *args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\utils\decorators.py", line 46, in _wrapper
    return bound_method(*args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\utils\decorators.py", line 134, in _wrapper_view
    response = view_func(request, *args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\contrib\admin\options.py", line 1926, in changelist_view
    cl = self.get_changelist_instance(request)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\contrib\admin\options.py", line 836, in get_changelist_instance
    return ChangeList(
        request,
    ...<12 lines>...
        self.search_help_text,
    )
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\contrib\admin\views\main.py", line 123, in __init__
    self.get_results(request)
    ~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\contrib\admin\views\main.py", line 279, in get_results
    result_count = paginator.count
                   ^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\utils\functional.py", line 57, in __get__
    res = instance.__dict__[self.name] = self.func(instance)
                                         ~~~~~~~~~^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\core\paginator.py", line 93, in count
    return c()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\db\models\query.py", line 608, in count
    return self.query.get_count(using=self.db)
           ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\db\models\sql\query.py", line 568, in get_count
    return obj.get_aggregation(using, {"__count": Count("*")})["__count"]
           ~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\db\models\sql\query.py", line 554, in get_aggregation
    result = compiler.execute_sql(SINGLE)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\db\models\sql\compiler.py", line 1562, in execute_sql
    cursor.execute(sql, params)
    ~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\db\backends\utils.py", line 102, in execute
    return super().execute(sql, params)
           ~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\db\backends\utils.py", line 67, in execute
    return self._execute_with_wrappers(
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~^
        sql, params, many=False, executor=self._execute
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\db\backends\utils.py", line 80, in _execute_with_wrappers
    return executor(sql, params, many, context)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\db\backends\utils.py", line 84, in _execute
    with self.db.wrap_database_errors:
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\db\utils.py", line 91, in __exit__
    raise dj_exc_value.with_traceback(traceback) from exc_value
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\db\backends\utils.py", line 89, in _execute
    return self.cursor.execute(sql, params)
           ~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\db\backends\sqlite3\base.py", line 328, in execute
    return super().execute(query, params)
           ~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
django.db.utils.OperationalError: no such table: billing_payment
ERROR 2025-07-25 21:53:32,965 basehttp 29648 19320 "GET /admin/billing/payment/ HTTP/1.1" 500 199774
INFO 2025-07-25 21:56:56,517 autoreload 29648 22532 C:\GPT4_PROJECTS\DENTAL_LAB_ARCHITECTURE_PATTERNS\dentflow\backend\dentflow_project\settings.py changed, reloading.
INFO 2025-07-25 21:56:58,037 autoreload 30820 18944 Watching for file changes with StatReloader
INFO 2025-07-25 21:57:28,870 autoreload 30820 18944 C:\GPT4_PROJECTS\DENTAL_LAB_ARCHITECTURE_PATTERNS\dentflow\backend\apps\inventory\models.py changed, reloading.
INFO 2025-07-25 21:57:30,191 autoreload 26104 3168 Watching for file changes with StatReloader
INFO 2025-07-25 21:58:00,868 autoreload 26104 3168 C:\GPT4_PROJECTS\DENTAL_LAB_ARCHITECTURE_PATTERNS\dentflow\backend\apps\inventory\models.py changed, reloading.
INFO 2025-07-25 21:58:02,250 autoreload 28208 26520 Watching for file changes with StatReloader
INFO 2025-07-25 21:58:15,426 autoreload 28208 26520 C:\GPT4_PROJECTS\DENTAL_LAB_ARCHITECTURE_PATTERNS\dentflow\backend\apps\inventory\models.py changed, reloading.
INFO 2025-07-25 21:58:17,110 autoreload 16472 1088 Watching for file changes with StatReloader
INFO 2025-07-25 21:58:51,263 autoreload 16472 1088 C:\GPT4_PROJECTS\DENTAL_LAB_ARCHITECTURE_PATTERNS\dentflow\backend\apps\inventory\models.py changed, reloading.
INFO 2025-07-25 21:58:52,545 autoreload 33740 26864 Watching for file changes with StatReloader
INFO 2025-07-25 21:59:21,160 autoreload 33740 26864 C:\GPT4_PROJECTS\DENTAL_LAB_ARCHITECTURE_PATTERNS\dentflow\backend\apps\billing\models.py changed, reloading.
INFO 2025-07-25 21:59:22,419 autoreload 6268 27284 Watching for file changes with StatReloader
INFO 2025-07-25 21:59:49,856 autoreload 6268 27284 C:\GPT4_PROJECTS\DENTAL_LAB_ARCHITECTURE_PATTERNS\dentflow\backend\apps\billing\models.py changed, reloading.
INFO 2025-07-25 21:59:51,155 autoreload 20216 6352 Watching for file changes with StatReloader
INFO 2025-07-25 22:00:06,201 autoreload 20216 6352 C:\GPT4_PROJECTS\DENTAL_LAB_ARCHITECTURE_PATTERNS\dentflow\backend\apps\billing\models.py changed, reloading.
INFO 2025-07-25 22:00:07,610 autoreload 25924 32704 Watching for file changes with StatReloader
INFO 2025-07-25 22:01:22,982 autoreload 25924 32704 C:\GPT4_PROJECTS\DENTAL_LAB_ARCHITECTURE_PATTERNS\dentflow\backend\apps\inventory\admin.py changed, reloading.
INFO 2025-07-25 22:01:24,366 autoreload 14236 31720 Watching for file changes with StatReloader
INFO 2025-07-25 22:02:03,604 autoreload 14236 31720 C:\GPT4_PROJECTS\DENTAL_LAB_ARCHITECTURE_PATTERNS\dentflow\backend\apps\billing\admin.py changed, reloading.
INFO 2025-07-25 22:02:04,902 autoreload 32856 31580 Watching for file changes with StatReloader
INFO 2025-07-25 22:02:18,768 autoreload 32856 31580 C:\GPT4_PROJECTS\DENTAL_LAB_ARCHITECTURE_PATTERNS\dentflow\backend\apps\billing\admin.py changed, reloading.
INFO 2025-07-25 22:02:20,200 autoreload 32252 27020 Watching for file changes with StatReloader
INFO 2025-07-25 22:02:35,222 autoreload 32252 27020 C:\GPT4_PROJECTS\DENTAL_LAB_ARCHITECTURE_PATTERNS\dentflow\backend\apps\billing\admin.py changed, reloading.
INFO 2025-07-25 22:02:36,566 autoreload 18804 18092 Watching for file changes with StatReloader
INFO 2025-07-25 22:02:51,872 autoreload 18804 18092 C:\GPT4_PROJECTS\DENTAL_LAB_ARCHITECTURE_PATTERNS\dentflow\backend\apps\billing\admin.py changed, reloading.
INFO 2025-07-25 22:02:53,259 autoreload 6684 22976 Watching for file changes with StatReloader
INFO 2025-07-25 22:03:00,955 autoreload 6684 22976 C:\GPT4_PROJECTS\DENTAL_LAB_ARCHITECTURE_PATTERNS\dentflow\backend\apps\billing\admin.py changed, reloading.
INFO 2025-07-25 22:03:02,686 autoreload 36276 19084 Watching for file changes with StatReloader
INFO 2025-07-25 22:03:58,699 autoreload 36276 19084 C:\GPT4_PROJECTS\DENTAL_LAB_ARCHITECTURE_PATTERNS\dentflow\backend\apps\billing\admin.py changed, reloading.
INFO 2025-07-25 22:03:59,996 autoreload 36404 8516 Watching for file changes with StatReloader
ERROR 2025-07-25 22:04:57,554 log 36404 18364 Internal Server Error: /admin/billing/payment/
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\db\backends\utils.py", line 89, in _execute
    return self.cursor.execute(sql, params)
           ~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\db\backends\sqlite3\base.py", line 328, in execute
    return super().execute(query, params)
           ~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
sqlite3.OperationalError: no such table: billing_payment

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\contrib\admin\options.py", line 688, in wrapper
    return self.admin_site.admin_view(view)(*args, **kwargs)
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\utils\decorators.py", line 134, in _wrapper_view
    response = view_func(request, *args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\views\decorators\cache.py", line 62, in _wrapper_view_func
    response = view_func(request, *args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\contrib\admin\sites.py", line 242, in inner
    return view(request, *args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\utils\decorators.py", line 46, in _wrapper
    return bound_method(*args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\utils\decorators.py", line 134, in _wrapper_view
    response = view_func(request, *args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\contrib\admin\options.py", line 1926, in changelist_view
    cl = self.get_changelist_instance(request)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\contrib\admin\options.py", line 836, in get_changelist_instance
    return ChangeList(
        request,
    ...<12 lines>...
        self.search_help_text,
    )
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\contrib\admin\views\main.py", line 123, in __init__
    self.get_results(request)
    ~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\contrib\admin\views\main.py", line 279, in get_results
    result_count = paginator.count
                   ^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\utils\functional.py", line 57, in __get__
    res = instance.__dict__[self.name] = self.func(instance)
                                         ~~~~~~~~~^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\core\paginator.py", line 93, in count
    return c()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\db\models\query.py", line 608, in count
    return self.query.get_count(using=self.db)
           ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\db\models\sql\query.py", line 568, in get_count
    return obj.get_aggregation(using, {"__count": Count("*")})["__count"]
           ~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\db\models\sql\query.py", line 554, in get_aggregation
    result = compiler.execute_sql(SINGLE)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\db\models\sql\compiler.py", line 1562, in execute_sql
    cursor.execute(sql, params)
    ~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\db\backends\utils.py", line 102, in execute
    return super().execute(sql, params)
           ~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\db\backends\utils.py", line 67, in execute
    return self._execute_with_wrappers(
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~^
        sql, params, many=False, executor=self._execute
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\db\backends\utils.py", line 80, in _execute_with_wrappers
    return executor(sql, params, many, context)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\db\backends\utils.py", line 84, in _execute
    with self.db.wrap_database_errors:
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\db\utils.py", line 91, in __exit__
    raise dj_exc_value.with_traceback(traceback) from exc_value
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\db\backends\utils.py", line 89, in _execute
    return self.cursor.execute(sql, params)
           ~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\db\backends\sqlite3\base.py", line 328, in execute
    return super().execute(query, params)
           ~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
django.db.utils.OperationalError: no such table: billing_payment
ERROR 2025-07-25 22:04:57,568 basehttp 36404 18364 "GET /admin/billing/payment/ HTTP/1.1" 500 199998
INFO 2025-07-25 22:05:01,085 basehttp 36404 18364 "GET /admin/cases/clinic/ HTTP/1.1" 200 17581
INFO 2025-07-25 22:05:06,309 autoreload 36404 8516 C:\GPT4_PROJECTS\DENTAL_LAB_ARCHITECTURE_PATTERNS\dentflow\backend\apps\billing\admin.py changed, reloading.
INFO 2025-07-25 22:05:08,026 autoreload 28216 30096 Watching for file changes with StatReloader
ERROR 2025-07-25 22:05:11,455 log 28216 1148 Internal Server Error: /admin/billing/invoiceitemmaterialusage/
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\db\backends\utils.py", line 89, in _execute
    return self.cursor.execute(sql, params)
           ~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\db\backends\sqlite3\base.py", line 328, in execute
    return super().execute(query, params)
           ~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
sqlite3.OperationalError: no such table: inventory_materialcategory

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\contrib\admin\options.py", line 688, in wrapper
    return self.admin_site.admin_view(view)(*args, **kwargs)
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\utils\decorators.py", line 134, in _wrapper_view
    response = view_func(request, *args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\views\decorators\cache.py", line 62, in _wrapper_view_func
    response = view_func(request, *args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\contrib\admin\sites.py", line 242, in inner
    return view(request, *args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\utils\decorators.py", line 46, in _wrapper
    return bound_method(*args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\utils\decorators.py", line 134, in _wrapper_view
    response = view_func(request, *args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\contrib\admin\options.py", line 1926, in changelist_view
    cl = self.get_changelist_instance(request)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\contrib\admin\options.py", line 836, in get_changelist_instance
    return ChangeList(
        request,
    ...<12 lines>...
        self.search_help_text,
    )
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\contrib\admin\views\main.py", line 122, in __init__
    self.queryset = self.get_queryset(request)
                    ~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\contrib\admin\views\main.py", line 503, in get_queryset
    ) = self.get_filters(request)
        ~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\contrib\admin\views\main.py", line 182, in get_filters
    spec = field_list_filter_class(
        field,
    ...<4 lines>...
        field_path=field_path,
    )
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\contrib\admin\filters.py", line 175, in create
    return list_filter_class(
        field, request, params, model, model_admin, field_path=field_path
    )
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\contrib\admin\filters.py", line 188, in __init__
    self.lookup_choices = self.field_choices(field, request, model_admin)
                          ~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\contrib\admin\filters.py", line 225, in field_choices
    return field.get_choices(include_blank=False, ordering=ordering)
           ~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\db\models\fields\__init__.py", line 1010, in get_choices
    (choice_func(x), str(x)) for x in qs
                                      ^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\db\models\query.py", line 398, in __iter__
    self._fetch_all()
    ~~~~~~~~~~~~~~~^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\db\models\query.py", line 1881, in _fetch_all
    self._result_cache = list(self._iterable_class(self))
                         ~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\db\models\query.py", line 91, in __iter__
    results = compiler.execute_sql(
        chunked_fetch=self.chunked_fetch, chunk_size=self.chunk_size
    )
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\db\models\sql\compiler.py", line 1562, in execute_sql
    cursor.execute(sql, params)
    ~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\db\backends\utils.py", line 102, in execute
    return super().execute(sql, params)
           ~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\db\backends\utils.py", line 67, in execute
    return self._execute_with_wrappers(
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~^
        sql, params, many=False, executor=self._execute
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\db\backends\utils.py", line 80, in _execute_with_wrappers
    return executor(sql, params, many, context)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\db\backends\utils.py", line 84, in _execute
    with self.db.wrap_database_errors:
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\db\utils.py", line 91, in __exit__
    raise dj_exc_value.with_traceback(traceback) from exc_value
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\db\backends\utils.py", line 89, in _execute
    return self.cursor.execute(sql, params)
           ~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\db\backends\sqlite3\base.py", line 328, in execute
    return super().execute(query, params)
           ~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
django.db.utils.OperationalError: no such table: inventory_materialcategory
ERROR 2025-07-25 22:05:11,516 basehttp 28216 1148 "GET /admin/billing/invoiceitemmaterialusage/ HTTP/1.1" 500 221849
INFO 2025-07-25 22:05:13,897 basehttp 28216 1148 "GET /admin/cases/clinic/ HTTP/1.1" 200 17581
INFO 2025-07-25 22:05:16,326 autoreload 28216 30096 C:\GPT4_PROJECTS\DENTAL_LAB_ARCHITECTURE_PATTERNS\dentflow\backend\apps\billing\admin.py changed, reloading.
INFO 2025-07-25 22:05:17,943 autoreload 30100 21572 Watching for file changes with StatReloader
INFO 2025-07-25 22:05:32,052 autoreload 30100 21572 C:\GPT4_PROJECTS\DENTAL_LAB_ARCHITECTURE_PATTERNS\dentflow\backend\apps\billing\admin.py changed, reloading.
INFO 2025-07-25 22:05:33,397 autoreload 16152 7476 Watching for file changes with StatReloader
INFO 2025-07-26 01:51:09,333 autoreload 9444 30172 Watching for file changes with StatReloader
INFO 2025-07-26 01:51:15,075 basehttp 9444 24212 "GET /admin/inventory/item/ HTTP/1.1" 200 21633
INFO 2025-07-26 01:51:15,111 basehttp 9444 24212 "GET /static/admin/css/changelists.css HTTP/1.1" 304 0
INFO 2025-07-26 01:51:15,121 basehttp 9444 24212 "GET /admin/jsi18n/ HTTP/1.1" 200 3343
INFO 2025-07-26 01:51:15,124 basehttp 9444 24212 "GET /static/admin/js/vendor/jquery/jquery.js HTTP/1.1" 304 0
INFO 2025-07-26 01:51:15,125 basehttp 9444 24212 "GET /static/admin/js/jquery.init.js HTTP/1.1" 304 0
INFO 2025-07-26 01:51:15,127 basehttp 9444 24212 "GET /static/admin/js/core.js HTTP/1.1" 304 0
INFO 2025-07-26 01:51:15,129 basehttp 9444 24212 "GET /static/admin/js/admin/RelatedObjectLookups.js HTTP/1.1" 304 0
INFO 2025-07-26 01:51:15,131 basehttp 9444 24212 "GET /static/admin/js/actions.js HTTP/1.1" 304 0
INFO 2025-07-26 01:51:15,133 basehttp 9444 24212 "GET /static/admin/js/urlify.js HTTP/1.1" 304 0
INFO 2025-07-26 01:51:15,136 basehttp 9444 24212 "GET /static/admin/js/prepopulate.js HTTP/1.1" 304 0
INFO 2025-07-26 01:51:15,139 basehttp 9444 24212 "GET /static/admin/js/vendor/xregexp/xregexp.js HTTP/1.1" 304 0
INFO 2025-07-26 01:51:15,140 basehttp 9444 24212 "GET /static/admin/img/search.svg HTTP/1.1" 304 0
INFO 2025-07-26 01:51:15,179 basehttp 9444 24212 "GET /static/admin/img/icon-yes.svg HTTP/1.1" 200 436
INFO 2025-07-26 01:51:15,182 basehttp 9444 24212 "GET /static/admin/js/filters.js HTTP/1.1" 304 0
INFO 2025-07-26 01:51:15,185 basehttp 9444 24212 "GET /static/admin/img/tooltag-add.svg HTTP/1.1" 304 0
INFO 2025-07-26 01:51:15,187 basehttp 9444 24212 "GET /static/admin/img/sorting-icons.svg HTTP/1.1" 200 1097
ERROR 2025-07-26 01:51:19,307 log 9444 24212 Internal Server Error: /admin/inventory/item/84b5d132-6ccd-4040-83c4-69f8f0f73cb0/change/
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\db\models\options.py", line 681, in get_field
    return self.fields_map[field_name]
           ~~~~~~~~~~~~~~~^^^^^^^^^^^^
KeyError: 'get_total_quantity_needed'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\contrib\admin\utils.py", line 272, in lookup_field
    f = _get_non_gfk_field(opts, name)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\contrib\admin\utils.py", line 303, in _get_non_gfk_field
    field = opts.get_field(name)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\db\models\options.py", line 683, in get_field
    raise FieldDoesNotExist(
        "%s has no field named '%s'" % (self.object_name, field_name)
    )
django.core.exceptions.FieldDoesNotExist: ItemMaterialComposition has no field named 'get_total_quantity_needed'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\core\handlers\base.py", line 220, in _get_response
    response = response.render()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\template\response.py", line 114, in render
    self.content = self.rendered_content
                   ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\template\response.py", line 92, in rendered_content
    return template.render(context, self._request)
           ~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\template\backends\django.py", line 61, in render
    return self.template.render(context)
           ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\template\base.py", line 175, in render
    return self._render(context)
           ~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\template\base.py", line 167, in _render
    return self.nodelist.render(context)
           ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\template\base.py", line 1005, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\template\base.py", line 966, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\template\loader_tags.py", line 157, in render
    return compiled_parent._render(context)
           ~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\template\base.py", line 167, in _render
    return self.nodelist.render(context)
           ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\template\base.py", line 1005, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\template\base.py", line 966, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\template\loader_tags.py", line 157, in render
    return compiled_parent._render(context)
           ~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\template\base.py", line 167, in _render
    return self.nodelist.render(context)
           ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\template\base.py", line 1005, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\template\base.py", line 966, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\template\loader_tags.py", line 63, in render
    result = block.nodelist.render(context)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\template\base.py", line 1005, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\template\base.py", line 966, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\template\loader_tags.py", line 63, in render
    result = block.nodelist.render(context)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\template\base.py", line 1005, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\template\base.py", line 966, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\template\defaulttags.py", line 238, in render
    nodelist.append(node.render_annotated(context))
                    ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\template\base.py", line 966, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\template\loader_tags.py", line 208, in render
    return template.render(context)
           ~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\template\base.py", line 177, in render
    return self._render(context)
           ~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\template\base.py", line 167, in _render
    return self.nodelist.render(context)
           ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\template\base.py", line 1005, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\template\base.py", line 966, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\template\defaulttags.py", line 238, in render
    nodelist.append(node.render_annotated(context))
                    ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\template\base.py", line 966, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\template\defaulttags.py", line 238, in render
    nodelist.append(node.render_annotated(context))
                    ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\template\base.py", line 966, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\template\defaulttags.py", line 238, in render
    nodelist.append(node.render_annotated(context))
                    ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\template\base.py", line 966, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\template\defaulttags.py", line 238, in render
    nodelist.append(node.render_annotated(context))
                    ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\template\base.py", line 966, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\template\defaulttags.py", line 321, in render
    return nodelist.render(context)
           ~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\template\base.py", line 1005, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\template\base.py", line 966, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\template\base.py", line 1064, in render
    output = self.filter_expression.resolve(context)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\template\base.py", line 715, in resolve
    obj = self.var.resolve(context)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\template\base.py", line 847, in resolve
    value = self._resolve_lookup(context)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\template\base.py", line 914, in _resolve_lookup
    current = current()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\contrib\admin\helpers.py", line 273, in contents
    f, attr, value = lookup_field(field, obj, model_admin)
                     ~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\contrib\admin\utils.py", line 285, in lookup_field
    value = attr()
  File "C:\GPT4_PROJECTS\DENTAL_LAB_ARCHITECTURE_PATTERNS\dentflow\backend\apps\inventory\models.py", line 485, in get_total_quantity_needed
    return self.quantity * waste_multiplier
           ~~~~~~~~~~~~~~^~~~~~~~~~~~~~~~~~
TypeError: unsupported operand type(s) for *: 'NoneType' and 'decimal.Decimal'
ERROR 2025-07-26 01:51:19,320 basehttp 9444 24212 "GET /admin/inventory/item/84b5d132-6ccd-4040-83c4-69f8f0f73cb0/change/ HTTP/1.1" 500 640441
INFO 2025-07-26 01:51:45,902 basehttp 9444 24212 "GET /admin/inventory/item/ HTTP/1.1" 200 21633
INFO 2025-07-26 01:51:52,102 autoreload 9444 30172 C:\GPT4_PROJECTS\DENTAL_LAB_ARCHITECTURE_PATTERNS\dentflow\backend\apps\inventory\models.py changed, reloading.
INFO 2025-07-26 01:51:53,568 autoreload 17404 13632 Watching for file changes with StatReloader
ERROR 2025-07-26 01:51:56,458 log 17404 16432 Internal Server Error: /admin/billing/pricelist/
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\db\models\options.py", line 681, in get_field
    return self.fields_map[field_name]
           ~~~~~~~~~~~~~~~^^^^^^^^^^^^
KeyError: 'service_count'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\contrib\admin\utils.py", line 272, in lookup_field
    f = _get_non_gfk_field(opts, name)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\contrib\admin\utils.py", line 303, in _get_non_gfk_field
    field = opts.get_field(name)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\db\models\options.py", line 683, in get_field
    raise FieldDoesNotExist(
        "%s has no field named '%s'" % (self.object_name, field_name)
    )
django.core.exceptions.FieldDoesNotExist: PriceList has no field named 'service_count'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\core\handlers\base.py", line 220, in _get_response
    response = response.render()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\template\response.py", line 114, in render
    self.content = self.rendered_content
                   ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\template\response.py", line 92, in rendered_content
    return template.render(context, self._request)
           ~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\template\backends\django.py", line 61, in render
    return self.template.render(context)
           ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\template\base.py", line 175, in render
    return self._render(context)
           ~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\template\base.py", line 167, in _render
    return self.nodelist.render(context)
           ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\template\base.py", line 1005, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\template\base.py", line 966, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\template\loader_tags.py", line 157, in render
    return compiled_parent._render(context)
           ~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\template\base.py", line 167, in _render
    return self.nodelist.render(context)
           ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\template\base.py", line 1005, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\template\base.py", line 966, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\template\loader_tags.py", line 157, in render
    return compiled_parent._render(context)
           ~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\template\base.py", line 167, in _render
    return self.nodelist.render(context)
           ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\template\base.py", line 1005, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\template\base.py", line 966, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\template\loader_tags.py", line 63, in render
    result = block.nodelist.render(context)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\template\base.py", line 1005, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\template\base.py", line 966, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\template\loader_tags.py", line 63, in render
    result = block.nodelist.render(context)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\template\base.py", line 1005, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\template\base.py", line 966, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\contrib\admin\templatetags\base.py", line 45, in render
    return super().render(context)
           ~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\template\library.py", line 258, in render
    _dict = self.func(*resolved_args, **resolved_kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\contrib\admin\templatetags\admin_list.py", line 336, in result_list
    "results": list(results(cl)),
               ~~~~^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\contrib\admin\templatetags\admin_list.py", line 312, in results
    yield ResultList(None, items_for_result(cl, res, None))
          ~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\contrib\admin\templatetags\admin_list.py", line 303, in __init__
    super().__init__(*items)
    ~~~~~~~~~~~~~~~~^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\contrib\admin\templatetags\admin_list.py", line 213, in items_for_result
    f, attr, value = lookup_field(field_name, result, cl.model_admin)
                     ~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\contrib\admin\utils.py", line 281, in lookup_field
    value = attr(obj)
  File "C:\GPT4_PROJECTS\DENTAL_LAB_ARCHITECTURE_PATTERNS\dentflow\backend\apps\billing\admin.py", line 237, in service_count
    count = obj.serviceprices.count()
            ^^^^^^^^^^^^^^^^^
AttributeError: 'PriceList' object has no attribute 'serviceprices'. Did you mean: 'service_prices'?
ERROR 2025-07-26 01:51:56,485 basehttp 17404 16432 "GET /admin/billing/pricelist/ HTTP/1.1" 500 412193
INFO 2025-07-26 01:52:00,111 basehttp 17404 16432 "GET /admin/inventory/item/ HTTP/1.1" 200 21633
INFO 2025-07-26 01:52:04,906 autoreload 17404 13632 C:\GPT4_PROJECTS\DENTAL_LAB_ARCHITECTURE_PATTERNS\dentflow\backend\apps\inventory\models.py changed, reloading.
INFO 2025-07-26 01:52:06,166 autoreload 16436 26592 Watching for file changes with StatReloader
INFO 2025-07-26 01:52:12,092 basehttp 16436 37468 "GET /admin/billing/invoiceitem/ HTTP/1.1" 200 18459
INFO 2025-07-26 01:52:12,124 basehttp 16436 37468 "GET /admin/jsi18n/ HTTP/1.1" 200 3343
INFO 2025-07-26 01:52:14,725 basehttp 16436 37468 "GET /admin/billing/invoice/ HTTP/1.1" 200 19303
INFO 2025-07-26 01:52:14,752 basehttp 16436 37468 "GET /admin/jsi18n/ HTTP/1.1" 200 3343
INFO 2025-07-26 01:52:20,057 basehttp 16436 37468 "GET /admin/billing/invoiceitem/ HTTP/1.1" 200 18459
INFO 2025-07-26 01:52:20,126 basehttp 16436 37468 "GET /admin/jsi18n/ HTTP/1.1" 200 3343
INFO 2025-07-26 01:52:20,856 autoreload 16436 26592 C:\GPT4_PROJECTS\DENTAL_LAB_ARCHITECTURE_PATTERNS\dentflow\backend\apps\inventory\models.py changed, reloading.
INFO 2025-07-26 01:52:22,103 autoreload 29984 29536 Watching for file changes with StatReloader
INFO 2025-07-26 01:52:22,793 basehttp 29984 2812 "GET /admin/billing/invoiceitem/ HTTP/1.1" 200 18459
INFO 2025-07-26 01:52:22,831 basehttp 29984 2812 "GET /admin/jsi18n/ HTTP/1.1" 200 3343
INFO 2025-07-26 01:52:30,004 basehttp 29984 2812 "GET /admin/inventory/item/ HTTP/1.1" 200 21633
INFO 2025-07-26 01:52:30,068 basehttp 29984 2812 "GET /admin/jsi18n/ HTTP/1.1" 200 3343
INFO 2025-07-26 01:52:32,128 basehttp 29984 2812 "GET /admin/inventory/item/84b5d132-6ccd-4040-83c4-69f8f0f73cb0/change/ HTTP/1.1" 200 41315
INFO 2025-07-26 01:52:32,185 basehttp 29984 2812 "GET /static/admin/css/forms.css HTTP/1.1" 304 0
INFO 2025-07-26 01:52:32,196 basehttp 29984 18996 "GET /admin/jsi18n/ HTTP/1.1" 200 3343
INFO 2025-07-26 01:52:32,203 basehttp 29984 18996 "GET /static/admin/css/widgets.css HTTP/1.1" 304 0
INFO 2025-07-26 01:52:32,208 basehttp 29984 18996 "GET /static/admin/js/collapse.js HTTP/1.1" 304 0
INFO 2025-07-26 01:52:32,213 basehttp 29984 18996 "GET /static/admin/img/icon-viewlink.svg HTTP/1.1" 304 0
INFO 2025-07-26 01:52:32,218 basehttp 29984 18996 "GET /static/admin/js/prepopulate_init.js HTTP/1.1" 304 0
INFO 2025-07-26 01:52:32,427 basehttp 29984 2812 "GET /static/admin/js/inlines.js HTTP/1.1" 200 15526
INFO 2025-07-26 01:52:32,434 basehttp 29984 2812 "GET /static/admin/js/change_form.js HTTP/1.1" 304 0
INFO 2025-07-26 01:52:32,440 basehttp 29984 18996 "GET /static/admin/img/icon-unknown.svg HTTP/1.1" 200 655
INFO 2025-07-26 01:52:42,777 autoreload 29984 29536 C:\GPT4_PROJECTS\DENTAL_LAB_ARCHITECTURE_PATTERNS\dentflow\backend\apps\inventory\models.py changed, reloading.
INFO 2025-07-26 01:52:44,025 autoreload 20492 14752 Watching for file changes with StatReloader
INFO 2025-07-26 01:52:57,434 basehttp 20492 26740 "GET /admin/inventory/itemmaterialcomposition/ HTTP/1.1" 200 20159
INFO 2025-07-26 01:52:57,489 basehttp 20492 26740 "GET /admin/jsi18n/ HTTP/1.1" 200 3343
INFO 2025-07-26 01:53:08,245 basehttp 20492 26740 "GET /admin/inventory/itemmaterialcomposition/ HTTP/1.1" 200 20159
INFO 2025-07-26 01:53:08,300 basehttp 20492 26740 "GET /admin/jsi18n/ HTTP/1.1" 200 3343
INFO 2025-07-26 01:53:14,708 basehttp 20492 26740 "GET /admin/inventory/itemmaterialcomposition/6efcd170-3f62-449c-90b6-54d3d5d2ebc5/change/ HTTP/1.1" 200 26162
INFO 2025-07-26 01:53:14,775 basehttp 20492 26740 "GET /admin/jsi18n/ HTTP/1.1" 200 3343
INFO 2025-07-26 01:53:44,940 basehttp 20492 26740 "GET /admin/billing/serviceprice/ HTTP/1.1" 200 19076
INFO 2025-07-26 01:53:45,002 basehttp 20492 26740 "GET /admin/jsi18n/ HTTP/1.1" 200 3343
ERROR 2025-07-26 01:53:48,194 log 20492 26740 Internal Server Error: /admin/billing/pricelist/
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\db\models\options.py", line 681, in get_field
    return self.fields_map[field_name]
           ~~~~~~~~~~~~~~~^^^^^^^^^^^^
KeyError: 'service_count'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\contrib\admin\utils.py", line 272, in lookup_field
    f = _get_non_gfk_field(opts, name)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\contrib\admin\utils.py", line 303, in _get_non_gfk_field
    field = opts.get_field(name)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\db\models\options.py", line 683, in get_field
    raise FieldDoesNotExist(
        "%s has no field named '%s'" % (self.object_name, field_name)
    )
django.core.exceptions.FieldDoesNotExist: PriceList has no field named 'service_count'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\core\handlers\base.py", line 220, in _get_response
    response = response.render()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\template\response.py", line 114, in render
    self.content = self.rendered_content
                   ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\template\response.py", line 92, in rendered_content
    return template.render(context, self._request)
           ~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\template\backends\django.py", line 61, in render
    return self.template.render(context)
           ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\template\base.py", line 175, in render
    return self._render(context)
           ~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\template\base.py", line 167, in _render
    return self.nodelist.render(context)
           ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\template\base.py", line 1005, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\template\base.py", line 966, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\template\loader_tags.py", line 157, in render
    return compiled_parent._render(context)
           ~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\template\base.py", line 167, in _render
    return self.nodelist.render(context)
           ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\template\base.py", line 1005, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\template\base.py", line 966, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\template\loader_tags.py", line 157, in render
    return compiled_parent._render(context)
           ~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\template\base.py", line 167, in _render
    return self.nodelist.render(context)
           ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\template\base.py", line 1005, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\template\base.py", line 966, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\template\loader_tags.py", line 63, in render
    result = block.nodelist.render(context)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\template\base.py", line 1005, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\template\base.py", line 966, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\template\loader_tags.py", line 63, in render
    result = block.nodelist.render(context)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\template\base.py", line 1005, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\template\base.py", line 966, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\contrib\admin\templatetags\base.py", line 45, in render
    return super().render(context)
           ~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\template\library.py", line 258, in render
    _dict = self.func(*resolved_args, **resolved_kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\contrib\admin\templatetags\admin_list.py", line 336, in result_list
    "results": list(results(cl)),
               ~~~~^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\contrib\admin\templatetags\admin_list.py", line 312, in results
    yield ResultList(None, items_for_result(cl, res, None))
          ~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\contrib\admin\templatetags\admin_list.py", line 303, in __init__
    super().__init__(*items)
    ~~~~~~~~~~~~~~~~^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\contrib\admin\templatetags\admin_list.py", line 213, in items_for_result
    f, attr, value = lookup_field(field_name, result, cl.model_admin)
                     ~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\contrib\admin\utils.py", line 281, in lookup_field
    value = attr(obj)
  File "C:\GPT4_PROJECTS\DENTAL_LAB_ARCHITECTURE_PATTERNS\dentflow\backend\apps\billing\admin.py", line 237, in service_count
    count = obj.serviceprices.count()
            ^^^^^^^^^^^^^^^^^
AttributeError: 'PriceList' object has no attribute 'serviceprices'. Did you mean: 'service_prices'?
ERROR 2025-07-26 01:53:48,219 basehttp 20492 26740 "GET /admin/billing/pricelist/ HTTP/1.1" 500 412199
INFO 2025-07-26 01:54:09,717 autoreload 36140 22816 Watching for file changes with StatReloader
ERROR 2025-07-26 01:54:17,104 log 20492 26740 Internal Server Error: /admin/billing/pricelist/
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\db\models\options.py", line 681, in get_field
    return self.fields_map[field_name]
           ~~~~~~~~~~~~~~~^^^^^^^^^^^^
KeyError: 'service_count'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\contrib\admin\utils.py", line 272, in lookup_field
    f = _get_non_gfk_field(opts, name)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\contrib\admin\utils.py", line 303, in _get_non_gfk_field
    field = opts.get_field(name)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\db\models\options.py", line 683, in get_field
    raise FieldDoesNotExist(
        "%s has no field named '%s'" % (self.object_name, field_name)
    )
django.core.exceptions.FieldDoesNotExist: PriceList has no field named 'service_count'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\core\handlers\base.py", line 220, in _get_response
    response = response.render()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\template\response.py", line 114, in render
    self.content = self.rendered_content
                   ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\template\response.py", line 92, in rendered_content
    return template.render(context, self._request)
           ~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\template\backends\django.py", line 61, in render
    return self.template.render(context)
           ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\template\base.py", line 175, in render
    return self._render(context)
           ~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\template\base.py", line 167, in _render
    return self.nodelist.render(context)
           ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\template\base.py", line 1005, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\template\base.py", line 966, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\template\loader_tags.py", line 157, in render
    return compiled_parent._render(context)
           ~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\template\base.py", line 167, in _render
    return self.nodelist.render(context)
           ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\template\base.py", line 1005, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\template\base.py", line 966, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\template\loader_tags.py", line 157, in render
    return compiled_parent._render(context)
           ~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\template\base.py", line 167, in _render
    return self.nodelist.render(context)
           ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\template\base.py", line 1005, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\template\base.py", line 966, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\template\loader_tags.py", line 63, in render
    result = block.nodelist.render(context)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\template\base.py", line 1005, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\template\base.py", line 966, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\template\loader_tags.py", line 63, in render
    result = block.nodelist.render(context)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\template\base.py", line 1005, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\template\base.py", line 966, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\contrib\admin\templatetags\base.py", line 45, in render
    return super().render(context)
           ~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\template\library.py", line 258, in render
    _dict = self.func(*resolved_args, **resolved_kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\contrib\admin\templatetags\admin_list.py", line 336, in result_list
    "results": list(results(cl)),
               ~~~~^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\contrib\admin\templatetags\admin_list.py", line 312, in results
    yield ResultList(None, items_for_result(cl, res, None))
          ~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\contrib\admin\templatetags\admin_list.py", line 303, in __init__
    super().__init__(*items)
    ~~~~~~~~~~~~~~~~^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\contrib\admin\templatetags\admin_list.py", line 213, in items_for_result
    f, attr, value = lookup_field(field_name, result, cl.model_admin)
                     ~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\contrib\admin\utils.py", line 281, in lookup_field
    value = attr(obj)
  File "C:\GPT4_PROJECTS\DENTAL_LAB_ARCHITECTURE_PATTERNS\dentflow\backend\apps\billing\admin.py", line 237, in service_count
    count = obj.serviceprices.count()
            ^^^^^^^^^^^^^^^^^
AttributeError: 'PriceList' object has no attribute 'serviceprices'. Did you mean: 'service_prices'?
ERROR 2025-07-26 01:54:17,110 basehttp 20492 26740 "GET /admin/billing/pricelist/ HTTP/1.1" 500 412336
ERROR 2025-07-26 01:54:18,094 log 20492 26740 Internal Server Error: /admin/billing/pricelist/
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\db\models\options.py", line 681, in get_field
    return self.fields_map[field_name]
           ~~~~~~~~~~~~~~~^^^^^^^^^^^^
KeyError: 'service_count'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\contrib\admin\utils.py", line 272, in lookup_field
    f = _get_non_gfk_field(opts, name)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\contrib\admin\utils.py", line 303, in _get_non_gfk_field
    field = opts.get_field(name)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\db\models\options.py", line 683, in get_field
    raise FieldDoesNotExist(
        "%s has no field named '%s'" % (self.object_name, field_name)
    )
django.core.exceptions.FieldDoesNotExist: PriceList has no field named 'service_count'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\core\handlers\base.py", line 220, in _get_response
    response = response.render()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\template\response.py", line 114, in render
    self.content = self.rendered_content
                   ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\template\response.py", line 92, in rendered_content
    return template.render(context, self._request)
           ~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\template\backends\django.py", line 61, in render
    return self.template.render(context)
           ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\template\base.py", line 175, in render
    return self._render(context)
           ~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\template\base.py", line 167, in _render
    return self.nodelist.render(context)
           ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\template\base.py", line 1005, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\template\base.py", line 966, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\template\loader_tags.py", line 157, in render
    return compiled_parent._render(context)
           ~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\template\base.py", line 167, in _render
    return self.nodelist.render(context)
           ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\template\base.py", line 1005, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\template\base.py", line 966, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\template\loader_tags.py", line 157, in render
    return compiled_parent._render(context)
           ~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\template\base.py", line 167, in _render
    return self.nodelist.render(context)
           ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\template\base.py", line 1005, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\template\base.py", line 966, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\template\loader_tags.py", line 63, in render
    result = block.nodelist.render(context)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\template\base.py", line 1005, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\template\base.py", line 966, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\template\loader_tags.py", line 63, in render
    result = block.nodelist.render(context)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\template\base.py", line 1005, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\template\base.py", line 966, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\contrib\admin\templatetags\base.py", line 45, in render
    return super().render(context)
           ~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\template\library.py", line 258, in render
    _dict = self.func(*resolved_args, **resolved_kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\contrib\admin\templatetags\admin_list.py", line 336, in result_list
    "results": list(results(cl)),
               ~~~~^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\contrib\admin\templatetags\admin_list.py", line 312, in results
    yield ResultList(None, items_for_result(cl, res, None))
          ~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\contrib\admin\templatetags\admin_list.py", line 303, in __init__
    super().__init__(*items)
    ~~~~~~~~~~~~~~~~^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\contrib\admin\templatetags\admin_list.py", line 213, in items_for_result
    f, attr, value = lookup_field(field_name, result, cl.model_admin)
                     ~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\contrib\admin\utils.py", line 281, in lookup_field
    value = attr(obj)
  File "C:\GPT4_PROJECTS\DENTAL_LAB_ARCHITECTURE_PATTERNS\dentflow\backend\apps\billing\admin.py", line 237, in service_count
    count = obj.serviceprices.count()
            ^^^^^^^^^^^^^^^^^
AttributeError: 'PriceList' object has no attribute 'serviceprices'. Did you mean: 'service_prices'?
ERROR 2025-07-26 01:54:18,100 basehttp 20492 26740 "GET /admin/billing/pricelist/ HTTP/1.1" 500 412336
INFO 2025-07-26 01:54:39,944 basehttp 20492 26740 "GET /admin/billing/serviceprice/ HTTP/1.1" 200 19076
INFO 2025-07-26 01:54:50,914 autoreload 20492 14752 C:\GPT4_PROJECTS\DENTAL_LAB_ARCHITECTURE_PATTERNS\dentflow\backend\apps\billing\admin.py changed, reloading.
INFO 2025-07-26 01:54:51,300 autoreload 36140 22816 C:\GPT4_PROJECTS\DENTAL_LAB_ARCHITECTURE_PATTERNS\dentflow\backend\apps\billing\admin.py changed, reloading.
INFO 2025-07-26 01:54:52,074 autoreload 26044 24112 Watching for file changes with StatReloader
INFO 2025-07-26 01:54:52,334 autoreload 14696 9404 Watching for file changes with StatReloader
INFO 2025-07-26 01:54:56,936 basehttp 26044 3024 "GET /admin/inventory/materialcategory/ HTTP/1.1" 200 20706
INFO 2025-07-26 01:54:56,978 basehttp 26044 3024 "GET /admin/jsi18n/ HTTP/1.1" 200 3343
INFO 2025-07-26 01:55:07,309 basehttp 26044 3024 "GET /admin/tenants/tenantsettings/ HTTP/1.1" 200 19042
INFO 2025-07-26 01:55:07,374 basehttp 26044 3024 "GET /admin/jsi18n/ HTTP/1.1" 200 3343
INFO 2025-07-26 01:55:08,895 basehttp 26044 3024 "GET /admin/tenants/tenantsettings/add/ HTTP/1.1" 200 26787
INFO 2025-07-26 01:55:08,971 basehttp 26044 3024 "GET /admin/jsi18n/ HTTP/1.1" 200 3343
INFO 2025-07-26 01:55:21,820 autoreload 14696 9404 C:\GPT4_PROJECTS\DENTAL_LAB_ARCHITECTURE_PATTERNS\dentflow\backend\apps\billing\admin.py changed, reloading.
INFO 2025-07-26 01:55:21,839 autoreload 26044 24112 C:\GPT4_PROJECTS\DENTAL_LAB_ARCHITECTURE_PATTERNS\dentflow\backend\apps\billing\admin.py changed, reloading.
INFO 2025-07-26 01:55:23,103 autoreload 14012 7704 Watching for file changes with StatReloader
INFO 2025-07-26 01:55:23,186 autoreload 34212 27664 Watching for file changes with StatReloader
INFO 2025-07-26 01:55:35,266 autoreload 34212 27664 C:\GPT4_PROJECTS\DENTAL_LAB_ARCHITECTURE_PATTERNS\dentflow\backend\apps\billing\admin.py changed, reloading.
INFO 2025-07-26 01:55:35,285 autoreload 14012 7704 C:\GPT4_PROJECTS\DENTAL_LAB_ARCHITECTURE_PATTERNS\dentflow\backend\apps\billing\admin.py changed, reloading.
INFO 2025-07-26 01:55:36,586 autoreload 17872 11280 Watching for file changes with StatReloader
INFO 2025-07-26 01:55:36,610 autoreload 14240 34964 Watching for file changes with StatReloader
INFO 2025-07-26 01:55:47,239 autoreload 5948 36844 Watching for file changes with StatReloader
INFO 2025-07-26 01:55:58,301 basehttp 5948 30680 "GET /admin/ HTTP/1.1" 200 19362
INFO 2025-07-26 01:55:58,501 basehttp 5948 30680 "GET /static/admin/css/base.css HTTP/1.1" 200 21207
INFO 2025-07-26 01:55:58,504 basehttp 5948 30680 "GET /static/admin/css/nav_sidebar.css HTTP/1.1" 200 2694
INFO 2025-07-26 01:55:58,507 basehttp 5948 30680 "GET /static/admin/css/dashboard.css HTTP/1.1" 200 441
INFO 2025-07-26 01:55:58,510 basehttp 5948 30680 "GET /static/admin/css/responsive.css HTTP/1.1" 200 18533
INFO 2025-07-26 01:55:58,514 basehttp 5948 30680 "GET /static/admin/js/theme.js HTTP/1.1" 200 1943
INFO 2025-07-26 01:55:58,517 basehttp 5948 30680 "GET /static/admin/js/nav_sidebar.js HTTP/1.1" 200 3063
INFO 2025-07-26 01:55:58,542 basehttp 5948 31512 "GET /static/admin/css/dark_mode.css HTTP/1.1" 200 2929
INFO 2025-07-26 01:55:58,555 basehttp 5948 30680 "GET /static/admin/img/icon-changelink.svg HTTP/1.1" 200 380
INFO 2025-07-26 01:55:58,555 basehttp 5948 31512 "GET /static/admin/img/icon-addlink.svg HTTP/1.1" 200 331
INFO 2025-07-26 01:55:58,561 basehttp 5948 31512 "GET /static/admin/img/icon-viewlink.svg HTTP/1.1" 200 581
WARNING 2025-07-26 01:55:58,940 log 5948 31512 Not Found: /favicon.ico
WARNING 2025-07-26 01:55:58,941 basehttp 5948 31512 "GET /favicon.ico HTTP/1.1" 404 2965
INFO 2025-07-26 01:59:52,474 autoreload 22944 21708 Watching for file changes with StatReloader
INFO 2025-07-26 02:08:12,884 basehttp 22944 16320 "GET /admin HTTP/1.1" 301 0
INFO 2025-07-26 02:08:13,218 basehttp 22944 23184 "GET /admin/ HTTP/1.1" 200 19362
INFO 2025-07-26 02:18:54,629 basehttp 22944 17296 "GET /admin/ HTTP/1.1" 200 19362
INFO 2025-07-26 02:24:08,415 basehttp 22944 33316 "GET /admin/billing/itemprice/ HTTP/1.1" 200 20034
INFO 2025-07-26 02:24:08,505 basehttp 22944 33316 "GET /static/admin/css/changelists.css HTTP/1.1" 200 6566
INFO 2025-07-26 02:24:08,515 basehttp 22944 33316 "GET /admin/jsi18n/ HTTP/1.1" 200 3343
INFO 2025-07-26 02:24:08,520 basehttp 22944 33316 "GET /static/admin/js/vendor/jquery/jquery.js HTTP/1.1" 200 292458
INFO 2025-07-26 02:24:08,524 basehttp 22944 33316 "GET /static/admin/js/jquery.init.js HTTP/1.1" 200 347
INFO 2025-07-26 02:24:08,537 basehttp 22944 33316 "GET /static/admin/js/core.js HTTP/1.1" 200 5682
INFO 2025-07-26 02:24:08,545 basehttp 22944 33316 "GET /static/admin/js/admin/RelatedObjectLookups.js HTTP/1.1" 200 8943
INFO 2025-07-26 02:24:08,551 basehttp 22944 33316 "GET /static/admin/js/actions.js HTTP/1.1" 200 7872
INFO 2025-07-26 02:24:08,556 basehttp 22944 33316 "GET /static/admin/js/urlify.js HTTP/1.1" 200 7887
INFO 2025-07-26 02:24:08,561 basehttp 22944 33316 "GET /static/admin/js/prepopulate.js HTTP/1.1" 200 1531
INFO 2025-07-26 02:24:08,568 basehttp 22944 33316 "GET /static/admin/js/vendor/xregexp/xregexp.js HTTP/1.1" 200 232381
INFO 2025-07-26 02:24:08,571 basehttp 22944 33316 "GET /static/admin/img/search.svg HTTP/1.1" 200 458
INFO 2025-07-26 02:24:08,575 basehttp 22944 33316 "GET /static/admin/img/icon-yes.svg HTTP/1.1" 200 436
INFO 2025-07-26 02:24:08,585 basehttp 22944 33316 "GET /static/admin/js/filters.js HTTP/1.1" 200 978
INFO 2025-07-26 02:24:08,593 basehttp 22944 33316 "GET /static/admin/img/tooltag-add.svg HTTP/1.1" 200 331
INFO 2025-07-26 02:24:20,607 basehttp 22944 33316 "GET /admin/inventory/item/ HTTP/1.1" 200 21633
INFO 2025-07-26 02:24:20,657 basehttp 22944 33316 "GET /admin/jsi18n/ HTTP/1.1" 200 3343
INFO 2025-07-26 02:24:20,681 basehttp 22944 33316 "GET /static/admin/img/sorting-icons.svg HTTP/1.1" 200 1097
INFO 2025-07-26 02:24:23,903 basehttp 22944 33316 "GET /admin/billing/serviceprice/ HTTP/1.1" 200 19076
INFO 2025-07-26 02:24:23,931 basehttp 22944 33316 "GET /admin/jsi18n/ HTTP/1.1" 200 3343
INFO 2025-07-26 02:24:24,940 basehttp 22944 33316 "GET /admin/billing/pricelist/ HTTP/1.1" 200 21543
INFO 2025-07-26 02:24:24,966 basehttp 22944 33316 "GET /admin/jsi18n/ HTTP/1.1" 200 3343
INFO 2025-07-26 02:24:27,922 basehttp 22944 33316 "GET /admin/billing/pricelist/f617cf64-f489-46ce-abf0-6cc1cba8f872/change/ HTTP/1.1" 200 38786
INFO 2025-07-26 02:24:27,951 basehttp 22944 9536 "GET /static/admin/js/collapse.js HTTP/1.1" 200 1803
INFO 2025-07-26 02:24:27,951 basehttp 22944 33332 "GET /static/admin/js/calendar.js HTTP/1.1" 200 8466
INFO 2025-07-26 02:24:27,952 basehttp 22944 17340 "GET /static/admin/js/admin/DateTimeShortcuts.js HTTP/1.1" 200 19319
INFO 2025-07-26 02:24:27,953 basehttp 22944 33316 "GET /static/admin/css/forms.css HTTP/1.1" 200 9047
INFO 2025-07-26 02:24:27,955 basehttp 22944 29076 "GET /static/admin/js/inlines.js HTTP/1.1" 200 15526
INFO 2025-07-26 02:24:27,965 basehttp 22944 25604 "GET /admin/jsi18n/ HTTP/1.1" 200 3343
INFO 2025-07-26 02:24:27,966 basehttp 22944 29076 "GET /static/admin/js/prepopulate_init.js HTTP/1.1" 200 586
INFO 2025-07-26 02:24:27,968 basehttp 22944 17340 "GET /static/admin/css/widgets.css HTTP/1.1" 200 11900
INFO 2025-07-26 02:24:27,972 basehttp 22944 17340 "GET /static/admin/js/change_form.js HTTP/1.1" 200 606
INFO 2025-07-26 02:24:28,011 basehttp 22944 17340 "GET /static/admin/img/inline-delete.svg HTTP/1.1" 200 560
INFO 2025-07-26 02:24:28,069 basehttp 22944 17340 "GET /static/admin/img/icon-calendar.svg HTTP/1.1" 200 1086
INFO 2025-07-26 02:24:41,159 basehttp 22944 17340 "GET /admin/inventory/material/ HTTP/1.1" 200 22380
INFO 2025-07-26 02:24:41,195 basehttp 22944 17340 "GET /admin/jsi18n/ HTTP/1.1" 200 3343
INFO 2025-07-26 02:24:43,101 basehttp 22944 17340 "GET /admin/inventory/material/9741e5f6-ce0e-447c-9ab9-8e8f14a840e2/change/ HTTP/1.1" 200 32478
INFO 2025-07-26 02:24:43,131 basehttp 22944 17340 "GET /admin/jsi18n/ HTTP/1.1" 200 3343
INFO 2025-07-26 02:26:47,463 basehttp 17872 3224 "OPTIONS /api/v1/auth/login/ HTTP/1.1" 200 0
WARNING 2025-07-26 02:26:47,502 log 17872 3224 Not Found: /api/v1/auth/login/
WARNING 2025-07-26 02:26:47,503 basehttp 17872 3224 "POST /api/v1/auth/login/ HTTP/1.1" 404 3040
WARNING 2025-07-26 02:26:58,844 log 17872 3224 Not Found: /api/v1/auth/login/
WARNING 2025-07-26 02:26:58,846 basehttp 17872 3224 "POST /api/v1/auth/login/ HTTP/1.1" 404 3040
WARNING 2025-07-26 02:27:21,861 log 17872 3224 Not Found: /api/v1/auth/login/
WARNING 2025-07-26 02:27:21,862 basehttp 17872 3224 "POST /api/v1/auth/login/ HTTP/1.1" 404 3040
WARNING 2025-07-26 02:28:23,353 log 17872 3224 Not Found: /api/v1/auth/login/
WARNING 2025-07-26 02:28:23,354 basehttp 17872 3224 "POST /api/v1/auth/login/ HTTP/1.1" 404 3040
WARNING 2025-07-26 02:28:31,460 log 17872 3224 Not Found: /api/v1/auth/login/
WARNING 2025-07-26 02:28:31,461 basehttp 17872 3224 "POST /api/v1/auth/login/ HTTP/1.1" 404 3040
WARNING 2025-07-26 02:29:10,899 log 22944 2528 Not Found: /api/v1/health/
WARNING 2025-07-26 02:29:10,900 basehttp 22944 2528 "GET /api/v1/health/ HTTP/1.1" 404 3027
WARNING 2025-07-26 02:29:33,999 connection 22944 8276 No hostname was supplied. Reverting to default 'localhost'
ERROR 2025-07-26 02:29:40,072 log 22944 8276 Service Unavailable: /health/
ERROR 2025-07-26 02:29:40,073 basehttp 22944 8276 "GET /health/ HTTP/1.1" 503 433
ERROR 2025-07-26 02:29:50,286 log 22944 24432 Service Unavailable: /health/
ERROR 2025-07-26 02:29:50,287 basehttp 22944 24432 "GET /health/ HTTP/1.1" 503 434
ERROR 2025-07-26 02:30:01,069 log 22944 2528 Service Unavailable: /health/
ERROR 2025-07-26 02:30:01,071 basehttp 22944 2528 "GET /health/ HTTP/1.1" 503 434
WARNING 2025-07-26 02:30:19,229 log 14240 18572 Not Found: /api/v1/auth/login/
WARNING 2025-07-26 02:30:19,231 basehttp 14240 18572 "POST /api/v1/auth/login/ HTTP/1.1" 404 3040
INFO 2025-07-26 02:30:51,429 autoreload 35304 32460 Watching for file changes with StatReloader
WARNING 2025-07-26 02:31:02,855 log 14240 18572 Not Found: /api/v1/auth/login/
WARNING 2025-07-26 02:31:02,856 basehttp 14240 18572 "POST /api/v1/auth/login/ HTTP/1.1" 404 3040
WARNING 2025-07-26 02:31:12,374 log 14240 18572 Not Found: /api/v1/auth/login/
WARNING 2025-07-26 02:31:12,375 basehttp 14240 18572 "POST /api/v1/auth/login/ HTTP/1.1" 404 3040
WARNING 2025-07-26 02:31:29,205 log 14240 18572 Not Found: /api/v1/auth/login/
WARNING 2025-07-26 02:31:29,206 basehttp 14240 18572 "POST /api/v1/auth/login/ HTTP/1.1" 404 3040
INFO 2025-07-26 02:31:52,671 basehttp 22944 24432 "POST /admin/logout/ HTTP/1.1" 200 3575
INFO 2025-07-26 02:31:55,282 basehttp 22944 24432 "GET /admin/ HTTP/1.1" 302 0
INFO 2025-07-26 02:31:55,294 basehttp 22944 24432 "GET /admin/login/?next=/admin/ HTTP/1.1" 200 4177
INFO 2025-07-26 02:31:55,316 basehttp 22944 24432 "GET /static/admin/css/login.css HTTP/1.1" 200 958
INFO 2025-07-26 02:32:05,990 basehttp 22944 24432 "POST /admin/login/?next=/admin/ HTTP/1.1" 200 4335
INFO 2025-07-26 02:33:27,522 basehttp 22944 24432 "POST /admin/login/?next=/admin/ HTTP/1.1" 200 4335
INFO 2025-07-26 02:33:31,250 basehttp 22944 24432 "POST /admin/login/?next=/admin/ HTTP/1.1" 200 4335
INFO 2025-07-26 02:33:44,807 basehttp 14240 18404 "GET /admin/ HTTP/1.1" 302 0
INFO 2025-07-26 02:33:44,858 basehttp 14240 18404 "GET /admin/login/?next=/admin/ HTTP/1.1" 200 4177
INFO 2025-07-26 02:33:51,937 basehttp 14240 18404 "POST /admin/login/?next=/admin/ HTTP/1.1" 200 4335
INFO 2025-07-26 02:34:03,818 basehttp 14240 18404 "POST /admin/login/?next=/admin/ HTTP/1.1" 302 0
INFO 2025-07-26 02:34:03,897 basehttp 14240 18404 "GET /admin/ HTTP/1.1" 200 19362
INFO 2025-07-26 02:34:04,105 basehttp 14240 18404 "GET /static/admin/img/icon-viewlink.svg HTTP/1.1" 200 581
INFO 2025-07-26 02:34:06,342 autoreload 35304 32460 C:\GPT4_PROJECTS\DENTAL_LAB_ARCHITECTURE_PATTERNS\dentflow\backend\dentflow_project\urls.py changed, reloading.
INFO 2025-07-26 02:34:06,873 autoreload 14240 34964 C:\GPT4_PROJECTS\DENTAL_LAB_ARCHITECTURE_PATTERNS\dentflow\backend\dentflow_project\urls.py changed, reloading.
INFO 2025-07-26 02:34:07,034 autoreload 22944 21708 C:\GPT4_PROJECTS\DENTAL_LAB_ARCHITECTURE_PATTERNS\dentflow\backend\dentflow_project\urls.py changed, reloading.
INFO 2025-07-26 02:34:07,781 autoreload 26928 37060 Watching for file changes with StatReloader
INFO 2025-07-26 02:34:08,296 autoreload 9676 21524 Watching for file changes with StatReloader
INFO 2025-07-26 02:34:08,506 autoreload 26600 21256 Watching for file changes with StatReloader
INFO 2025-07-26 02:34:29,687 basehttp 9676 31724 "POST /api/v1/auth/login/ HTTP/1.1" 200 796
INFO 2025-07-26 02:34:29,813 basehttp 9676 31724 "OPTIONS /api/v1/health/ HTTP/1.1" 200 0
INFO 2025-07-26 02:34:29,813 basehttp 9676 24824 "OPTIONS /api/v1/health/ HTTP/1.1" 200 0
WARNING 2025-07-26 02:34:29,839 log 9676 31724 Not Found: /api/v1/health/
WARNING 2025-07-26 02:34:29,840 basehttp 9676 31724 "GET /api/v1/health/ HTTP/1.1" 404 3186
WARNING 2025-07-26 02:34:29,862 log 9676 24824 Not Found: /api/v1/health/
WARNING 2025-07-26 02:34:29,863 basehttp 9676 24824 "GET /api/v1/health/ HTTP/1.1" 404 3186
INFO 2025-07-26 02:34:40,814 autoreload 17212 17060 Watching for file changes with StatReloader
INFO 2025-07-26 02:34:44,772 basehttp 9676 24824 "OPTIONS /api/v1/workflow/tasks/? HTTP/1.1" 200 0
INFO 2025-07-26 02:34:44,773 basehttp 9676 31724 "OPTIONS /api/v1/workflow/technicians/ HTTP/1.1" 200 0
WARNING 2025-07-26 02:34:44,805 log 9676 24824 Not Found: /api/v1/workflow/tasks/
WARNING 2025-07-26 02:34:44,809 log 9676 31724 Not Found: /api/v1/workflow/technicians/
WARNING 2025-07-26 02:34:44,812 basehttp 9676 24824 "GET /api/v1/workflow/tasks/? HTTP/1.1" 404 3210
WARNING 2025-07-26 02:34:44,814 basehttp 9676 31724 "GET /api/v1/workflow/technicians/ HTTP/1.1" 404 3228
WARNING 2025-07-26 02:34:56,943 log 26928 3500 Method Not Allowed: /api/v1/auth/login/
WARNING 2025-07-26 02:34:56,945 basehttp 26928 3500 "GET /api/v1/auth/login/ HTTP/1.1" 405 40
INFO 2025-07-26 02:35:17,387 basehttp 9676 31724 "OPTIONS /api/v1/billing/invoices/? HTTP/1.1" 200 0
WARNING 2025-07-26 02:35:17,406 log 9676 31724 Not Found: /api/v1/billing/invoices/
WARNING 2025-07-26 02:35:17,408 basehttp 9676 31724 "GET /api/v1/billing/invoices/? HTTP/1.1" 404 3216
INFO 2025-07-26 03:28:30,538 autoreload 35604 13704 Watching for file changes with StatReloader
INFO 2025-07-26 03:28:41,596 basehttp 35604 36140 "OPTIONS /api/v1/auth/login/ HTTP/1.1" 200 0
INFO 2025-07-26 03:28:42,186 basehttp 35604 36140 "POST /api/v1/auth/login/ HTTP/1.1" 200 796
INFO 2025-07-26 03:28:42,315 basehttp 35604 36140 "OPTIONS /api/v1/health/ HTTP/1.1" 200 0
INFO 2025-07-26 03:28:42,315 basehttp 35604 1660 "OPTIONS /api/v1/health/ HTTP/1.1" 200 0
WARNING 2025-07-26 03:28:42,346 log 35604 1660 Not Found: /api/v1/health/
WARNING 2025-07-26 03:28:42,348 basehttp 35604 1660 "GET /api/v1/health/ HTTP/1.1" 404 3186
WARNING 2025-07-26 03:28:42,370 log 35604 36140 Not Found: /api/v1/health/
WARNING 2025-07-26 03:28:42,372 basehttp 35604 36140 "GET /api/v1/health/ HTTP/1.1" 404 3186
INFO 2025-07-26 03:30:21,081 basehttp 35604 1660 "OPTIONS /api/v1/workflow/technicians/ HTTP/1.1" 200 0
INFO 2025-07-26 03:30:21,081 basehttp 35604 36140 "OPTIONS /api/v1/workflow/tasks/? HTTP/1.1" 200 0
WARNING 2025-07-26 03:30:21,117 log 35604 36140 Not Found: /api/v1/workflow/technicians/
WARNING 2025-07-26 03:30:21,120 log 35604 1660 Not Found: /api/v1/workflow/tasks/
WARNING 2025-07-26 03:30:21,122 basehttp 35604 36140 "GET /api/v1/workflow/technicians/ HTTP/1.1" 404 3228
WARNING 2025-07-26 03:30:21,123 basehttp 35604 1660 "GET /api/v1/workflow/tasks/? HTTP/1.1" 404 3210
INFO 2025-07-26 03:30:25,013 basehttp 35604 1660 "OPTIONS /api/v1/billing/invoices/? HTTP/1.1" 200 0
WARNING 2025-07-26 03:30:25,028 log 35604 1660 Not Found: /api/v1/billing/invoices/
WARNING 2025-07-26 03:30:25,030 basehttp 35604 1660 "GET /api/v1/billing/invoices/? HTTP/1.1" 404 3216
INFO 2025-07-26 03:39:22,041 messagebus 4524 1596 Handling message: CreateCase
ERROR 2025-07-26 03:39:22,043 messagebus 4524 1596 Error handling command CreateCase: near "SET": syntax error
INFO 2025-07-26 03:39:22,045 messagebus 4524 1596 Handling message: CreateCase
ERROR 2025-07-26 03:39:22,047 messagebus 4524 1596 Error handling command CreateCase: near "SET": syntax error
INFO 2025-07-26 03:39:22,048 messagebus 4524 1596 Handling message: CreateCase
ERROR 2025-07-26 03:39:22,049 messagebus 4524 1596 Error handling command CreateCase: near "SET": syntax error
INFO 2025-07-26 03:39:22,051 messagebus 4524 1596 Handling message: CreateCase
ERROR 2025-07-26 03:39:22,052 messagebus 4524 1596 Error handling command CreateCase: near "SET": syntax error
INFO 2025-07-26 03:39:22,053 messagebus 4524 1596 Handling message: CreateCase
ERROR 2025-07-26 03:39:22,053 messagebus 4524 1596 Error handling command CreateCase: near "SET": syntax error
INFO 2025-07-26 03:40:55,852 basehttp 35604 6208 "OPTIONS /api/v1/cases/ HTTP/1.1" 200 0
INFO 2025-07-26 03:40:55,911 basehttp 35604 6208 "GET /api/v1/cases/ HTTP/1.1" 200 53
WARNING 2025-07-26 03:41:02,211 log 35604 18168 Not Found: /api/v1/workflow/technicians/
WARNING 2025-07-26 03:41:02,213 basehttp 35604 18168 "GET /api/v1/workflow/technicians/ HTTP/1.1" 404 3228
WARNING 2025-07-26 03:41:02,219 log 35604 6208 Not Found: /api/v1/workflow/tasks/
WARNING 2025-07-26 03:41:02,221 basehttp 35604 6208 "GET /api/v1/workflow/tasks/? HTTP/1.1" 404 3210
WARNING 2025-07-26 03:41:07,700 log 35604 6208 Not Found: /api/v1/health/
WARNING 2025-07-26 03:41:07,701 basehttp 35604 6208 "GET /api/v1/health/ HTTP/1.1" 404 3186
WARNING 2025-07-26 03:41:07,724 log 35604 18168 Not Found: /api/v1/health/
WARNING 2025-07-26 03:41:07,726 basehttp 35604 18168 "GET /api/v1/health/ HTTP/1.1" 404 3186
INFO 2025-07-26 03:41:20,103 autoreload 32972 16060 Watching for file changes with StatReloader
WARNING 2025-07-26 03:42:42,836 log 35604 18168 Not Found: /api/v1/health/
WARNING 2025-07-26 03:42:42,837 basehttp 35604 18168 "GET /api/v1/health/ HTTP/1.1" 404 3186
WARNING 2025-07-26 03:42:42,853 log 35604 6208 Not Found: /api/v1/health/
WARNING 2025-07-26 03:42:42,854 basehttp 35604 6208 "GET /api/v1/health/ HTTP/1.1" 404 3186
WARNING 2025-07-26 03:42:43,003 log 35604 6208 Not Found: /api/v1/health/
WARNING 2025-07-26 03:42:43,004 basehttp 35604 6208 "GET /api/v1/health/ HTTP/1.1" 404 3186
WARNING 2025-07-26 03:42:43,018 log 35604 18168 Not Found: /api/v1/health/
WARNING 2025-07-26 03:42:43,019 basehttp 35604 18168 "GET /api/v1/health/ HTTP/1.1" 404 3186
WARNING 2025-07-26 03:42:57,804 log 35604 18168 Not Found: /api/v1/health/
WARNING 2025-07-26 03:42:57,805 basehttp 35604 18168 "GET /api/v1/health/ HTTP/1.1" 404 3186
WARNING 2025-07-26 03:42:57,820 log 35604 6208 Not Found: /api/v1/health/
WARNING 2025-07-26 03:42:57,821 basehttp 35604 6208 "GET /api/v1/health/ HTTP/1.1" 404 3186
WARNING 2025-07-26 03:42:57,834 log 35604 18168 Not Found: /api/v1/health/
WARNING 2025-07-26 03:42:57,835 basehttp 35604 18168 "GET /api/v1/health/ HTTP/1.1" 404 3186
WARNING 2025-07-26 03:42:57,849 log 35604 6208 Not Found: /api/v1/health/
WARNING 2025-07-26 03:42:57,850 basehttp 35604 6208 "GET /api/v1/health/ HTTP/1.1" 404 3186
WARNING 2025-07-26 03:43:10,849 log 35604 6208 Not Found: /api/v1/health/
WARNING 2025-07-26 03:43:10,850 basehttp 35604 6208 "GET /api/v1/health/ HTTP/1.1" 404 3186
WARNING 2025-07-26 03:43:10,869 log 35604 18168 Not Found: /api/v1/health/
WARNING 2025-07-26 03:43:10,870 basehttp 35604 18168 "GET /api/v1/health/ HTTP/1.1" 404 3186
WARNING 2025-07-26 03:43:10,885 log 35604 6208 Not Found: /api/v1/health/
WARNING 2025-07-26 03:43:10,886 basehttp 35604 6208 "GET /api/v1/health/ HTTP/1.1" 404 3186
WARNING 2025-07-26 03:43:10,900 log 35604 18168 Not Found: /api/v1/health/
WARNING 2025-07-26 03:43:10,901 basehttp 35604 18168 "GET /api/v1/health/ HTTP/1.1" 404 3186
WARNING 2025-07-26 03:45:30,808 log 35604 18168 Not Found: /api/v1/health/
WARNING 2025-07-26 03:45:30,809 basehttp 35604 18168 "GET /api/v1/health/ HTTP/1.1" 404 3186
WARNING 2025-07-26 03:45:30,822 log 35604 6208 Not Found: /api/v1/health/
WARNING 2025-07-26 03:45:30,823 basehttp 35604 6208 "GET /api/v1/health/ HTTP/1.1" 404 3186
WARNING 2025-07-26 03:45:30,837 log 35604 18168 Not Found: /api/v1/health/
WARNING 2025-07-26 03:45:30,838 basehttp 35604 18168 "GET /api/v1/health/ HTTP/1.1" 404 3186
WARNING 2025-07-26 03:45:30,853 log 35604 6208 Not Found: /api/v1/health/
WARNING 2025-07-26 03:45:30,854 basehttp 35604 6208 "GET /api/v1/health/ HTTP/1.1" 404 3186
WARNING 2025-07-26 03:50:12,209 log 32972 32432 Unauthorized: /api/v1/cases/
WARNING 2025-07-26 03:50:12,210 basehttp 32972 32432 "GET /api/v1/cases/ HTTP/1.1" 401 58
INFO 2025-07-26 03:52:01,425 signals 20488 26880 Processed Django signals for case LAB-2024-0001
INFO 2025-07-26 03:52:01,427 signals 20488 26880 Processed Django signals for case LAB-2024-0002
ERROR 2025-07-26 03:52:01,428 signals 20488 26880 Error in case signal handler: Invalid tooth number: 14-16
ERROR 2025-07-26 03:52:01,430 signals 20488 26880 Error in case signal handler: Invalid tooth number: 21-23
INFO 2025-07-26 03:52:01,431 signals 20488 26880 Processed Django signals for case LAB-2024-0005
WARNING 2025-07-26 03:52:37,180 log 35604 2516 Not Found: /api/v1/health/
WARNING 2025-07-26 03:52:37,181 basehttp 35604 2516 "GET /api/v1/health/ HTTP/1.1" 404 3186
WARNING 2025-07-26 03:52:37,196 log 35604 2516 Not Found: /api/v1/health/
WARNING 2025-07-26 03:52:37,197 basehttp 35604 2516 "GET /api/v1/health/ HTTP/1.1" 404 3186
WARNING 2025-07-26 03:52:37,219 log 35604 2516 Not Found: /api/v1/health/
WARNING 2025-07-26 03:52:37,220 basehttp 35604 2516 "GET /api/v1/health/ HTTP/1.1" 404 3186
WARNING 2025-07-26 03:52:37,234 log 35604 2516 Not Found: /api/v1/health/
WARNING 2025-07-26 03:52:37,235 basehttp 35604 2516 "GET /api/v1/health/ HTTP/1.1" 404 3186
WARNING 2025-07-26 03:53:47,811 log 35604 2516 Not Found: /api/v1/health/
WARNING 2025-07-26 03:53:47,812 basehttp 35604 2516 "GET /api/v1/health/ HTTP/1.1" 404 3186
WARNING 2025-07-26 03:53:47,828 log 35604 2516 Not Found: /api/v1/health/
WARNING 2025-07-26 03:53:47,829 basehttp 35604 2516 "GET /api/v1/health/ HTTP/1.1" 404 3186
WARNING 2025-07-26 03:53:47,845 log 35604 2516 Not Found: /api/v1/health/
WARNING 2025-07-26 03:53:47,846 basehttp 35604 2516 "GET /api/v1/health/ HTTP/1.1" 404 3186
WARNING 2025-07-26 03:53:47,862 log 35604 2516 Not Found: /api/v1/health/
WARNING 2025-07-26 03:53:47,863 basehttp 35604 2516 "GET /api/v1/health/ HTTP/1.1" 404 3186
WARNING 2025-07-26 03:54:56,795 log 35604 2516 Not Found: /api/v1/health/
WARNING 2025-07-26 03:54:56,796 basehttp 35604 2516 "GET /api/v1/health/ HTTP/1.1" 404 3186
WARNING 2025-07-26 03:54:56,816 log 35604 2516 Not Found: /api/v1/health/
WARNING 2025-07-26 03:54:56,817 basehttp 35604 2516 "GET /api/v1/health/ HTTP/1.1" 404 3186
WARNING 2025-07-26 03:56:33,831 log 35604 2516 Not Found: /api/v1/health/
WARNING 2025-07-26 03:56:33,832 basehttp 35604 2516 "GET /api/v1/health/ HTTP/1.1" 404 3186
WARNING 2025-07-26 03:56:33,849 log 35604 2516 Not Found: /api/v1/health/
WARNING 2025-07-26 03:56:33,850 basehttp 35604 2516 "GET /api/v1/health/ HTTP/1.1" 404 3186
WARNING 2025-07-26 03:56:35,702 log 35604 2516 Unauthorized: /api/v1/cases/
WARNING 2025-07-26 03:56:35,703 basehttp 35604 2516 "GET /api/v1/cases/ HTTP/1.1" 401 58
WARNING 2025-07-26 03:56:35,993 log 35604 2516 Not Found: /api/v1/health/
WARNING 2025-07-26 03:56:35,994 basehttp 35604 2516 "GET /api/v1/health/ HTTP/1.1" 404 3186
WARNING 2025-07-26 03:56:36,009 log 35604 31272 Not Found: /api/v1/health/
WARNING 2025-07-26 03:56:36,010 basehttp 35604 31272 "GET /api/v1/health/ HTTP/1.1" 404 3186
WARNING 2025-07-26 03:56:37,557 log 35604 31272 Unauthorized: /api/v1/cases/
WARNING 2025-07-26 03:56:37,558 basehttp 35604 31272 "GET /api/v1/cases/ HTTP/1.1" 401 58
WARNING 2025-07-26 03:56:37,816 log 35604 31272 Not Found: /api/v1/health/
WARNING 2025-07-26 03:56:37,817 basehttp 35604 31272 "GET /api/v1/health/ HTTP/1.1" 404 3186
WARNING 2025-07-26 03:56:37,833 log 35604 2516 Not Found: /api/v1/health/
WARNING 2025-07-26 03:56:37,834 basehttp 35604 2516 "GET /api/v1/health/ HTTP/1.1" 404 3186
WARNING 2025-07-26 03:56:38,855 log 35604 2516 Unauthorized: /api/v1/cases/
WARNING 2025-07-26 03:56:38,856 basehttp 35604 2516 "GET /api/v1/cases/ HTTP/1.1" 401 58
WARNING 2025-07-26 03:56:39,110 log 35604 2516 Not Found: /api/v1/health/
WARNING 2025-07-26 03:56:39,111 basehttp 35604 2516 "GET /api/v1/health/ HTTP/1.1" 404 3186
WARNING 2025-07-26 03:56:39,127 log 35604 31272 Not Found: /api/v1/health/
WARNING 2025-07-26 03:56:39,128 basehttp 35604 31272 "GET /api/v1/health/ HTTP/1.1" 404 3186
WARNING 2025-07-26 03:56:39,938 log 35604 31272 Unauthorized: /api/v1/cases/
WARNING 2025-07-26 03:56:39,938 basehttp 35604 31272 "GET /api/v1/cases/ HTTP/1.1" 401 58
WARNING 2025-07-26 03:56:40,162 log 35604 31272 Not Found: /api/v1/health/
WARNING 2025-07-26 03:56:40,163 basehttp 35604 31272 "GET /api/v1/health/ HTTP/1.1" 404 3186
WARNING 2025-07-26 03:56:40,180 log 35604 2516 Not Found: /api/v1/health/
WARNING 2025-07-26 03:56:40,181 basehttp 35604 2516 "GET /api/v1/health/ HTTP/1.1" 404 3186
WARNING 2025-07-26 03:56:41,402 log 35604 2516 Not Found: /api/v1/workflow/tasks/
WARNING 2025-07-26 03:56:41,404 basehttp 35604 2516 "GET /api/v1/workflow/tasks/? HTTP/1.1" 404 3210
WARNING 2025-07-26 03:56:41,407 log 35604 31272 Not Found: /api/v1/workflow/technicians/
WARNING 2025-07-26 03:56:41,408 basehttp 35604 31272 "GET /api/v1/workflow/technicians/ HTTP/1.1" 404 3228
WARNING 2025-07-26 03:56:43,031 log 35604 31272 Unauthorized: /api/v1/cases/
WARNING 2025-07-26 03:56:43,033 basehttp 35604 31272 "GET /api/v1/cases/ HTTP/1.1" 401 58
WARNING 2025-07-26 03:56:43,290 log 35604 31272 Not Found: /api/v1/health/
WARNING 2025-07-26 03:56:43,291 basehttp 35604 31272 "GET /api/v1/health/ HTTP/1.1" 404 3186
WARNING 2025-07-26 03:56:43,310 log 35604 2516 Not Found: /api/v1/health/
WARNING 2025-07-26 03:56:43,311 basehttp 35604 2516 "GET /api/v1/health/ HTTP/1.1" 404 3186
WARNING 2025-07-26 03:56:56,301 log 35604 2516 Not Found: /api/v1/health/
WARNING 2025-07-26 03:56:56,302 basehttp 35604 2516 "GET /api/v1/health/ HTTP/1.1" 404 3186
WARNING 2025-07-26 03:56:56,317 log 35604 31272 Not Found: /api/v1/health/
WARNING 2025-07-26 03:56:56,318 basehttp 35604 31272 "GET /api/v1/health/ HTTP/1.1" 404 3186
WARNING 2025-07-26 03:56:59,123 log 35604 31272 Unauthorized: /api/v1/cases/
WARNING 2025-07-26 03:56:59,124 basehttp 35604 31272 "GET /api/v1/cases/ HTTP/1.1" 401 58
WARNING 2025-07-26 03:56:59,415 log 35604 31272 Not Found: /api/v1/health/
WARNING 2025-07-26 03:56:59,416 basehttp 35604 31272 "GET /api/v1/health/ HTTP/1.1" 404 3186
WARNING 2025-07-26 03:56:59,431 log 35604 2516 Not Found: /api/v1/health/
WARNING 2025-07-26 03:56:59,432 basehttp 35604 2516 "GET /api/v1/health/ HTTP/1.1" 404 3186
WARNING 2025-07-26 03:57:00,855 log 35604 2516 Unauthorized: /api/v1/cases/
WARNING 2025-07-26 03:57:00,856 basehttp 35604 2516 "GET /api/v1/cases/ HTTP/1.1" 401 58
WARNING 2025-07-26 03:57:01,084 log 35604 2516 Not Found: /api/v1/health/
WARNING 2025-07-26 03:57:01,085 basehttp 35604 2516 "GET /api/v1/health/ HTTP/1.1" 404 3186
WARNING 2025-07-26 03:57:01,100 log 35604 31272 Not Found: /api/v1/health/
WARNING 2025-07-26 03:57:01,101 basehttp 35604 31272 "GET /api/v1/health/ HTTP/1.1" 404 3186
WARNING 2025-07-26 03:57:04,996 log 35604 31272 Not Found: /api/v1/health/
WARNING 2025-07-26 03:57:04,997 basehttp 35604 31272 "GET /api/v1/health/ HTTP/1.1" 404 3186
INFO 2025-07-26 03:57:26,262 basehttp 35604 31272 "OPTIONS /api/v1/auth/login/ HTTP/1.1" 200 0
INFO 2025-07-26 03:57:26,783 basehttp 35604 31272 "POST /api/v1/auth/login/ HTTP/1.1" 200 796
INFO 2025-07-26 03:57:26,908 basehttp 35604 2516 "OPTIONS /api/v1/health/ HTTP/1.1" 200 0
INFO 2025-07-26 03:57:26,909 basehttp 35604 31272 "OPTIONS /api/v1/health/ HTTP/1.1" 200 0
WARNING 2025-07-26 03:57:26,927 log 35604 31272 Not Found: /api/v1/health/
WARNING 2025-07-26 03:57:26,929 basehttp 35604 31272 "GET /api/v1/health/ HTTP/1.1" 404 3186
WARNING 2025-07-26 03:57:26,947 log 35604 2516 Not Found: /api/v1/health/
WARNING 2025-07-26 03:57:26,948 basehttp 35604 2516 "GET /api/v1/health/ HTTP/1.1" 404 3186
INFO 2025-07-26 03:57:33,744 basehttp 35604 2516 "OPTIONS /api/v1/workflow/tasks/? HTTP/1.1" 200 0
INFO 2025-07-26 03:57:33,744 basehttp 35604 31272 "OPTIONS /api/v1/workflow/technicians/ HTTP/1.1" 200 0
WARNING 2025-07-26 03:57:33,776 log 35604 2516 Not Found: /api/v1/workflow/technicians/
WARNING 2025-07-26 03:57:33,776 log 35604 31272 Not Found: /api/v1/workflow/tasks/
WARNING 2025-07-26 03:57:33,778 basehttp 35604 2516 "GET /api/v1/workflow/technicians/ HTTP/1.1" 404 3228
WARNING 2025-07-26 03:57:33,778 basehttp 35604 31272 "GET /api/v1/workflow/tasks/? HTTP/1.1" 404 3210
WARNING 2025-07-26 03:57:35,156 log 35604 2516 Unauthorized: /api/v1/cases/
WARNING 2025-07-26 03:57:35,157 basehttp 35604 2516 "GET /api/v1/cases/ HTTP/1.1" 401 58
WARNING 2025-07-26 03:57:35,950 log 35604 2516 Not Found: /api/v1/health/
WARNING 2025-07-26 03:57:35,951 basehttp 35604 2516 "GET /api/v1/health/ HTTP/1.1" 404 3186
WARNING 2025-07-26 03:57:35,968 log 35604 31272 Not Found: /api/v1/health/
WARNING 2025-07-26 03:57:35,969 basehttp 35604 31272 "GET /api/v1/health/ HTTP/1.1" 404 3186
WARNING 2025-07-26 03:57:36,950 log 35604 31272 Unauthorized: /api/v1/cases/
WARNING 2025-07-26 03:57:36,951 basehttp 35604 31272 "GET /api/v1/cases/ HTTP/1.1" 401 58
WARNING 2025-07-26 03:57:37,210 log 35604 31272 Not Found: /api/v1/health/
WARNING 2025-07-26 03:57:37,211 basehttp 35604 31272 "GET /api/v1/health/ HTTP/1.1" 404 3186
WARNING 2025-07-26 03:57:37,226 log 35604 2516 Not Found: /api/v1/health/
WARNING 2025-07-26 03:57:37,227 basehttp 35604 2516 "GET /api/v1/health/ HTTP/1.1" 404 3186
WARNING 2025-07-26 03:57:38,055 log 35604 2516 Unauthorized: /api/v1/cases/
WARNING 2025-07-26 03:57:38,056 basehttp 35604 2516 "GET /api/v1/cases/ HTTP/1.1" 401 58
WARNING 2025-07-26 03:57:38,279 log 35604 2516 Not Found: /api/v1/health/
WARNING 2025-07-26 03:57:38,280 basehttp 35604 2516 "GET /api/v1/health/ HTTP/1.1" 404 3186
WARNING 2025-07-26 03:57:38,296 log 35604 31272 Not Found: /api/v1/health/
WARNING 2025-07-26 03:57:38,297 basehttp 35604 31272 "GET /api/v1/health/ HTTP/1.1" 404 3186
WARNING 2025-07-26 03:59:26,606 log 35604 31272 Unauthorized: /api/v1/cases/
WARNING 2025-07-26 03:59:26,623 basehttp 35604 31272 "GET /api/v1/cases/ HTTP/1.1" 401 58
WARNING 2025-07-26 03:59:27,738 log 35604 31272 Unauthorized: /api/v1/cases/
WARNING 2025-07-26 03:59:27,742 basehttp 35604 31272 "GET /api/v1/cases/ HTTP/1.1" 401 58
WARNING 2025-07-26 03:59:29,895 log 35604 31272 Not Found: /api/v1/health/
WARNING 2025-07-26 03:59:29,896 basehttp 35604 31272 "GET /api/v1/health/ HTTP/1.1" 404 3186
WARNING 2025-07-26 03:59:29,906 log 35604 2516 Not Found: /api/v1/health/
WARNING 2025-07-26 03:59:29,907 basehttp 35604 2516 "GET /api/v1/health/ HTTP/1.1" 404 3186
WARNING 2025-07-26 03:59:32,306 log 35604 2516 Unauthorized: /api/v1/cases/
WARNING 2025-07-26 03:59:32,307 basehttp 35604 2516 "GET /api/v1/cases/ HTTP/1.1" 401 58
WARNING 2025-07-26 03:59:32,573 log 35604 2516 Not Found: /api/v1/health/
WARNING 2025-07-26 03:59:32,574 basehttp 35604 2516 "GET /api/v1/health/ HTTP/1.1" 404 3186
WARNING 2025-07-26 03:59:32,586 log 35604 31272 Not Found: /api/v1/health/
WARNING 2025-07-26 03:59:32,587 basehttp 35604 31272 "GET /api/v1/health/ HTTP/1.1" 404 3186
WARNING 2025-07-26 03:59:34,004 log 35604 31272 Unauthorized: /api/v1/cases/
WARNING 2025-07-26 03:59:34,005 basehttp 35604 31272 "GET /api/v1/cases/ HTTP/1.1" 401 58
WARNING 2025-07-26 03:59:34,225 log 35604 31272 Not Found: /api/v1/health/
WARNING 2025-07-26 03:59:34,226 basehttp 35604 31272 "GET /api/v1/health/ HTTP/1.1" 404 3186
WARNING 2025-07-26 03:59:34,241 log 35604 2516 Not Found: /api/v1/health/
WARNING 2025-07-26 03:59:34,242 basehttp 35604 2516 "GET /api/v1/health/ HTTP/1.1" 404 3186
WARNING 2025-07-26 03:59:35,528 log 35604 2516 Unauthorized: /api/v1/cases/
WARNING 2025-07-26 03:59:35,529 basehttp 35604 2516 "GET /api/v1/cases/ HTTP/1.1" 401 58
WARNING 2025-07-26 03:59:35,747 log 35604 2516 Not Found: /api/v1/health/
WARNING 2025-07-26 03:59:35,748 basehttp 35604 2516 "GET /api/v1/health/ HTTP/1.1" 404 3186
WARNING 2025-07-26 03:59:35,762 log 35604 31272 Not Found: /api/v1/health/
WARNING 2025-07-26 03:59:35,763 basehttp 35604 31272 "GET /api/v1/health/ HTTP/1.1" 404 3186
WARNING 2025-07-26 03:59:36,556 log 35604 31272 Unauthorized: /api/v1/cases/
WARNING 2025-07-26 03:59:36,557 basehttp 35604 31272 "GET /api/v1/cases/ HTTP/1.1" 401 58
WARNING 2025-07-26 03:59:36,794 log 35604 31272 Not Found: /api/v1/health/
WARNING 2025-07-26 03:59:36,794 basehttp 35604 31272 "GET /api/v1/health/ HTTP/1.1" 404 3186
WARNING 2025-07-26 03:59:36,808 log 35604 2516 Not Found: /api/v1/health/
WARNING 2025-07-26 03:59:36,809 basehttp 35604 2516 "GET /api/v1/health/ HTTP/1.1" 404 3186
WARNING 2025-07-26 03:59:37,408 log 35604 2516 Unauthorized: /api/v1/cases/
WARNING 2025-07-26 03:59:37,409 basehttp 35604 2516 "GET /api/v1/cases/ HTTP/1.1" 401 58
WARNING 2025-07-26 03:59:37,658 log 35604 2516 Not Found: /api/v1/health/
WARNING 2025-07-26 03:59:37,659 basehttp 35604 2516 "GET /api/v1/health/ HTTP/1.1" 404 3186
WARNING 2025-07-26 03:59:37,673 log 35604 31272 Not Found: /api/v1/health/
WARNING 2025-07-26 03:59:37,674 basehttp 35604 31272 "GET /api/v1/health/ HTTP/1.1" 404 3186
WARNING 2025-07-26 03:59:38,314 log 35604 31272 Unauthorized: /api/v1/cases/
WARNING 2025-07-26 03:59:38,315 basehttp 35604 31272 "GET /api/v1/cases/ HTTP/1.1" 401 58
WARNING 2025-07-26 03:59:38,542 log 35604 31272 Not Found: /api/v1/health/
WARNING 2025-07-26 03:59:38,543 basehttp 35604 31272 "GET /api/v1/health/ HTTP/1.1" 404 3186
WARNING 2025-07-26 03:59:38,557 log 35604 2516 Not Found: /api/v1/health/
WARNING 2025-07-26 03:59:38,558 basehttp 35604 2516 "GET /api/v1/health/ HTTP/1.1" 404 3186
WARNING 2025-07-26 04:01:03,553 log 35604 2516 Not Found: /api/v1/health/
WARNING 2025-07-26 04:01:03,554 basehttp 35604 2516 "GET /api/v1/health/ HTTP/1.1" 404 3186
WARNING 2025-07-26 04:01:03,560 log 35604 31272 Not Found: /api/v1/health/
WARNING 2025-07-26 04:01:03,561 basehttp 35604 31272 "GET /api/v1/health/ HTTP/1.1" 404 3186
WARNING 2025-07-26 04:01:05,145 log 35604 31272 Unauthorized: /api/v1/cases/
WARNING 2025-07-26 04:01:05,146 basehttp 35604 31272 "GET /api/v1/cases/ HTTP/1.1" 401 58
WARNING 2025-07-26 04:01:05,437 log 35604 31272 Not Found: /api/v1/health/
WARNING 2025-07-26 04:01:05,437 basehttp 35604 31272 "GET /api/v1/health/ HTTP/1.1" 404 3186
WARNING 2025-07-26 04:01:05,445 log 35604 2516 Not Found: /api/v1/health/
WARNING 2025-07-26 04:01:05,445 basehttp 35604 2516 "GET /api/v1/health/ HTTP/1.1" 404 3186
WARNING 2025-07-26 04:01:06,346 log 35604 2516 Unauthorized: /api/v1/cases/
WARNING 2025-07-26 04:01:06,346 basehttp 35604 2516 "GET /api/v1/cases/ HTTP/1.1" 401 58
WARNING 2025-07-26 04:01:06,570 log 35604 2516 Not Found: /api/v1/health/
WARNING 2025-07-26 04:01:06,572 basehttp 35604 2516 "GET /api/v1/health/ HTTP/1.1" 404 3186
WARNING 2025-07-26 04:01:06,579 log 35604 31272 Not Found: /api/v1/health/
WARNING 2025-07-26 04:01:06,580 basehttp 35604 31272 "GET /api/v1/health/ HTTP/1.1" 404 3186
WARNING 2025-07-26 04:01:11,006 log 35604 31272 Unauthorized: /api/v1/cases/
WARNING 2025-07-26 04:01:11,007 basehttp 35604 31272 "GET /api/v1/cases/ HTTP/1.1" 401 58
WARNING 2025-07-26 04:01:12,126 log 35604 31272 Not Found: /api/v1/health/
WARNING 2025-07-26 04:01:12,127 basehttp 35604 31272 "GET /api/v1/health/ HTTP/1.1" 404 3186
WARNING 2025-07-26 04:01:12,140 log 35604 2516 Not Found: /api/v1/health/
WARNING 2025-07-26 04:01:12,141 basehttp 35604 2516 "GET /api/v1/health/ HTTP/1.1" 404 3186
WARNING 2025-07-26 04:01:15,671 log 35604 2516 Unauthorized: /api/v1/cases/
WARNING 2025-07-26 04:01:15,672 basehttp 35604 2516 "GET /api/v1/cases/ HTTP/1.1" 401 58
WARNING 2025-07-26 04:01:15,956 log 35604 2516 Not Found: /api/v1/health/
WARNING 2025-07-26 04:01:15,957 basehttp 35604 2516 "GET /api/v1/health/ HTTP/1.1" 404 3186
WARNING 2025-07-26 04:01:15,972 log 35604 31272 Not Found: /api/v1/health/
WARNING 2025-07-26 04:01:15,973 basehttp 35604 31272 "GET /api/v1/health/ HTTP/1.1" 404 3186
WARNING 2025-07-26 04:01:16,981 log 35604 31272 Unauthorized: /api/v1/cases/
WARNING 2025-07-26 04:01:16,981 basehttp 35604 31272 "GET /api/v1/cases/ HTTP/1.1" 401 58
WARNING 2025-07-26 04:01:17,209 log 35604 31272 Not Found: /api/v1/health/
WARNING 2025-07-26 04:01:17,210 basehttp 35604 31272 "GET /api/v1/health/ HTTP/1.1" 404 3186
WARNING 2025-07-26 04:01:17,224 log 35604 2516 Not Found: /api/v1/health/
WARNING 2025-07-26 04:01:17,225 basehttp 35604 2516 "GET /api/v1/health/ HTTP/1.1" 404 3186
WARNING 2025-07-26 04:01:49,721 log 35604 2516 Unauthorized: /api/v1/cases/
WARNING 2025-07-26 04:01:49,722 basehttp 35604 2516 "GET /api/v1/cases/ HTTP/1.1" 401 58
WARNING 2025-07-26 04:01:49,954 log 35604 2516 Not Found: /api/v1/health/
WARNING 2025-07-26 04:01:49,956 basehttp 35604 2516 "GET /api/v1/health/ HTTP/1.1" 404 3186
WARNING 2025-07-26 04:01:49,965 log 35604 31272 Not Found: /api/v1/health/
WARNING 2025-07-26 04:01:49,966 basehttp 35604 31272 "GET /api/v1/health/ HTTP/1.1" 404 3186
WARNING 2025-07-26 04:02:24,237 log 35604 31272 Unauthorized: /api/v1/cases/
WARNING 2025-07-26 04:02:24,238 basehttp 35604 31272 "GET /api/v1/cases/ HTTP/1.1" 401 58
WARNING 2025-07-26 04:02:24,806 log 35604 31272 Not Found: /api/v1/health/
WARNING 2025-07-26 04:02:24,807 basehttp 35604 31272 "GET /api/v1/health/ HTTP/1.1" 404 3186
WARNING 2025-07-26 04:02:24,822 log 35604 2516 Not Found: /api/v1/health/
WARNING 2025-07-26 04:02:24,823 basehttp 35604 2516 "GET /api/v1/health/ HTTP/1.1" 404 3186
WARNING 2025-07-26 04:02:27,505 log 35604 2516 Unauthorized: /api/v1/cases/
WARNING 2025-07-26 04:02:27,505 basehttp 35604 2516 "GET /api/v1/cases/ HTTP/1.1" 401 58
WARNING 2025-07-26 04:02:27,777 log 35604 2516 Not Found: /api/v1/health/
WARNING 2025-07-26 04:02:27,778 basehttp 35604 2516 "GET /api/v1/health/ HTTP/1.1" 404 3186
WARNING 2025-07-26 04:02:27,795 log 35604 31272 Not Found: /api/v1/health/
WARNING 2025-07-26 04:02:27,796 basehttp 35604 31272 "GET /api/v1/health/ HTTP/1.1" 404 3186
WARNING 2025-07-26 04:02:28,588 log 35604 31272 Unauthorized: /api/v1/cases/
WARNING 2025-07-26 04:02:28,589 basehttp 35604 31272 "GET /api/v1/cases/ HTTP/1.1" 401 58
WARNING 2025-07-26 04:02:28,823 log 35604 31272 Not Found: /api/v1/health/
WARNING 2025-07-26 04:02:28,824 basehttp 35604 31272 "GET /api/v1/health/ HTTP/1.1" 404 3186
WARNING 2025-07-26 04:02:28,838 log 35604 2516 Not Found: /api/v1/health/
WARNING 2025-07-26 04:02:28,838 basehttp 35604 2516 "GET /api/v1/health/ HTTP/1.1" 404 3186
WARNING 2025-07-26 04:02:35,678 log 35604 2516 Not Found: /api/v1/billing/invoices/
WARNING 2025-07-26 04:02:35,679 basehttp 35604 2516 "GET /api/v1/billing/invoices/? HTTP/1.1" 404 3216
WARNING 2025-07-26 04:02:37,843 log 35604 31272 Not Found: /api/v1/workflow/technicians/
WARNING 2025-07-26 04:02:37,845 log 35604 2516 Not Found: /api/v1/workflow/tasks/
WARNING 2025-07-26 04:02:37,846 basehttp 35604 31272 "GET /api/v1/workflow/technicians/ HTTP/1.1" 404 3228
WARNING 2025-07-26 04:02:37,847 basehttp 35604 2516 "GET /api/v1/workflow/tasks/? HTTP/1.1" 404 3210
WARNING 2025-07-26 04:02:41,429 log 35604 2516 Unauthorized: /api/v1/cases/
WARNING 2025-07-26 04:02:41,430 basehttp 35604 2516 "GET /api/v1/cases/ HTTP/1.1" 401 58
WARNING 2025-07-26 04:02:41,683 log 35604 2516 Not Found: /api/v1/health/
WARNING 2025-07-26 04:02:41,684 basehttp 35604 2516 "GET /api/v1/health/ HTTP/1.1" 404 3186
WARNING 2025-07-26 04:02:41,700 log 35604 31272 Not Found: /api/v1/health/
WARNING 2025-07-26 04:02:41,701 basehttp 35604 31272 "GET /api/v1/health/ HTTP/1.1" 404 3186
WARNING 2025-07-26 04:03:04,233 log 35604 31272 Unauthorized: /api/v1/cases/
WARNING 2025-07-26 04:03:04,234 basehttp 35604 31272 "GET /api/v1/cases/ HTTP/1.1" 401 58
WARNING 2025-07-26 04:03:05,239 log 35604 31272 Not Found: /api/v1/health/
WARNING 2025-07-26 04:03:05,240 basehttp 35604 31272 "GET /api/v1/health/ HTTP/1.1" 404 3186
WARNING 2025-07-26 04:03:05,255 log 35604 2516 Not Found: /api/v1/health/
WARNING 2025-07-26 04:03:05,257 basehttp 35604 2516 "GET /api/v1/health/ HTTP/1.1" 404 3186
WARNING 2025-07-26 04:03:19,000 log 32972 25112 Unauthorized: /api/v1/cases/
WARNING 2025-07-26 04:03:19,000 basehttp 32972 25112 "GET /api/v1/cases/ HTTP/1.1" 401 58
WARNING 2025-07-26 04:03:57,844 log 35604 2516 Not Found: /api/v1/health/
WARNING 2025-07-26 04:03:57,845 basehttp 35604 2516 "GET /api/v1/health/ HTTP/1.1" 404 3186
WARNING 2025-07-26 04:03:57,864 log 35604 31272 Not Found: /api/v1/health/
WARNING 2025-07-26 04:03:57,865 basehttp 35604 31272 "GET /api/v1/health/ HTTP/1.1" 404 3186
WARNING 2025-07-26 04:03:57,880 log 35604 2516 Not Found: /api/v1/health/
WARNING 2025-07-26 04:03:57,881 basehttp 35604 2516 "GET /api/v1/health/ HTTP/1.1" 404 3186
WARNING 2025-07-26 04:03:57,893 log 35604 31272 Not Found: /api/v1/health/
WARNING 2025-07-26 04:03:57,894 basehttp 35604 31272 "GET /api/v1/health/ HTTP/1.1" 404 3186
WARNING 2025-07-26 04:03:57,906 log 35604 2516 Not Found: /api/v1/health/
WARNING 2025-07-26 04:03:57,906 basehttp 35604 2516 "GET /api/v1/health/ HTTP/1.1" 404 3186
WARNING 2025-07-26 04:03:57,917 log 35604 31272 Not Found: /api/v1/health/
WARNING 2025-07-26 04:03:57,918 basehttp 35604 31272 "GET /api/v1/health/ HTTP/1.1" 404 3186
ERROR 2025-07-26 04:04:07,587 log 32972 25112 Internal Server Error: /api/v1/auth/login/
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\views\decorators\csrf.py", line 56, in wrapper_view
    return view_func(*args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\views\generic\base.py", line 104, in view
    return self.dispatch(request, *args, **kwargs)
           ~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\rest_framework\views.py", line 509, in dispatch
    response = self.handle_exception(exc)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\rest_framework\views.py", line 469, in handle_exception
    self.raise_uncaught_exception(exc)
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\rest_framework\views.py", line 480, in raise_uncaught_exception
    raise exc
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\rest_framework\views.py", line 506, in dispatch
    response = handler(request, *args, **kwargs)
  File "C:\GPT4_PROJECTS\DENTAL_LAB_ARCHITECTURE_PATTERNS\dentflow\backend\apps\users\views.py", line 42, in post
    user_obj = User.objects.get(email=email)
               ^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\db\models\manager.py", line 196, in __get__
    raise AttributeError(
    ...<5 lines>...
    )
AttributeError: Manager isn't available; 'auth.User' has been swapped for 'users.User'
ERROR 2025-07-26 04:04:07,590 basehttp 32972 25112 "POST /api/v1/auth/login/ HTTP/1.1" 500 112812
INFO 2025-07-26 04:04:25,710 basehttp 35604 30452 "GET /admin/ HTTP/1.1" 200 19362
INFO 2025-07-26 04:04:25,799 basehttp 35604 30452 "GET /static/admin/img/icon-viewlink.svg HTTP/1.1" 304 0
INFO 2025-07-26 04:04:31,714 basehttp 32972 11080 "GET /admin/ HTTP/1.1" 200 19362
INFO 2025-07-26 04:04:32,142 basehttp 32972 15036 "GET /static/admin/js/theme.js HTTP/1.1" 200 1943
INFO 2025-07-26 04:04:32,148 basehttp 32972 27476 "GET /static/admin/css/responsive.css HTTP/1.1" 200 18533
INFO 2025-07-26 04:04:32,148 basehttp 32972 15036 "GET /static/admin/js/nav_sidebar.js HTTP/1.1" 200 3063
INFO 2025-07-26 04:04:32,228 basehttp 32972 28600 "GET /static/admin/css/dark_mode.css HTTP/1.1" 200 2929
INFO 2025-07-26 04:04:32,237 basehttp 32972 29832 "GET /static/admin/css/dashboard.css HTTP/1.1" 200 441
INFO 2025-07-26 04:04:32,249 basehttp 32972 10992 "GET /static/admin/css/nav_sidebar.css HTTP/1.1" 200 2694
INFO 2025-07-26 04:04:32,250 basehttp 32972 11080 "GET /static/admin/css/base.css HTTP/1.1" 200 21207
INFO 2025-07-26 04:04:32,263 basehttp 32972 29832 "GET /static/admin/img/icon-viewlink.svg HTTP/1.1" 200 581
INFO 2025-07-26 04:04:32,263 basehttp 32972 11080 "GET /static/admin/img/icon-addlink.svg HTTP/1.1" 200 331
INFO 2025-07-26 04:04:32,264 basehttp 32972 10992 "GET /static/admin/img/icon-changelink.svg HTTP/1.1" 200 380
WARNING 2025-07-26 04:04:32,349 log 32972 10992 Not Found: /favicon.ico
WARNING 2025-07-26 04:04:32,350 basehttp 32972 10992 "GET /favicon.ico HTTP/1.1" 404 2965
INFO 2025-07-26 04:04:36,840 basehttp 35604 30452 "GET /admin/ HTTP/1.1" 200 19362
ERROR 2025-07-26 04:06:15,242 log 32972 37192 Internal Server Error: /api/v1/auth/login/
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\views\decorators\csrf.py", line 56, in wrapper_view
    return view_func(*args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\views\generic\base.py", line 104, in view
    return self.dispatch(request, *args, **kwargs)
           ~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\rest_framework\views.py", line 509, in dispatch
    response = self.handle_exception(exc)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\rest_framework\views.py", line 469, in handle_exception
    self.raise_uncaught_exception(exc)
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\rest_framework\views.py", line 480, in raise_uncaught_exception
    raise exc
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\rest_framework\views.py", line 506, in dispatch
    response = handler(request, *args, **kwargs)
  File "C:\GPT4_PROJECTS\DENTAL_LAB_ARCHITECTURE_PATTERNS\dentflow\backend\apps\users\views.py", line 42, in post
    user_obj = User.objects.get(email=email)
               ^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\db\models\manager.py", line 196, in __get__
    raise AttributeError(
    ...<5 lines>...
    )
AttributeError: Manager isn't available; 'auth.User' has been swapped for 'users.User'
ERROR 2025-07-26 04:06:15,245 basehttp 32972 37192 "POST /api/v1/auth/login/ HTTP/1.1" 500 113143
WARNING 2025-07-26 04:08:06,795 log 35604 31272 Unauthorized: /api/v1/cases/
WARNING 2025-07-26 04:08:06,795 basehttp 35604 31272 "GET /api/v1/cases/ HTTP/1.1" 401 58
WARNING 2025-07-26 04:08:07,961 log 35604 31272 Not Found: /api/v1/health/
WARNING 2025-07-26 04:08:07,961 basehttp 35604 31272 "GET /api/v1/health/ HTTP/1.1" 404 3186
WARNING 2025-07-26 04:08:07,968 log 35604 2516 Not Found: /api/v1/health/
WARNING 2025-07-26 04:08:07,969 basehttp 35604 2516 "GET /api/v1/health/ HTTP/1.1" 404 3186
WARNING 2025-07-26 04:08:40,777 log 35604 2516 Not Found: /api/v1/health/
WARNING 2025-07-26 04:08:40,778 basehttp 35604 2516 "GET /api/v1/health/ HTTP/1.1" 404 3186
WARNING 2025-07-26 04:08:40,792 log 35604 31272 Not Found: /api/v1/health/
WARNING 2025-07-26 04:08:40,793 basehttp 35604 31272 "GET /api/v1/health/ HTTP/1.1" 404 3186
WARNING 2025-07-26 04:08:40,807 log 35604 2516 Not Found: /api/v1/health/
WARNING 2025-07-26 04:08:40,808 basehttp 35604 2516 "GET /api/v1/health/ HTTP/1.1" 404 3186
WARNING 2025-07-26 04:08:40,822 log 35604 31272 Not Found: /api/v1/health/
WARNING 2025-07-26 04:08:40,823 basehttp 35604 31272 "GET /api/v1/health/ HTTP/1.1" 404 3186
WARNING 2025-07-26 05:58:14,314 log 35604 3444 Unauthorized: /api/v1/cases/
WARNING 2025-07-26 05:58:14,316 basehttp 35604 3444 "GET /api/v1/cases/ HTTP/1.1" 401 58
INFO 2025-07-26 05:58:15,255 basehttp 35604 3444 "OPTIONS /api/v1/health/ HTTP/1.1" 200 0
INFO 2025-07-26 05:58:15,255 basehttp 35604 25584 "OPTIONS /api/v1/health/ HTTP/1.1" 200 0
WARNING 2025-07-26 05:58:15,272 log 35604 25584 Not Found: /api/v1/health/
WARNING 2025-07-26 05:58:15,273 basehttp 35604 25584 "GET /api/v1/health/ HTTP/1.1" 404 3186
WARNING 2025-07-26 05:58:15,288 log 35604 3444 Not Found: /api/v1/health/
WARNING 2025-07-26 05:58:15,288 basehttp 35604 3444 "GET /api/v1/health/ HTTP/1.1" 404 3186
WARNING 2025-07-26 05:58:50,846 log 35604 3444 Not Found: /api/v1/health/
WARNING 2025-07-26 05:58:50,847 basehttp 35604 3444 "GET /api/v1/health/ HTTP/1.1" 404 3186
WARNING 2025-07-26 05:58:50,857 log 35604 25584 Not Found: /api/v1/health/
WARNING 2025-07-26 05:58:50,858 basehttp 35604 25584 "GET /api/v1/health/ HTTP/1.1" 404 3186
WARNING 2025-07-26 05:58:50,875 log 35604 3444 Not Found: /api/v1/health/
WARNING 2025-07-26 05:58:50,877 basehttp 35604 3444 "GET /api/v1/health/ HTTP/1.1" 404 3186
WARNING 2025-07-26 05:58:50,890 log 35604 25584 Not Found: /api/v1/health/
WARNING 2025-07-26 05:58:50,892 basehttp 35604 25584 "GET /api/v1/health/ HTTP/1.1" 404 3186
WARNING 2025-07-26 05:59:20,413 log 35604 25584 Not Found: /api/v1/health/
WARNING 2025-07-26 05:59:20,413 basehttp 35604 25584 "GET /api/v1/health/ HTTP/1.1" 404 3186
WARNING 2025-07-26 05:59:20,423 log 35604 3444 Not Found: /api/v1/health/
WARNING 2025-07-26 05:59:20,424 basehttp 35604 3444 "GET /api/v1/health/ HTTP/1.1" 404 3186
INFO 2025-07-26 05:59:28,967 basehttp 35604 9876 "GET /admin/ HTTP/1.1" 200 19362
INFO 2025-07-26 05:59:29,050 basehttp 35604 9876 "GET /static/admin/img/icon-viewlink.svg HTTP/1.1" 304 0
INFO 2025-07-26 05:59:55,353 autoreload 21160 36476 Watching for file changes with StatReloader
INFO 2025-07-26 06:00:00,798 basehttp 21160 20388 "GET /admin/ HTTP/1.1" 200 19362
WARNING 2025-07-26 06:00:06,493 log 21160 19060 Not Found: /api/v1/health/
WARNING 2025-07-26 06:00:06,496 basehttp 21160 19060 "GET /api/v1/health/ HTTP/1.1" 404 3186
WARNING 2025-07-26 06:00:06,527 log 21160 19060 Not Found: /api/v1/health/
WARNING 2025-07-26 06:00:06,529 basehttp 21160 19060 "GET /api/v1/health/ HTTP/1.1" 404 3186
WARNING 2025-07-26 06:00:11,080 log 21160 19060 Unauthorized: /api/v1/cases/
WARNING 2025-07-26 06:00:11,081 basehttp 21160 19060 "GET /api/v1/cases/ HTTP/1.1" 401 58
WARNING 2025-07-26 06:00:11,669 log 21160 19060 Not Found: /api/v1/health/
WARNING 2025-07-26 06:00:11,670 basehttp 21160 19060 "GET /api/v1/health/ HTTP/1.1" 404 3186
WARNING 2025-07-26 06:00:11,685 log 21160 22584 Not Found: /api/v1/health/
WARNING 2025-07-26 06:00:11,686 basehttp 21160 22584 "GET /api/v1/health/ HTTP/1.1" 404 3186
INFO 2025-07-26 06:00:28,348 basehttp 21160 22584 "OPTIONS /api/v1/billing/invoices/? HTTP/1.1" 200 0
WARNING 2025-07-26 06:00:28,358 log 21160 22584 Not Found: /api/v1/billing/invoices/
WARNING 2025-07-26 06:00:28,359 basehttp 21160 22584 "GET /api/v1/billing/invoices/? HTTP/1.1" 404 3216
WARNING 2025-07-26 06:00:32,590 log 21160 22584 Not Found: /api/v1/health/
WARNING 2025-07-26 06:00:32,591 basehttp 21160 22584 "GET /api/v1/health/ HTTP/1.1" 404 3186
WARNING 2025-07-26 06:00:32,608 log 21160 19060 Not Found: /api/v1/health/
WARNING 2025-07-26 06:00:32,609 basehttp 21160 19060 "GET /api/v1/health/ HTTP/1.1" 404 3186
WARNING 2025-07-26 06:01:11,759 log 21160 19060 Unauthorized: /api/v1/cases/
WARNING 2025-07-26 06:01:11,761 basehttp 21160 19060 "GET /api/v1/cases/ HTTP/1.1" 401 58
WARNING 2025-07-26 06:01:12,412 log 21160 19060 Not Found: /api/v1/health/
WARNING 2025-07-26 06:01:12,413 basehttp 21160 19060 "GET /api/v1/health/ HTTP/1.1" 404 3186
WARNING 2025-07-26 06:01:12,424 log 21160 22584 Not Found: /api/v1/health/
WARNING 2025-07-26 06:01:12,425 basehttp 21160 22584 "GET /api/v1/health/ HTTP/1.1" 404 3186
WARNING 2025-07-26 06:01:49,826 log 21160 22584 Not Found: /api/v1/health/
WARNING 2025-07-26 06:01:49,827 basehttp 21160 22584 "GET /api/v1/health/ HTTP/1.1" 404 3186
WARNING 2025-07-26 06:01:49,841 log 21160 19060 Not Found: /api/v1/health/
WARNING 2025-07-26 06:01:49,842 basehttp 21160 19060 "GET /api/v1/health/ HTTP/1.1" 404 3186
INFO 2025-07-26 06:02:10,701 autoreload 24684 21784 Watching for file changes with StatReloader
INFO 2025-07-26 06:02:21,904 autoreload 20636 19820 Watching for file changes with StatReloader
INFO 2025-07-26 06:04:41,933 basehttp 24684 37688 "GET /admin/ HTTP/1.1" 200 19362
INFO 2025-07-26 06:04:41,966 basehttp 24684 37688 "GET /static/admin/css/base.css HTTP/1.1" 304 0
INFO 2025-07-26 06:04:41,970 basehttp 24684 3932 "GET /static/admin/css/dark_mode.css HTTP/1.1" 304 0
INFO 2025-07-26 06:04:41,973 basehttp 24684 37688 "GET /static/admin/css/nav_sidebar.css HTTP/1.1" 304 0
INFO 2025-07-26 06:04:41,977 basehttp 24684 25244 "GET /static/admin/css/dashboard.css HTTP/1.1" 304 0
INFO 2025-07-26 06:04:41,978 basehttp 24684 3932 "GET /static/admin/js/theme.js HTTP/1.1" 304 0
INFO 2025-07-26 06:04:41,979 basehttp 24684 33424 "GET /static/admin/css/responsive.css HTTP/1.1" 304 0
INFO 2025-07-26 06:04:41,979 basehttp 24684 37688 "GET /static/admin/js/nav_sidebar.js HTTP/1.1" 304 0
INFO 2025-07-26 06:04:41,991 basehttp 24684 3932 "GET /static/admin/img/icon-viewlink.svg HTTP/1.1" 304 0
INFO 2025-07-26 06:04:41,991 basehttp 24684 37688 "GET /static/admin/img/icon-addlink.svg HTTP/1.1" 304 0
INFO 2025-07-26 06:04:41,992 basehttp 24684 33424 "GET /static/admin/img/icon-changelink.svg HTTP/1.1" 304 0
INFO 2025-07-26 06:04:46,146 basehttp 24684 33424 "POST /admin/logout/ HTTP/1.1" 200 3575
INFO 2025-07-26 06:04:48,278 basehttp 24684 33424 "GET /admin/ HTTP/1.1" 302 0
INFO 2025-07-26 06:04:48,334 basehttp 24684 33424 "GET /admin/login/?next=/admin/ HTTP/1.1" 200 4177
INFO 2025-07-26 06:04:48,525 basehttp 24684 33424 "GET /static/admin/css/login.css HTTP/1.1" 200 958
INFO 2025-07-26 06:05:03,664 basehttp 24684 33424 "POST /admin/login/?next=/admin/ HTTP/1.1" 302 0
INFO 2025-07-26 06:05:03,785 basehttp 24684 33424 "GET /admin/ HTTP/1.1" 200 19362
INFO 2025-07-26 06:09:01,217 basehttp 24684 16352 "OPTIONS /api/v1/auth/login/ HTTP/1.1" 200 0
INFO 2025-07-26 06:09:01,837 basehttp 24684 16352 "POST /api/v1/auth/login/ HTTP/1.1" 200 796
INFO 2025-07-26 06:09:02,001 basehttp 24684 16352 "OPTIONS /api/v1/health/ HTTP/1.1" 200 0
INFO 2025-07-26 06:09:02,001 basehttp 24684 15468 "OPTIONS /api/v1/health/ HTTP/1.1" 200 0
WARNING 2025-07-26 06:09:02,022 log 24684 16352 Not Found: /api/v1/health/
WARNING 2025-07-26 06:09:02,022 basehttp 24684 16352 "GET /api/v1/health/ HTTP/1.1" 404 3186
WARNING 2025-07-26 06:09:02,037 log 24684 15468 Not Found: /api/v1/health/
WARNING 2025-07-26 06:09:02,038 basehttp 24684 15468 "GET /api/v1/health/ HTTP/1.1" 404 3186
WARNING 2025-07-26 06:09:09,156 log 24684 15468 Unauthorized: /api/v1/cases/
WARNING 2025-07-26 06:09:09,157 basehttp 24684 15468 "GET /api/v1/cases/ HTTP/1.1" 401 58
WARNING 2025-07-26 06:09:09,439 log 24684 15468 Not Found: /api/v1/health/
WARNING 2025-07-26 06:09:09,440 basehttp 24684 15468 "GET /api/v1/health/ HTTP/1.1" 404 3186
WARNING 2025-07-26 06:09:09,455 log 24684 16352 Not Found: /api/v1/health/
WARNING 2025-07-26 06:09:09,456 basehttp 24684 16352 "GET /api/v1/health/ HTTP/1.1" 404 3186
WARNING 2025-07-26 06:09:24,559 log 24684 16352 Unauthorized: /api/v1/cases/
WARNING 2025-07-26 06:09:24,560 basehttp 24684 16352 "GET /api/v1/cases/ HTTP/1.1" 401 58
WARNING 2025-07-26 06:09:25,931 log 24684 16352 Not Found: /api/v1/health/
WARNING 2025-07-26 06:09:25,932 basehttp 24684 16352 "GET /api/v1/health/ HTTP/1.1" 404 3186
WARNING 2025-07-26 06:09:25,948 log 24684 15468 Not Found: /api/v1/health/
WARNING 2025-07-26 06:09:25,949 basehttp 24684 15468 "GET /api/v1/health/ HTTP/1.1" 404 3186
INFO 2025-07-26 06:11:03,091 basehttp 24684 15468 "OPTIONS /api/v1/workflow/tasks/? HTTP/1.1" 200 0
INFO 2025-07-26 06:11:03,091 basehttp 24684 16352 "OPTIONS /api/v1/workflow/technicians/ HTTP/1.1" 200 0
WARNING 2025-07-26 06:11:03,115 log 24684 16352 Not Found: /api/v1/workflow/tasks/
WARNING 2025-07-26 06:11:03,117 log 24684 15468 Not Found: /api/v1/workflow/technicians/
WARNING 2025-07-26 06:11:03,118 basehttp 24684 16352 "GET /api/v1/workflow/tasks/? HTTP/1.1" 404 3210
WARNING 2025-07-26 06:11:03,119 basehttp 24684 15468 "GET /api/v1/workflow/technicians/ HTTP/1.1" 404 3228
WARNING 2025-07-26 06:11:25,652 connection 24684 22352 No hostname was supplied. Reverting to default 'localhost'
ERROR 2025-07-26 06:11:31,734 log 24684 22352 Service Unavailable: /health/
ERROR 2025-07-26 06:11:31,735 basehttp 24684 22352 "GET /health/ HTTP/1.1" 503 434
WARNING 2025-07-26 06:13:02,996 log 24684 16352 Not Found: /api/v1/workflow/technicians/
WARNING 2025-07-26 06:13:02,997 basehttp 24684 16352 "GET /api/v1/workflow/technicians/ HTTP/1.1" 404 3228
WARNING 2025-07-26 06:13:02,999 log 24684 15468 Not Found: /api/v1/workflow/tasks/
WARNING 2025-07-26 06:13:03,000 basehttp 24684 15468 "GET /api/v1/workflow/tasks/? HTTP/1.1" 404 3210
ERROR 2025-07-26 06:13:09,448 log 24684 15468 Service Unavailable: /health/
ERROR 2025-07-26 06:13:09,449 basehttp 24684 15468 "GET /health/ HTTP/1.1" 503 434
INFO 2025-07-26 06:13:09,457 basehttp 24684 15468 "OPTIONS /api/v1/cases/ HTTP/1.1" 200 0
INFO 2025-07-26 06:13:09,460 basehttp 24684 15468 "OPTIONS /api/v1/cases/ HTTP/1.1" 200 0
INFO 2025-07-26 06:13:09,469 basehttp 24684 15468 "GET /api/v1/cases/ HTTP/1.1" 200 53
INFO 2025-07-26 06:13:09,478 basehttp 24684 15468 "GET /api/v1/cases/ HTTP/1.1" 200 53
ERROR 2025-07-26 06:13:15,508 log 24684 16352 Service Unavailable: /health/
ERROR 2025-07-26 06:13:15,510 basehttp 24684 16352 "GET /health/ HTTP/1.1" 503 434
INFO 2025-07-26 06:13:15,523 basehttp 24684 16352 "GET /api/v1/cases/ HTTP/1.1" 200 53
INFO 2025-07-26 06:13:15,531 basehttp 24684 16352 "GET /api/v1/cases/ HTTP/1.1" 200 53
WARNING 2025-07-26 06:13:16,146 log 24684 16352 Unauthorized: /api/v1/cases/
WARNING 2025-07-26 06:13:16,147 basehttp 24684 16352 "GET /api/v1/cases/ HTTP/1.1" 401 58
ERROR 2025-07-26 06:13:22,465 log 24684 16352 Service Unavailable: /health/
ERROR 2025-07-26 06:13:22,466 basehttp 24684 16352 "GET /health/ HTTP/1.1" 503 434
INFO 2025-07-26 06:13:22,485 basehttp 24684 16352 "GET /api/v1/cases/ HTTP/1.1" 200 53
INFO 2025-07-26 06:13:22,498 basehttp 24684 10960 "GET /api/v1/cases/ HTTP/1.1" 200 53
WARNING 2025-07-26 06:13:23,535 log 24684 10960 Unauthorized: /api/v1/cases/
WARNING 2025-07-26 06:13:23,537 basehttp 24684 10960 "GET /api/v1/cases/ HTTP/1.1" 401 58
WARNING 2025-07-26 06:13:23,791 connection 24684 10960 No hostname was supplied. Reverting to default 'localhost'
ERROR 2025-07-26 06:13:28,559 log 24684 15468 Service Unavailable: /health/
INFO 2025-07-26 06:13:28,560 basehttp 24684 15468 - Broken pipe from ('127.0.0.1', 5781)
ERROR 2025-07-26 06:13:29,868 log 24684 10960 Service Unavailable: /health/
ERROR 2025-07-26 06:13:29,869 basehttp 24684 10960 "GET /health/ HTTP/1.1" 503 434
INFO 2025-07-26 06:13:29,881 basehttp 24684 10960 "GET /api/v1/cases/ HTTP/1.1" 200 53
INFO 2025-07-26 06:13:29,891 basehttp 24684 6180 "GET /api/v1/cases/ HTTP/1.1" 200 53
ERROR 2025-07-26 06:13:35,979 log 24684 16352 Service Unavailable: /health/
ERROR 2025-07-26 06:13:35,981 basehttp 24684 16352 "GET /health/ HTTP/1.1" 503 434
INFO 2025-07-26 06:13:35,994 basehttp 24684 16352 "GET /api/v1/cases/ HTTP/1.1" 200 53
INFO 2025-07-26 06:13:36,002 basehttp 24684 6180 "GET /api/v1/cases/ HTTP/1.1" 200 53
ERROR 2025-07-26 06:14:00,378 log 24684 6180 Service Unavailable: /health/
ERROR 2025-07-26 06:14:00,379 basehttp 24684 6180 "GET /health/ HTTP/1.1" 503 434
INFO 2025-07-26 06:14:00,396 basehttp 24684 6180 "GET /api/v1/cases/ HTTP/1.1" 200 53
INFO 2025-07-26 06:14:00,405 basehttp 24684 10960 "GET /api/v1/cases/ HTTP/1.1" 200 53
ERROR 2025-07-26 06:14:06,481 log 24684 16352 Service Unavailable: /health/
ERROR 2025-07-26 06:14:06,482 basehttp 24684 16352 "GET /health/ HTTP/1.1" 503 434
INFO 2025-07-26 06:14:06,497 basehttp 24684 16352 "GET /api/v1/cases/ HTTP/1.1" 200 53
INFO 2025-07-26 06:14:06,508 basehttp 24684 10960 "GET /api/v1/cases/ HTTP/1.1" 200 53
