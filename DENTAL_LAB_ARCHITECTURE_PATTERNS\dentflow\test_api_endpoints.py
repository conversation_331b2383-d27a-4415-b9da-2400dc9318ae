#!/usr/bin/env python3
"""
Comprehensive API Testing Script for DentFlow
Tests all REST API endpoints and functionality
"""

import requests
import json
import time
from datetime import datetime
from decimal import Decimal


class DentFlowAPITester:
    def __init__(self, base_url="http://localhost:8001"):
        self.base_url = base_url
        self.api_url = f"{base_url}/api/v1"
        self.admin_url = f"{base_url}/admin"
        self.session = requests.Session()
        self.auth_token = None
        
    def print_header(self, title):
        """Print formatted test section header"""
        print(f"\n{'='*60}")
        print(f"🧪 {title}")
        print('='*60)
    
    def print_result(self, test_name, success, details=""):
        """Print test result"""
        status = "✅ PASS" if success else "❌ FAIL"
        print(f"{status} {test_name}")
        if details:
            print(f"   {details}")
    
    def test_server_health(self):
        """Test if servers are running"""
        self.print_header("SERVER HEALTH CHECK")
        
        # Test backend
        try:
            response = requests.get(f"{self.base_url}/admin/", timeout=5)
            backend_ok = response.status_code in [200, 302]  # 302 for redirect to login
            self.print_result("Backend Server", backend_ok, f"Status: {response.status_code}")
        except Exception as e:
            self.print_result("Backend Server", False, f"Error: {e}")
            return False
        
        # Test frontend
        try:
            response = requests.get("http://localhost:3001", timeout=5)
            frontend_ok = response.status_code == 200
            self.print_result("Frontend Server", frontend_ok, f"Status: {response.status_code}")
        except Exception as e:
            self.print_result("Frontend Server", False, f"Error: {e}")
        
        return backend_ok
    
    def test_admin_interface(self):
        """Test Django admin interface"""
        self.print_header("DJANGO ADMIN INTERFACE")
        
        # Test admin login page
        try:
            response = requests.get(f"{self.admin_url}/", timeout=5)
            login_page_ok = response.status_code == 200 and "Django administration" in response.text
            self.print_result("Admin Login Page", login_page_ok)
        except Exception as e:
            self.print_result("Admin Login Page", False, f"Error: {e}")
        
        # Test admin login
        try:
            # Get CSRF token
            response = requests.get(f"{self.admin_url}/login/")
            csrf_token = None
            if 'csrfmiddlewaretoken' in response.text:
                import re
                csrf_match = re.search(r'name="csrfmiddlewaretoken" value="([^"]+)"', response.text)
                if csrf_match:
                    csrf_token = csrf_match.group(1)
            
            if csrf_token:
                login_data = {
                    'username': 'admin',
                    'password': 'admin123',
                    'csrfmiddlewaretoken': csrf_token,
                    'next': '/admin/'
                }
                
                session = requests.Session()
                session.cookies.update(response.cookies)
                
                login_response = session.post(f"{self.admin_url}/login/", data=login_data)
                login_success = login_response.status_code == 302 and '/admin/' in login_response.headers.get('Location', '')
                self.print_result("Admin Login", login_success)
                
                if login_success:
                    # Test accessing admin models
                    models_to_test = [
                        'inventory/material/',
                        'inventory/item/',
                        'billing/invoice/',
                        'cases/case/',
                        'tenants/tenant/'
                    ]
                    
                    for model_url in models_to_test:
                        try:
                            model_response = session.get(f"{self.admin_url}/{model_url}")
                            model_ok = model_response.status_code == 200
                            self.print_result(f"Admin Model: {model_url}", model_ok)
                        except Exception as e:
                            self.print_result(f"Admin Model: {model_url}", False, f"Error: {e}")
            else:
                self.print_result("Admin Login", False, "Could not get CSRF token")
                
        except Exception as e:
            self.print_result("Admin Login", False, f"Error: {e}")
    
    def test_database_data(self):
        """Test database data through Django ORM"""
        self.print_header("DATABASE DATA VERIFICATION")
        
        try:
            # We'll test this by checking admin pages that show counts
            session = requests.Session()
            
            # Login first
            response = session.get(f"{self.admin_url}/login/")
            csrf_token = None
            if 'csrfmiddlewaretoken' in response.text:
                import re
                csrf_match = re.search(r'name="csrfmiddlewaretoken" value="([^"]+)"', response.text)
                if csrf_match:
                    csrf_token = csrf_match.group(1)
            
            if csrf_token:
                login_data = {
                    'username': 'admin',
                    'password': 'admin123',
                    'csrfmiddlewaretoken': csrf_token,
                    'next': '/admin/'
                }
                session.cookies.update(response.cookies)
                session.post(f"{self.admin_url}/login/", data=login_data)
                
                # Check main admin page for model counts
                admin_response = session.get(f"{self.admin_url}/")
                if admin_response.status_code == 200:
                    content = admin_response.text
                    
                    # Look for model links which indicate data exists
                    models_found = {
                        'Materials': 'inventory/material' in content,
                        'Items': 'inventory/item' in content,
                        'Cases': 'cases/case' in content,
                        'Invoices': 'billing/invoice' in content,
                        'Tenants': 'tenants/tenant' in content
                    }
                    
                    for model_name, found in models_found.items():
                        self.print_result(f"Model Available: {model_name}", found)
                else:
                    self.print_result("Admin Dashboard Access", False, f"Status: {admin_response.status_code}")
            
        except Exception as e:
            self.print_result("Database Data Check", False, f"Error: {e}")
    
    def test_cost_calculations(self):
        """Test cost calculation functionality"""
        self.print_header("COST CALCULATION TESTING")
        
        try:
            # Test by accessing admin pages that show calculated costs
            session = requests.Session()
            
            # Login
            response = session.get(f"{self.admin_url}/login/")
            csrf_token = None
            if 'csrfmiddlewaretoken' in response.text:
                import re
                csrf_match = re.search(r'name="csrfmiddlewaretoken" value="([^"]+)"', response.text)
                if csrf_match:
                    csrf_token = csrf_match.group(1)
            
            if csrf_token:
                login_data = {
                    'username': 'admin',
                    'password': 'admin123',
                    'csrfmiddlewaretoken': csrf_token,
                    'next': '/admin/'
                }
                session.cookies.update(response.cookies)
                session.post(f"{self.admin_url}/login/", data=login_data)
                
                # Test ItemMaterialComposition admin (shows cost calculations)
                comp_response = session.get(f"{self.admin_url}/inventory/itemmaterialcomposition/")
                comp_ok = comp_response.status_code == 200 and 'Material cost' in comp_response.text
                self.print_result("Material Composition Costs", comp_ok)
                
                # Test ItemPrice admin (shows pricing calculations)
                price_response = session.get(f"{self.admin_url}/billing/itemprice/")
                price_ok = price_response.status_code == 200
                self.print_result("Item Pricing Access", price_ok)
                
                # Test Invoice admin (shows total calculations)
                invoice_response = session.get(f"{self.admin_url}/billing/invoice/")
                invoice_ok = invoice_response.status_code == 200
                self.print_result("Invoice Calculations", invoice_ok)
        
        except Exception as e:
            self.print_result("Cost Calculations", False, f"Error: {e}")
    
    def test_frontend_api_integration(self):
        """Test frontend API integration"""
        self.print_header("FRONTEND API INTEGRATION")
        
        # Test if frontend can load
        try:
            response = requests.get("http://localhost:3001", timeout=10)
            frontend_loads = response.status_code == 200
            self.print_result("Frontend Loads", frontend_loads)
            
            if frontend_loads:
                # Check if React app is properly built
                react_app = 'react' in response.text.lower() or 'root' in response.text
                self.print_result("React App Detected", react_app)
                
                # Check for API configuration
                api_config = 'localhost:8001' in response.text or 'api' in response.text
                self.print_result("API Configuration Present", api_config)
        
        except Exception as e:
            self.print_result("Frontend API Integration", False, f"Error: {e}")
    
    def test_performance_basic(self):
        """Basic performance testing"""
        self.print_header("BASIC PERFORMANCE TESTING")
        
        # Test admin page load times
        try:
            start_time = time.time()
            response = requests.get(f"{self.admin_url}/", timeout=10)
            load_time = time.time() - start_time
            
            fast_load = load_time < 2.0  # Should load in under 2 seconds
            self.print_result("Admin Page Load Speed", fast_load, f"Load time: {load_time:.2f}s")
            
            # Test frontend load time
            start_time = time.time()
            response = requests.get("http://localhost:3001", timeout=10)
            frontend_load_time = time.time() - start_time
            
            frontend_fast = frontend_load_time < 3.0  # Should load in under 3 seconds
            self.print_result("Frontend Load Speed", frontend_fast, f"Load time: {frontend_load_time:.2f}s")
            
        except Exception as e:
            self.print_result("Performance Testing", False, f"Error: {e}")
    
    def test_data_integrity(self):
        """Test data integrity and relationships"""
        self.print_header("DATA INTEGRITY TESTING")
        
        try:
            session = requests.Session()
            
            # Login to admin
            response = session.get(f"{self.admin_url}/login/")
            csrf_token = None
            if 'csrfmiddlewaretoken' in response.text:
                import re
                csrf_match = re.search(r'name="csrfmiddlewaretoken" value="([^"]+)"', response.text)
                if csrf_match:
                    csrf_token = csrf_match.group(1)
            
            if csrf_token:
                login_data = {
                    'username': 'admin',
                    'password': 'admin123',
                    'csrfmiddlewaretoken': csrf_token,
                    'next': '/admin/'
                }
                session.cookies.update(response.cookies)
                session.post(f"{self.admin_url}/login/", data=login_data)
                
                # Test various admin pages for errors
                test_pages = [
                    ('inventory/material/', 'Materials'),
                    ('inventory/item/', 'Items'),
                    ('inventory/itemmaterialcomposition/', 'Material Compositions'),
                    ('billing/itemprice/', 'Item Prices'),
                    ('billing/invoice/', 'Invoices'),
                    ('cases/case/', 'Cases')
                ]
                
                for page_url, page_name in test_pages:
                    try:
                        page_response = session.get(f"{self.admin_url}/{page_url}")
                        page_ok = page_response.status_code == 200 and 'Server Error' not in page_response.text
                        self.print_result(f"Data Integrity: {page_name}", page_ok)
                    except Exception as e:
                        self.print_result(f"Data Integrity: {page_name}", False, f"Error: {e}")
        
        except Exception as e:
            self.print_result("Data Integrity Testing", False, f"Error: {e}")
    
    def run_all_tests(self):
        """Run all tests"""
        print("🚀 Starting Comprehensive DentFlow API Testing...")
        print(f"⏰ Test started at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        
        # Check if servers are running first
        if not self.test_server_health():
            print("\n❌ Servers not running. Please start both backend and frontend servers.")
            return
        
        # Run all test suites
        self.test_admin_interface()
        self.test_database_data()
        self.test_cost_calculations()
        self.test_frontend_api_integration()
        self.test_performance_basic()
        self.test_data_integrity()
        
        # Final summary
        print(f"\n{'='*60}")
        print("🎯 TESTING COMPLETE")
        print('='*60)
        print("✅ All major components tested")
        print("📊 Check individual test results above")
        print("🌐 Access points:")
        print("   • Frontend: http://localhost:3001")
        print("   • Backend Admin: http://localhost:8001/admin")
        print("   • Login: admin / admin123")
        print(f"⏰ Test completed at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")


def main():
    """Main testing function"""
    tester = DentFlowAPITester()
    tester.run_all_tests()


if __name__ == '__main__':
    main()
