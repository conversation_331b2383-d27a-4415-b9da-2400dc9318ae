"""
Django Admin for Cases App
Professional admin interface for dental case management
"""

from django.contrib import admin
from django.utils.html import format_html
from django.urls import reverse
from django.utils import timezone
from apps.cases.models import Case, Clinic, CaseEvent


@admin.register(Clinic)
class ClinicAdmin(admin.ModelAdmin):
    """Admin interface for Clinics"""
    list_display = ['name', 'tenant', 'email', 'phone', 'case_count', 'last_case']
    list_filter = ['tenant', 'name']
    search_fields = ['name', 'email', 'phone']
    readonly_fields = ['id']
    
    fieldsets = (
        ('Basic Information', {
            'fields': ('tenant', 'name', 'email', 'phone')
        }),
        ('Address', {
            'fields': ('address',)
        }),
        ('System Information', {
            'fields': ('id',),
            'classes': ('collapse',)
        }),
    )
    
    def case_count(self, obj):
        """Show number of cases from this clinic"""
        count = Case.objects.filter(clinic=obj).count()
        url = reverse('admin:cases_case_changelist') + f'?clinic__id__exact={obj.id}'
        return format_html('<a href="{}">{} cases</a>', url, count)
    case_count.short_description = 'Cases'
    
    def last_case(self, obj):
        """Show last case from this clinic"""
        last_case = Case.objects.filter(clinic=obj).order_by('-created_at').first()
        if last_case:
            url = reverse('admin:cases_case_change', args=[last_case.id])
            return format_html('<a href="{}">{}</a>', url, last_case.id)
        return 'No cases'
    last_case.short_description = 'Last Case'


@admin.register(Case)
class CaseAdmin(admin.ModelAdmin):
    """Admin interface for Dental Cases"""
    list_display = [
        'id', 'patient_name', 'clinic', 'service_type', 'priority_badge', 
        'status_badge', 'current_stage', 'assigned_technician_id', 
        'due_date_display', 'created_at'
    ]
    list_filter = [
        'status', 'priority', 'service_type', 'tenant', 'clinic', 
        'created_at', 'due_date'
    ]
    search_fields = [
        'id', 'patient_name', 'tooth_number', 'clinic__name', 
        'assigned_technician_id'
    ]
    readonly_fields = [
        'id', 'created_at', 'updated_at', 'days_until_due_display', 
        'is_overdue_display'
    ]
    
    fieldsets = (
        ('Case Information', {
            'fields': (
                'id', 'tenant', 'clinic', 'patient_name', 'tooth_number', 
                'service_type'
            )
        }),
        ('Workflow & Status', {
            'fields': (
                'priority', 'status', 'current_stage_index', 'workflow_stages',
                'assigned_technician_id'
            )
        }),
        ('Scheduling', {
            'fields': ('due_date', 'days_until_due_display', 'is_overdue_display')
        }),
        ('Additional Information', {
            'fields': ('notes', 'files'),
            'classes': ('collapse',)
        }),
        ('System Information', {
            'fields': ('created_at', 'updated_at', 'deleted_at'),
            'classes': ('collapse',)
        }),
    )
    
    def priority_badge(self, obj):
        """Display priority with color badge"""
        colors = {
            'low': '#4caf50',
            'normal': '#2196f3', 
            'urgent': '#ff9800',
            'stat': '#f44336'
        }
        color = colors.get(obj.priority, '#666')
        return format_html(
            '<span style="background: {}; color: white; padding: 2px 8px; '
            'border-radius: 12px; font-size: 11px; font-weight: bold;">{}</span>',
            color, obj.priority.upper()
        )
    priority_badge.short_description = 'Priority'
    
    def status_badge(self, obj):
        """Display status with color badge"""
        colors = {
            'received': '#2196f3',
            'design': '#ff9800',
            'milling': '#9c27b0',
            'sintering': '#e91e63',
            'qc': '#ff5722',
            'shipped': '#4caf50',
            'delivered': '#8bc34a',
            'cancelled': '#757575'
        }
        color = colors.get(obj.status, '#666')
        return format_html(
            '<span style="background: {}; color: white; padding: 2px 8px; '
            'border-radius: 12px; font-size: 11px; font-weight: bold;">{}</span>',
            color, obj.status.upper()
        )
    status_badge.short_description = 'Status'

    def current_stage(self, obj):
        """Display current workflow stage"""
        if (obj.current_stage_index < len(obj.workflow_stages) and
            obj.workflow_stages):
            stage_name = obj.workflow_stages[obj.current_stage_index].get('name', '')
            return stage_name
        return obj.status.title()
    current_stage.short_description = 'Current Stage'

    def due_date_display(self, obj):
        """Display due date with overdue warning"""
        if not obj.due_date:
            return 'Not set'
        
        is_overdue = timezone.now() > obj.due_date
        if is_overdue:
            return format_html(
                '<span style="color: #f44336; font-weight: bold;">⚠️ {}</span>',
                obj.due_date.strftime('%Y-%m-%d %H:%M')
            )
        return obj.due_date.strftime('%Y-%m-%d %H:%M')
    due_date_display.short_description = 'Due Date'
    
    def days_until_due_display(self, obj):
        """Show days until due date"""
        if not obj.due_date:
            return 'Not set'
        
        delta = obj.due_date - timezone.now()
        days = delta.days
        
        if days < 0:
            return format_html(
                '<span style="color: #f44336; font-weight: bold;">{} days overdue</span>',
                abs(days)
            )
        elif days == 0:
            return format_html('<span style="color: #ff9800; font-weight: bold;">Due today</span>')
        elif days <= 1:
            return format_html('<span style="color: #ff9800;">{} day</span>', days)
        else:
            return f'{days} days'
    days_until_due_display.short_description = 'Days Until Due'
    
    def is_overdue_display(self, obj):
        """Show if case is overdue"""
        if not obj.due_date:
            return 'N/A'
        
        is_overdue = timezone.now() > obj.due_date
        if is_overdue:
            return format_html('<span style="color: #f44336;">❌ Yes</span>')
        return format_html('<span style="color: #4caf50;">✅ No</span>')
    is_overdue_display.short_description = 'Overdue'
    
    actions = ['mark_as_urgent', 'mark_as_normal', 'mark_as_qc']
    
    def mark_as_urgent(self, request, queryset):
        """Mark selected cases as urgent priority"""
        updated = queryset.update(priority='urgent')
        self.message_user(request, f'{updated} cases marked as urgent.')
    mark_as_urgent.short_description = 'Mark as urgent priority'
    
    def mark_as_normal(self, request, queryset):
        """Mark selected cases as normal priority"""
        updated = queryset.update(priority='normal')
        self.message_user(request, f'{updated} cases marked as normal priority.')
    mark_as_normal.short_description = 'Mark as normal priority'
    
    def mark_as_qc(self, request, queryset):
        """Move selected cases to quality control"""
        updated = queryset.update(status='qc')
        self.message_user(request, f'{updated} cases moved to quality control.')
    mark_as_qc.short_description = 'Move to quality control'


@admin.register(CaseEvent)
class CaseEventAdmin(admin.ModelAdmin):
    """Admin interface for Case Events (Audit Trail)"""
    list_display = ['case', 'event_type', 'created_at', 'event_summary']
    list_filter = ['event_type', 'created_at', 'case__status']
    search_fields = ['case__id', 'case__patient_name', 'event_type']
    readonly_fields = ['id', 'case', 'event_type', 'event_data', 'created_at']
    
    def event_summary(self, obj):
        """Show a summary of the event data"""
        if isinstance(obj.event_data, dict):
            # Extract key information from event data
            summary_parts = []
            for key, value in obj.event_data.items():
                if key in ['status', 'technician_id', 'notes']:
                    summary_parts.append(f'{key}: {value}')
            return ' | '.join(summary_parts[:3])  # Limit to 3 items
        return str(obj.event_data)[:50] + '...' if len(str(obj.event_data)) > 50 else str(obj.event_data)
    event_summary.short_description = 'Event Summary'
    
    def has_add_permission(self, request):
        """Prevent manual creation of events"""
        return False
    
    def has_change_permission(self, request, obj=None):
        """Make events read-only"""
        return False


# Custom admin site configuration
admin.site.site_header = 'DentFlow Administration'
admin.site.site_title = 'DentFlow Admin'
admin.site.index_title = 'Dental Laboratory Management System'