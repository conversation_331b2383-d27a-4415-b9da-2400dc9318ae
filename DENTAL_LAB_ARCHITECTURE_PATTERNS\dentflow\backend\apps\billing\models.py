"""
Billing Models for DentFlow
Financial management, invoicing, and payment tracking
"""

from django.db import models
from django.core.validators import MinValueValidator
from decimal import Decimal
import uuid


class PriceList(models.Model):
    """
    Price lists for different services offered by dental lab
    """
    
    CURRENCY_CHOICES = [
        ('USD', 'US Dollar'),
        ('EUR', 'Euro'),
        ('GBP', 'British Pound'),
        ('CAD', 'Canadian Dollar'),
    ]
    
    id = models.UUIDField(primary_key=True, default=uuid.uuid4)
    tenant = models.ForeignKey('tenants.Tenant', on_delete=models.CASCADE, related_name='price_lists')
    name = models.CharField(max_length=255)
    description = models.TextField(blank=True)
    currency = models.CharField(max_length=3, choices=CURRENCY_CHOICES, default='USD')
    is_active = models.BooleanField(default=True)
    effective_date = models.DateField()
    
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    class Meta:
        indexes = [
            models.Index(fields=['tenant', 'is_active']),
            models.Index(fields=['effective_date']),
        ]
        unique_together = ['tenant', 'name']
    
    def __str__(self):
        return f'{self.name} ({self.currency})'


class ItemPrice(models.Model):
    """
    Pricing for specific items within a price list
    Links to the inventory Item model for proper cost calculation
    """

    id = models.UUIDField(primary_key=True, default=uuid.uuid4)
    price_list = models.ForeignKey(PriceList, on_delete=models.CASCADE, related_name='item_prices')
    item = models.ForeignKey('inventory.Item', on_delete=models.CASCADE, related_name='prices')

    # Pricing details
    base_price = models.DecimalField(max_digits=10, decimal_places=2, validators=[MinValueValidator(Decimal('0.01'))])
    material_cost_override = models.DecimalField(max_digits=10, decimal_places=2, null=True, blank=True, help_text="Override calculated material cost")
    labor_cost_override = models.DecimalField(max_digits=10, decimal_places=2, null=True, blank=True, help_text="Override calculated labor cost")

    # Discounts and adjustments
    discount_percentage = models.DecimalField(max_digits=5, decimal_places=2, default=Decimal('0.00'))

    is_active = models.BooleanField(default=True)

    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        indexes = [
            models.Index(fields=['price_list', 'is_active']),
            models.Index(fields=['item', 'is_active']),
        ]
        unique_together = ['price_list', 'item']

    def get_material_cost(self):
        """Get material cost (override or calculated from item)"""
        if self.material_cost_override is not None:
            return self.material_cost_override
        return self.item.calculate_material_cost()

    def get_labor_cost(self):
        """Get labor cost (override or from item)"""
        if self.labor_cost_override is not None:
            return self.labor_cost_override
        return self.item.base_labor_cost

    def get_total_cost(self):
        """Get total cost (materials + labor)"""
        return self.get_material_cost() + self.get_labor_cost()

    def get_final_price(self):
        """Get final price after discount"""
        discount_amount = self.base_price * (self.discount_percentage / Decimal('100'))
        return self.base_price - discount_amount

    def __str__(self):
        return f'{self.item.name} - ${self.base_price}'


# Keep ServicePrice for backward compatibility but mark as deprecated
class ServicePrice(models.Model):
    """
    DEPRECATED: Individual service pricing within a price list
    Use ItemPrice instead for new implementations
    """

    SERVICE_TYPE_CHOICES = [
        ('crown', 'Crown'),
        ('bridge', 'Bridge'),
        ('denture', 'Denture'),
        ('implant', 'Implant Crown'),
        ('veneer', 'Veneer'),
        ('inlay', 'Inlay'),
        ('onlay', 'Onlay'),
        ('nightguard', 'Night Guard'),
        ('retainer', 'Retainer'),
    ]

    id = models.UUIDField(primary_key=True, default=uuid.uuid4)
    price_list = models.ForeignKey(PriceList, on_delete=models.CASCADE, related_name='service_prices')
    service_type = models.CharField(max_length=50, choices=SERVICE_TYPE_CHOICES)
    description = models.CharField(max_length=255, blank=True)
    base_price = models.DecimalField(max_digits=10, decimal_places=2, validators=[MinValueValidator(Decimal('0.01'))])
    is_active = models.BooleanField(default=True)

    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        indexes = [
            models.Index(fields=['price_list', 'service_type']),
            models.Index(fields=['is_active']),
        ]
        unique_together = ['price_list', 'service_type']

    def __str__(self):
        return f'{self.service_type} - ${self.base_price}'


class Invoice(models.Model):
    """
    Invoices sent to dental clinics
    """
    
    STATUS_CHOICES = [
        ('draft', 'Draft'),
        ('sent', 'Sent'),
        ('paid', 'Paid'),
        ('overdue', 'Overdue'),
        ('cancelled', 'Cancelled'),
    ]
    
    CURRENCY_CHOICES = [
        ('USD', 'US Dollar'),
        ('EUR', 'Euro'),
        ('GBP', 'British Pound'),
        ('CAD', 'Canadian Dollar'),
    ]
    
    id = models.UUIDField(primary_key=True, default=uuid.uuid4)
    invoice_number = models.CharField(max_length=50, unique=True)
    tenant = models.ForeignKey('tenants.Tenant', on_delete=models.CASCADE, related_name='invoices')
    clinic = models.ForeignKey('cases.Clinic', on_delete=models.CASCADE, related_name='invoices')
    
    # Financial details
    subtotal = models.DecimalField(max_digits=10, decimal_places=2, default=Decimal('0.00'))
    tax_amount = models.DecimalField(max_digits=10, decimal_places=2, default=Decimal('0.00'))
    total_amount = models.DecimalField(max_digits=10, decimal_places=2, default=Decimal('0.00'))
    currency = models.CharField(max_length=3, choices=CURRENCY_CHOICES, default='USD')
    
    # Status and dates
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='draft')
    due_date = models.DateField()
    
    # Additional information
    notes = models.TextField(blank=True)
    payment_terms = models.CharField(max_length=255, default='Net 30')
    
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    class Meta:
        indexes = [
            models.Index(fields=['tenant', 'status']),
            models.Index(fields=['clinic', 'status']),
            models.Index(fields=['due_date']),
            models.Index(fields=['invoice_number']),
        ]
        ordering = ['-created_at']
    
    def save(self, *args, **kwargs):
        """Auto-generate invoice number if not provided"""
        if not self.invoice_number:
            # Generate invoice number: INV-YYYY-NNNN
            from django.utils import timezone
            year = timezone.now().year
            last_invoice = Invoice.objects.filter(
                invoice_number__startswith=f'INV-{year}'
            ).order_by('-invoice_number').first()
            
            if last_invoice:
                last_number = int(last_invoice.invoice_number.split('-')[-1])
                new_number = last_number + 1
            else:
                new_number = 1
            
            self.invoice_number = f'INV-{year}-{new_number:04d}'
        
        super().save(*args, **kwargs)
    
    def __str__(self):
        return f'{self.invoice_number} - {self.clinic.name}'


class InvoiceItem(models.Model):
    """
    Individual line items on an invoice with detailed cost breakdown
    """

    ITEM_TYPE_CHOICES = [
        ('item', 'Dental Item'),
        ('service', 'Service Only'),
        ('material', 'Material Only'),
        ('adjustment', 'Adjustment'),
    ]

    id = models.UUIDField(primary_key=True, default=uuid.uuid4)
    invoice = models.ForeignKey(Invoice, on_delete=models.CASCADE, related_name='items')
    case = models.ForeignKey('cases.Case', on_delete=models.CASCADE, null=True, blank=True)

    # Item references
    item = models.ForeignKey('inventory.Item', on_delete=models.CASCADE, null=True, blank=True, help_text="Reference to inventory item")
    item_type = models.CharField(max_length=20, choices=ITEM_TYPE_CHOICES, default='item')

    # Basic details
    description = models.CharField(max_length=255)
    quantity = models.PositiveIntegerField(default=1)

    # Cost breakdown
    material_cost_per_unit = models.DecimalField(max_digits=10, decimal_places=4, default=Decimal('0.0000'))
    labor_cost_per_unit = models.DecimalField(max_digits=10, decimal_places=4, default=Decimal('0.0000'))
    unit_price = models.DecimalField(max_digits=10, decimal_places=2)

    # Calculated totals
    total_material_cost = models.DecimalField(max_digits=10, decimal_places=2, default=Decimal('0.00'))
    total_labor_cost = models.DecimalField(max_digits=10, decimal_places=2, default=Decimal('0.00'))
    total_price = models.DecimalField(max_digits=10, decimal_places=2)

    # Discounts and adjustments
    discount_percentage = models.DecimalField(max_digits=5, decimal_places=2, default=Decimal('0.00'))
    discount_amount = models.DecimalField(max_digits=10, decimal_places=2, default=Decimal('0.00'))

    # Additional information
    notes = models.TextField(blank=True)

    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        indexes = [
            models.Index(fields=['invoice']),
            models.Index(fields=['case']),
            models.Index(fields=['item']),
            models.Index(fields=['item_type']),
        ]

    def save(self, *args, **kwargs):
        """Auto-calculate totals and populate costs from item if available"""

        # If item is linked, populate costs from item
        if self.item and self.item_type == 'item':
            self.material_cost_per_unit = self.item.calculate_material_cost()
            self.labor_cost_per_unit = self.item.base_labor_cost

            # If unit_price is not set, calculate from item
            if not self.unit_price:
                self.unit_price = self.item.calculate_selling_price()

        # Calculate totals
        self.total_material_cost = self.material_cost_per_unit * self.quantity
        self.total_labor_cost = self.labor_cost_per_unit * self.quantity

        # Calculate discount
        if self.discount_percentage > 0:
            gross_total = self.quantity * self.unit_price
            self.discount_amount = gross_total * (self.discount_percentage / Decimal('100'))
            self.total_price = gross_total - self.discount_amount
        else:
            self.total_price = self.quantity * self.unit_price

        super().save(*args, **kwargs)

    def get_material_cost_percentage(self):
        """Calculate what percentage of the total price is material cost"""
        if self.total_price > 0:
            return (self.total_material_cost / self.total_price) * Decimal('100')
        return Decimal('0.00')

    def get_labor_cost_percentage(self):
        """Calculate what percentage of the total price is labor cost"""
        if self.total_price > 0:
            return (self.total_labor_cost / self.total_price) * Decimal('100')
        return Decimal('0.00')

    def get_profit_margin(self):
        """Calculate profit margin (total price - material cost - labor cost)"""
        return self.total_price - self.total_material_cost - self.total_labor_cost

    def get_profit_percentage(self):
        """Calculate profit percentage"""
        cost_base = self.total_material_cost + self.total_labor_cost
        if cost_base > 0:
            profit = self.get_profit_margin()
            return (profit / cost_base) * Decimal('100')
        return Decimal('0.00')

    def __str__(self):
        return f'{self.description} - ${self.total_price}'


class InvoiceItemMaterialUsage(models.Model):
    """
    Tracks actual material usage for each invoice item
    This creates the link between billing and inventory consumption
    """

    id = models.UUIDField(primary_key=True, default=uuid.uuid4)
    invoice_item = models.ForeignKey(InvoiceItem, on_delete=models.CASCADE, related_name='material_usage')
    material = models.ForeignKey('inventory.Material', on_delete=models.CASCADE, related_name='invoice_usage')

    # Usage details
    planned_quantity = models.DecimalField(max_digits=10, decimal_places=4, help_text="Planned quantity from item composition")
    actual_quantity = models.DecimalField(max_digits=10, decimal_places=4, help_text="Actual quantity used")
    unit_cost = models.DecimalField(max_digits=10, decimal_places=4, help_text="Cost per unit at time of usage")
    total_cost = models.DecimalField(max_digits=10, decimal_places=2, help_text="Total cost for this material usage")

    # Tracking information
    batch_number = models.CharField(max_length=100, blank=True)
    usage_date = models.DateTimeField(auto_now_add=True)
    notes = models.TextField(blank=True)

    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        indexes = [
            models.Index(fields=['invoice_item']),
            models.Index(fields=['material']),
            models.Index(fields=['usage_date']),
        ]
        unique_together = ['invoice_item', 'material']

    def save(self, *args, **kwargs):
        """Auto-calculate total cost"""
        self.total_cost = self.actual_quantity * self.unit_cost
        super().save(*args, **kwargs)

    def get_variance(self):
        """Calculate variance between planned and actual usage"""
        return self.actual_quantity - self.planned_quantity

    def get_variance_percentage(self):
        """Calculate variance percentage"""
        if self.planned_quantity > 0:
            variance = self.get_variance()
            return (variance / self.planned_quantity) * Decimal('100')
        return Decimal('0.00')

    def __str__(self):
        return f"{self.invoice_item.description} - {self.material.name}: {self.actual_quantity} {self.material.unit_of_measure}"


class Payment(models.Model):
    """
    Payment records for invoices
    """
    
    PAYMENT_METHOD_CHOICES = [
        ('credit_card', 'Credit Card'),
        ('debit_card', 'Debit Card'),
        ('bank_transfer', 'Bank Transfer'),
        ('check', 'Check'),
        ('cash', 'Cash'),
        ('other', 'Other'),
    ]
    
    STATUS_CHOICES = [
        ('pending', 'Pending'),
        ('completed', 'Completed'),
        ('failed', 'Failed'),
        ('refunded', 'Refunded'),
    ]
    
    CURRENCY_CHOICES = Invoice.CURRENCY_CHOICES
    
    id = models.UUIDField(primary_key=True, default=uuid.uuid4)
    payment_id = models.CharField(max_length=50, unique=True)
    invoice = models.ForeignKey(Invoice, on_delete=models.CASCADE, related_name='payments')
    
    # Payment details
    amount = models.DecimalField(max_digits=10, decimal_places=2, validators=[MinValueValidator(Decimal('0.01'))])
    currency = models.CharField(max_length=3, choices=CURRENCY_CHOICES, default='USD')
    payment_method = models.CharField(max_length=20, choices=PAYMENT_METHOD_CHOICES)
    
    # Status and dates
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='pending')
    payment_date = models.DateTimeField()
    
    # Transaction details
    reference_number = models.CharField(max_length=100, blank=True)
    processor_response = models.JSONField(default=dict, blank=True)
    
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    class Meta:
        indexes = [
            models.Index(fields=['invoice', 'status']),
            models.Index(fields=['payment_date']),
            models.Index(fields=['payment_id']),
        ]
        ordering = ['-payment_date']
    
    def save(self, *args, **kwargs):
        """Auto-generate payment ID if not provided"""
        if not self.payment_id:
            # Generate payment ID: PAY-YYYYMMDD-NNNN
            from django.utils import timezone
            today = timezone.now().date()
            date_str = today.strftime('%Y%m%d')
            
            last_payment = Payment.objects.filter(
                payment_id__startswith=f'PAY-{date_str}'
            ).order_by('-payment_id').first()
            
            if last_payment:
                last_number = int(last_payment.payment_id.split('-')[-1])
                new_number = last_number + 1
            else:
                new_number = 1
            
            self.payment_id = f'PAY-{date_str}-{new_number:04d}'
        
        super().save(*args, **kwargs)
    
    def __str__(self):
        return f'{self.payment_id} - ${self.amount}'