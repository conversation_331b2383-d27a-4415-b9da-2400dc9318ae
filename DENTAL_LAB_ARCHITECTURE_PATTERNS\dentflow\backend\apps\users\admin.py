"""
Django Admin for Users App
User management and authentication for DentFlow
"""

from django.contrib import admin
from django.contrib.auth.admin import UserAdmin as BaseUserAdmin
from django.contrib.auth.models import User, Group
from django.utils.html import format_html
from django.urls import reverse


# Unregister the default User admin (disabled for development)
# admin.site.unregister(User)
admin.site.unregister(Group)


class UserProfileInline(admin.StackedInline):
    """Inline for user profile information"""
    model = None  # Will be defined when UserProfile model is created
    can_delete = False
    verbose_name_plural = 'Profile'
    extra = 0


@admin.register(User)
class UserAdmin(BaseUserAdmin):
    """Enhanced User Admin for DentFlow"""
    list_display = [
        'username', 'email', 'first_name', 'last_name', 'role_display',
        'tenant_display', 'is_active', 'is_staff', 'last_login', 'date_joined'
    ]
    list_filter = [
        'is_active', 'is_staff', 'is_superuser', 'date_joined', 'last_login'
    ]
    search_fields = ['username', 'first_name', 'last_name', 'email']
    
    fieldsets = (
        ('Authentication', {
            'fields': ('username', 'password')
        }),
        ('Personal Info', {
            'fields': ('first_name', 'last_name', 'email')
        }),
        ('Permissions', {
            'fields': ('is_active', 'is_staff', 'is_superuser', 'groups', 'user_permissions'),
            'classes': ('collapse',)
        }),
        ('Important Dates', {
            'fields': ('last_login', 'date_joined'),
            'classes': ('collapse',)
        }),
    )
    
    readonly_fields = ['last_login', 'date_joined']
    
    def role_display(self, obj):
        """Display user role based on groups"""
        groups = obj.groups.all()
        if not groups:
            if obj.is_superuser:
                return format_html('<span style="color: #f44336; font-weight: bold;">Super Admin</span>')
            elif obj.is_staff:
                return format_html('<span style="color: #ff9800; font-weight: bold;">Staff</span>')
            else:
                return format_html('<span style="color: #666;">User</span>')
        
        role_names = [group.name for group in groups]
        return ', '.join(role_names)
    role_display.short_description = 'Role'
    
    def tenant_display(self, obj):
        """Display associated tenant"""
        # This will be implemented when UserProfile is created
        return 'N/A'
    tenant_display.short_description = 'Tenant'
    
    actions = ['activate_users', 'deactivate_users']
    
    def activate_users(self, request, queryset):
        """Activate selected users"""
        updated = queryset.update(is_active=True)
        self.message_user(request, f'{updated} users activated.')
    activate_users.short_description = 'Activate selected users'
    
    def deactivate_users(self, request, queryset):
        """Deactivate selected users"""
        updated = queryset.update(is_active=False)
        self.message_user(request, f'{updated} users deactivated.')
    deactivate_users.short_description = 'Deactivate selected users'


@admin.register(Group)
class GroupAdmin(admin.ModelAdmin):
    """Enhanced Group Admin for role management"""
    list_display = ['name', 'user_count', 'permission_count']
    search_fields = ['name']
    filter_horizontal = ['permissions']
    
    def user_count(self, obj):
        """Show number of users in this group"""
        count = obj.user_set.count()
        if count > 0:
            url = reverse('admin:auth_user_changelist') + f'?groups__id__exact={obj.id}'
            return format_html('<a href="{}">{} users</a>', url, count)
        return '0 users'
    user_count.short_description = 'Users'
    
    def permission_count(self, obj):
        """Show number of permissions in this group"""
        return obj.permissions.count()
    permission_count.short_description = 'Permissions'


# Create default groups for DentFlow
def create_default_groups():
    """Create default user groups for DentFlow"""
    from django.contrib.contenttypes.models import ContentType
    from django.contrib.auth.models import Permission
    
    # Define role permissions
    role_permissions = {
        'Lab Administrator': [
            'add_case', 'change_case', 'delete_case', 'view_case',
            'add_clinic', 'change_clinic', 'delete_clinic', 'view_clinic',
            'view_tenant', 'change_tenant'
        ],
        'Lab Manager': [
            'add_case', 'change_case', 'view_case',
            'view_clinic', 'change_clinic'
        ],
        'Technician': [
            'view_case', 'change_case'
        ],
        'Quality Control': [
            'view_case', 'change_case'
        ],
        'Clinic Staff': [
            'add_case', 'view_case'
        ]
    }
    
    for role_name, permission_codenames in role_permissions.items():
        group, created = Group.objects.get_or_create(name=role_name)
        if created:
            print(f'Created group: {role_name}')
        
        # Add permissions to group
        for codename in permission_codenames:
            try:
                permission = Permission.objects.get(codename=codename)
                group.permissions.add(permission)
            except Permission.DoesNotExist:
                print(f'Permission {codename} not found')


# Auto-create groups when admin is loaded
try:
    create_default_groups()
except Exception as e:
    print(f'Could not create default groups: {e}')