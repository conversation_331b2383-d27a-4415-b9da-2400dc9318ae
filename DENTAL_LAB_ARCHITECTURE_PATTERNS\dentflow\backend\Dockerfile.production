# Multi-stage Dockerfile for Production
FROM python:3.11-slim as base

# Set environment variables
ENV PYTHONDONTWRITEBYTECODE=1 \
    PYTHONUNBUFFERED=1 \
    PYTHONHASHSEED=random \
    PIP_NO_CACHE_DIR=1 \
    PIP_DISABLE_PIP_VERSION_CHECK=1

# Install system dependencies
RUN apt-get update && apt-get install -y \
    build-essential \
    libpq-dev \
    curl \
    && rm -rf /var/lib/apt/lists/*

# Create app user
RUN groupadd -r dentflow && useradd -r -g dentflow dentflow

# Set work directory
WORKDIR /app

# Install Python dependencies
COPY requirements.txt .
COPY requirements-production.txt .
RUN pip install --no-cache-dir -r requirements-production.txt

# Development stage
FROM base as development
COPY requirements-dev.txt .
RUN pip install --no-cache-dir -r requirements-dev.txt
COPY . .
RUN chown -R dentflow:dentflow /app
USER dentflow
CMD ["python", "manage.py", "runserver", "0.0.0.0:8000"]

# Production stage
FROM base as production

# Copy application code
COPY . .

# Collect static files
RUN python manage.py collectstatic --noinput

# Set proper ownership
RUN chown -R dentflow:dentflow /app

# Create volume mount points
RUN mkdir -p /app/static /app/media /app/logs \
    && chown -R dentflow:dentflow /app/static /app/media /app/logs

# Switch to non-root user
USER dentflow

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=60s --retries=3 \
    CMD python manage.py health_check

# Expose port
EXPOSE 8000

# Start application with Gunicorn
CMD ["gunicorn", "--bind", "0.0.0.0:8000", "--workers", "4", "--timeout", "30", \
     "--keep-alive", "2", "--max-requests", "1000", "--max-requests-jitter", "50", \
     "dentflow_project.wsgi:application"]