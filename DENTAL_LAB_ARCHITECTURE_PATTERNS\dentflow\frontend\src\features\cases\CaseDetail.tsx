/**
 * Case Detail Component - Comprehensive Enhancement
 * Professional case detail interface with tabbed layout and advanced features
 */

import React, { useState } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import {
  Box,
  Typography,
  Paper,
  CircularProgress,
  Grid,
  Card,
  CardContent,
  Chip,
  Button,
  IconButton,
  Avatar,
  LinearProgress,
  Tabs,
  Tab,

  Stepper,
  Step,
  StepLabel,
  StepContent,
  Divider,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  ListItemSecondaryAction,
  TextField,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Alert,
  Breadcrumbs,
  Link,
  useTheme,
  alpha,
} from '@mui/material';
import {
  ArrowBack,
  Edit,
  Delete,
  Assignment,
  CheckCircle,
  Warning,
  Schedule,
  AttachFile,
  Comment,
  Person,
  Business,
  CalendarToday,
  Phone,
  Email,
  LocationOn,
  Print,
  Share,
  Download,
  Add,
  Visibility,
  PhotoCamera,
  Description,
  PictureAsPdf,
  Image,
  InsertDriveFile,
  Send,
  Save,
  Cancel,
  Timeline as TimelineIcon,
  Assessment,
  MonetizationOn,
  Build,
  Science,
  LocalShipping,
  Done,
  NavigateNext,
} from '@mui/icons-material';
import { useCase } from '../../hooks/api';
import { statusColors, priorityColors } from '../../theme';

interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

function TabPanel(props: TabPanelProps) {
  const { children, value, index, ...other } = props;

  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`case-tabpanel-${index}`}
      aria-labelledby={`case-tab-${index}`}
      {...other}
    >
      {value === index && <Box sx={{ p: 3 }}>{children}</Box>}
    </div>
  );
}

function CaseDetail() {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const theme = useTheme();
  const { data: case_, isLoading, error } = useCase(id || '');

  const [tabValue, setTabValue] = useState(0);
  const [commentDialog, setCommentDialog] = useState(false);
  const [newComment, setNewComment] = useState('');
  const [fileDialog, setFileDialog] = useState(false);

  const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {
    setTabValue(newValue);
  };

  const formatDate = (dateString: string) => {
    if (!dateString) return 'N/A';
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const getStatusIcon = (status: string) => {
    const icons: Record<string, React.ReactNode> = {
      'received': <Schedule color="primary" />,
      'design': <Edit color="warning" />,
      'milling': <Build color="info" />,
      'sintering': <Science color="error" />,
      'qc': <CheckCircle color="success" />,
      'shipped': <LocalShipping color="success" />,
      'delivered': <Done color="success" />,
      'cancelled': <Warning color="disabled" />,
    };
    return icons[status] || <Schedule />;
  };

  const workflowSteps = [
    { label: 'Received', status: 'received', icon: <Schedule /> },
    { label: 'Design', status: 'design', icon: <Edit /> },
    { label: 'Milling', status: 'milling', icon: <Build /> },
    { label: 'Sintering', status: 'sintering', icon: <Science /> },
    { label: 'Quality Control', status: 'qc', icon: <CheckCircle /> },
    { label: 'Shipped', status: 'shipped', icon: <LocalShipping /> },
    { label: 'Delivered', status: 'delivered', icon: <Done /> },
  ];

  const getCurrentStepIndex = () => {
    if (!case_) return 0;
    return workflowSteps.findIndex(step => step.status === case_.status) || 0;
  };

  // Enhanced loading state
  if (isLoading) {
    return (
      <Box sx={{ p: 3 }}>
        <Box display="flex" alignItems="center" mb={3}>
          <IconButton onClick={() => navigate('/cases')} sx={{ mr: 2 }}>
            <ArrowBack />
          </IconButton>
          <Typography variant="h4" fontWeight="bold">
            Loading Case Details...
          </Typography>
        </Box>
        <Paper sx={{ p: 4, textAlign: 'center' }}>
          <CircularProgress size={60} />
          <Typography variant="h6" sx={{ mt: 2, color: 'text.secondary' }}>
            Please wait while we load the case information
          </Typography>
        </Paper>
      </Box>
    );
  }

  // Enhanced error state
  if (error || !case_) {
    return (
      <Box sx={{ p: 3 }}>
        <Box display="flex" alignItems="center" mb={3}>
          <IconButton onClick={() => navigate('/cases')} sx={{ mr: 2 }}>
            <ArrowBack />
          </IconButton>
          <Typography variant="h4" fontWeight="bold">
            Case Not Found
          </Typography>
        </Box>
        <Alert
          severity="error"
          action={
            <Button color="inherit" size="small" onClick={() => navigate('/cases')}>
              Back to Cases
            </Button>
          }
        >
          The requested case could not be found or you don't have permission to view it.
        </Alert>
      </Box>
    );
  }

  return (
    <Box sx={{ p: 3, backgroundColor: '#f5f5f5', minHeight: '100vh' }}>
      {/* Enhanced Header */}
      <Box sx={{ mb: 4 }}>
        {/* Breadcrumbs */}
        <Breadcrumbs aria-label="breadcrumb" sx={{ mb: 2 }}>
          <Link
            underline="hover"
            color="inherit"
            href="#"
            onClick={() => navigate('/cases')}
            sx={{ display: 'flex', alignItems: 'center' }}
          >
            Cases
          </Link>
          <Typography color="text.primary">Case #{case_.id}</Typography>
        </Breadcrumbs>

        {/* Header with Actions */}
        <Box display="flex" justifyContent="space-between" alignItems="flex-start" mb={3}>
          <Box display="flex" alignItems="center" gap={2}>
            <IconButton
              onClick={() => navigate('/cases')}
              sx={{
                bgcolor: 'background.paper',
                boxShadow: 1,
                '&:hover': { boxShadow: 2 }
              }}
            >
              <ArrowBack />
            </IconButton>
            <Box>
              <Typography variant="h3" component="h1" sx={{
                color: 'primary.main',
                fontWeight: 'bold',
                display: 'flex',
                alignItems: 'center',
                gap: 1
              }}>
                🦷 Case #{case_.id}
              </Typography>
              <Typography variant="h6" color="text.secondary" sx={{ mt: 1 }}>
                {case_.patient_name} - {case_.service_type}
              </Typography>
            </Box>
          </Box>

          <Box display="flex" alignItems="center" gap={2}>
            <Button
              variant="outlined"
              startIcon={<Print />}
              onClick={() => window.print()}
            >
              Print
            </Button>
            <Button
              variant="outlined"
              startIcon={<Share />}
            >
              Share
            </Button>
            <Button
              variant="contained"
              startIcon={<Edit />}
              onClick={() => navigate(`/cases/${case_.id}/edit`)}
            >
              Edit Case
            </Button>
          </Box>
        </Box>

        {/* Status Overview Cards */}
        <Grid container spacing={3} sx={{ mb: 3 }}>
          <Grid item xs={12} sm={6} md={3}>
            <Card>
              <CardContent>
                <Box display="flex" alignItems="center" justifyContent="space-between">
                  <Box>
                    <Typography variant="h6" sx={{ fontWeight: 'bold' }}>
                      Status
                    </Typography>
                    <Chip
                      label={case_.current_stage?.name || case_.status}
                      sx={{
                        bgcolor: statusColors[case_.status as keyof typeof statusColors],
                        color: 'white',
                        mt: 1
                      }}
                    />
                  </Box>
                  <Avatar sx={{ bgcolor: statusColors[case_.status as keyof typeof statusColors] }}>
                    {getStatusIcon(case_.status)}
                  </Avatar>
                </Box>
              </CardContent>
            </Card>
          </Grid>

          <Grid item xs={12} sm={6} md={3}>
            <Card>
              <CardContent>
                <Box display="flex" alignItems="center" justifyContent="space-between">
                  <Box>
                    <Typography variant="h6" sx={{ fontWeight: 'bold' }}>
                      Priority
                    </Typography>
                    <Chip
                      label={case_.priority}
                      sx={{
                        bgcolor: priorityColors[case_.priority as keyof typeof priorityColors],
                        color: 'white',
                        mt: 1
                      }}
                    />
                  </Box>
                  <Avatar sx={{ bgcolor: priorityColors[case_.priority as keyof typeof priorityColors] }}>
                    <Warning />
                  </Avatar>
                </Box>
              </CardContent>
            </Card>
          </Grid>

          <Grid item xs={12} sm={6} md={3}>
            <Card>
              <CardContent>
                <Box display="flex" alignItems="center" justifyContent="space-between">
                  <Box>
                    <Typography variant="h6" sx={{ fontWeight: 'bold' }}>
                      Progress
                    </Typography>
                    <Box sx={{ mt: 1, width: '100%' }}>
                      <LinearProgress
                        variant="determinate"
                        value={(case_ as any).progress_percentage || 0}
                        sx={{ mb: 0.5 }}
                      />
                      <Typography variant="body2" color="text.secondary">
                        {(case_ as any).progress_percentage || 0}% Complete
                      </Typography>
                    </Box>
                  </Box>
                  <Avatar sx={{ bgcolor: 'info.main' }}>
                    <Assessment />
                  </Avatar>
                </Box>
              </CardContent>
            </Card>
          </Grid>

          <Grid item xs={12} sm={6} md={3}>
            <Card>
              <CardContent>
                <Box display="flex" alignItems="center" justifyContent="space-between">
                  <Box>
                    <Typography variant="h6" sx={{ fontWeight: 'bold' }}>
                      Due Date
                    </Typography>
                    <Typography
                      variant="body2"
                      color={case_.is_overdue ? 'error.main' : 'text.primary'}
                      sx={{ mt: 1, fontWeight: case_.is_overdue ? 'bold' : 'normal' }}
                    >
                      {case_.due_date ? formatDate(case_.due_date) : 'No due date set'}
                    </Typography>
                    {case_.is_overdue && (
                      <Chip label="OVERDUE" size="small" color="error" sx={{ mt: 0.5 }} />
                    )}
                  </Box>
                  <Avatar sx={{ bgcolor: case_.is_overdue ? 'error.main' : 'success.main' }}>
                    <CalendarToday />
                  </Avatar>
                </Box>
              </CardContent>
            </Card>
          </Grid>
        </Grid>
      </Box>

      {/* Enhanced Tabbed Interface */}
      <Paper sx={{ mb: 3 }}>
        <Tabs
          value={tabValue}
          onChange={handleTabChange}
          variant="scrollable"
          scrollButtons="auto"
          sx={{ borderBottom: 1, borderColor: 'divider' }}
        >
          <Tab
            label="Overview"
            icon={<Visibility />}
            iconPosition="start"
            sx={{ minHeight: 64 }}
          />
          <Tab
            label="Workflow"
            icon={<TimelineIcon />}
            iconPosition="start"
            sx={{ minHeight: 64 }}
          />
          <Tab
            label="Files & Photos"
            icon={<AttachFile />}
            iconPosition="start"
            sx={{ minHeight: 64 }}
          />
          <Tab
            label="Comments"
            icon={<Comment />}
            iconPosition="start"
            sx={{ minHeight: 64 }}
          />
          <Tab
            label="Billing"
            icon={<MonetizationOn />}
            iconPosition="start"
            sx={{ minHeight: 64 }}
          />
        </Tabs>

        {/* Tab Panel 1: Overview */}
        <TabPanel value={tabValue} index={0}>
          <Grid container spacing={3}>
            {/* Patient Information */}
            <Grid item xs={12} md={6}>
              <Card>
                <CardContent>
                  <Typography variant="h6" gutterBottom sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                    <Person color="primary" />
                    Patient Information
                  </Typography>
                  <Divider sx={{ mb: 2 }} />
                  <List dense>
                    <ListItem>
                      <ListItemIcon><Person /></ListItemIcon>
                      <ListItemText
                        primary="Patient Name"
                        secondary={case_.patient_name}
                      />
                    </ListItem>
                    <ListItem>
                      <ListItemIcon><Business /></ListItemIcon>
                      <ListItemText
                        primary="Clinic"
                        secondary={case_.clinic?.name || 'N/A'}
                      />
                    </ListItem>
                    <ListItem>
                      <ListItemIcon><Phone /></ListItemIcon>
                      <ListItemText
                        primary="Phone"
                        secondary={(case_ as any).patient_phone || 'N/A'}
                      />
                    </ListItem>
                    <ListItem>
                      <ListItemIcon><Email /></ListItemIcon>
                      <ListItemText
                        primary="Email"
                        secondary={(case_ as any).patient_email || 'N/A'}
                      />
                    </ListItem>
                  </List>
                </CardContent>
              </Card>
            </Grid>

            {/* Case Details */}
            <Grid item xs={12} md={6}>
              <Card>
                <CardContent>
                  <Typography variant="h6" gutterBottom sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                    <Assignment color="primary" />
                    Case Details
                  </Typography>
                  <Divider sx={{ mb: 2 }} />
                  <List dense>
                    <ListItem>
                      <ListItemIcon><Assignment /></ListItemIcon>
                      <ListItemText
                        primary="Service Type"
                        secondary={case_.service_type}
                      />
                    </ListItem>
                    <ListItem>
                      <ListItemIcon><Description /></ListItemIcon>
                      <ListItemText
                        primary="Tooth Number"
                        secondary={case_.tooth_number || 'N/A'}
                      />
                    </ListItem>
                    <ListItem>
                      <ListItemIcon><CalendarToday /></ListItemIcon>
                      <ListItemText
                        primary="Created Date"
                        secondary={formatDate(case_.created_at)}
                      />
                    </ListItem>
                    <ListItem>
                      <ListItemIcon><Schedule /></ListItemIcon>
                      <ListItemText
                        primary="Due Date"
                        secondary={case_.due_date ? formatDate(case_.due_date) : 'No due date set'}
                      />
                    </ListItem>
                  </List>
                </CardContent>
              </Card>
            </Grid>

            {/* Technician Assignment */}
            <Grid item xs={12} md={6}>
              <Card>
                <CardContent>
                  <Typography variant="h6" gutterBottom sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                    <Person color="primary" />
                    Assignment
                  </Typography>
                  <Divider sx={{ mb: 2 }} />
                  {case_.assigned_technician_id ? (
                    <Box display="flex" alignItems="center" gap={2}>
                      <Avatar sx={{ width: 56, height: 56 }}>
                        {case_.assigned_technician_id.charAt(0)}
                      </Avatar>
                      <Box>
                        <Typography variant="h6">
                          {case_.assigned_technician_id}
                        </Typography>
                        <Typography variant="body2" color="text.secondary">
                          Dental Technician
                        </Typography>
                        <Button size="small" startIcon={<Edit />} sx={{ mt: 1 }}>
                          Reassign
                        </Button>
                      </Box>
                    </Box>
                  ) : (
                    <Box textAlign="center" py={3}>
                      <Typography variant="body2" color="text.secondary" gutterBottom>
                        No technician assigned
                      </Typography>
                      <Button variant="contained" startIcon={<Assignment />}>
                        Assign Technician
                      </Button>
                    </Box>
                  )}
                </CardContent>
              </Card>
            </Grid>

            {/* Special Instructions */}
            <Grid item xs={12} md={6}>
              <Card>
                <CardContent>
                  <Typography variant="h6" gutterBottom sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                    <Description color="primary" />
                    Special Instructions
                  </Typography>
                  <Divider sx={{ mb: 2 }} />
                  <Typography variant="body2" color="text.secondary">
                    {(case_ as any).special_instructions || 'No special instructions provided.'}
                  </Typography>
                  <Button size="small" startIcon={<Edit />} sx={{ mt: 2 }}>
                    Edit Instructions
                  </Button>
                </CardContent>
              </Card>
            </Grid>
          </Grid>
        </TabPanel>

        {/* Tab Panel 2: Workflow */}
        <TabPanel value={tabValue} index={1}>
          <Grid container spacing={3}>
            {/* Workflow Progress */}
            <Grid item xs={12} md={8}>
              <Card>
                <CardContent>
                  <Typography variant="h6" gutterBottom sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                    <TimelineIcon color="primary" />
                    Workflow Progress
                  </Typography>
                  <Divider sx={{ mb: 3 }} />

                  <Stepper activeStep={getCurrentStepIndex()} orientation="vertical">
                    {workflowSteps.map((step, index) => (
                      <Step key={step.status}>
                        <StepLabel
                          StepIconComponent={() => (
                            <Box
                              sx={{
                                width: 40,
                                height: 40,
                                borderRadius: '50%',
                                display: 'flex',
                                alignItems: 'center',
                                justifyContent: 'center',
                                bgcolor: index <= getCurrentStepIndex() ? 'primary.main' : 'grey.300',
                                color: 'white'
                              }}
                            >
                              {step.icon}
                            </Box>
                          )}
                        >
                          <Typography variant="h6">{step.label}</Typography>
                        </StepLabel>
                        <StepContent>
                          <Typography variant="body2" color="text.secondary">
                            {index <= getCurrentStepIndex()
                              ? `Completed on ${formatDate(case_.updated_at)}`
                              : 'Pending'
                            }
                          </Typography>
                          {index === getCurrentStepIndex() && (
                            <Box sx={{ mt: 2 }}>
                              <Button variant="contained" size="small" sx={{ mr: 1 }}>
                                Mark Complete
                              </Button>
                              <Button variant="outlined" size="small">
                                Add Note
                              </Button>
                            </Box>
                          )}
                        </StepContent>
                      </Step>
                    ))}
                  </Stepper>
                </CardContent>
              </Card>
            </Grid>

            {/* Activity Timeline */}
            <Grid item xs={12} md={4}>
              <Card>
                <CardContent>
                  <Typography variant="h6" gutterBottom sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                    <Schedule color="primary" />
                    Recent Activity
                  </Typography>
                  <Divider sx={{ mb: 2 }} />

                  <List>
                    <ListItem>
                      <ListItemIcon>
                        <Avatar sx={{ bgcolor: 'primary.main', width: 32, height: 32 }}>
                          <CheckCircle />
                        </Avatar>
                      </ListItemIcon>
                      <ListItemText
                        primary={
                          <Typography variant="body2" fontWeight="bold">
                            Case Created
                          </Typography>
                        }
                        secondary={
                          <Typography variant="caption" color="text.secondary">
                            {formatDate(case_.created_at)}
                          </Typography>
                        }
                      />
                    </ListItem>

                    <ListItem>
                      <ListItemIcon>
                        <Avatar sx={{ bgcolor: 'warning.main', width: 32, height: 32 }}>
                          <Assignment />
                        </Avatar>
                      </ListItemIcon>
                      <ListItemText
                        primary={
                          <Typography variant="body2" fontWeight="bold">
                            Status Updated
                          </Typography>
                        }
                        secondary={
                          <Typography variant="caption" color="text.secondary">
                            {formatDate(case_.updated_at)}
                          </Typography>
                        }
                      />
                    </ListItem>

                    <ListItem>
                      <ListItemIcon>
                        <Avatar sx={{ bgcolor: 'info.main', width: 32, height: 32 }}>
                          <Comment />
                        </Avatar>
                      </ListItemIcon>
                      <ListItemText
                        primary={
                          <Typography variant="body2" fontWeight="bold">
                            Comment Added
                          </Typography>
                        }
                        secondary={
                          <Typography variant="caption" color="text.secondary">
                            2 hours ago
                          </Typography>
                        }
                      />
                    </ListItem>
                  </List>
                </CardContent>
              </Card>
            </Grid>
          </Grid>
        </TabPanel>

        {/* Tab Panel 3: Files & Photos */}
        <TabPanel value={tabValue} index={2}>
          <Grid container spacing={3}>
            {/* File Upload Area */}
            <Grid item xs={12}>
              <Card>
                <CardContent>
                  <Box display="flex" justifyContent="space-between" alignItems="center" mb={2}>
                    <Typography variant="h6" sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                      <AttachFile color="primary" />
                      Case Files & Photos
                    </Typography>
                    <Button
                      variant="contained"
                      startIcon={<Add />}
                      onClick={() => setFileDialog(true)}
                    >
                      Upload Files
                    </Button>
                  </Box>
                  <Divider sx={{ mb: 3 }} />

                  {/* File Grid */}
                  <Grid container spacing={2}>
                    {/* Sample files - replace with actual file data */}
                    <Grid item xs={12} sm={6} md={4} lg={3}>
                      <Card variant="outlined">
                        <CardContent sx={{ textAlign: 'center', p: 2 }}>
                          <Image sx={{ fontSize: 48, color: 'primary.main', mb: 1 }} />
                          <Typography variant="body2" fontWeight="bold">
                            impression_scan.jpg
                          </Typography>
                          <Typography variant="caption" color="text.secondary">
                            2.4 MB • Uploaded 2 days ago
                          </Typography>
                          <Box display="flex" justifyContent="center" gap={1} mt={2}>
                            <IconButton size="small">
                              <Visibility />
                            </IconButton>
                            <IconButton size="small">
                              <Download />
                            </IconButton>
                            <IconButton size="small" color="error">
                              <Delete />
                            </IconButton>
                          </Box>
                        </CardContent>
                      </Card>
                    </Grid>

                    <Grid item xs={12} sm={6} md={4} lg={3}>
                      <Card variant="outlined">
                        <CardContent sx={{ textAlign: 'center', p: 2 }}>
                          <PictureAsPdf sx={{ fontSize: 48, color: 'error.main', mb: 1 }} />
                          <Typography variant="body2" fontWeight="bold">
                            prescription.pdf
                          </Typography>
                          <Typography variant="caption" color="text.secondary">
                            1.2 MB • Uploaded 3 days ago
                          </Typography>
                          <Box display="flex" justifyContent="center" gap={1} mt={2}>
                            <IconButton size="small">
                              <Visibility />
                            </IconButton>
                            <IconButton size="small">
                              <Download />
                            </IconButton>
                            <IconButton size="small" color="error">
                              <Delete />
                            </IconButton>
                          </Box>
                        </CardContent>
                      </Card>
                    </Grid>

                    <Grid item xs={12} sm={6} md={4} lg={3}>
                      <Card variant="outlined">
                        <CardContent sx={{ textAlign: 'center', p: 2 }}>
                          <InsertDriveFile sx={{ fontSize: 48, color: 'info.main', mb: 1 }} />
                          <Typography variant="body2" fontWeight="bold">
                            case_notes.docx
                          </Typography>
                          <Typography variant="caption" color="text.secondary">
                            0.8 MB • Uploaded 1 week ago
                          </Typography>
                          <Box display="flex" justifyContent="center" gap={1} mt={2}>
                            <IconButton size="small">
                              <Visibility />
                            </IconButton>
                            <IconButton size="small">
                              <Download />
                            </IconButton>
                            <IconButton size="small" color="error">
                              <Delete />
                            </IconButton>
                          </Box>
                        </CardContent>
                      </Card>
                    </Grid>

                    {/* Add more file placeholder */}
                    <Grid item xs={12} sm={6} md={4} lg={3}>
                      <Card
                        variant="outlined"
                        sx={{
                          border: '2px dashed',
                          borderColor: 'primary.main',
                          cursor: 'pointer',
                          '&:hover': { bgcolor: alpha(theme.palette.primary.main, 0.04) }
                        }}
                        onClick={() => setFileDialog(true)}
                      >
                        <CardContent sx={{ textAlign: 'center', p: 4 }}>
                          <Add sx={{ fontSize: 48, color: 'primary.main', mb: 1 }} />
                          <Typography variant="body2" color="primary.main" fontWeight="bold">
                            Upload More Files
                          </Typography>
                          <Typography variant="caption" color="text.secondary">
                            Click to add files
                          </Typography>
                        </CardContent>
                      </Card>
                    </Grid>
                  </Grid>
                </CardContent>
              </Card>
            </Grid>
          </Grid>
        </TabPanel>

        {/* Tab Panel 4: Comments */}
        <TabPanel value={tabValue} index={3}>
          <Grid container spacing={3}>
            <Grid item xs={12}>
              <Card>
                <CardContent>
                  <Box display="flex" justifyContent="space-between" alignItems="center" mb={2}>
                    <Typography variant="h6" sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                      <Comment color="primary" />
                      Case Comments & Notes
                    </Typography>
                    <Button
                      variant="contained"
                      startIcon={<Add />}
                      onClick={() => setCommentDialog(true)}
                    >
                      Add Comment
                    </Button>
                  </Box>
                  <Divider sx={{ mb: 3 }} />

                  {/* Comments List */}
                  <Box>
                    {/* Sample comment - replace with actual comments */}
                    <Box mb={3}>
                      <Box display="flex" alignItems="flex-start" gap={2}>
                        <Avatar>JD</Avatar>
                        <Box flex={1}>
                          <Box display="flex" alignItems="center" gap={2} mb={1}>
                            <Typography variant="subtitle2" fontWeight="bold">
                              Dr. John Doe
                            </Typography>
                            <Typography variant="caption" color="text.secondary">
                              2 hours ago
                            </Typography>
                          </Box>
                          <Paper variant="outlined" sx={{ p: 2, bgcolor: 'grey.50' }}>
                            <Typography variant="body2">
                              Please ensure the crown matches the adjacent teeth color.
                              Patient is very particular about aesthetics.
                            </Typography>
                          </Paper>
                        </Box>
                      </Box>
                    </Box>

                    <Box mb={3}>
                      <Box display="flex" alignItems="flex-start" gap={2}>
                        <Avatar sx={{ bgcolor: 'primary.main' }}>MT</Avatar>
                        <Box flex={1}>
                          <Box display="flex" alignItems="center" gap={2} mb={1}>
                            <Typography variant="subtitle2" fontWeight="bold">
                              Mike Technician
                            </Typography>
                            <Typography variant="caption" color="text.secondary">
                              1 day ago
                            </Typography>
                          </Box>
                          <Paper variant="outlined" sx={{ p: 2, bgcolor: 'primary.50' }}>
                            <Typography variant="body2">
                              Received the impression. Quality looks good.
                              Starting the design phase tomorrow.
                            </Typography>
                          </Paper>
                        </Box>
                      </Box>
                    </Box>

                    {/* No comments placeholder */}
                    <Box textAlign="center" py={4}>
                      <Comment sx={{ fontSize: 48, color: 'text.disabled', mb: 2 }} />
                      <Typography variant="body2" color="text.secondary">
                        No additional comments yet
                      </Typography>
                    </Box>
                  </Box>
                </CardContent>
              </Card>
            </Grid>
          </Grid>
        </TabPanel>

        {/* Tab Panel 5: Billing */}
        <TabPanel value={tabValue} index={4}>
          <Grid container spacing={3}>
            {/* Cost Breakdown */}
            <Grid item xs={12} md={8}>
              <Card>
                <CardContent>
                  <Typography variant="h6" gutterBottom sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                    <MonetizationOn color="primary" />
                    Cost Breakdown
                  </Typography>
                  <Divider sx={{ mb: 3 }} />

                  <List>
                    <ListItem>
                      <ListItemText
                        primary="Base Service Cost"
                        secondary="Crown fabrication"
                      />
                      <Typography variant="h6">$250.00</Typography>
                    </ListItem>
                    <ListItem>
                      <ListItemText
                        primary="Material Cost"
                        secondary="Zirconia block"
                      />
                      <Typography variant="h6">$75.00</Typography>
                    </ListItem>
                    <ListItem>
                      <ListItemText
                        primary="Labor Cost"
                        secondary="Design and milling"
                      />
                      <Typography variant="h6">$125.00</Typography>
                    </ListItem>
                    <Divider />
                    <ListItem>
                      <ListItemText
                        primary={<Typography variant="h6">Total Cost</Typography>}
                      />
                      <Typography variant="h5" color="primary.main" fontWeight="bold">
                        $450.00
                      </Typography>
                    </ListItem>
                  </List>
                </CardContent>
              </Card>
            </Grid>

            {/* Billing Status */}
            <Grid item xs={12} md={4}>
              <Card>
                <CardContent>
                  <Typography variant="h6" gutterBottom sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                    <Assessment color="primary" />
                    Billing Status
                  </Typography>
                  <Divider sx={{ mb: 2 }} />

                  <Box textAlign="center" py={2}>
                    <Chip
                      label="Pending Invoice"
                      color="warning"
                      sx={{ mb: 2 }}
                    />
                    <Typography variant="body2" color="text.secondary" gutterBottom>
                      Invoice will be generated upon completion
                    </Typography>
                    <Button variant="outlined" fullWidth sx={{ mt: 2 }}>
                      Generate Invoice
                    </Button>
                  </Box>
                </CardContent>
              </Card>
            </Grid>
          </Grid>
        </TabPanel>
      </Paper>

      {/* Dialogs */}
      {/* Comment Dialog */}
      <Dialog open={commentDialog} onClose={() => setCommentDialog(false)} maxWidth="md" fullWidth>
        <DialogTitle>Add Comment</DialogTitle>
        <DialogContent>
          <TextField
            autoFocus
            margin="dense"
            label="Comment"
            fullWidth
            multiline
            rows={4}
            variant="outlined"
            value={newComment}
            onChange={(e) => setNewComment(e.target.value)}
            placeholder="Enter your comment or note..."
          />
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setCommentDialog(false)}>Cancel</Button>
          <Button
            variant="contained"
            onClick={() => {
              // Handle comment submission
              setCommentDialog(false);
              setNewComment('');
            }}
            startIcon={<Send />}
          >
            Add Comment
          </Button>
        </DialogActions>
      </Dialog>

      {/* File Upload Dialog */}
      <Dialog open={fileDialog} onClose={() => setFileDialog(false)} maxWidth="sm" fullWidth>
        <DialogTitle>Upload Files</DialogTitle>
        <DialogContent>
          <Box
            sx={{
              border: '2px dashed',
              borderColor: 'primary.main',
              borderRadius: 2,
              p: 4,
              textAlign: 'center',
              cursor: 'pointer',
              '&:hover': { bgcolor: alpha(theme.palette.primary.main, 0.04) }
            }}
          >
            <PhotoCamera sx={{ fontSize: 48, color: 'primary.main', mb: 2 }} />
            <Typography variant="h6" gutterBottom>
              Drop files here or click to browse
            </Typography>
            <Typography variant="body2" color="text.secondary">
              Supported formats: JPG, PNG, PDF, DOC, DOCX
            </Typography>
          </Box>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setFileDialog(false)}>Cancel</Button>
          <Button variant="contained" startIcon={<Add />}>
            Upload Files
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
}

export default CaseDetail;
