1. Setting Django settings module...
2. Importing Django...
3. Setting up Django...

Error occurred: The model ItemPrice is already registered with 'billing.ItemPriceAdmin'.

Full traceback:
Traceback (most recent call last):
  File "C:\GPT4_PROJECTS\DENTAL_LAB_ARCHITECTURE_PATTERNS\dentflow\backend\test_imports.py", line 21, in <module>
    django.setup()
    ~~~~~~~~~~~~^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\__init__.py", line 24, in setup
    apps.populate(settings.INSTALLED_APPS)
    ~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\apps\registry.py", line 124, in populate
    app_config.ready()
    ~~~~~~~~~~~~~~~~^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\contrib\admin\apps.py", line 27, in ready
    self.module.autodiscover()
    ~~~~~~~~~~~~~~~~~~~~~~~~^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\contrib\admin\__init__.py", line 50, in autodiscover
    autodiscover_modules("admin", register_to=site)
    ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\utils\module_loading.py", line 58, in autodiscover_modules
    import_module("%s.%s" % (app_config.name, module_to_search))
    ~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\importlib\__init__.py", line 88, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
           ~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "<frozen importlib._bootstrap>", line 1387, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1360, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1331, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 935, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 1026, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "C:\GPT4_PROJECTS\DENTAL_LAB_ARCHITECTURE_PATTERNS\dentflow\backend\apps\billing\admin.py", line 360, in <module>
    @admin.register(ItemPrice)
     ~~~~~~~~~~~~~~^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\contrib\admin\decorators.py", line 107, in _model_admin_wrapper
    admin_site.register(models, admin_class=admin_class)
    ~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\contrib\admin\sites.py", line 132, in register
    raise AlreadyRegistered(msg)
django.contrib.admin.sites.AlreadyRegistered: The model ItemPrice is already registered with 'billing.ItemPriceAdmin'.
