#!/usr/bin/env python3
"""
Run Django server with proper configuration
"""

import os
import sys
import subprocess

# Set Django settings module
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'dentflow_project.settings')

def run_command(cmd):
    """Run a command and return the result"""
    try:
        result = subprocess.run(cmd, shell=True, capture_output=True, text=True, cwd=os.getcwd())
        print(f"Command: {cmd}")
        print(f"Return code: {result.returncode}")
        if result.stdout:
            print(f"STDOUT:\n{result.stdout}")
        if result.stderr:
            print(f"STDERR:\n{result.stderr}")
        return result.returncode == 0
    except Exception as e:
        print(f"Error running command {cmd}: {e}")
        return False

def main():
    print("🚀 Starting Django setup...")
    
    # Test Django import
    try:
        import django
        print(f"✅ Django {django.get_version()} imported successfully")
        
        django.setup()
        print("✅ Django setup completed")
        
        from django.conf import settings
        print(f"✅ Settings loaded - DEBUG: {settings.DEBUG}")
        print(f"✅ ALLOWED_HOSTS: {settings.ALLOWED_HOSTS}")
        
    except Exception as e:
        print(f"❌ Django setup failed: {e}")
        return False
    
    # Run makemigrations
    print("\n📝 Creating migrations...")
    if not run_command("python manage.py makemigrations"):
        print("❌ Failed to create migrations")
        return False
    
    # Run migrate
    print("\n🗄️ Running migrations...")
    if not run_command("python manage.py migrate"):
        print("❌ Failed to run migrations")
        return False
    
    # Create superuser if needed
    print("\n👤 Creating superuser...")
    run_command('python manage.py shell -c "from django.contrib.auth.models import User; User.objects.filter(username=\'admin\').exists() or User.objects.create_superuser(\'admin\', \'<EMAIL>\', \'admin123\')"')
    
    # Start server
    print("\n🌐 Starting Django server on port 9000...")
    run_command("python manage.py runserver 9000")
    
    return True

if __name__ == "__main__":
    main()
