{"ast": null, "code": "var _jsxFileName = \"C:\\\\GPT4_PROJECTS\\\\DENTAL_LAB_ARCHITECTURE_PATTERNS\\\\dentflow\\\\frontend\\\\src\\\\context\\\\AuthContext.tsx\",\n  _s = $RefreshSig$(),\n  _s2 = $RefreshSig$();\n/**\n * Authentication Context Provider\n * Manages authentication state and user data\n */\n\nimport React, { createContext, useContext, useEffect } from 'react';\nimport { useCurrentUser, useLogin, useLogout } from '../hooks/api';\nimport { DentFlowAPI } from '../api';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst AuthContext = /*#__PURE__*/createContext(undefined);\nexport function AuthProvider({\n  children\n}) {\n  _s();\n  const [initialCheckDone, setInitialCheckDone] = React.useState(false);\n  const {\n    data: user,\n    isLoading: userLoading,\n    refetch: refetchUser,\n    error: userError\n  } = useCurrentUser({\n    retry: false,\n    refetchOnWindowFocus: false,\n    enabled: DentFlowAPI.auth.isAuthenticated()\n  });\n  const loginMutation = useLogin({\n    onSuccess: () => {\n      refetchUser();\n    },\n    onError: error => {\n      console.error('Login failed:', error);\n    }\n  });\n  const logoutMutation = useLogout({\n    onSuccess: () => {\n      // Redirect will be handled by the logout function\n    }\n  });\n\n  // Check initial authentication state\n  React.useEffect(() => {\n    const checkInitialAuth = () => {\n      // If no token exists, we're not authenticated and not loading\n      if (!DentFlowAPI.auth.isAuthenticated()) {\n        setInitialCheckDone(true);\n      }\n    };\n    checkInitialAuth();\n  }, []);\n\n  // Set initial check done when user query completes (success or error)\n  React.useEffect(() => {\n    if (!userLoading && (user !== undefined || userError)) {\n      setInitialCheckDone(true);\n    }\n  }, [userLoading, user, userError]);\n  const isAuthenticated = DentFlowAPI.auth.isAuthenticated() && !!user && !userError;\n  const isLoading = !initialCheckDone || DentFlowAPI.auth.isAuthenticated() && userLoading;\n\n  // Debug logging\n  console.log('AuthProvider state:', {\n    initialCheckDone,\n    hasToken: DentFlowAPI.auth.isAuthenticated(),\n    user: !!user,\n    userError: !!userError,\n    userLoading,\n    isAuthenticated,\n    isLoading\n  });\n  const login = async (email, password) => {\n    try {\n      await loginMutation.mutateAsync({\n        email,\n        password\n      });\n    } catch (error) {\n      throw new Error(error.message || 'Login failed');\n    }\n  };\n  const logout = async () => {\n    try {\n      await logoutMutation.mutateAsync();\n    } catch (error) {\n      console.warn('Logout API call failed, but clearing local state');\n    }\n  };\n\n  // Initialize API when component mounts\n  useEffect(() => {\n    DentFlowAPI.initialize();\n  }, []);\n  const value = {\n    user: user && Object.keys(user).length > 0 ? user : null,\n    isAuthenticated,\n    isLoading: isLoading || loginMutation.isPending || logoutMutation.isPending,\n    login,\n    logout,\n    refetchUser\n  };\n  return /*#__PURE__*/_jsxDEV(AuthContext.Provider, {\n    value: value,\n    children: children\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 119,\n    columnNumber: 5\n  }, this);\n}\n_s(AuthProvider, \"KJ6pAXw3OhRTzncfhp/F+X8Jnyo=\", false, function () {\n  return [useCurrentUser, useLogin, useLogout];\n});\n_c = AuthProvider;\nexport function useAuth() {\n  _s2();\n  const context = useContext(AuthContext);\n  if (context === undefined) {\n    throw new Error('useAuth must be used within an AuthProvider');\n  }\n  return context;\n}\n\n// HOC for protecting routes\n_s2(useAuth, \"b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=\");\nexport function withAuth(Component) {\n  var _s3 = $RefreshSig$();\n  return _s3(function AuthenticatedComponent(props) {\n    _s3();\n    const {\n      isAuthenticated,\n      isLoading\n    } = useAuth();\n    if (isLoading) {\n      return /*#__PURE__*/_jsxDEV(\"div\", {\n        children: \"Loading...\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 139,\n        columnNumber: 14\n      }, this); // Replace with your loading component\n    }\n    if (!isAuthenticated) {\n      // Redirect to login will be handled by route protection\n      return null;\n    }\n    return /*#__PURE__*/_jsxDEV(Component, {\n      ...props\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 147,\n      columnNumber: 12\n    }, this);\n  }, \"yb/FJYAIXt7wZoU4a4YvGQ4Nlsc=\", false, function () {\n    return [useAuth];\n  });\n}\nvar _c;\n$RefreshReg$(_c, \"AuthProvider\");", "map": {"version": 3, "names": ["React", "createContext", "useContext", "useEffect", "useCurrentUser", "useLogin", "useLogout", "DentFlowAPI", "jsxDEV", "_jsxDEV", "AuthContext", "undefined", "<PERSON>th<PERSON><PERSON><PERSON>", "children", "_s", "initialCheckDone", "setInitialCheckDone", "useState", "data", "user", "isLoading", "userLoading", "refetch", "refetchUser", "error", "userError", "retry", "refetchOnWindowFocus", "enabled", "auth", "isAuthenticated", "loginMutation", "onSuccess", "onError", "console", "logoutMutation", "checkInitialAuth", "log", "hasToken", "login", "email", "password", "mutateAsync", "Error", "message", "logout", "warn", "initialize", "value", "Object", "keys", "length", "isPending", "Provider", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "useAuth", "_s2", "context", "<PERSON><PERSON><PERSON>", "Component", "_s3", "$RefreshSig$", "AuthenticatedComponent", "props", "$RefreshReg$"], "sources": ["C:/GPT4_PROJECTS/DENTAL_LAB_ARCHITECTURE_PATTERNS/dentflow/frontend/src/context/AuthContext.tsx"], "sourcesContent": ["/**\n * Authentication Context Provider\n * Manages authentication state and user data\n */\n\nimport React, { createContext, useContext, useEffect, ReactNode } from 'react';\nimport { useCurrentUser, useLogin, useLogout } from '../hooks/api';\nimport { AuthUser } from '../api/types';\nimport { DentFlowAPI } from '../api';\n\ninterface AuthContextValue {\n  user: AuthUser | null;\n  isAuthenticated: boolean;\n  isLoading: boolean;\n  login: (email: string, password: string) => Promise<void>;\n  logout: () => Promise<void>;\n  refetchUser: () => void;\n}\n\nconst AuthContext = createContext<AuthContextValue | undefined>(undefined);\n\ninterface AuthProviderProps {\n  children: ReactNode;\n}\n\nexport function AuthProvider({ children }: AuthProviderProps) {\n  const [initialCheckDone, setInitialCheckDone] = React.useState(false);\n\n  const {\n    data: user,\n    isLoading: userLoading,\n    refetch: refetchUser,\n    error: userError,\n  } = useCurrentUser({\n    retry: false,\n    refetchOnWindowFocus: false,\n    enabled: DentFlowAPI.auth.isAuthenticated(),\n  });\n\n  const loginMutation = useLogin({\n    onSuccess: () => {\n      refetchUser();\n    },\n    onError: (error) => {\n      console.error('Login failed:', error);\n    },\n  });\n\n  const logoutMutation = useLogout({\n    onSuccess: () => {\n      // Redirect will be handled by the logout function\n    },\n  });\n\n  // Check initial authentication state\n  React.useEffect(() => {\n    const checkInitialAuth = () => {\n      // If no token exists, we're not authenticated and not loading\n      if (!DentFlowAPI.auth.isAuthenticated()) {\n        setInitialCheckDone(true);\n      }\n    };\n\n    checkInitialAuth();\n  }, []);\n\n  // Set initial check done when user query completes (success or error)\n  React.useEffect(() => {\n    if (!userLoading && (user !== undefined || userError)) {\n      setInitialCheckDone(true);\n    }\n  }, [userLoading, user, userError]);\n\n  const isAuthenticated = DentFlowAPI.auth.isAuthenticated() && !!user && !userError;\n  const isLoading = !initialCheckDone || (DentFlowAPI.auth.isAuthenticated() && userLoading);\n\n  // Debug logging\n  console.log('AuthProvider state:', {\n    initialCheckDone,\n    hasToken: DentFlowAPI.auth.isAuthenticated(),\n    user: !!user,\n    userError: !!userError,\n    userLoading,\n    isAuthenticated,\n    isLoading\n  });\n\n  const login = async (email: string, password: string) => {\n    try {\n      await loginMutation.mutateAsync({ email, password });\n    } catch (error: any) {\n      throw new Error(error.message || 'Login failed');\n    }\n  };\n\n  const logout = async () => {\n    try {\n      await logoutMutation.mutateAsync();\n    } catch (error) {\n      console.warn('Logout API call failed, but clearing local state');\n    }\n  };\n\n  // Initialize API when component mounts\n  useEffect(() => {\n    DentFlowAPI.initialize();\n  }, []);\n\n  const value: AuthContextValue = {\n    user: (user && Object.keys(user).length > 0) ? user as AuthUser : null,\n    isAuthenticated,\n    isLoading: isLoading || loginMutation.isPending || logoutMutation.isPending,\n    login,\n    logout,\n    refetchUser,\n  };\n\n  return (\n    <AuthContext.Provider value={value}>\n      {children}\n    </AuthContext.Provider>\n  );\n}\n\nexport function useAuth() {\n  const context = useContext(AuthContext);\n  if (context === undefined) {\n    throw new Error('useAuth must be used within an AuthProvider');\n  }\n  return context;\n}\n\n// HOC for protecting routes\nexport function withAuth<P extends object>(Component: React.ComponentType<P>) {\n  return function AuthenticatedComponent(props: P) {\n    const { isAuthenticated, isLoading } = useAuth();\n\n    if (isLoading) {\n      return <div>Loading...</div>; // Replace with your loading component\n    }\n\n    if (!isAuthenticated) {\n      // Redirect to login will be handled by route protection\n      return null;\n    }\n\n    return <Component {...props} />;\n  };\n}\n"], "mappings": ";;;AAAA;AACA;AACA;AACA;;AAEA,OAAOA,KAAK,IAAIC,aAAa,EAAEC,UAAU,EAAEC,SAAS,QAAmB,OAAO;AAC9E,SAASC,cAAc,EAAEC,QAAQ,EAAEC,SAAS,QAAQ,cAAc;AAElE,SAASC,WAAW,QAAQ,QAAQ;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAWrC,MAAMC,WAAW,gBAAGT,aAAa,CAA+BU,SAAS,CAAC;AAM1E,OAAO,SAASC,YAAYA,CAAC;EAAEC;AAA4B,CAAC,EAAE;EAAAC,EAAA;EAC5D,MAAM,CAACC,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGhB,KAAK,CAACiB,QAAQ,CAAC,KAAK,CAAC;EAErE,MAAM;IACJC,IAAI,EAAEC,IAAI;IACVC,SAAS,EAAEC,WAAW;IACtBC,OAAO,EAAEC,WAAW;IACpBC,KAAK,EAAEC;EACT,CAAC,GAAGrB,cAAc,CAAC;IACjBsB,KAAK,EAAE,KAAK;IACZC,oBAAoB,EAAE,KAAK;IAC3BC,OAAO,EAAErB,WAAW,CAACsB,IAAI,CAACC,eAAe,CAAC;EAC5C,CAAC,CAAC;EAEF,MAAMC,aAAa,GAAG1B,QAAQ,CAAC;IAC7B2B,SAAS,EAAEA,CAAA,KAAM;MACfT,WAAW,CAAC,CAAC;IACf,CAAC;IACDU,OAAO,EAAGT,KAAK,IAAK;MAClBU,OAAO,CAACV,KAAK,CAAC,eAAe,EAAEA,KAAK,CAAC;IACvC;EACF,CAAC,CAAC;EAEF,MAAMW,cAAc,GAAG7B,SAAS,CAAC;IAC/B0B,SAAS,EAAEA,CAAA,KAAM;MACf;IAAA;EAEJ,CAAC,CAAC;;EAEF;EACAhC,KAAK,CAACG,SAAS,CAAC,MAAM;IACpB,MAAMiC,gBAAgB,GAAGA,CAAA,KAAM;MAC7B;MACA,IAAI,CAAC7B,WAAW,CAACsB,IAAI,CAACC,eAAe,CAAC,CAAC,EAAE;QACvCd,mBAAmB,CAAC,IAAI,CAAC;MAC3B;IACF,CAAC;IAEDoB,gBAAgB,CAAC,CAAC;EACpB,CAAC,EAAE,EAAE,CAAC;;EAEN;EACApC,KAAK,CAACG,SAAS,CAAC,MAAM;IACpB,IAAI,CAACkB,WAAW,KAAKF,IAAI,KAAKR,SAAS,IAAIc,SAAS,CAAC,EAAE;MACrDT,mBAAmB,CAAC,IAAI,CAAC;IAC3B;EACF,CAAC,EAAE,CAACK,WAAW,EAAEF,IAAI,EAAEM,SAAS,CAAC,CAAC;EAElC,MAAMK,eAAe,GAAGvB,WAAW,CAACsB,IAAI,CAACC,eAAe,CAAC,CAAC,IAAI,CAAC,CAACX,IAAI,IAAI,CAACM,SAAS;EAClF,MAAML,SAAS,GAAG,CAACL,gBAAgB,IAAKR,WAAW,CAACsB,IAAI,CAACC,eAAe,CAAC,CAAC,IAAIT,WAAY;;EAE1F;EACAa,OAAO,CAACG,GAAG,CAAC,qBAAqB,EAAE;IACjCtB,gBAAgB;IAChBuB,QAAQ,EAAE/B,WAAW,CAACsB,IAAI,CAACC,eAAe,CAAC,CAAC;IAC5CX,IAAI,EAAE,CAAC,CAACA,IAAI;IACZM,SAAS,EAAE,CAAC,CAACA,SAAS;IACtBJ,WAAW;IACXS,eAAe;IACfV;EACF,CAAC,CAAC;EAEF,MAAMmB,KAAK,GAAG,MAAAA,CAAOC,KAAa,EAAEC,QAAgB,KAAK;IACvD,IAAI;MACF,MAAMV,aAAa,CAACW,WAAW,CAAC;QAAEF,KAAK;QAAEC;MAAS,CAAC,CAAC;IACtD,CAAC,CAAC,OAAOjB,KAAU,EAAE;MACnB,MAAM,IAAImB,KAAK,CAACnB,KAAK,CAACoB,OAAO,IAAI,cAAc,CAAC;IAClD;EACF,CAAC;EAED,MAAMC,MAAM,GAAG,MAAAA,CAAA,KAAY;IACzB,IAAI;MACF,MAAMV,cAAc,CAACO,WAAW,CAAC,CAAC;IACpC,CAAC,CAAC,OAAOlB,KAAK,EAAE;MACdU,OAAO,CAACY,IAAI,CAAC,kDAAkD,CAAC;IAClE;EACF,CAAC;;EAED;EACA3C,SAAS,CAAC,MAAM;IACdI,WAAW,CAACwC,UAAU,CAAC,CAAC;EAC1B,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMC,KAAuB,GAAG;IAC9B7B,IAAI,EAAGA,IAAI,IAAI8B,MAAM,CAACC,IAAI,CAAC/B,IAAI,CAAC,CAACgC,MAAM,GAAG,CAAC,GAAIhC,IAAI,GAAe,IAAI;IACtEW,eAAe;IACfV,SAAS,EAAEA,SAAS,IAAIW,aAAa,CAACqB,SAAS,IAAIjB,cAAc,CAACiB,SAAS;IAC3Eb,KAAK;IACLM,MAAM;IACNtB;EACF,CAAC;EAED,oBACEd,OAAA,CAACC,WAAW,CAAC2C,QAAQ;IAACL,KAAK,EAAEA,KAAM;IAAAnC,QAAA,EAChCA;EAAQ;IAAAyC,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACW,CAAC;AAE3B;AAAC3C,EAAA,CAjGeF,YAAY;EAAA,QAQtBR,cAAc,EAMIC,QAAQ,EASPC,SAAS;AAAA;AAAAoD,EAAA,GAvBlB9C,YAAY;AAmG5B,OAAO,SAAS+C,OAAOA,CAAA,EAAG;EAAAC,GAAA;EACxB,MAAMC,OAAO,GAAG3D,UAAU,CAACQ,WAAW,CAAC;EACvC,IAAImD,OAAO,KAAKlD,SAAS,EAAE;IACzB,MAAM,IAAIgC,KAAK,CAAC,6CAA6C,CAAC;EAChE;EACA,OAAOkB,OAAO;AAChB;;AAEA;AAAAD,GAAA,CARgBD,OAAO;AASvB,OAAO,SAASG,QAAQA,CAAmBC,SAAiC,EAAE;EAAA,IAAAC,GAAA,GAAAC,YAAA;EAC5E,OAAAD,GAAA,CAAO,SAASE,sBAAsBA,CAACC,KAAQ,EAAE;IAAAH,GAAA;IAC/C,MAAM;MAAElC,eAAe;MAAEV;IAAU,CAAC,GAAGuC,OAAO,CAAC,CAAC;IAEhD,IAAIvC,SAAS,EAAE;MACb,oBAAOX,OAAA;QAAAI,QAAA,EAAK;MAAU;QAAAyC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC,CAAC,CAAC;IAChC;IAEA,IAAI,CAAC3B,eAAe,EAAE;MACpB;MACA,OAAO,IAAI;IACb;IAEA,oBAAOrB,OAAA,CAACsD,SAAS;MAAA,GAAKI;IAAK;MAAAb,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAG,CAAC;EACjC,CAAC;IAAA,QAZwCE,OAAO;EAAA;AAalD;AAAC,IAAAD,EAAA;AAAAU,YAAA,CAAAV,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}