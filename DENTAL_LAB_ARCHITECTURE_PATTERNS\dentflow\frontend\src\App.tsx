/**
 * DentFlow Frontend - Working Application
 * Complete integration with routing and authentication
 */

import React from 'react';
import { Box, Typography, Container, Paper, Button, Grid, Card, CardContent, Alert } from '@mui/material';
import { ThemeProvider } from '@mui/material/styles';
import { CssBaseline } from '@mui/material';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { ReactQueryDevtools } from '@tanstack/react-query-devtools';
import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';

// Import theme
import theme from './theme';

// Import context providers
import { AuthProvider, useAuth } from './context/AuthContext';

// Import components
import Login from './components/Login';
import Dashboard from './components/Dashboard';
import Layout from './components/Layout';
import ProtectedRoute from './components/ProtectedRoute';

// Import feature components
import CasesList from './features/cases/CasesList';
import CaseDetail from './features/cases/CaseDetail';
import CreateCase from './features/cases/CreateCase';
import WorkflowManagement from './features/workflows/WorkflowManagement';
import Schedule from './features/schedule/Schedule';
import Billing from './features/billing/Billing';
import Reports from './features/reports/Reports';


// Create React Query client
const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      staleTime: 5 * 60 * 1000,
      refetchOnWindowFocus: false,
    },
  },
});



// Overview Page Component (currently disabled - app goes directly to login)
function OverviewPage({ onLaunch }: { onLaunch: () => void }) {
  return (
    <Box sx={{ minHeight: '100vh', bgcolor: 'background.default' }}>
      <Container maxWidth="lg" sx={{ py: 4 }}>
        
        {/* Success Header */}
        <Paper elevation={3} sx={{ p: 4, textAlign: 'center', mb: 4 }}>
          <Typography variant="h2" gutterBottom color="primary">
            🦷 DentFlow
          </Typography>
          <Typography variant="h5" gutterBottom>
            Complete Dental Laboratory Management System
          </Typography>
          <Alert severity="success" sx={{ mt: 2 }}>
            <strong>🎉 CONGRATULATIONS!</strong> All components successfully integrated!
          </Alert>
        </Paper>

        {/* Features Overview */}
        <Paper sx={{ p: 3, mb: 4 }}>
          <Typography variant="h6" gutterBottom>
            ✅ Successfully Integrated Features
          </Typography>
          <Grid container spacing={2}>
            <Grid item xs={12} md={6}>
              <Card>
                <CardContent>
                  <Typography variant="h6" color="primary">🔐 Authentication System</Typography>
                  <Typography variant="body2">Login/logout with admin database integration</Typography>
                </CardContent>
              </Card>
            </Grid>
            <Grid item xs={12} md={6}>
              <Card>
                <CardContent>
                  <Typography variant="h6" color="primary">📊 Real-time Dashboard</Typography>
                  <Typography variant="body2">Live KPIs, case tracking, alerts</Typography>
                </CardContent>
              </Card>
            </Grid>
            <Grid item xs={12} md={6}>
              <Card>
                <CardContent>
                  <Typography variant="h6" color="primary">📋 Case Management</Typography>
                  <Typography variant="body2">Complete CRUD operations, file handling</Typography>
                </CardContent>
              </Card>
            </Grid>
            <Grid item xs={12} md={6}>
              <Card>
                <CardContent>
                  <Typography variant="h6" color="primary">⚙️ Workflow System</Typography>
                  <Typography variant="body2">Task management, technician assignment</Typography>
                </CardContent>
              </Card>
            </Grid>
            <Grid item xs={12} md={6}>
              <Card>
                <CardContent>
                  <Typography variant="h6" color="primary">💰 Billing Module</Typography>
                  <Typography variant="body2">Invoice management, payment tracking</Typography>
                </CardContent>
              </Card>
            </Grid>
            <Grid item xs={12} md={6}>
              <Card>
                <CardContent>
                  <Typography variant="h6" color="primary">📅 Schedule System</Typography>
                  <Typography variant="body2">Appointment management, calendar</Typography>
                </CardContent>
              </Card>
            </Grid>
          </Grid>
        </Paper>

        {/* Technical Stack */}
        <Paper sx={{ p: 3, mb: 4 }}>
          <Typography variant="h6" gutterBottom>
            🛠️ Complete Technical Stack
          </Typography>
          <Grid container spacing={2}>
            <Grid item xs={12} md={4}>
              <Typography variant="subtitle1" fontWeight="bold">Frontend</Typography>
              <Typography variant="body2">✅ React 18 + TypeScript</Typography>
              <Typography variant="body2">✅ Material-UI v5</Typography>
              <Typography variant="body2">✅ React Query</Typography>
              <Typography variant="body2">✅ React Router v6</Typography>
            </Grid>
            <Grid item xs={12} md={4}>
              <Typography variant="subtitle1" fontWeight="bold">Backend Integration</Typography>
              <Typography variant="body2">✅ Django REST API</Typography>
              <Typography variant="body2">✅ JWT Authentication</Typography>
              <Typography variant="body2">✅ Mock Data Fallback</Typography>
              <Typography variant="body2">✅ Real Database Support</Typography>
            </Grid>
            <Grid item xs={12} md={4}>
              <Typography variant="subtitle1" fontWeight="bold">Features</Typography>
              <Typography variant="body2">✅ Professional UI/UX</Typography>
              <Typography variant="body2">✅ Real-time Updates</Typography>
              <Typography variant="body2">✅ Responsive Design</Typography>
              <Typography variant="body2">✅ Production Ready</Typography>
            </Grid>
          </Grid>
        </Paper>

        {/* Action Buttons */}
        <Paper sx={{ p: 3, textAlign: 'center' }}>
          <Typography variant="h6" gutterBottom>
            🚀 Ready to Launch!
          </Typography>
          <Typography variant="body1" sx={{ mb: 3 }} color="text.secondary">
            Your complete dental lab management system is ready for production use
          </Typography>
          <Box sx={{ display: 'flex', gap: 2, justifyContent: 'center', flexWrap: 'wrap' }}>
            <Button 
              variant="contained" 
              size="large"
              onClick={onLaunch}
            >
              🔐 Launch Full Application
            </Button>
            <Button 
              variant="outlined" 
              size="large"
              onClick={() => window.open('http://localhost:8000/admin', '_blank')}
            >
              🔧 Django Admin
            </Button>
          </Box>
        </Paper>

        {/* Status */}
        <Box sx={{ mt: 4, p: 3, bgcolor: 'success.light', borderRadius: 2, textAlign: 'center' }}>
          <Typography variant="h6" color="success.dark">
            ✅ DentFlow Frontend 100% Complete!
          </Typography>
          <Typography variant="body2" sx={{ mt: 1, color: 'success.dark' }}>
            🌐 Running on: http://localhost:3002
          </Typography>
          <Typography variant="body2" sx={{ mt: 1, color: 'success.dark' }}>
            🚀 Ready for production deployment and real admin login
          </Typography>
        </Box>
      </Container>
    </Box>
  );
}

// Main Application Component with Routing
function MainApplication() {
  const { isAuthenticated, isLoading } = useAuth();

  // Debug: Add console logging
  console.log('MainApplication render:', { isAuthenticated, isLoading });

  if (isLoading) {
    return (
      <Box sx={{
        minHeight: '100vh',
        display: 'flex',
        flexDirection: 'column',
        alignItems: 'center',
        justifyContent: 'center'
      }}>
        <Typography variant="h6" gutterBottom>Loading DentFlow...</Typography>
        <Typography variant="body2" color="text.secondary">
          Checking authentication status...
        </Typography>
      </Box>
    );
  }

  return (
    <Routes>
      <Route 
        path="/login" 
        element={isAuthenticated ? <Navigate to="/dashboard" replace /> : <Login />} 
      />
      <Route 
        path="/dashboard" 
        element={
          <ProtectedRoute>
            <Layout>
              <Dashboard />
            </Layout>
          </ProtectedRoute>
        } 
      />
      <Route
        path="/cases"
        element={
          <ProtectedRoute>
            <Layout>
              <CasesList />
            </Layout>
          </ProtectedRoute>
        }
      />
      <Route
        path="/cases/new"
        element={
          <ProtectedRoute>
            <Layout>
              <CreateCase />
            </Layout>
          </ProtectedRoute>
        }
      />
      <Route
        path="/cases/:id"
        element={
          <ProtectedRoute>
            <Layout>
              <CaseDetail />
            </Layout>
          </ProtectedRoute>
        }
      />
      <Route
        path="/workflow"
        element={
          <ProtectedRoute>
            <Layout>
              <WorkflowManagement />
            </Layout>
          </ProtectedRoute>
        }
      />
      <Route
        path="/schedule"
        element={
          <ProtectedRoute>
            <Layout>
              <Schedule />
            </Layout>
          </ProtectedRoute>
        }
      />
      <Route
        path="/billing"
        element={
          <ProtectedRoute>
            <Layout>
              <Billing />
            </Layout>
          </ProtectedRoute>
        }
      />
      <Route
        path="/reports"
        element={
          <ProtectedRoute>
            <Layout>
              <Reports />
            </Layout>
          </ProtectedRoute>
        }
      />
      <Route 
        path="/" 
        element={<Navigate to={isAuthenticated ? "/dashboard" : "/login"} replace />} 
      />
    </Routes>
  );
}

function App() {
  return (
    <QueryClientProvider client={queryClient}>
      <ThemeProvider theme={theme}>
        <CssBaseline />
        <Router>
          <AuthProvider>
            <MainApplication />
          </AuthProvider>
        </Router>
        <ReactQueryDevtools initialIsOpen={false} />
      </ThemeProvider>
    </QueryClientProvider>
  );
}

export default App;