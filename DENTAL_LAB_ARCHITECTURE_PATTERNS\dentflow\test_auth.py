#!/usr/bin/env python3
"""
Test authentication endpoints
"""

import requests
import json

def test_auth():
    base_url = "http://localhost:8001/api/v1"
    
    print("🔐 Testing DentFlow Authentication")
    print("=" * 50)
    
    # Test login endpoint
    login_url = f"{base_url}/auth/login/"
    login_data = {
        "email": "admin",  # Try with username first
        "password": "admin123"
    }
    
    print(f"📡 Testing login at: {login_url}")
    print(f"📝 Login data: {login_data}")
    
    try:
        response = requests.post(login_url, json=login_data, timeout=10)
        print(f"📊 Response status: {response.status_code}")
        print(f"📄 Response headers: {dict(response.headers)}")
        
        if response.status_code == 200:
            data = response.json()
            print("✅ Login successful!")
            print(f"🎫 Access token: {data.get('access', 'Not found')[:50]}...")
            print(f"🔄 Refresh token: {data.get('refresh', 'Not found')[:50]}...")
            print(f"👤 User data: {data.get('user', 'Not found')}")
            
            # Test the /me endpoint with the token
            access_token = data.get('access')
            if access_token:
                me_url = f"{base_url}/auth/me/"
                headers = {"Authorization": f"Bearer {access_token}"}
                
                print(f"\n📡 Testing /me endpoint: {me_url}")
                me_response = requests.get(me_url, headers=headers, timeout=10)
                print(f"📊 /me Response status: {me_response.status_code}")
                
                if me_response.status_code == 200:
                    me_data = me_response.json()
                    print("✅ /me endpoint successful!")
                    print(f"👤 Current user: {me_data}")
                else:
                    print(f"❌ /me endpoint failed: {me_response.text}")
            
        else:
            print(f"❌ Login failed: {response.text}")
            
            # Try with email instead
            print("\n🔄 Trying with email...")
            login_data_email = {
                "email": "<EMAIL>",
                "password": "admin123"
            }
            
            response2 = requests.post(login_url, json=login_data_email, timeout=10)
            print(f"📊 Email login status: {response2.status_code}")
            
            if response2.status_code == 200:
                data2 = response2.json()
                print("✅ Email login successful!")
                print(f"👤 User data: {data2.get('user', 'Not found')}")
            else:
                print(f"❌ Email login also failed: {response2.text}")
    
    except requests.exceptions.RequestException as e:
        print(f"❌ Connection error: {e}")
        print("🔧 Make sure the backend server is running on localhost:8001")
    
    print("\n" + "=" * 50)
    print("🎯 Next steps:")
    print("1. If login works, try the frontend at http://localhost:3001")
    print("2. Use the same credentials: admin / admin123")
    print("3. Check browser console for any errors")

if __name__ == '__main__':
    test_auth()
