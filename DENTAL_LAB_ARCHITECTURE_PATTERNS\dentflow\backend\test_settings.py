#!/usr/bin/env python3
"""
Test Django settings configuration
"""

import os
import sys

# Add the project directory to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# Set Django settings module
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'dentflow_project.settings')

try:
    import django
    django.setup()
    
    from django.conf import settings
    
    print("✅ Django settings loaded successfully!")
    print(f"DEBUG: {settings.DEBUG}")
    print(f"ALLOWED_HOSTS: {settings.ALLOWED_HOSTS}")
    print(f"SECRET_KEY: {settings.SECRET_KEY[:20]}...")
    print(f"INSTALLED_APPS: {len(settings.INSTALLED_APPS)} apps")
    print(f"DATABASE: {settings.DATABASES['default']['ENGINE']}")
    
    # Test if we can import our apps
    try:
        from apps.users.models import User
        print("✅ User model imported successfully")
    except Exception as e:
        print(f"❌ User model import failed: {e}")
    
    try:
        from apps.cases.models import Case
        print("✅ Case model imported successfully")
    except Exception as e:
        print(f"❌ Case model import failed: {e}")
        
except Exception as e:
    print(f"❌ Django setup failed: {e}")
    import traceback
    traceback.print_exc()
