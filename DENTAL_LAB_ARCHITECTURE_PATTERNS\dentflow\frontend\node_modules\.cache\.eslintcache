[{"C:\\GPT4_PROJECTS\\DENTAL_LAB_ARCHITECTURE_PATTERNS\\dentflow\\frontend\\src\\index.tsx": "1", "C:\\GPT4_PROJECTS\\DENTAL_LAB_ARCHITECTURE_PATTERNS\\dentflow\\frontend\\src\\App.tsx": "2", "C:\\GPT4_PROJECTS\\DENTAL_LAB_ARCHITECTURE_PATTERNS\\dentflow\\frontend\\src\\theme.ts": "3", "C:\\GPT4_PROJECTS\\DENTAL_LAB_ARCHITECTURE_PATTERNS\\dentflow\\frontend\\src\\components\\Dashboard.tsx": "4", "C:\\GPT4_PROJECTS\\DENTAL_LAB_ARCHITECTURE_PATTERNS\\dentflow\\frontend\\src\\components\\Login.tsx": "5", "C:\\GPT4_PROJECTS\\DENTAL_LAB_ARCHITECTURE_PATTERNS\\dentflow\\frontend\\src\\components\\ProtectedRoute.tsx": "6", "C:\\GPT4_PROJECTS\\DENTAL_LAB_ARCHITECTURE_PATTERNS\\dentflow\\frontend\\src\\context\\AuthContext.tsx": "7", "C:\\GPT4_PROJECTS\\DENTAL_LAB_ARCHITECTURE_PATTERNS\\dentflow\\frontend\\src\\components\\Layout.tsx": "8", "C:\\GPT4_PROJECTS\\DENTAL_LAB_ARCHITECTURE_PATTERNS\\dentflow\\frontend\\src\\features\\workflows\\WorkflowManagement.tsx": "9", "C:\\GPT4_PROJECTS\\DENTAL_LAB_ARCHITECTURE_PATTERNS\\dentflow\\frontend\\src\\features\\schedule\\Schedule.tsx": "10", "C:\\GPT4_PROJECTS\\DENTAL_LAB_ARCHITECTURE_PATTERNS\\dentflow\\frontend\\src\\features\\cases\\CaseDetail.tsx": "11", "C:\\GPT4_PROJECTS\\DENTAL_LAB_ARCHITECTURE_PATTERNS\\dentflow\\frontend\\src\\features\\reports\\Reports.tsx": "12", "C:\\GPT4_PROJECTS\\DENTAL_LAB_ARCHITECTURE_PATTERNS\\dentflow\\frontend\\src\\features\\cases\\CreateCase.tsx": "13", "C:\\GPT4_PROJECTS\\DENTAL_LAB_ARCHITECTURE_PATTERNS\\dentflow\\frontend\\src\\features\\billing\\Billing.tsx": "14", "C:\\GPT4_PROJECTS\\DENTAL_LAB_ARCHITECTURE_PATTERNS\\dentflow\\frontend\\src\\features\\cases\\CasesList.tsx": "15", "C:\\GPT4_PROJECTS\\DENTAL_LAB_ARCHITECTURE_PATTERNS\\dentflow\\frontend\\src\\api\\casesService.ts": "16", "C:\\GPT4_PROJECTS\\DENTAL_LAB_ARCHITECTURE_PATTERNS\\dentflow\\frontend\\src\\hooks\\api.ts": "17", "C:\\GPT4_PROJECTS\\DENTAL_LAB_ARCHITECTURE_PATTERNS\\dentflow\\frontend\\src\\context\\index.ts": "18", "C:\\GPT4_PROJECTS\\DENTAL_LAB_ARCHITECTURE_PATTERNS\\dentflow\\frontend\\src\\api\\index.ts": "19", "C:\\GPT4_PROJECTS\\DENTAL_LAB_ARCHITECTURE_PATTERNS\\dentflow\\frontend\\src\\api\\client.ts": "20", "C:\\GPT4_PROJECTS\\DENTAL_LAB_ARCHITECTURE_PATTERNS\\dentflow\\frontend\\src\\api\\mockData.ts": "21", "C:\\GPT4_PROJECTS\\DENTAL_LAB_ARCHITECTURE_PATTERNS\\dentflow\\frontend\\src\\api\\workflowService.ts": "22", "C:\\GPT4_PROJECTS\\DENTAL_LAB_ARCHITECTURE_PATTERNS\\dentflow\\frontend\\src\\api\\scheduleService.ts": "23", "C:\\GPT4_PROJECTS\\DENTAL_LAB_ARCHITECTURE_PATTERNS\\dentflow\\frontend\\src\\api\\billingService.ts": "24", "C:\\GPT4_PROJECTS\\DENTAL_LAB_ARCHITECTURE_PATTERNS\\dentflow\\frontend\\src\\api\\authService.ts": "25", "C:\\GPT4_PROJECTS\\DENTAL_LAB_ARCHITECTURE_PATTERNS\\dentflow\\frontend\\src\\api\\unifiedClient.ts": "26", "C:\\GPT4_PROJECTS\\DENTAL_LAB_ARCHITECTURE_PATTERNS\\dentflow\\frontend\\src\\api\\reportsService.ts": "27", "C:\\GPT4_PROJECTS\\DENTAL_LAB_ARCHITECTURE_PATTERNS\\dentflow\\frontend\\src\\context\\NotificationContext.tsx": "28", "C:\\GPT4_PROJECTS\\DENTAL_LAB_ARCHITECTURE_PATTERNS\\dentflow\\frontend\\src\\context\\AppContext.tsx": "29", "C:\\GPT4_PROJECTS\\DENTAL_LAB_ARCHITECTURE_PATTERNS\\dentflow\\frontend\\src\\api\\types.ts": "30", "C:\\GPT4_PROJECTS\\DENTAL_LAB_ARCHITECTURE_PATTERNS\\dentflow\\frontend\\src\\hooks\\useNotification.ts": "31", "C:\\GPT4_PROJECTS\\DENTAL_LAB_ARCHITECTURE_PATTERNS\\dentflow\\frontend\\src\\services\\api.ts": "32"}, {"size": 316, "mtime": 1753458667163, "results": "33", "hashOfConfig": "34"}, {"size": 10885, "mtime": 1753495165452, "results": "35", "hashOfConfig": "34"}, {"size": 1970, "mtime": 1753458573546, "results": "36", "hashOfConfig": "34"}, {"size": 32002, "mtime": 1753490764906, "results": "37", "hashOfConfig": "34"}, {"size": 4573, "mtime": 1753459113280, "results": "38", "hashOfConfig": "34"}, {"size": 1231, "mtime": 1753452739618, "results": "39", "hashOfConfig": "34"}, {"size": 3869, "mtime": 1753489720915, "results": "40", "hashOfConfig": "34"}, {"size": 6696, "mtime": 1753451120647, "results": "41", "hashOfConfig": "34"}, {"size": 3888, "mtime": 1753451215175, "results": "42", "hashOfConfig": "34"}, {"size": 1881, "mtime": 1753451229501, "results": "43", "hashOfConfig": "34"}, {"size": 39802, "mtime": 1753494864499, "results": "44", "hashOfConfig": "34"}, {"size": 6546, "mtime": 1753451276642, "results": "45", "hashOfConfig": "34"}, {"size": 24359, "mtime": 1753492849761, "results": "46", "hashOfConfig": "34"}, {"size": 3628, "mtime": 1753451246789, "results": "47", "hashOfConfig": "34"}, {"size": 37527, "mtime": 1753495600778, "results": "48", "hashOfConfig": "34"}, {"size": 11679, "mtime": 1753503121759, "results": "49", "hashOfConfig": "34"}, {"size": 10403, "mtime": 1753494326329, "results": "50", "hashOfConfig": "34"}, {"size": 264, "mtime": 1753450672480, "results": "51", "hashOfConfig": "34"}, {"size": 2788, "mtime": 1753460072732, "results": "52", "hashOfConfig": "34"}, {"size": 2130, "mtime": 1753410821200, "results": "53", "hashOfConfig": "34"}, {"size": 12471, "mtime": 1753459082723, "results": "54", "hashOfConfig": "34"}, {"size": 7935, "mtime": 1753450193445, "results": "55", "hashOfConfig": "34"}, {"size": 10649, "mtime": 1753450274781, "results": "56", "hashOfConfig": "34"}, {"size": 9132, "mtime": 1753460027532, "results": "57", "hashOfConfig": "34"}, {"size": 3601, "mtime": 1753462067868, "results": "58", "hashOfConfig": "34"}, {"size": 4346, "mtime": 1753489807515, "results": "59", "hashOfConfig": "34"}, {"size": 10533, "mtime": 1753459710098, "results": "60", "hashOfConfig": "34"}, {"size": 2360, "mtime": 1753450645278, "results": "61", "hashOfConfig": "34"}, {"size": 2655, "mtime": 1753458589878, "results": "62", "hashOfConfig": "34"}, {"size": 4421, "mtime": 1753464339677, "results": "63", "hashOfConfig": "34"}, {"size": 1996, "mtime": 1753450611261, "results": "64", "hashOfConfig": "34"}, {"size": 13854, "mtime": 1753502507161, "results": "65", "hashOfConfig": "34"}, {"filePath": "66", "messages": "67", "suppressedMessages": "68", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1nadh6z", {"filePath": "69", "messages": "70", "suppressedMessages": "71", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "72", "messages": "73", "suppressedMessages": "74", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "75", "messages": "76", "suppressedMessages": "77", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 8, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "78", "messages": "79", "suppressedMessages": "80", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "81", "messages": "82", "suppressedMessages": "83", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "84", "messages": "85", "suppressedMessages": "86", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "87", "messages": "88", "suppressedMessages": "89", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "90", "messages": "91", "suppressedMessages": "92", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "93", "messages": "94", "suppressedMessages": "95", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "96", "messages": "97", "suppressedMessages": "98", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "99", "messages": "100", "suppressedMessages": "101", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "102", "messages": "103", "suppressedMessages": "104", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 21, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "105", "messages": "106", "suppressedMessages": "107", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "108", "messages": "109", "suppressedMessages": "110", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 17, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "111", "messages": "112", "suppressedMessages": "113", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "114", "messages": "115", "suppressedMessages": "116", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "117", "messages": "118", "suppressedMessages": "119", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "120", "messages": "121", "suppressedMessages": "122", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "123", "messages": "124", "suppressedMessages": "125", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "126", "messages": "127", "suppressedMessages": "128", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "129", "messages": "130", "suppressedMessages": "131", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "132", "messages": "133", "suppressedMessages": "134", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "135", "messages": "136", "suppressedMessages": "137", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "138", "messages": "139", "suppressedMessages": "140", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "141", "messages": "142", "suppressedMessages": "143", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "144", "messages": "145", "suppressedMessages": "146", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "147", "messages": "148", "suppressedMessages": "149", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "150", "messages": "151", "suppressedMessages": "152", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "153", "messages": "154", "suppressedMessages": "155", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "156", "messages": "157", "suppressedMessages": "158", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "159", "messages": "160", "suppressedMessages": "161", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "C:\\GPT4_PROJECTS\\DENTAL_LAB_ARCHITECTURE_PATTERNS\\dentflow\\frontend\\src\\index.tsx", [], [], "C:\\GPT4_PROJECTS\\DENTAL_LAB_ARCHITECTURE_PATTERNS\\dentflow\\frontend\\src\\App.tsx", ["162"], [], "C:\\GPT4_PROJECTS\\DENTAL_LAB_ARCHITECTURE_PATTERNS\\dentflow\\frontend\\src\\theme.ts", [], [], "C:\\GPT4_PROJECTS\\DENTAL_LAB_ARCHITECTURE_PATTERNS\\dentflow\\frontend\\src\\components\\Dashboard.tsx", ["163", "164", "165", "166", "167", "168", "169", "170"], [], "C:\\GPT4_PROJECTS\\DENTAL_LAB_ARCHITECTURE_PATTERNS\\dentflow\\frontend\\src\\components\\Login.tsx", [], [], "C:\\GPT4_PROJECTS\\DENTAL_LAB_ARCHITECTURE_PATTERNS\\dentflow\\frontend\\src\\components\\ProtectedRoute.tsx", [], [], "C:\\GPT4_PROJECTS\\DENTAL_LAB_ARCHITECTURE_PATTERNS\\dentflow\\frontend\\src\\context\\AuthContext.tsx", [], [], "C:\\GPT4_PROJECTS\\DENTAL_LAB_ARCHITECTURE_PATTERNS\\dentflow\\frontend\\src\\components\\Layout.tsx", [], [], "C:\\GPT4_PROJECTS\\DENTAL_LAB_ARCHITECTURE_PATTERNS\\dentflow\\frontend\\src\\features\\workflows\\WorkflowManagement.tsx", [], [], "C:\\GPT4_PROJECTS\\DENTAL_LAB_ARCHITECTURE_PATTERNS\\dentflow\\frontend\\src\\features\\schedule\\Schedule.tsx", [], [], "C:\\GPT4_PROJECTS\\DENTAL_LAB_ARCHITECTURE_PATTERNS\\dentflow\\frontend\\src\\features\\cases\\CaseDetail.tsx", ["171", "172", "173", "174", "175"], [], "C:\\GPT4_PROJECTS\\DENTAL_LAB_ARCHITECTURE_PATTERNS\\dentflow\\frontend\\src\\features\\reports\\Reports.tsx", [], [], "C:\\GPT4_PROJECTS\\DENTAL_LAB_ARCHITECTURE_PATTERNS\\dentflow\\frontend\\src\\features\\cases\\CreateCase.tsx", ["176", "177", "178", "179", "180", "181", "182", "183", "184", "185", "186", "187", "188", "189", "190", "191", "192", "193", "194", "195", "196"], [], "C:\\GPT4_PROJECTS\\DENTAL_LAB_ARCHITECTURE_PATTERNS\\dentflow\\frontend\\src\\features\\billing\\Billing.tsx", [], [], "C:\\GPT4_PROJECTS\\DENTAL_LAB_ARCHITECTURE_PATTERNS\\dentflow\\frontend\\src\\features\\cases\\CasesList.tsx", ["197", "198", "199", "200", "201", "202", "203", "204", "205", "206", "207", "208", "209", "210", "211", "212", "213"], [], "C:\\GPT4_PROJECTS\\DENTAL_LAB_ARCHITECTURE_PATTERNS\\dentflow\\frontend\\src\\api\\casesService.ts", ["214"], [], "C:\\GPT4_PROJECTS\\DENTAL_LAB_ARCHITECTURE_PATTERNS\\dentflow\\frontend\\src\\hooks\\api.ts", ["215"], [], "C:\\GPT4_PROJECTS\\DENTAL_LAB_ARCHITECTURE_PATTERNS\\dentflow\\frontend\\src\\context\\index.ts", [], [], "C:\\GPT4_PROJECTS\\DENTAL_LAB_ARCHITECTURE_PATTERNS\\dentflow\\frontend\\src\\api\\index.ts", [], [], "C:\\GPT4_PROJECTS\\DENTAL_LAB_ARCHITECTURE_PATTERNS\\dentflow\\frontend\\src\\api\\client.ts", [], [], "C:\\GPT4_PROJECTS\\DENTAL_LAB_ARCHITECTURE_PATTERNS\\dentflow\\frontend\\src\\api\\mockData.ts", ["216"], [], "C:\\GPT4_PROJECTS\\DENTAL_LAB_ARCHITECTURE_PATTERNS\\dentflow\\frontend\\src\\api\\workflowService.ts", [], [], "C:\\GPT4_PROJECTS\\DENTAL_LAB_ARCHITECTURE_PATTERNS\\dentflow\\frontend\\src\\api\\scheduleService.ts", [], [], "C:\\GPT4_PROJECTS\\DENTAL_LAB_ARCHITECTURE_PATTERNS\\dentflow\\frontend\\src\\api\\billingService.ts", [], [], "C:\\GPT4_PROJECTS\\DENTAL_LAB_ARCHITECTURE_PATTERNS\\dentflow\\frontend\\src\\api\\authService.ts", [], [], "C:\\GPT4_PROJECTS\\DENTAL_LAB_ARCHITECTURE_PATTERNS\\dentflow\\frontend\\src\\api\\unifiedClient.ts", [], [], "C:\\GPT4_PROJECTS\\DENTAL_LAB_ARCHITECTURE_PATTERNS\\dentflow\\frontend\\src\\api\\reportsService.ts", [], [], "C:\\GPT4_PROJECTS\\DENTAL_LAB_ARCHITECTURE_PATTERNS\\dentflow\\frontend\\src\\context\\NotificationContext.tsx", [], [], "C:\\GPT4_PROJECTS\\DENTAL_LAB_ARCHITECTURE_PATTERNS\\dentflow\\frontend\\src\\context\\AppContext.tsx", [], [], "C:\\GPT4_PROJECTS\\DENTAL_LAB_ARCHITECTURE_PATTERNS\\dentflow\\frontend\\src\\api\\types.ts", [], [], "C:\\GPT4_PROJECTS\\DENTAL_LAB_ARCHITECTURE_PATTERNS\\dentflow\\frontend\\src\\hooks\\useNotification.ts", ["217"], [], "C:\\GPT4_PROJECTS\\DENTAL_LAB_ARCHITECTURE_PATTERNS\\dentflow\\frontend\\src\\services\\api.ts", ["218"], [], {"ruleId": "219", "severity": 1, "message": "220", "line": 49, "column": 10, "nodeType": "221", "messageId": "222", "endLine": 49, "endColumn": 22}, {"ruleId": "219", "severity": 1, "message": "223", "line": 19, "column": 3, "nodeType": "221", "messageId": "222", "endLine": 19, "endColumn": 8}, {"ruleId": "219", "severity": 1, "message": "224", "line": 39, "column": 3, "nodeType": "221", "messageId": "222", "endLine": 39, "endColumn": 15}, {"ruleId": "219", "severity": 1, "message": "225", "line": 43, "column": 3, "nodeType": "221", "messageId": "222", "endLine": 43, "endColumn": 11}, {"ruleId": "219", "severity": 1, "message": "226", "line": 50, "column": 3, "nodeType": "221", "messageId": "222", "endLine": 50, "endColumn": 16}, {"ruleId": "219", "severity": 1, "message": "227", "line": 52, "column": 3, "nodeType": "221", "messageId": "222", "endLine": 52, "endColumn": 16}, {"ruleId": "219", "severity": 1, "message": "228", "line": 104, "column": 9, "nodeType": "221", "messageId": "222", "endLine": 104, "endColumn": 14}, {"ruleId": "229", "severity": 1, "message": "230", "line": 134, "column": 6, "nodeType": "231", "endLine": 134, "endColumn": 8, "suggestions": "232"}, {"ruleId": "219", "severity": 1, "message": "233", "line": 294, "column": 9, "nodeType": "221", "messageId": "222", "endLine": 294, "endColumn": 25}, {"ruleId": "219", "severity": 1, "message": "234", "line": 33, "column": 3, "nodeType": "221", "messageId": "222", "endLine": 33, "endColumn": 26}, {"ruleId": "219", "severity": 1, "message": "235", "line": 60, "column": 3, "nodeType": "221", "messageId": "222", "endLine": 60, "endColumn": 13}, {"ruleId": "219", "severity": 1, "message": "236", "line": 72, "column": 3, "nodeType": "221", "messageId": "222", "endLine": 72, "endColumn": 7}, {"ruleId": "219", "severity": 1, "message": "237", "line": 73, "column": 3, "nodeType": "221", "messageId": "222", "endLine": 73, "endColumn": 9}, {"ruleId": "219", "severity": 1, "message": "238", "line": 81, "column": 3, "nodeType": "221", "messageId": "222", "endLine": 81, "endColumn": 15}, {"ruleId": "219", "severity": 1, "message": "239", "line": 19, "column": 3, "nodeType": "221", "messageId": "222", "endLine": 19, "endColumn": 14}, {"ruleId": "219", "severity": 1, "message": "240", "line": 30, "column": 3, "nodeType": "221", "messageId": "222", "endLine": 30, "endColumn": 11}, {"ruleId": "219", "severity": 1, "message": "241", "line": 35, "column": 3, "nodeType": "221", "messageId": "222", "endLine": 35, "endColumn": 8}, {"ruleId": "219", "severity": 1, "message": "242", "line": 40, "column": 3, "nodeType": "221", "messageId": "222", "endLine": 40, "endColumn": 8}, {"ruleId": "219", "severity": 1, "message": "226", "line": 49, "column": 3, "nodeType": "221", "messageId": "222", "endLine": 49, "endColumn": 16}, {"ruleId": "219", "severity": 1, "message": "243", "line": 52, "column": 3, "nodeType": "221", "messageId": "222", "endLine": 52, "endColumn": 10}, {"ruleId": "219", "severity": 1, "message": "244", "line": 53, "column": 3, "nodeType": "221", "messageId": "222", "endLine": 53, "endColumn": 6}, {"ruleId": "219", "severity": 1, "message": "245", "line": 54, "column": 3, "nodeType": "221", "messageId": "222", "endLine": 54, "endColumn": 14}, {"ruleId": "219", "severity": 1, "message": "246", "line": 55, "column": 3, "nodeType": "221", "messageId": "222", "endLine": 55, "endColumn": 14}, {"ruleId": "219", "severity": 1, "message": "247", "line": 59, "column": 3, "nodeType": "221", "messageId": "222", "endLine": 59, "endColumn": 8}, {"ruleId": "219", "severity": 1, "message": "248", "line": 60, "column": 3, "nodeType": "221", "messageId": "222", "endLine": 60, "endColumn": 10}, {"ruleId": "219", "severity": 1, "message": "227", "line": 61, "column": 3, "nodeType": "221", "messageId": "222", "endLine": 61, "endColumn": 16}, {"ruleId": "219", "severity": 1, "message": "249", "line": 62, "column": 3, "nodeType": "221", "messageId": "222", "endLine": 62, "endColumn": 7}, {"ruleId": "219", "severity": 1, "message": "238", "line": 63, "column": 3, "nodeType": "221", "messageId": "222", "endLine": 63, "endColumn": 15}, {"ruleId": "219", "severity": 1, "message": "228", "line": 108, "column": 9, "nodeType": "221", "messageId": "222", "endLine": 108, "endColumn": 14}, {"ruleId": "219", "severity": 1, "message": "250", "line": 133, "column": 22, "nodeType": "221", "messageId": "222", "endLine": 133, "endColumn": 35}, {"ruleId": "219", "severity": 1, "message": "251", "line": 139, "column": 20, "nodeType": "221", "messageId": "222", "endLine": 139, "endColumn": 31}, {"ruleId": "219", "severity": 1, "message": "252", "line": 153, "column": 10, "nodeType": "221", "messageId": "222", "endLine": 153, "endColumn": 23}, {"ruleId": "229", "severity": 1, "message": "253", "line": 282, "column": 6, "nodeType": "231", "endLine": 282, "endColumn": 75, "suggestions": "254"}, {"ruleId": "219", "severity": 1, "message": "255", "line": 327, "column": 9, "nodeType": "221", "messageId": "222", "endLine": 327, "endColumn": 25}, {"ruleId": "219", "severity": 1, "message": "256", "line": 332, "column": 9, "nodeType": "221", "messageId": "222", "endLine": 332, "endColumn": 19}, {"ruleId": "219", "severity": 1, "message": "257", "line": 35, "column": 3, "nodeType": "221", "messageId": "222", "endLine": 35, "endColumn": 8}, {"ruleId": "219", "severity": 1, "message": "258", "line": 38, "column": 3, "nodeType": "221", "messageId": "222", "endLine": 38, "endColumn": 9}, {"ruleId": "219", "severity": 1, "message": "259", "line": 39, "column": 3, "nodeType": "221", "messageId": "222", "endLine": 39, "endColumn": 14}, {"ruleId": "219", "severity": 1, "message": "260", "line": 40, "column": 3, "nodeType": "221", "messageId": "222", "endLine": 40, "endColumn": 16}, {"ruleId": "219", "severity": 1, "message": "261", "line": 41, "column": 3, "nodeType": "221", "messageId": "222", "endLine": 41, "endColumn": 16}, {"ruleId": "219", "severity": 1, "message": "262", "line": 67, "column": 3, "nodeType": "221", "messageId": "222", "endLine": 67, "endColumn": 8}, {"ruleId": "219", "severity": 1, "message": "263", "line": 68, "column": 3, "nodeType": "221", "messageId": "222", "endLine": 68, "endColumn": 8}, {"ruleId": "219", "severity": 1, "message": "264", "line": 72, "column": 3, "nodeType": "221", "messageId": "222", "endLine": 72, "endColumn": 7}, {"ruleId": "219", "severity": 1, "message": "265", "line": 129, "column": 10, "nodeType": "221", "messageId": "222", "endLine": 129, "endColumn": 26}, {"ruleId": "219", "severity": 1, "message": "266", "line": 129, "column": 28, "nodeType": "221", "messageId": "222", "endLine": 129, "endColumn": 47}, {"ruleId": "219", "severity": 1, "message": "267", "line": 133, "column": 10, "nodeType": "221", "messageId": "222", "endLine": 133, "endColumn": 26}, {"ruleId": "219", "severity": 1, "message": "268", "line": 133, "column": 28, "nodeType": "221", "messageId": "222", "endLine": 133, "endColumn": 47}, {"ruleId": "219", "severity": 1, "message": "269", "line": 134, "column": 10, "nodeType": "221", "messageId": "222", "endLine": 134, "endColumn": 29}, {"ruleId": "219", "severity": 1, "message": "270", "line": 134, "column": 31, "nodeType": "221", "messageId": "222", "endLine": 134, "endColumn": 53}, {"ruleId": "219", "severity": 1, "message": "271", "line": 261, "column": 9, "nodeType": "221", "messageId": "222", "endLine": 261, "endColumn": 22}, {"ruleId": "219", "severity": 1, "message": "272", "line": 275, "column": 9, "nodeType": "221", "messageId": "222", "endLine": 275, "endColumn": 24}, {"ruleId": "219", "severity": 1, "message": "273", "line": 827, "column": 21, "nodeType": "221", "messageId": "222", "endLine": 827, "endColumn": 33}, {"ruleId": "219", "severity": 1, "message": "274", "line": 309, "column": 13, "nodeType": "221", "messageId": "222", "endLine": 309, "endColumn": 21}, {"ruleId": "219", "severity": 1, "message": "275", "line": 7, "column": 29, "nodeType": "221", "messageId": "222", "endLine": 7, "endColumn": 46}, {"ruleId": "219", "severity": 1, "message": "276", "line": 6, "column": 24, "nodeType": "221", "messageId": "222", "endLine": 6, "endColumn": 37}, {"ruleId": "229", "severity": 1, "message": "277", "line": 39, "column": 6, "nodeType": "231", "endLine": 39, "endColumn": 8, "suggestions": "278"}, {"ruleId": "279", "severity": 1, "message": "280", "line": 509, "column": 1, "nodeType": "281", "endLine": 520, "endColumn": 3}, "@typescript-eslint/no-unused-vars", "'OverviewPage' is defined but never used.", "Identifier", "unusedVar", "'Paper' is defined but never used.", "'TrendingDown' is defined but never used.", "'Schedule' is defined but never used.", "'CalendarToday' is defined but never used.", "'LocalShipping' is defined but never used.", "'theme' is assigned a value but never used.", "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'loadDashboardData'. Either include it or remove the dependency array.", "ArrayExpression", ["282"], "'getPriorityColor' is assigned a value but never used.", "'ListItemSecondaryAction' is defined but never used.", "'LocationOn' is defined but never used.", "'Save' is defined but never used.", "'Cancel' is defined but never used.", "'NavigateNext' is defined but never used.", "'StepContent' is defined but never used.", "'Checkbox' is defined but never used.", "'Alert' is defined but never used.", "'alpha' is defined but never used.", "'Warning' is defined but never used.", "'Add' is defined but never used.", "'PhotoCamera' is defined but never used.", "'Description' is defined but never used.", "'Build' is defined but never used.", "'Science' is defined but never used.", "'Done' is defined but never used.", "'setClinicInfo' is assigned a value but never used.", "'setTimeline' is assigned a value but never used.", "'attachedFiles' is assigned a value but never used.", "React Hook React.useEffect has a missing dependency: 'calculateCost'. Either include it or remove the dependency array.", ["283"], "'handleFileUpload' is assigned a value but never used.", "'removeFile' is assigned a value but never used.", "'Badge' is defined but never used.", "'Dialog' is defined but never used.", "'DialogTitle' is defined but never used.", "'DialogContent' is defined but never used.", "'DialogActions' is defined but never used.", "'Print' is defined but never used.", "'Share' is defined but never used.", "'Sort' is defined but never used.", "'filterMenuAnchor' is assigned a value but never used.", "'setFilterMenuAnchor' is assigned a value but never used.", "'bulkActionDialog' is assigned a value but never used.", "'setBulkActionDialog' is assigned a value but never used.", "'deleteConfirmDialog' is assigned a value but never used.", "'setDeleteConfirmDialog' is assigned a value but never used.", "'getStatusIcon' is assigned a value but never used.", "'getPriorityIcon' is assigned a value but never used.", "'daysUntilDue' is assigned a value but never used.", "'response' is assigned a value but never used.", "'CreateCaseRequest' is defined but never used.", "'WorkflowStage' is defined but never used.", "React Hook useCallback has a missing dependency: 'removeNotification'. Either include it or remove the dependency array.", ["284"], "import/no-anonymous-default-export", "Assign object to a variable before exporting as module default", "ExportDefaultDeclaration", {"desc": "285", "fix": "286"}, {"desc": "287", "fix": "288"}, {"desc": "289", "fix": "290"}, "Update the dependencies array to be: [loadDashboardData]", {"range": "291", "text": "292"}, "Update the dependencies array to be: [caseDetails.service_type, caseDetails.material, timeline.rush_order, calculateCost]", {"range": "293", "text": "294"}, "Update the dependencies array to be: [removeNotification]", {"range": "295", "text": "296"}, [2993, 2995], "[loadDashboardData]", [6971, 7040], "[caseDetails.service_type, caseDetails.material, timeline.rush_order, calculateCost]", [945, 947], "[removeNotification]"]