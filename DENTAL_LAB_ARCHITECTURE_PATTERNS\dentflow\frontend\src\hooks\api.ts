/**
 * React Query Hooks for DentFlow API
 * Provides data fetching, caching, and mutation hooks
 */

import { useQuery, useMutation, useQueryClient, UseQueryOptions, UseMutationOptions } from '@tanstack/react-query';
import { DentFlowAPI, Case, CreateCaseRequest, DashboardStats, Task, Technician, Invoice, Appointment } from '../api';
import { casesService, Case as ApiCase, CreateCaseRequest as ApiCreateCaseRequest } from '../services/api';

// Query keys for React Query
export const queryKeys = {
  cases: ['cases'] as const,
  case: (id: string) => ['cases', id] as const,
  casesByStatus: (status: string) => ['cases', 'status', status] as const,
  overdueCases: ['cases', 'overdue'] as const,
  dashboardStats: ['dashboard', 'stats'] as const,
  tasks: ['tasks'] as const,
  technicians: ['technicians'] as const,
  workflowStages: ['workflow', 'stages'] as const,
  invoices: ['invoices'] as const,
  invoice: (id: string) => ['invoices', id] as const,
  appointments: ['appointments'] as const,
  appointment: (id: string) => ['appointments', id] as const,
  reports: ['reports'] as const,
  currentUser: ['auth', 'current-user'] as const,
};

// ===== AUTHENTICATION HOOKS =====

export const useCurrentUser = (options?: UseQueryOptions) => {
  return useQuery({
    queryKey: queryKeys.currentUser,
    queryFn: () => DentFlowAPI.auth.getCurrentUser(),
    enabled: DentFlowAPI.auth.isAuthenticated(),
    staleTime: 5 * 60 * 1000, // 5 minutes
    ...options,
  });
};

export const useLogin = (options?: UseMutationOptions<any, Error, { email: string; password: string }>) => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: ({ email, password }) => DentFlowAPI.auth.login({ email, password }),
    onSuccess: (data) => {
      queryClient.setQueryData(queryKeys.currentUser, data.user);
      queryClient.invalidateQueries({ queryKey: ['auth'] });
    },
    ...options,
  });
};

export const useLogout = (options?: UseMutationOptions) => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: () => DentFlowAPI.auth.logout(),
    onSuccess: () => {
      queryClient.clear();
    },
    ...options,
  });
};

// ===== CASES HOOKS =====

export const useCases = (options?: UseQueryOptions<ApiCase[]>) => {
  return useQuery({
    queryKey: queryKeys.cases,
    queryFn: async () => {
      const response = await casesService.getCases();
      return response.results || [];
    },
    staleTime: 2 * 60 * 1000, // 2 minutes
    ...options,
  });
};

export const useCase = (id: string, options?: UseQueryOptions<ApiCase>) => {
  return useQuery({
    queryKey: queryKeys.case(id),
    queryFn: () => casesService.getCaseById(id),
    enabled: !!id,
    staleTime: 1 * 60 * 1000, // 1 minute
    ...options,
  });
};

export const useCasesByStatus = (status: string, options?: UseQueryOptions<Case[]>) => {
  return useQuery({
    queryKey: queryKeys.casesByStatus(status),
    queryFn: () => DentFlowAPI.cases.getCasesByStatus(status),
    enabled: !!status,
    staleTime: 1 * 60 * 1000,
    ...options,
  });
};

export const useOverdueCases = (options?: UseQueryOptions<Case[]>) => {
  return useQuery({
    queryKey: queryKeys.overdueCases,
    queryFn: () => DentFlowAPI.cases.getOverdueCases(),
    staleTime: 30 * 1000, // 30 seconds (more critical data)
    ...options,
  });
};

export const useCreateCase = (options?: UseMutationOptions<ApiCase, Error, ApiCreateCaseRequest>) => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (data: ApiCreateCaseRequest) => casesService.createCase(data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: queryKeys.cases });
      queryClient.invalidateQueries({ queryKey: queryKeys.dashboardStats });
    },
    ...options,
  });
};

export const useUpdateCase = (options?: UseMutationOptions<Case, Error, { id: string; data: Partial<Case> }>) => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: ({ id, data }) => DentFlowAPI.cases.updateCase(id, data),
    onSuccess: (updatedCase) => {
      queryClient.setQueryData(queryKeys.case(updatedCase.id), updatedCase);
      queryClient.invalidateQueries({ queryKey: queryKeys.cases });
      queryClient.invalidateQueries({ queryKey: queryKeys.dashboardStats });
    },
    ...options,
  });
};

export const useAdvanceCase = (options?: UseMutationOptions<Case, Error, { id: string; notes?: string }>) => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: ({ id, notes }) => DentFlowAPI.cases.advanceCase(id, { notes }),
    onSuccess: (updatedCase) => {
      queryClient.setQueryData(queryKeys.case(updatedCase.id), updatedCase);
      queryClient.invalidateQueries({ queryKey: queryKeys.cases });
      queryClient.invalidateQueries({ queryKey: queryKeys.tasks });
    },
    ...options,
  });
};

export const useAssignTechnician = (options?: UseMutationOptions<Case, Error, { caseId: string; technicianId: string }>) => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: ({ caseId, technicianId }) => DentFlowAPI.cases.assignTechnician(caseId, { technician_id: technicianId }),
    onSuccess: (updatedCase) => {
      queryClient.setQueryData(queryKeys.case(updatedCase.id), updatedCase);
      queryClient.invalidateQueries({ queryKey: queryKeys.cases });
      queryClient.invalidateQueries({ queryKey: queryKeys.tasks });
    },
    ...options,
  });
};

// ===== DASHBOARD HOOKS =====

export const useDashboardStats = (options?: UseQueryOptions<DashboardStats>) => {
  return useQuery({
    queryKey: queryKeys.dashboardStats,
    queryFn: () => DentFlowAPI.cases.getDashboardStats(),
    staleTime: 30 * 1000, // 30 seconds
    refetchInterval: 60 * 1000, // Refresh every minute
    ...options,
  });
};

// ===== WORKFLOW HOOKS =====

export const useTasks = (filters?: any, options?: UseQueryOptions<Task[]>) => {
  return useQuery({
    queryKey: [...queryKeys.tasks, filters],
    queryFn: () => DentFlowAPI.workflow.getTasks(filters),
    staleTime: 1 * 60 * 1000,
    ...options,
  });
};

export const useTechnicians = (options?: UseQueryOptions<Technician[]>) => {
  return useQuery({
    queryKey: queryKeys.technicians,
    queryFn: () => DentFlowAPI.workflow.getTechnicians(),
    staleTime: 5 * 60 * 1000, // 5 minutes (technician data changes less frequently)
    ...options,
  });
};

export const useWorkflowStages = (options?: UseQueryOptions) => {
  return useQuery({
    queryKey: queryKeys.workflowStages,
    queryFn: () => DentFlowAPI.workflow.getWorkflowStages(),
    staleTime: 10 * 60 * 1000, // 10 minutes (rarely changes)
    ...options,
  });
};

export const useStartTask = (options?: UseMutationOptions<Task, Error, string>) => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: (taskId: string) => DentFlowAPI.workflow.startTask(taskId),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: queryKeys.tasks });
    },
    ...options,
  });
};

export const useCompleteTask = (options?: UseMutationOptions<Task, Error, { taskId: string; notes?: string }>) => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: ({ taskId, notes }) => DentFlowAPI.workflow.completeTask(taskId, notes),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: queryKeys.tasks });
      queryClient.invalidateQueries({ queryKey: queryKeys.cases });
    },
    ...options,
  });
};

// ===== BILLING HOOKS =====

export const useInvoices = (filters?: any, options?: UseQueryOptions<Invoice[]>) => {
  return useQuery({
    queryKey: [...queryKeys.invoices, filters],
    queryFn: () => DentFlowAPI.billing.getInvoices(filters),
    staleTime: 2 * 60 * 1000,
    ...options,
  });
};

export const useInvoice = (id: string, options?: UseQueryOptions<Invoice>) => {
  return useQuery({
    queryKey: queryKeys.invoice(id),
    queryFn: () => DentFlowAPI.billing.getInvoiceById(id),
    enabled: !!id,
    staleTime: 1 * 60 * 1000,
    ...options,
  });
};

export const useCreateInvoice = (options?: UseMutationOptions<Invoice, Error, any>) => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: (data) => DentFlowAPI.billing.createInvoice(data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: queryKeys.invoices });
    },
    ...options,
  });
};

// ===== SCHEDULE HOOKS =====

export const useAppointments = (filters?: any, options?: UseQueryOptions<Appointment[]>) => {
  return useQuery({
    queryKey: [...queryKeys.appointments, filters],
    queryFn: () => DentFlowAPI.schedule.getAppointments(filters),
    staleTime: 1 * 60 * 1000,
    ...options,
  });
};

export const useCreateAppointment = (options?: UseMutationOptions<Appointment, Error, any>) => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: (data) => DentFlowAPI.schedule.createAppointment(data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: queryKeys.appointments });
    },
    ...options,
  });
};

// ===== REPORTS HOOKS =====

export const useReports = (options?: UseQueryOptions) => {
  return useQuery({
    queryKey: queryKeys.reports,
    queryFn: () => DentFlowAPI.reports.getReports(),
    staleTime: 5 * 60 * 1000,
    ...options,
  });
};

export const useGenerateReport = (options?: UseMutationOptions) => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: (data: any) => DentFlowAPI.reports.generateReport(data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: queryKeys.reports });
    },
    ...options,
  });
};

// ===== UTILITY HOOKS =====

export const useApiHealth = () => {
  return useQuery({
    queryKey: ['api', 'health'],
    queryFn: () => DentFlowAPI.healthCheck(),
    staleTime: 30 * 1000,
    refetchInterval: 60 * 1000,
  });
};

// Custom hook for optimistic updates
export const useOptimisticUpdate = <T>(queryKey: any[], updateFn: (old: T) => T) => {
  const queryClient = useQueryClient();
  
  const updateOptimistically = (updateData: any) => {
    queryClient.setQueryData(queryKey, (old: T | undefined) => old ? updateFn(old) : old);
  };
  
  const revert = () => {
    queryClient.invalidateQueries({ queryKey });
  };
  
  return { updateOptimistically, revert };
};
