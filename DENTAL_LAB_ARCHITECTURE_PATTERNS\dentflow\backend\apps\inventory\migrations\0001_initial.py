# Generated by Django 4.2.7 on 2025-07-25 20:11

from decimal import Decimal
import django.core.validators
from django.db import migrations, models
import django.db.models.deletion
import uuid


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ("cases", "0002_alter_case_tenant_alter_clinic_tenant_delete_tenant"),
        ("tenants", "0001_initial"),
    ]

    operations = [
        migrations.CreateModel(
            name="Item",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4, primary_key=True, serialize=False
                    ),
                ),
                ("name", models.CharField(max_length=255)),
                ("description", models.TextField(blank=True)),
                ("item_code", models.CharField(blank=True, max_length=100)),
                (
                    "complexity",
                    models.CharField(
                        choices=[
                            ("simple", "Simple"),
                            ("moderate", "Moderate"),
                            ("complex", "Complex"),
                            ("highly_complex", "Highly Complex"),
                        ],
                        default="simple",
                        max_length=20,
                    ),
                ),
                (
                    "estimated_production_time_minutes",
                    models.PositiveIntegerField(
                        help_text="Estimated time to produce this item"
                    ),
                ),
                (
                    "base_labor_cost",
                    models.DecimalField(
                        decimal_places=2,
                        help_text="Base labor cost for this item",
                        max_digits=10,
                    ),
                ),
                (
                    "markup_percentage",
                    models.DecimalField(
                        decimal_places=2,
                        default=Decimal("50.00"),
                        help_text="Markup percentage over material + labor costs",
                        max_digits=5,
                    ),
                ),
                ("is_active", models.BooleanField(default=True)),
                (
                    "requires_approval",
                    models.BooleanField(
                        default=False,
                        help_text="Whether this item requires special approval",
                    ),
                ),
                ("notes", models.TextField(blank=True)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
            ],
        ),
        migrations.CreateModel(
            name="Material",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4, primary_key=True, serialize=False
                    ),
                ),
                ("name", models.CharField(max_length=255)),
                ("description", models.TextField(blank=True)),
                ("sku", models.CharField(blank=True, max_length=100)),
                ("barcode", models.CharField(blank=True, max_length=100)),
                (
                    "unit_of_measure",
                    models.CharField(
                        choices=[
                            ("g", "Grams"),
                            ("kg", "Kilograms"),
                            ("ml", "Milliliters"),
                            ("l", "Liters"),
                            ("pcs", "Pieces"),
                            ("mm", "Millimeters"),
                            ("cm", "Centimeters"),
                            ("m", "Meters"),
                            ("oz", "Ounces"),
                            ("lb", "Pounds"),
                        ],
                        default="g",
                        max_length=10,
                    ),
                ),
                (
                    "density",
                    models.DecimalField(
                        blank=True,
                        decimal_places=4,
                        help_text="Density in g/cm³",
                        max_digits=8,
                        null=True,
                    ),
                ),
                (
                    "standard_cost",
                    models.DecimalField(
                        decimal_places=4,
                        max_digits=10,
                        validators=[
                            django.core.validators.MinValueValidator(Decimal("0.0001"))
                        ],
                    ),
                ),
                ("currency", models.CharField(default="USD", max_length=3)),
                (
                    "minimum_stock_level",
                    models.DecimalField(
                        decimal_places=2, default=Decimal("0.00"), max_digits=10
                    ),
                ),
                (
                    "maximum_stock_level",
                    models.DecimalField(
                        blank=True, decimal_places=2, max_digits=10, null=True
                    ),
                ),
                (
                    "reorder_point",
                    models.DecimalField(
                        decimal_places=2, default=Decimal("0.00"), max_digits=10
                    ),
                ),
                ("is_active", models.BooleanField(default=True)),
                ("is_hazardous", models.BooleanField(default=False)),
                ("manufacturer", models.CharField(blank=True, max_length=255)),
                ("batch_tracking_required", models.BooleanField(default=False)),
                ("expiry_tracking_required", models.BooleanField(default=False)),
                ("notes", models.TextField(blank=True)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
            ],
        ),
        migrations.CreateModel(
            name="PurchaseOrder",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4, primary_key=True, serialize=False
                    ),
                ),
                ("po_number", models.CharField(max_length=50, unique=True)),
                (
                    "status",
                    models.CharField(
                        choices=[
                            ("draft", "Draft"),
                            ("sent", "Sent"),
                            ("confirmed", "Confirmed"),
                            ("partially_received", "Partially Received"),
                            ("received", "Received"),
                            ("cancelled", "Cancelled"),
                        ],
                        default="draft",
                        max_length=20,
                    ),
                ),
                (
                    "subtotal",
                    models.DecimalField(
                        decimal_places=2, default=Decimal("0.00"), max_digits=15
                    ),
                ),
                (
                    "tax_amount",
                    models.DecimalField(
                        decimal_places=2, default=Decimal("0.00"), max_digits=15
                    ),
                ),
                (
                    "shipping_cost",
                    models.DecimalField(
                        decimal_places=2, default=Decimal("0.00"), max_digits=15
                    ),
                ),
                (
                    "total_amount",
                    models.DecimalField(
                        decimal_places=2, default=Decimal("0.00"), max_digits=15
                    ),
                ),
                ("currency", models.CharField(default="USD", max_length=3)),
                ("order_date", models.DateField()),
                ("expected_delivery_date", models.DateField(blank=True, null=True)),
                ("actual_delivery_date", models.DateField(blank=True, null=True)),
                ("notes", models.TextField(blank=True)),
                ("created_by", models.CharField(blank=True, max_length=100)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
            ],
            options={
                "ordering": ["-created_at"],
            },
        ),
        migrations.CreateModel(
            name="Supplier",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4, primary_key=True, serialize=False
                    ),
                ),
                ("name", models.CharField(max_length=255)),
                ("contact_person", models.CharField(blank=True, max_length=255)),
                ("email", models.EmailField(blank=True, max_length=254)),
                ("phone", models.CharField(blank=True, max_length=20)),
                ("address", models.TextField(blank=True)),
                ("city", models.CharField(blank=True, max_length=100)),
                ("state", models.CharField(blank=True, max_length=100)),
                ("postal_code", models.CharField(blank=True, max_length=20)),
                ("country", models.CharField(blank=True, max_length=100)),
                ("tax_id", models.CharField(blank=True, max_length=50)),
                ("payment_terms", models.CharField(default="Net 30", max_length=100)),
                ("is_active", models.BooleanField(default=True)),
                ("notes", models.TextField(blank=True)),
                ("website", models.URLField(blank=True)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                (
                    "tenant",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="suppliers",
                        to="tenants.tenant",
                    ),
                ),
            ],
        ),
        migrations.CreateModel(
            name="PurchaseOrderItem",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4, primary_key=True, serialize=False
                    ),
                ),
                (
                    "quantity_ordered",
                    models.DecimalField(decimal_places=4, max_digits=12),
                ),
                (
                    "quantity_received",
                    models.DecimalField(
                        decimal_places=4, default=Decimal("0.0000"), max_digits=12
                    ),
                ),
                ("unit_price", models.DecimalField(decimal_places=4, max_digits=10)),
                ("total_price", models.DecimalField(decimal_places=2, max_digits=15)),
                ("notes", models.TextField(blank=True)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                (
                    "material",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="purchase_order_items",
                        to="inventory.material",
                    ),
                ),
                (
                    "purchase_order",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="items",
                        to="inventory.purchaseorder",
                    ),
                ),
            ],
        ),
        migrations.AddField(
            model_name="purchaseorder",
            name="supplier",
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.CASCADE,
                related_name="purchase_orders",
                to="inventory.supplier",
            ),
        ),
        migrations.AddField(
            model_name="purchaseorder",
            name="tenant",
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.CASCADE,
                related_name="purchase_orders",
                to="tenants.tenant",
            ),
        ),
        migrations.CreateModel(
            name="MaterialTransaction",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4, primary_key=True, serialize=False
                    ),
                ),
                (
                    "transaction_type",
                    models.CharField(
                        choices=[
                            ("purchase", "Purchase"),
                            ("usage", "Usage"),
                            ("adjustment", "Inventory Adjustment"),
                            ("transfer", "Transfer"),
                            ("return", "Return"),
                            ("waste", "Waste/Loss"),
                        ],
                        max_length=20,
                    ),
                ),
                ("quantity", models.DecimalField(decimal_places=4, max_digits=12)),
                (
                    "unit_cost",
                    models.DecimalField(
                        blank=True, decimal_places=4, max_digits=10, null=True
                    ),
                ),
                (
                    "total_cost",
                    models.DecimalField(
                        blank=True, decimal_places=2, max_digits=15, null=True
                    ),
                ),
                ("reference_number", models.CharField(blank=True, max_length=100)),
                ("batch_number", models.CharField(blank=True, max_length=100)),
                ("expiry_date", models.DateField(blank=True, null=True)),
                ("notes", models.TextField(blank=True)),
                ("created_by", models.CharField(blank=True, max_length=100)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                (
                    "case",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        to="cases.case",
                    ),
                ),
                (
                    "material",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="transactions",
                        to="inventory.material",
                    ),
                ),
                (
                    "supplier",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        to="inventory.supplier",
                    ),
                ),
            ],
            options={
                "ordering": ["-created_at"],
            },
        ),
        migrations.CreateModel(
            name="MaterialStock",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4, primary_key=True, serialize=False
                    ),
                ),
                (
                    "current_quantity",
                    models.DecimalField(
                        decimal_places=4, default=Decimal("0.0000"), max_digits=12
                    ),
                ),
                (
                    "reserved_quantity",
                    models.DecimalField(
                        decimal_places=4, default=Decimal("0.0000"), max_digits=12
                    ),
                ),
                (
                    "available_quantity",
                    models.DecimalField(
                        decimal_places=4, default=Decimal("0.0000"), max_digits=12
                    ),
                ),
                (
                    "total_value",
                    models.DecimalField(
                        decimal_places=2, default=Decimal("0.00"), max_digits=15
                    ),
                ),
                (
                    "average_cost",
                    models.DecimalField(
                        decimal_places=4, default=Decimal("0.0000"), max_digits=10
                    ),
                ),
                ("is_low_stock", models.BooleanField(default=False)),
                ("is_out_of_stock", models.BooleanField(default=False)),
                ("last_updated", models.DateTimeField(auto_now=True)),
                (
                    "material",
                    models.OneToOneField(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="stock",
                        to="inventory.material",
                    ),
                ),
            ],
        ),
        migrations.CreateModel(
            name="MaterialCategory",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4, primary_key=True, serialize=False
                    ),
                ),
                ("name", models.CharField(max_length=100)),
                ("description", models.TextField(blank=True)),
                ("is_active", models.BooleanField(default=True)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                (
                    "tenant",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="material_categories",
                        to="tenants.tenant",
                    ),
                ),
            ],
            options={
                "verbose_name_plural": "Material Categories",
            },
        ),
        migrations.AddField(
            model_name="material",
            name="category",
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.CASCADE,
                related_name="materials",
                to="inventory.materialcategory",
            ),
        ),
        migrations.AddField(
            model_name="material",
            name="tenant",
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.CASCADE,
                related_name="materials",
                to="tenants.tenant",
            ),
        ),
        migrations.CreateModel(
            name="ItemMaterialComposition",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4, primary_key=True, serialize=False
                    ),
                ),
                (
                    "quantity",
                    models.DecimalField(
                        decimal_places=4,
                        help_text="Quantity of material needed",
                        max_digits=10,
                    ),
                ),
                (
                    "waste_factor",
                    models.DecimalField(
                        decimal_places=2,
                        default=Decimal("10.00"),
                        help_text="Waste percentage (e.g., 10% = 10.00)",
                        max_digits=5,
                    ),
                ),
                (
                    "notes",
                    models.TextField(
                        blank=True,
                        help_text="Special instructions for using this material",
                    ),
                ),
                (
                    "is_optional",
                    models.BooleanField(
                        default=False, help_text="Whether this material is optional"
                    ),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                (
                    "item",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="material_compositions",
                        to="inventory.item",
                    ),
                ),
                (
                    "material",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="item_compositions",
                        to="inventory.material",
                    ),
                ),
            ],
            options={
                "verbose_name": "Item Material Composition",
                "verbose_name_plural": "Item Material Compositions",
            },
        ),
        migrations.CreateModel(
            name="ItemCategory",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4, primary_key=True, serialize=False
                    ),
                ),
                ("name", models.CharField(max_length=100)),
                ("description", models.TextField(blank=True)),
                ("is_active", models.BooleanField(default=True)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                (
                    "tenant",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="item_categories",
                        to="tenants.tenant",
                    ),
                ),
            ],
            options={
                "verbose_name_plural": "Item Categories",
            },
        ),
        migrations.AddField(
            model_name="item",
            name="category",
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.CASCADE,
                related_name="items",
                to="inventory.itemcategory",
            ),
        ),
        migrations.AddField(
            model_name="item",
            name="tenant",
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.CASCADE,
                related_name="items",
                to="tenants.tenant",
            ),
        ),
        migrations.AddIndex(
            model_name="supplier",
            index=models.Index(
                fields=["tenant", "is_active"], name="inventory_s_tenant__8e668b_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="supplier",
            index=models.Index(fields=["name"], name="inventory_s_name_d435cf_idx"),
        ),
        migrations.AlterUniqueTogether(
            name="supplier",
            unique_together={("tenant", "name")},
        ),
        migrations.AddIndex(
            model_name="purchaseorderitem",
            index=models.Index(
                fields=["purchase_order"], name="inventory_p_purchas_55a2cf_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="purchaseorderitem",
            index=models.Index(
                fields=["material"], name="inventory_p_materia_20a25c_idx"
            ),
        ),
        migrations.AlterUniqueTogether(
            name="purchaseorderitem",
            unique_together={("purchase_order", "material")},
        ),
        migrations.AddIndex(
            model_name="purchaseorder",
            index=models.Index(
                fields=["tenant", "status"], name="inventory_p_tenant__909421_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="purchaseorder",
            index=models.Index(
                fields=["supplier", "status"], name="inventory_p_supplie_80088d_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="purchaseorder",
            index=models.Index(
                fields=["po_number"], name="inventory_p_po_numb_319f55_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="purchaseorder",
            index=models.Index(
                fields=["order_date"], name="inventory_p_order_d_472534_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="materialtransaction",
            index=models.Index(
                fields=["material", "transaction_type"],
                name="inventory_m_materia_b40c4d_idx",
            ),
        ),
        migrations.AddIndex(
            model_name="materialtransaction",
            index=models.Index(fields=["case"], name="inventory_m_case_id_1ac146_idx"),
        ),
        migrations.AddIndex(
            model_name="materialtransaction",
            index=models.Index(
                fields=["supplier"], name="inventory_m_supplie_5aa9c4_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="materialtransaction",
            index=models.Index(
                fields=["created_at"], name="inventory_m_created_cbf164_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="materialtransaction",
            index=models.Index(
                fields=["batch_number"], name="inventory_m_batch_n_cc1bd5_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="materialstock",
            index=models.Index(
                fields=["material"], name="inventory_m_materia_609f47_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="materialstock",
            index=models.Index(
                fields=["is_low_stock"], name="inventory_m_is_low__6ba2ba_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="materialstock",
            index=models.Index(
                fields=["is_out_of_stock"], name="inventory_m_is_out__8f481f_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="materialcategory",
            index=models.Index(
                fields=["tenant", "is_active"], name="inventory_m_tenant__91335e_idx"
            ),
        ),
        migrations.AlterUniqueTogether(
            name="materialcategory",
            unique_together={("tenant", "name")},
        ),
        migrations.AddIndex(
            model_name="material",
            index=models.Index(
                fields=["tenant", "is_active"], name="inventory_m_tenant__be7b1f_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="material",
            index=models.Index(
                fields=["category", "is_active"], name="inventory_m_categor_f6aaba_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="material",
            index=models.Index(fields=["sku"], name="inventory_m_sku_012b76_idx"),
        ),
        migrations.AddIndex(
            model_name="material",
            index=models.Index(fields=["name"], name="inventory_m_name_1014de_idx"),
        ),
        migrations.AlterUniqueTogether(
            name="material",
            unique_together={("tenant", "name"), ("tenant", "sku")},
        ),
        migrations.AddIndex(
            model_name="itemmaterialcomposition",
            index=models.Index(fields=["item"], name="inventory_i_item_id_2429cd_idx"),
        ),
        migrations.AddIndex(
            model_name="itemmaterialcomposition",
            index=models.Index(
                fields=["material"], name="inventory_i_materia_ce9f41_idx"
            ),
        ),
        migrations.AlterUniqueTogether(
            name="itemmaterialcomposition",
            unique_together={("item", "material")},
        ),
        migrations.AddIndex(
            model_name="itemcategory",
            index=models.Index(
                fields=["tenant", "is_active"], name="inventory_i_tenant__4a9d2f_idx"
            ),
        ),
        migrations.AlterUniqueTogether(
            name="itemcategory",
            unique_together={("tenant", "name")},
        ),
        migrations.AddIndex(
            model_name="item",
            index=models.Index(
                fields=["tenant", "is_active"], name="inventory_i_tenant__09634c_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="item",
            index=models.Index(
                fields=["category", "is_active"], name="inventory_i_categor_b03a42_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="item",
            index=models.Index(
                fields=["item_code"], name="inventory_i_item_co_e61695_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="item",
            index=models.Index(fields=["name"], name="inventory_i_name_637656_idx"),
        ),
        migrations.AlterUniqueTogether(
            name="item",
            unique_together={("tenant", "name"), ("tenant", "item_code")},
        ),
    ]
