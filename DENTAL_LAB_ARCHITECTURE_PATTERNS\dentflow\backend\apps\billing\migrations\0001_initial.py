# Generated by Django 4.2.7 on 2025-07-25 20:11

from decimal import Decimal
import django.core.validators
from django.db import migrations, models
import django.db.models.deletion
import uuid


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ("tenants", "0001_initial"),
        ("cases", "0002_alter_case_tenant_alter_clinic_tenant_delete_tenant"),
        ("inventory", "0001_initial"),
    ]

    operations = [
        migrations.CreateModel(
            name="Invoice",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4, primary_key=True, serialize=False
                    ),
                ),
                ("invoice_number", models.CharField(max_length=50, unique=True)),
                (
                    "subtotal",
                    models.DecimalField(
                        decimal_places=2, default=Decimal("0.00"), max_digits=10
                    ),
                ),
                (
                    "tax_amount",
                    models.DecimalField(
                        decimal_places=2, default=Decimal("0.00"), max_digits=10
                    ),
                ),
                (
                    "total_amount",
                    models.DecimalField(
                        decimal_places=2, default=Decimal("0.00"), max_digits=10
                    ),
                ),
                (
                    "currency",
                    models.CharField(
                        choices=[
                            ("USD", "US Dollar"),
                            ("EUR", "Euro"),
                            ("GBP", "British Pound"),
                            ("CAD", "Canadian Dollar"),
                        ],
                        default="USD",
                        max_length=3,
                    ),
                ),
                (
                    "status",
                    models.CharField(
                        choices=[
                            ("draft", "Draft"),
                            ("sent", "Sent"),
                            ("paid", "Paid"),
                            ("overdue", "Overdue"),
                            ("cancelled", "Cancelled"),
                        ],
                        default="draft",
                        max_length=20,
                    ),
                ),
                ("due_date", models.DateField()),
                ("notes", models.TextField(blank=True)),
                ("payment_terms", models.CharField(default="Net 30", max_length=255)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                (
                    "clinic",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="invoices",
                        to="cases.clinic",
                    ),
                ),
                (
                    "tenant",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="invoices",
                        to="tenants.tenant",
                    ),
                ),
            ],
            options={
                "ordering": ["-created_at"],
            },
        ),
        migrations.CreateModel(
            name="InvoiceItem",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4, primary_key=True, serialize=False
                    ),
                ),
                (
                    "item_type",
                    models.CharField(
                        choices=[
                            ("item", "Dental Item"),
                            ("service", "Service Only"),
                            ("material", "Material Only"),
                            ("adjustment", "Adjustment"),
                        ],
                        default="item",
                        max_length=20,
                    ),
                ),
                ("description", models.CharField(max_length=255)),
                ("quantity", models.PositiveIntegerField(default=1)),
                (
                    "material_cost_per_unit",
                    models.DecimalField(
                        decimal_places=4, default=Decimal("0.0000"), max_digits=10
                    ),
                ),
                (
                    "labor_cost_per_unit",
                    models.DecimalField(
                        decimal_places=4, default=Decimal("0.0000"), max_digits=10
                    ),
                ),
                ("unit_price", models.DecimalField(decimal_places=2, max_digits=10)),
                (
                    "total_material_cost",
                    models.DecimalField(
                        decimal_places=2, default=Decimal("0.00"), max_digits=10
                    ),
                ),
                (
                    "total_labor_cost",
                    models.DecimalField(
                        decimal_places=2, default=Decimal("0.00"), max_digits=10
                    ),
                ),
                ("total_price", models.DecimalField(decimal_places=2, max_digits=10)),
                (
                    "discount_percentage",
                    models.DecimalField(
                        decimal_places=2, default=Decimal("0.00"), max_digits=5
                    ),
                ),
                (
                    "discount_amount",
                    models.DecimalField(
                        decimal_places=2, default=Decimal("0.00"), max_digits=10
                    ),
                ),
                ("notes", models.TextField(blank=True)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                (
                    "case",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        to="cases.case",
                    ),
                ),
                (
                    "invoice",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="items",
                        to="billing.invoice",
                    ),
                ),
                (
                    "item",
                    models.ForeignKey(
                        blank=True,
                        help_text="Reference to inventory item",
                        null=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        to="inventory.item",
                    ),
                ),
            ],
        ),
        migrations.CreateModel(
            name="PriceList",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4, primary_key=True, serialize=False
                    ),
                ),
                ("name", models.CharField(max_length=255)),
                ("description", models.TextField(blank=True)),
                (
                    "currency",
                    models.CharField(
                        choices=[
                            ("USD", "US Dollar"),
                            ("EUR", "Euro"),
                            ("GBP", "British Pound"),
                            ("CAD", "Canadian Dollar"),
                        ],
                        default="USD",
                        max_length=3,
                    ),
                ),
                ("is_active", models.BooleanField(default=True)),
                ("effective_date", models.DateField()),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                (
                    "tenant",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="price_lists",
                        to="tenants.tenant",
                    ),
                ),
            ],
        ),
        migrations.CreateModel(
            name="Payment",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4, primary_key=True, serialize=False
                    ),
                ),
                ("payment_id", models.CharField(max_length=50, unique=True)),
                (
                    "amount",
                    models.DecimalField(
                        decimal_places=2,
                        max_digits=10,
                        validators=[
                            django.core.validators.MinValueValidator(Decimal("0.01"))
                        ],
                    ),
                ),
                (
                    "currency",
                    models.CharField(
                        choices=[
                            ("USD", "US Dollar"),
                            ("EUR", "Euro"),
                            ("GBP", "British Pound"),
                            ("CAD", "Canadian Dollar"),
                        ],
                        default="USD",
                        max_length=3,
                    ),
                ),
                (
                    "payment_method",
                    models.CharField(
                        choices=[
                            ("credit_card", "Credit Card"),
                            ("debit_card", "Debit Card"),
                            ("bank_transfer", "Bank Transfer"),
                            ("check", "Check"),
                            ("cash", "Cash"),
                            ("other", "Other"),
                        ],
                        max_length=20,
                    ),
                ),
                (
                    "status",
                    models.CharField(
                        choices=[
                            ("pending", "Pending"),
                            ("completed", "Completed"),
                            ("failed", "Failed"),
                            ("refunded", "Refunded"),
                        ],
                        default="pending",
                        max_length=20,
                    ),
                ),
                ("payment_date", models.DateTimeField()),
                ("reference_number", models.CharField(blank=True, max_length=100)),
                ("processor_response", models.JSONField(blank=True, default=dict)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                (
                    "invoice",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="payments",
                        to="billing.invoice",
                    ),
                ),
            ],
            options={
                "ordering": ["-payment_date"],
            },
        ),
        migrations.CreateModel(
            name="ItemPrice",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4, primary_key=True, serialize=False
                    ),
                ),
                (
                    "base_price",
                    models.DecimalField(
                        decimal_places=2,
                        max_digits=10,
                        validators=[
                            django.core.validators.MinValueValidator(Decimal("0.01"))
                        ],
                    ),
                ),
                (
                    "material_cost_override",
                    models.DecimalField(
                        blank=True,
                        decimal_places=2,
                        help_text="Override calculated material cost",
                        max_digits=10,
                        null=True,
                    ),
                ),
                (
                    "labor_cost_override",
                    models.DecimalField(
                        blank=True,
                        decimal_places=2,
                        help_text="Override calculated labor cost",
                        max_digits=10,
                        null=True,
                    ),
                ),
                (
                    "discount_percentage",
                    models.DecimalField(
                        decimal_places=2, default=Decimal("0.00"), max_digits=5
                    ),
                ),
                ("is_active", models.BooleanField(default=True)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                (
                    "item",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="prices",
                        to="inventory.item",
                    ),
                ),
                (
                    "price_list",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="item_prices",
                        to="billing.pricelist",
                    ),
                ),
            ],
        ),
        migrations.CreateModel(
            name="InvoiceItemMaterialUsage",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4, primary_key=True, serialize=False
                    ),
                ),
                (
                    "planned_quantity",
                    models.DecimalField(
                        decimal_places=4,
                        help_text="Planned quantity from item composition",
                        max_digits=10,
                    ),
                ),
                (
                    "actual_quantity",
                    models.DecimalField(
                        decimal_places=4,
                        help_text="Actual quantity used",
                        max_digits=10,
                    ),
                ),
                (
                    "unit_cost",
                    models.DecimalField(
                        decimal_places=4,
                        help_text="Cost per unit at time of usage",
                        max_digits=10,
                    ),
                ),
                (
                    "total_cost",
                    models.DecimalField(
                        decimal_places=2,
                        help_text="Total cost for this material usage",
                        max_digits=10,
                    ),
                ),
                ("batch_number", models.CharField(blank=True, max_length=100)),
                ("usage_date", models.DateTimeField(auto_now_add=True)),
                ("notes", models.TextField(blank=True)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                (
                    "invoice_item",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="material_usage",
                        to="billing.invoiceitem",
                    ),
                ),
                (
                    "material",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="invoice_usage",
                        to="inventory.material",
                    ),
                ),
            ],
        ),
        migrations.CreateModel(
            name="ServicePrice",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4, primary_key=True, serialize=False
                    ),
                ),
                (
                    "service_type",
                    models.CharField(
                        choices=[
                            ("crown", "Crown"),
                            ("bridge", "Bridge"),
                            ("denture", "Denture"),
                            ("implant", "Implant Crown"),
                            ("veneer", "Veneer"),
                            ("inlay", "Inlay"),
                            ("onlay", "Onlay"),
                            ("nightguard", "Night Guard"),
                            ("retainer", "Retainer"),
                        ],
                        max_length=50,
                    ),
                ),
                ("description", models.CharField(blank=True, max_length=255)),
                (
                    "base_price",
                    models.DecimalField(
                        decimal_places=2,
                        max_digits=10,
                        validators=[
                            django.core.validators.MinValueValidator(Decimal("0.01"))
                        ],
                    ),
                ),
                ("is_active", models.BooleanField(default=True)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                (
                    "price_list",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="service_prices",
                        to="billing.pricelist",
                    ),
                ),
            ],
            options={
                "indexes": [
                    models.Index(
                        fields=["price_list", "service_type"],
                        name="billing_ser_price_l_d601c6_idx",
                    ),
                    models.Index(
                        fields=["is_active"], name="billing_ser_is_acti_a3cf21_idx"
                    ),
                ],
                "unique_together": {("price_list", "service_type")},
            },
        ),
        migrations.AddIndex(
            model_name="pricelist",
            index=models.Index(
                fields=["tenant", "is_active"], name="billing_pri_tenant__b8d8bc_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="pricelist",
            index=models.Index(
                fields=["effective_date"], name="billing_pri_effecti_db0def_idx"
            ),
        ),
        migrations.AlterUniqueTogether(
            name="pricelist",
            unique_together={("tenant", "name")},
        ),
        migrations.AddIndex(
            model_name="payment",
            index=models.Index(
                fields=["invoice", "status"], name="billing_pay_invoice_a67165_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="payment",
            index=models.Index(
                fields=["payment_date"], name="billing_pay_payment_bed741_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="payment",
            index=models.Index(
                fields=["payment_id"], name="billing_pay_payment_27051a_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="itemprice",
            index=models.Index(
                fields=["price_list", "is_active"],
                name="billing_ite_price_l_21382c_idx",
            ),
        ),
        migrations.AddIndex(
            model_name="itemprice",
            index=models.Index(
                fields=["item", "is_active"], name="billing_ite_item_id_2810e3_idx"
            ),
        ),
        migrations.AlterUniqueTogether(
            name="itemprice",
            unique_together={("price_list", "item")},
        ),
        migrations.AddIndex(
            model_name="invoiceitemmaterialusage",
            index=models.Index(
                fields=["invoice_item"], name="billing_inv_invoice_916e45_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="invoiceitemmaterialusage",
            index=models.Index(
                fields=["material"], name="billing_inv_materia_1c8545_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="invoiceitemmaterialusage",
            index=models.Index(
                fields=["usage_date"], name="billing_inv_usage_d_48e5c4_idx"
            ),
        ),
        migrations.AlterUniqueTogether(
            name="invoiceitemmaterialusage",
            unique_together={("invoice_item", "material")},
        ),
        migrations.AddIndex(
            model_name="invoiceitem",
            index=models.Index(
                fields=["invoice"], name="billing_inv_invoice_563f13_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="invoiceitem",
            index=models.Index(fields=["case"], name="billing_inv_case_id_1cb369_idx"),
        ),
        migrations.AddIndex(
            model_name="invoiceitem",
            index=models.Index(fields=["item"], name="billing_inv_item_id_8f1e8b_idx"),
        ),
        migrations.AddIndex(
            model_name="invoiceitem",
            index=models.Index(
                fields=["item_type"], name="billing_inv_item_ty_025328_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="invoice",
            index=models.Index(
                fields=["tenant", "status"], name="billing_inv_tenant__f82d44_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="invoice",
            index=models.Index(
                fields=["clinic", "status"], name="billing_inv_clinic__b6458b_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="invoice",
            index=models.Index(
                fields=["due_date"], name="billing_inv_due_dat_e51895_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="invoice",
            index=models.Index(
                fields=["invoice_number"], name="billing_inv_invoice_70511c_idx"
            ),
        ),
    ]
