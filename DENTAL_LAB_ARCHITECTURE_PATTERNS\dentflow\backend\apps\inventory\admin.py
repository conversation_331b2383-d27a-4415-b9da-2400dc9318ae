from django.contrib import admin
from .models import (
    MaterialCategory, Supplier, Material, MaterialStock, MaterialTransaction,
    PurchaseOrder, PurchaseOrderItem, ItemCategory, Item, ItemMaterialComposition
)


@admin.register(MaterialCategory)
class MaterialCategoryAdmin(admin.ModelAdmin):
    list_display = ['name', 'tenant', 'is_active', 'created_at']
    list_filter = ['is_active', 'created_at']
    search_fields = ['name', 'description']
    ordering = ['name']


@admin.register(Supplier)
class SupplierAdmin(admin.ModelAdmin):
    list_display = ['name', 'tenant', 'contact_person', 'email', 'phone', 'is_active']
    list_filter = ['is_active', 'country', 'created_at']
    search_fields = ['name', 'contact_person', 'email']
    ordering = ['name']
    fieldsets = (
        ('Basic Information', {
            'fields': ('tenant', 'name', 'contact_person', 'email', 'phone')
        }),
        ('Address', {
            'fields': ('address', 'city', 'state', 'postal_code', 'country')
        }),
        ('Business Details', {
            'fields': ('tax_id', 'payment_terms', 'is_active')
        }),
        ('Additional Information', {
            'fields': ('notes', 'website')
        }),
    )


@admin.register(Material)
class MaterialAdmin(admin.ModelAdmin):
    list_display = ['name', 'sku', 'category', 'tenant', 'standard_cost', 'unit_of_measure', 'is_active']
    list_filter = ['is_active', 'category', 'unit_of_measure', 'is_hazardous', 'created_at']
    search_fields = ['name', 'sku', 'description']
    ordering = ['name']
    fieldsets = (
        ('Basic Information', {
            'fields': ('tenant', 'category', 'name', 'description', 'sku', 'barcode')
        }),
        ('Physical Properties', {
            'fields': ('unit_of_measure', 'density')
        }),
        ('Cost Information', {
            'fields': ('standard_cost', 'currency')
        }),
        ('Inventory Settings', {
            'fields': ('minimum_stock_level', 'maximum_stock_level', 'reorder_point')
        }),
        ('Status & Properties', {
            'fields': ('is_active', 'is_hazardous', 'batch_tracking_required', 'expiry_tracking_required')
        }),
        ('Additional Information', {
            'fields': ('manufacturer', 'notes')
        }),
    )


@admin.register(MaterialStock)
class MaterialStockAdmin(admin.ModelAdmin):
    list_display = ['material', 'current_quantity', 'available_quantity', 'reserved_quantity', 'is_low_stock', 'is_out_of_stock']
    list_filter = ['is_low_stock', 'is_out_of_stock', 'last_updated']
    search_fields = ['material__name', 'material__sku']
    readonly_fields = ['available_quantity', 'is_low_stock', 'is_out_of_stock', 'total_value']
    ordering = ['material__name']


@admin.register(MaterialTransaction)
class MaterialTransactionAdmin(admin.ModelAdmin):
    list_display = ['material', 'transaction_type', 'quantity', 'unit_cost', 'total_cost', 'created_at']
    list_filter = ['transaction_type', 'created_at', 'supplier']
    search_fields = ['material__name', 'reference_number', 'batch_number']
    ordering = ['-created_at']
    readonly_fields = ['total_cost']


class PurchaseOrderItemInline(admin.TabularInline):
    model = PurchaseOrderItem
    extra = 0
    readonly_fields = ['total_price', 'quantity_pending']


@admin.register(PurchaseOrder)
class PurchaseOrderAdmin(admin.ModelAdmin):
    list_display = ['po_number', 'supplier', 'status', 'total_amount', 'order_date', 'expected_delivery_date']
    list_filter = ['status', 'order_date', 'expected_delivery_date']
    search_fields = ['po_number', 'supplier__name']
    ordering = ['-created_at']
    inlines = [PurchaseOrderItemInline]
    fieldsets = (
        ('Order Information', {
            'fields': ('tenant', 'supplier', 'po_number', 'status')
        }),
        ('Financial Details', {
            'fields': ('subtotal', 'tax_amount', 'shipping_cost', 'total_amount', 'currency')
        }),
        ('Dates', {
            'fields': ('order_date', 'expected_delivery_date', 'actual_delivery_date')
        }),
        ('Additional Information', {
            'fields': ('notes', 'created_by')
        }),
    )


@admin.register(ItemCategory)
class ItemCategoryAdmin(admin.ModelAdmin):
    list_display = ['name', 'tenant', 'is_active', 'created_at']
    list_filter = ['is_active', 'created_at']
    search_fields = ['name', 'description']
    ordering = ['name']


class ItemMaterialCompositionInline(admin.TabularInline):
    model = ItemMaterialComposition
    extra = 0
    readonly_fields = ['get_total_quantity_needed', 'get_material_cost']


@admin.register(Item)
class ItemAdmin(admin.ModelAdmin):
    list_display = ['name', 'item_code', 'category', 'tenant', 'complexity', 'base_labor_cost', 'is_active']
    list_filter = ['is_active', 'category', 'complexity', 'created_at']
    search_fields = ['name', 'item_code', 'description']
    ordering = ['name']
    inlines = [ItemMaterialCompositionInline]
    readonly_fields = ['calculate_material_cost', 'calculate_total_cost', 'calculate_selling_price']
    fieldsets = (
        ('Basic Information', {
            'fields': ('tenant', 'category', 'name', 'description', 'item_code')
        }),
        ('Production Details', {
            'fields': ('complexity', 'estimated_production_time_minutes')
        }),
        ('Pricing', {
            'fields': ('base_labor_cost', 'markup_percentage')
        }),
        ('Calculated Costs (Read-only)', {
            'fields': ('calculate_material_cost', 'calculate_total_cost', 'calculate_selling_price'),
            'classes': ('collapse',)
        }),
        ('Status', {
            'fields': ('is_active', 'requires_approval')
        }),
        ('Additional Information', {
            'fields': ('notes',)
        }),
    )


@admin.register(ItemMaterialComposition)
class ItemMaterialCompositionAdmin(admin.ModelAdmin):
    list_display = ['item', 'material', 'quantity', 'waste_factor', 'get_total_quantity_needed', 'get_material_cost']
    list_filter = ['is_optional', 'item__category', 'material__category']
    search_fields = ['item__name', 'material__name']
    ordering = ['item__name', 'material__name']
    readonly_fields = ['get_total_quantity_needed', 'get_material_cost']
