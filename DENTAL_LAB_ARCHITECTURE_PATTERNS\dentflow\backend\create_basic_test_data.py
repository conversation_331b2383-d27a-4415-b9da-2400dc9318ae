"""
Basic test data creation for DentFlow
Run this in Django shell: exec(open('create_basic_test_data.py').read())
"""

from decimal import Decimal
from datetime import datetime, timedelta
from django.contrib.auth.models import User
from apps.tenants.models import Tenant
from apps.inventory.models import MaterialCategory, Material, ItemCategory, Item, ItemMaterialComposition
from apps.billing.models import PriceList, ItemPrice
from apps.cases.models import Case, CaseStatus

print("🚀 Creating basic test data...")

# Create admin user
admin_user, created = User.objects.get_or_create(
    username='admin',
    defaults={
        'email': '<EMAIL>',
        'first_name': 'Admin',
        'last_name': 'User',
        'is_staff': True,
        'is_superuser': True
    }
)
if created or not admin_user.check_password('admin123'):
    admin_user.set_password('admin123')
    admin_user.save()
print(f"✅ Admin user: {'created' if created else 'verified'}")

# Create tenant
tenant, created = Tenant.objects.get_or_create(
    subdomain='demo-lab',
    defaults={
        'name': 'Demo Dental Laboratory',
        'email': '<EMAIL>',
        'phone': '******-0123',
        'address': '123 Dental Street, Lab City, LC 12345',
        'plan': 'professional',
        'max_users': 10,
        'max_cases_per_month': 100,
        'is_active': True
    }
)
print(f"✅ Tenant: {'created' if created else 'verified'}")

# Create material categories
ceramic_cat, _ = MaterialCategory.objects.get_or_create(
    name='Ceramics',
    defaults={'description': 'Ceramic materials for dental restorations'}
)
metal_cat, _ = MaterialCategory.objects.get_or_create(
    name='Metals',
    defaults={'description': 'Metal alloys for dental work'}
)
print("✅ Material categories created")

# Create materials
zirconia, _ = Material.objects.get_or_create(
    name='Zirconia Block - High Translucency',
    defaults={
        'category': ceramic_cat,
        'unit': 'piece',
        'standard_cost': Decimal('52.00'),
        'current_stock': Decimal('30.0000'),
        'minimum_stock': Decimal('5.0000'),
        'description': 'High-strength, high-translucency zirconia block'
    }
)

titanium, _ = Material.objects.get_or_create(
    name='Titanium Grade 2 Alloy',
    defaults={
        'category': metal_cat,
        'unit': 'gram',
        'standard_cost': Decimal('3.20'),
        'current_stock': Decimal('800.0000'),
        'minimum_stock': Decimal('100.0000'),
        'description': 'Biocompatible titanium alloy'
    }
)

porcelain, _ = Material.objects.get_or_create(
    name='Feldspathic Porcelain Powder',
    defaults={
        'category': ceramic_cat,
        'unit': 'gram',
        'standard_cost': Decimal('0.85'),
        'current_stock': Decimal('2500.0000'),
        'minimum_stock': Decimal('500.0000'),
        'description': 'Traditional feldspathic porcelain for layering'
    }
)
print("✅ Materials created")

# Create item categories
crown_cat, _ = ItemCategory.objects.get_or_create(
    name='Crowns',
    defaults={'description': 'All types of dental crowns'}
)
bridge_cat, _ = ItemCategory.objects.get_or_create(
    name='Bridges',
    defaults={'description': 'Fixed dental bridges'}
)
print("✅ Item categories created")

# Create items
zirconia_crown, _ = Item.objects.get_or_create(
    name='Zirconia Crown - Anterior',
    defaults={
        'category': crown_cat,
        'description': 'High-translucency zirconia crown for anterior teeth',
        'estimated_production_time_minutes': 180,
        'is_active': True
    }
)

titanium_bridge, _ = Item.objects.get_or_create(
    name='Titanium Bridge (3-unit)',
    defaults={
        'category': bridge_cat,
        'description': '3-unit titanium bridge for posterior region',
        'estimated_production_time_minutes': 360,
        'is_active': True
    }
)
print("✅ Items created")

# Create material compositions
comp1, _ = ItemMaterialComposition.objects.get_or_create(
    item=zirconia_crown,
    material=zirconia,
    defaults={
        'quantity': Decimal('0.6000'),
        'waste_factor': Decimal('12.00'),
        'notes': 'Zirconia block for crown structure'
    }
)

comp2, _ = ItemMaterialComposition.objects.get_or_create(
    item=zirconia_crown,
    material=porcelain,
    defaults={
        'quantity': Decimal('2.5000'),
        'waste_factor': Decimal('8.00'),
        'notes': 'Porcelain for layering'
    }
)

comp3, _ = ItemMaterialComposition.objects.get_or_create(
    item=titanium_bridge,
    material=titanium,
    defaults={
        'quantity': Decimal('25.0000'),
        'waste_factor': Decimal('8.00'),
        'notes': 'Titanium for 3-unit bridge framework'
    }
)
print("✅ Material compositions created")

# Create price list
price_list, _ = PriceList.objects.get_or_create(
    name='Standard Pricing 2024',
    defaults={
        'description': 'Standard laboratory pricing for 2024',
        'is_active': True,
        'effective_date': datetime.now().date()
    }
)

# Create item prices
price1, _ = ItemPrice.objects.get_or_create(
    price_list=price_list,
    item=zirconia_crown,
    defaults={
        'base_price': Decimal('185.00'),
        'labor_cost': Decimal('95.00'),
        'markup_percentage': Decimal('35.00')
    }
)

price2, _ = ItemPrice.objects.get_or_create(
    price_list=price_list,
    item=titanium_bridge,
    defaults={
        'base_price': Decimal('520.00'),
        'labor_cost': Decimal('280.00'),
        'markup_percentage': Decimal('38.00')
    }
)
print("✅ Item prices created")

# Create sample cases
case1, _ = Case.objects.get_or_create(
    case_number='DEMO-2024-001',
    defaults={
        'tenant': tenant,
        'patient_name': 'John Doe',
        'dentist_name': 'Dr. Smith',
        'service_type': 'crown',
        'priority': 'normal',
        'due_date': datetime.now().date() + timedelta(days=7),
        'notes': 'Anterior crown replacement',
        'created_by': admin_user
    }
)

case2, _ = Case.objects.get_or_create(
    case_number='DEMO-2024-002',
    defaults={
        'tenant': tenant,
        'patient_name': 'Jane Smith',
        'dentist_name': 'Dr. Johnson',
        'service_type': 'bridge',
        'priority': 'high',
        'due_date': datetime.now().date() + timedelta(days=5),
        'notes': '3-unit posterior bridge',
        'created_by': admin_user
    }
)
print("✅ Sample cases created")

print("\n🎉 Basic test data creation completed!")
print("📊 Summary:")
print(f"   Materials: {Material.objects.count()}")
print(f"   Items: {Item.objects.count()}")
print(f"   Compositions: {ItemMaterialComposition.objects.count()}")
print(f"   Cases: {Case.objects.count()}")
print(f"   Item Prices: {ItemPrice.objects.count()}")

print("\n💰 Cost Calculation Examples:")
print("Zirconia Crown Material Costs:")
for comp in zirconia_crown.material_compositions.all():
    cost = comp.get_material_cost()
    print(f"   {comp.material.name}: {comp.get_total_quantity_needed()} {comp.material.unit} × ${comp.material.standard_cost} = ${cost}")

total_material_cost = sum([comp.get_material_cost() for comp in zirconia_crown.material_compositions.all()])
print(f"   Total Material Cost: ${total_material_cost}")

print("\n🌐 Access Points:")
print("   Frontend: http://localhost:3001")
print("   Admin: http://localhost:8001/admin")
print("   Login: admin / admin123")

print("\n✅ Ready for comprehensive testing!")
