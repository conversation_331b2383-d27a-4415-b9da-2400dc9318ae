{"ast": null, "code": "/**\n * Cases API Service - Django REST API integration with Mock Fallback\n */\n\nimport axios from 'axios';\nimport { apiClient } from './client';\nimport { mockCases, mockDashboardStats, getMockCasesByStatus, getMockOverdueCases } from './mockData';\nexport class CasesService {\n  /**\n   * Get all cases with fallback to mock data\n   */\n  static async getCases() {\n    try {\n      if (!this.isBackendAvailable) {\n        console.log('🔄 Using mock data (backend unavailable)');\n        return mockCases;\n      }\n      const cases = await apiClient.get('/cases/');\n      console.log('✅ Backend data loaded successfully');\n      this.isBackendAvailable = true;\n      return cases;\n    } catch (err) {\n      console.warn('⚠️ Backend unavailable, using mock data:', err.message);\n      this.isBackendAvailable = false;\n      return mockCases;\n    }\n  }\n\n  /**\n   * Get case by ID with fallback\n   */\n  static async getCase(caseId) {\n    try {\n      if (!this.isBackendAvailable) {\n        const mockCase = mockCases.find(c => c.id === caseId);\n        if (!mockCase) throw new Error('Case not found');\n        return mockCase;\n      }\n      const case_ = await apiClient.get(`/cases/${caseId}/`);\n      this.isBackendAvailable = true;\n      return case_;\n    } catch (err) {\n      console.warn('⚠️ Backend unavailable for case fetch, using mock data');\n      this.isBackendAvailable = false;\n      const mockCase = mockCases.find(c => c.id === caseId);\n      if (!mockCase) throw new Error('Case not found');\n      return mockCase;\n    }\n  }\n\n  /**\n   * Create new case with fallback\n   */\n  static async createCase(data) {\n    try {\n      if (!this.isBackendAvailable) {\n        // Simulate case creation with mock data\n        const newCase = {\n          id: `LAB-${new Date().toISOString().split('T')[0]}-${Math.random().toString(36).substr(2, 3).toUpperCase()}`,\n          clinic: mockCases[0].clinic,\n          // Use first clinic as default\n          patient_name: data.patient_name,\n          tooth_number: data.tooth_number,\n          service_type: data.service_type,\n          priority: data.priority || 'normal',\n          status: 'received',\n          current_stage_index: 0,\n          workflow_stages: [{\n            name: 'received',\n            department: 'intake',\n            estimated_duration_minutes: 30,\n            auto_assign: true,\n            requires_quality_check: false\n          }],\n          assigned_technician_id: undefined,\n          due_date: new Date(Date.now() + 3 * 24 * 60 * 60 * 1000).toISOString(),\n          created_at: new Date().toISOString(),\n          updated_at: new Date().toISOString(),\n          notes: data.notes ? [data.notes] : [],\n          files: (data.files || []).map(file => ({\n            id: `file-${Math.random().toString(36).substr(2, 9)}`,\n            name: file.name,\n            size: file.size,\n            url: URL.createObjectURL(file),\n            uploaded_at: new Date().toISOString(),\n            uploaded_by: 'current-user'\n          })),\n          days_until_due: 3,\n          is_overdue: false,\n          current_stage: 'Received'\n        };\n        console.log('✅ Mock case created:', newCase.id);\n        mockCases.unshift(newCase); // Add to beginning of array\n        return newCase;\n      }\n      const case_ = await apiClient.post('/cases/', data);\n      this.isBackendAvailable = true;\n      return case_;\n    } catch (err) {\n      console.warn('⚠️ Backend unavailable for case creation, using mock creation');\n      this.isBackendAvailable = false;\n      // Fallback to mock creation (same logic as above)\n      return this.createCase(data);\n    }\n  }\n  /**\n   * Update case with fallback\n   */\n  static async updateCase(caseId, data) {\n    try {\n      if (!this.isBackendAvailable) {\n        const caseIndex = mockCases.findIndex(c => c.id === caseId);\n        if (caseIndex === -1) throw new Error('Case not found');\n        mockCases[caseIndex] = {\n          ...mockCases[caseIndex],\n          ...data,\n          updated_at: new Date().toISOString()\n        };\n        console.log('✅ Mock case updated:', caseId);\n        return mockCases[caseIndex];\n      }\n      const case_ = await apiClient.patch(`/cases/${caseId}/`, data);\n      this.isBackendAvailable = true;\n      return case_;\n    } catch (err) {\n      console.warn('⚠️ Backend unavailable for case update, using mock update');\n      this.isBackendAvailable = false;\n      return this.updateCase(caseId, data);\n    }\n  }\n\n  /**\n   * Delete case with fallback\n   */\n  static async deleteCase(caseId) {\n    try {\n      if (!this.isBackendAvailable) {\n        const caseIndex = mockCases.findIndex(c => c.id === caseId);\n        if (caseIndex === -1) throw new Error('Case not found');\n        mockCases.splice(caseIndex, 1);\n        console.log('✅ Mock case deleted:', caseId);\n        return;\n      }\n      await apiClient.delete(`/cases/${caseId}/`);\n      this.isBackendAvailable = true;\n    } catch (err) {\n      console.warn('⚠️ Backend unavailable for case deletion, using mock deletion');\n      this.isBackendAvailable = false;\n      return this.deleteCase(caseId);\n    }\n  }\n\n  /**\n   * Advance case to next stage with fallback\n   */\n  static async advanceCase(caseId, data = {}) {\n    try {\n      if (!this.isBackendAvailable) {\n        const caseIndex = mockCases.findIndex(c => c.id === caseId);\n        if (caseIndex === -1) throw new Error('Case not found');\n        const case_ = mockCases[caseIndex];\n        const nextStageIndex = case_.current_stage_index + 1;\n        if (nextStageIndex < case_.workflow_stages.length) {\n          const nextStage = case_.workflow_stages[nextStageIndex];\n          case_.current_stage_index = nextStageIndex;\n          case_.status = nextStage.name;\n          case_.current_stage = nextStage.name;\n          case_.assigned_technician_id = undefined; // Clear assignment\n        } else {\n          // Move to quality control or delivery\n          case_.status = 'qc';\n          case_.current_stage = 'Quality Control';\n        }\n        case_.updated_at = new Date().toISOString();\n        if (data.notes) {\n          case_.notes.push(`${new Date().toISOString()}: ${data.notes}`);\n        }\n        console.log('✅ Mock case advanced:', caseId, 'to', case_.current_stage);\n        return case_;\n      }\n      const case_ = await apiClient.post(`/cases/${caseId}/advance/`, data);\n      this.isBackendAvailable = true;\n      return case_;\n    } catch (err) {\n      console.warn('⚠️ Backend unavailable for case advance, using mock advance');\n      this.isBackendAvailable = false;\n      return this.advanceCase(caseId, data);\n    }\n  }\n  /**\n   * Assign technician to case with fallback\n   */\n  static async assignTechnician(caseId, data) {\n    try {\n      if (!this.isBackendAvailable) {\n        const caseIndex = mockCases.findIndex(c => c.id === caseId);\n        if (caseIndex === -1) throw new Error('Case not found');\n        mockCases[caseIndex].assigned_technician_id = data.technician_id;\n        mockCases[caseIndex].updated_at = new Date().toISOString();\n        console.log('✅ Mock technician assigned:', data.technician_id, 'to case:', caseId);\n        return mockCases[caseIndex];\n      }\n      const case_ = await apiClient.post(`/cases/${caseId}/assign_technician/`, data);\n      this.isBackendAvailable = true;\n      return case_;\n    } catch (err) {\n      console.warn('⚠️ Backend unavailable for technician assignment, using mock assignment');\n      this.isBackendAvailable = false;\n      return this.assignTechnician(caseId, data);\n    }\n  }\n\n  /**\n   * Get overdue cases with fallback\n   */\n  static async getOverdueCases() {\n    try {\n      if (!this.isBackendAvailable) {\n        return getMockOverdueCases();\n      }\n      const cases = await apiClient.get('/cases/overdue/');\n      this.isBackendAvailable = true;\n      return cases;\n    } catch (err) {\n      console.warn('⚠️ Backend unavailable for overdue cases, using mock data');\n      this.isBackendAvailable = false;\n      return getMockOverdueCases();\n    }\n  }\n\n  /**\n   * Get cases by status with fallback\n   */\n  static async getCasesByStatus(status) {\n    try {\n      if (!this.isBackendAvailable) {\n        return getMockCasesByStatus(status);\n      }\n      const cases = await apiClient.get(`/cases/by_status/?status=${status}`);\n      this.isBackendAvailable = true;\n      return cases;\n    } catch (err) {\n      console.warn('⚠️ Backend unavailable for status filter, using mock data');\n      this.isBackendAvailable = false;\n      return getMockCasesByStatus(status);\n    }\n  }\n\n  /**\n   * Get dashboard statistics with fallback\n   */\n  static async getDashboardStats() {\n    try {\n      if (!this.isBackendAvailable) {\n        console.log('📊 Using mock dashboard stats');\n        return mockDashboardStats;\n      }\n\n      // Try to get real data from backend\n      const cases = await this.getCases();\n      const today = new Date().toISOString().split('T')[0];\n      const stats = {\n        active_cases: cases.filter(c => !['delivered', 'cancelled'].includes(c.status)).length,\n        completed_today: cases.filter(c => c.status === 'delivered' && c.updated_at.startsWith(today)).length,\n        pending_qc: cases.filter(c => c.status === 'qc').length,\n        revenue_today: 2850,\n        // This would come from billing service\n        overdue_cases: cases.filter(c => c.is_overdue).length\n      };\n      console.log('📊 Real dashboard stats calculated');\n      this.isBackendAvailable = true;\n      return stats;\n    } catch (error) {\n      console.warn('⚠️ Backend unavailable for dashboard stats, using mock data');\n      this.isBackendAvailable = false;\n      return mockDashboardStats;\n    }\n  }\n\n  /**\n   * Check backend health\n   */\n  static async checkBackendHealth() {\n    try {\n      // Try a simple health check - health endpoint is at root level, not under /api/v1\n      const response = await axios.get('http://localhost:8001/health/');\n      this.isBackendAvailable = true;\n      console.log('✅ Backend health check passed');\n      return true;\n    } catch (error) {\n      this.isBackendAvailable = false;\n      console.log('❌ Backend health check failed');\n      return false;\n    }\n  }\n\n  /**\n   * Get backend status\n   */\n  static getBackendStatus() {\n    return {\n      available: this.isBackendAvailable,\n      mode: this.isBackendAvailable ? 'live' : 'mock'\n    };\n  }\n}\nCasesService.isBackendAvailable = true;", "map": {"version": 3, "names": ["axios", "apiClient", "mockCases", "mockDashboardStats", "getMockCasesByStatus", "getMockOverdueCases", "CasesService", "getCases", "isBackendAvailable", "console", "log", "cases", "get", "err", "warn", "message", "getCase", "caseId", "mockCase", "find", "c", "id", "Error", "case_", "createCase", "data", "newCase", "Date", "toISOString", "split", "Math", "random", "toString", "substr", "toUpperCase", "clinic", "patient_name", "tooth_number", "service_type", "priority", "status", "current_stage_index", "workflow_stages", "name", "department", "estimated_duration_minutes", "auto_assign", "requires_quality_check", "assigned_technician_id", "undefined", "due_date", "now", "created_at", "updated_at", "notes", "files", "map", "file", "size", "url", "URL", "createObjectURL", "uploaded_at", "uploaded_by", "days_until_due", "is_overdue", "current_stage", "unshift", "post", "updateCase", "caseIndex", "findIndex", "patch", "deleteCase", "splice", "delete", "advanceCase", "nextStageIndex", "length", "nextStage", "push", "assignTechnician", "technician_id", "getOverdueCases", "getCasesByStatus", "getDashboardStats", "today", "stats", "active_cases", "filter", "includes", "completed_today", "startsWith", "pending_qc", "revenue_today", "overdue_cases", "error", "checkBackendHealth", "response", "getBackendStatus", "available", "mode"], "sources": ["C:/GPT4_PROJECTS/DENTAL_LAB_ARCHITECTURE_PATTERNS/dentflow/frontend/src/api/casesService.ts"], "sourcesContent": ["/**\n * Cases API Service - Django REST API integration with Mock Fallback\n */\n\nimport axios from 'axios';\nimport { apiClient } from './client';\nimport { Case, CreateCaseRequest, AdvanceCaseRequest, AssignTechnicianRequest } from './types';\nimport { mockCases, mockDashboardStats, getMockCasesByStatus, getMockOverdueCases } from './mockData';\n\nexport class CasesService {\n  private static isBackendAvailable = true;\n  \n  /**\n   * Get all cases with fallback to mock data\n   */\n  static async getCases(): Promise<Case[]> {\n    try {\n      if (!this.isBackendAvailable) {\n        console.log('🔄 Using mock data (backend unavailable)');\n        return mockCases;\n      }\n      \n      const cases = await apiClient.get<Case[]>('/cases/');\n      console.log('✅ Backend data loaded successfully');\n      this.isBackendAvailable = true;\n      return cases;\n    } catch (err: any) {\n      console.warn('⚠️ Backend unavailable, using mock data:', err.message);\n      this.isBackendAvailable = false;\n      return mockCases;\n    }\n  }\n\n  /**\n   * Get case by ID with fallback\n   */\n  static async getCase(caseId: string): Promise<Case> {\n    try {\n      if (!this.isBackendAvailable) {\n        const mockCase = mockCases.find((c: Case) => c.id === caseId);\n        if (!mockCase) throw new Error('Case not found');\n        return mockCase;\n      }\n      \n      const case_ = await apiClient.get<Case>(`/cases/${caseId}/`);\n      this.isBackendAvailable = true;\n      return case_;\n    } catch (err: any) {\n      console.warn('⚠️ Backend unavailable for case fetch, using mock data');\n      this.isBackendAvailable = false;\n      const mockCase = mockCases.find((c: Case) => c.id === caseId);\n      if (!mockCase) throw new Error('Case not found');\n      return mockCase;\n    }\n  }\n\n  /**\n   * Create new case with fallback\n   */\n  static async createCase(data: CreateCaseRequest): Promise<Case> {\n    try {\n      if (!this.isBackendAvailable) {\n        // Simulate case creation with mock data\n        const newCase: Case = {\n          id: `LAB-${new Date().toISOString().split('T')[0]}-${Math.random().toString(36).substr(2, 3).toUpperCase()}`,\n          clinic: mockCases[0].clinic, // Use first clinic as default\n          patient_name: data.patient_name,\n          tooth_number: data.tooth_number,\n          service_type: data.service_type as any,\n          priority: (data.priority || 'normal') as any,\n          status: 'received',\n          current_stage_index: 0,\n          workflow_stages: [\n            {\n              name: 'received',\n              department: 'intake',\n              estimated_duration_minutes: 30,\n              auto_assign: true,\n              requires_quality_check: false\n            }\n          ],\n          assigned_technician_id: undefined,\n          due_date: new Date(Date.now() + 3 * 24 * 60 * 60 * 1000).toISOString(),\n          created_at: new Date().toISOString(),\n          updated_at: new Date().toISOString(),\n          notes: data.notes ? [data.notes] : [],\n          files: (data.files || []).map((file: File) => ({\n            id: `file-${Math.random().toString(36).substr(2, 9)}`,\n            name: file.name,\n            size: file.size,\n            url: URL.createObjectURL(file),\n            uploaded_at: new Date().toISOString(),\n            uploaded_by: 'current-user'\n          })),\n          days_until_due: 3,\n          is_overdue: false,\n          current_stage: 'Received'\n        };\n        \n        console.log('✅ Mock case created:', newCase.id);\n        mockCases.unshift(newCase); // Add to beginning of array\n        return newCase;\n      }\n      \n      const case_ = await apiClient.post<Case>('/cases/', data);\n      this.isBackendAvailable = true;\n      return case_;\n    } catch (err: any) {\n      console.warn('⚠️ Backend unavailable for case creation, using mock creation');\n      this.isBackendAvailable = false;\n      // Fallback to mock creation (same logic as above)\n      return this.createCase(data);\n    }\n  }\n  /**\n   * Update case with fallback\n   */\n  static async updateCase(caseId: string, data: Partial<Case>): Promise<Case> {\n    try {\n      if (!this.isBackendAvailable) {\n        const caseIndex = mockCases.findIndex((c: Case) => c.id === caseId);\n        if (caseIndex === -1) throw new Error('Case not found');\n        \n        mockCases[caseIndex] = { ...mockCases[caseIndex], ...data, updated_at: new Date().toISOString() };\n        console.log('✅ Mock case updated:', caseId);\n        return mockCases[caseIndex];\n      }\n      \n      const case_ = await apiClient.patch<Case>(`/cases/${caseId}/`, data);\n      this.isBackendAvailable = true;\n      return case_;\n    } catch (err: any) {\n      console.warn('⚠️ Backend unavailable for case update, using mock update');\n      this.isBackendAvailable = false;\n      return this.updateCase(caseId, data);\n    }\n  }\n\n  /**\n   * Delete case with fallback\n   */\n  static async deleteCase(caseId: string): Promise<void> {\n    try {\n      if (!this.isBackendAvailable) {\n        const caseIndex = mockCases.findIndex((c: Case) => c.id === caseId);\n        if (caseIndex === -1) throw new Error('Case not found');\n        \n        mockCases.splice(caseIndex, 1);\n        console.log('✅ Mock case deleted:', caseId);\n        return;\n      }\n      \n      await apiClient.delete(`/cases/${caseId}/`);\n      this.isBackendAvailable = true;\n    } catch (err: any) {\n      console.warn('⚠️ Backend unavailable for case deletion, using mock deletion');\n      this.isBackendAvailable = false;\n      return this.deleteCase(caseId);\n    }\n  }\n\n  /**\n   * Advance case to next stage with fallback\n   */\n  static async advanceCase(caseId: string, data: AdvanceCaseRequest = {}): Promise<Case> {\n    try {\n      if (!this.isBackendAvailable) {\n        const caseIndex = mockCases.findIndex((c: Case) => c.id === caseId);\n        if (caseIndex === -1) throw new Error('Case not found');\n        \n        const case_ = mockCases[caseIndex];\n        const nextStageIndex = case_.current_stage_index + 1;\n        \n        if (nextStageIndex < case_.workflow_stages.length) {\n          const nextStage = case_.workflow_stages[nextStageIndex];\n          case_.current_stage_index = nextStageIndex;\n          case_.status = nextStage.name as any;\n          case_.current_stage = nextStage.name;\n          case_.assigned_technician_id = undefined; // Clear assignment\n        } else {\n          // Move to quality control or delivery\n          case_.status = 'qc';\n          case_.current_stage = 'Quality Control';\n        }\n        \n        case_.updated_at = new Date().toISOString();\n        if (data.notes) {\n          case_.notes.push(`${new Date().toISOString()}: ${data.notes}`);\n        }\n        \n        console.log('✅ Mock case advanced:', caseId, 'to', case_.current_stage);\n        return case_;\n      }\n      \n      const case_ = await apiClient.post<Case>(`/cases/${caseId}/advance/`, data);\n      this.isBackendAvailable = true;\n      return case_;\n    } catch (err: any) {\n      console.warn('⚠️ Backend unavailable for case advance, using mock advance');\n      this.isBackendAvailable = false;\n      return this.advanceCase(caseId, data);\n    }\n  }\n  /**\n   * Assign technician to case with fallback\n   */\n  static async assignTechnician(caseId: string, data: AssignTechnicianRequest): Promise<Case> {\n    try {\n      if (!this.isBackendAvailable) {\n        const caseIndex = mockCases.findIndex((c: Case) => c.id === caseId);\n        if (caseIndex === -1) throw new Error('Case not found');\n        \n        mockCases[caseIndex].assigned_technician_id = data.technician_id;\n        mockCases[caseIndex].updated_at = new Date().toISOString();\n        \n        console.log('✅ Mock technician assigned:', data.technician_id, 'to case:', caseId);\n        return mockCases[caseIndex];\n      }\n      \n      const case_ = await apiClient.post<Case>(`/cases/${caseId}/assign_technician/`, data);\n      this.isBackendAvailable = true;\n      return case_;\n    } catch (err: any) {\n      console.warn('⚠️ Backend unavailable for technician assignment, using mock assignment');\n      this.isBackendAvailable = false;\n      return this.assignTechnician(caseId, data);\n    }\n  }\n\n  /**\n   * Get overdue cases with fallback\n   */\n  static async getOverdueCases(): Promise<Case[]> {\n    try {\n      if (!this.isBackendAvailable) {\n        return getMockOverdueCases();\n      }\n      \n      const cases = await apiClient.get<Case[]>('/cases/overdue/');\n      this.isBackendAvailable = true;\n      return cases;\n    } catch (err: any) {\n      console.warn('⚠️ Backend unavailable for overdue cases, using mock data');\n      this.isBackendAvailable = false;\n      return getMockOverdueCases();\n    }\n  }\n\n  /**\n   * Get cases by status with fallback\n   */\n  static async getCasesByStatus(status: string): Promise<Case[]> {\n    try {\n      if (!this.isBackendAvailable) {\n        return getMockCasesByStatus(status);\n      }\n      \n      const cases = await apiClient.get<Case[]>(`/cases/by_status/?status=${status}`);\n      this.isBackendAvailable = true;\n      return cases;\n    } catch (err: any) {\n      console.warn('⚠️ Backend unavailable for status filter, using mock data');\n      this.isBackendAvailable = false;\n      return getMockCasesByStatus(status);\n    }\n  }\n\n  /**\n   * Get dashboard statistics with fallback\n   */\n  static async getDashboardStats(): Promise<any> {\n    try {\n      if (!this.isBackendAvailable) {\n        console.log('📊 Using mock dashboard stats');\n        return mockDashboardStats;\n      }\n      \n      // Try to get real data from backend\n      const cases = await this.getCases();\n      const today = new Date().toISOString().split('T')[0];\n      \n      const stats = {\n        active_cases: cases.filter(c => !['delivered', 'cancelled'].includes(c.status)).length,\n        completed_today: cases.filter(c => \n          c.status === 'delivered' && \n          c.updated_at.startsWith(today)\n        ).length,\n        pending_qc: cases.filter(c => c.status === 'qc').length,\n        revenue_today: 2850, // This would come from billing service\n        overdue_cases: cases.filter(c => c.is_overdue).length,\n      };\n      \n      console.log('📊 Real dashboard stats calculated');\n      this.isBackendAvailable = true;\n      return stats;\n    } catch (error) {\n      console.warn('⚠️ Backend unavailable for dashboard stats, using mock data');\n      this.isBackendAvailable = false;\n      return mockDashboardStats;\n    }\n  }\n\n  /**\n   * Check backend health\n   */\n  static async checkBackendHealth(): Promise<boolean> {\n    try {\n      // Try a simple health check - health endpoint is at root level, not under /api/v1\n      const response = await axios.get('http://localhost:8001/health/');\n      this.isBackendAvailable = true;\n      console.log('✅ Backend health check passed');\n      return true;\n    } catch (error) {\n      this.isBackendAvailable = false;\n      console.log('❌ Backend health check failed');\n      return false;\n    }\n  }\n\n  /**\n   * Get backend status\n   */\n  static getBackendStatus(): { available: boolean; mode: string } {\n    return {\n      available: this.isBackendAvailable,\n      mode: this.isBackendAvailable ? 'live' : 'mock'\n    };\n  }\n}"], "mappings": "AAAA;AACA;AACA;;AAEA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,SAAS,QAAQ,UAAU;AAEpC,SAASC,SAAS,EAAEC,kBAAkB,EAAEC,oBAAoB,EAAEC,mBAAmB,QAAQ,YAAY;AAErG,OAAO,MAAMC,YAAY,CAAC;EAGxB;AACF;AACA;EACE,aAAaC,QAAQA,CAAA,EAAoB;IACvC,IAAI;MACF,IAAI,CAAC,IAAI,CAACC,kBAAkB,EAAE;QAC5BC,OAAO,CAACC,GAAG,CAAC,0CAA0C,CAAC;QACvD,OAAOR,SAAS;MAClB;MAEA,MAAMS,KAAK,GAAG,MAAMV,SAAS,CAACW,GAAG,CAAS,SAAS,CAAC;MACpDH,OAAO,CAACC,GAAG,CAAC,oCAAoC,CAAC;MACjD,IAAI,CAACF,kBAAkB,GAAG,IAAI;MAC9B,OAAOG,KAAK;IACd,CAAC,CAAC,OAAOE,GAAQ,EAAE;MACjBJ,OAAO,CAACK,IAAI,CAAC,0CAA0C,EAAED,GAAG,CAACE,OAAO,CAAC;MACrE,IAAI,CAACP,kBAAkB,GAAG,KAAK;MAC/B,OAAON,SAAS;IAClB;EACF;;EAEA;AACF;AACA;EACE,aAAac,OAAOA,CAACC,MAAc,EAAiB;IAClD,IAAI;MACF,IAAI,CAAC,IAAI,CAACT,kBAAkB,EAAE;QAC5B,MAAMU,QAAQ,GAAGhB,SAAS,CAACiB,IAAI,CAAEC,CAAO,IAAKA,CAAC,CAACC,EAAE,KAAKJ,MAAM,CAAC;QAC7D,IAAI,CAACC,QAAQ,EAAE,MAAM,IAAII,KAAK,CAAC,gBAAgB,CAAC;QAChD,OAAOJ,QAAQ;MACjB;MAEA,MAAMK,KAAK,GAAG,MAAMtB,SAAS,CAACW,GAAG,CAAO,UAAUK,MAAM,GAAG,CAAC;MAC5D,IAAI,CAACT,kBAAkB,GAAG,IAAI;MAC9B,OAAOe,KAAK;IACd,CAAC,CAAC,OAAOV,GAAQ,EAAE;MACjBJ,OAAO,CAACK,IAAI,CAAC,wDAAwD,CAAC;MACtE,IAAI,CAACN,kBAAkB,GAAG,KAAK;MAC/B,MAAMU,QAAQ,GAAGhB,SAAS,CAACiB,IAAI,CAAEC,CAAO,IAAKA,CAAC,CAACC,EAAE,KAAKJ,MAAM,CAAC;MAC7D,IAAI,CAACC,QAAQ,EAAE,MAAM,IAAII,KAAK,CAAC,gBAAgB,CAAC;MAChD,OAAOJ,QAAQ;IACjB;EACF;;EAEA;AACF;AACA;EACE,aAAaM,UAAUA,CAACC,IAAuB,EAAiB;IAC9D,IAAI;MACF,IAAI,CAAC,IAAI,CAACjB,kBAAkB,EAAE;QAC5B;QACA,MAAMkB,OAAa,GAAG;UACpBL,EAAE,EAAE,OAAO,IAAIM,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,IAAIC,IAAI,CAACC,MAAM,CAAC,CAAC,CAACC,QAAQ,CAAC,EAAE,CAAC,CAACC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,EAAE;UAC5GC,MAAM,EAAEjC,SAAS,CAAC,CAAC,CAAC,CAACiC,MAAM;UAAE;UAC7BC,YAAY,EAAEX,IAAI,CAACW,YAAY;UAC/BC,YAAY,EAAEZ,IAAI,CAACY,YAAY;UAC/BC,YAAY,EAAEb,IAAI,CAACa,YAAmB;UACtCC,QAAQ,EAAGd,IAAI,CAACc,QAAQ,IAAI,QAAgB;UAC5CC,MAAM,EAAE,UAAU;UAClBC,mBAAmB,EAAE,CAAC;UACtBC,eAAe,EAAE,CACf;YACEC,IAAI,EAAE,UAAU;YAChBC,UAAU,EAAE,QAAQ;YACpBC,0BAA0B,EAAE,EAAE;YAC9BC,WAAW,EAAE,IAAI;YACjBC,sBAAsB,EAAE;UAC1B,CAAC,CACF;UACDC,sBAAsB,EAAEC,SAAS;UACjCC,QAAQ,EAAE,IAAIvB,IAAI,CAACA,IAAI,CAACwB,GAAG,CAAC,CAAC,GAAG,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC,CAACvB,WAAW,CAAC,CAAC;UACtEwB,UAAU,EAAE,IAAIzB,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC;UACpCyB,UAAU,EAAE,IAAI1B,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC;UACpC0B,KAAK,EAAE7B,IAAI,CAAC6B,KAAK,GAAG,CAAC7B,IAAI,CAAC6B,KAAK,CAAC,GAAG,EAAE;UACrCC,KAAK,EAAE,CAAC9B,IAAI,CAAC8B,KAAK,IAAI,EAAE,EAAEC,GAAG,CAAEC,IAAU,KAAM;YAC7CpC,EAAE,EAAE,QAAQS,IAAI,CAACC,MAAM,CAAC,CAAC,CAACC,QAAQ,CAAC,EAAE,CAAC,CAACC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE;YACrDU,IAAI,EAAEc,IAAI,CAACd,IAAI;YACfe,IAAI,EAAED,IAAI,CAACC,IAAI;YACfC,GAAG,EAAEC,GAAG,CAACC,eAAe,CAACJ,IAAI,CAAC;YAC9BK,WAAW,EAAE,IAAInC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC;YACrCmC,WAAW,EAAE;UACf,CAAC,CAAC,CAAC;UACHC,cAAc,EAAE,CAAC;UACjBC,UAAU,EAAE,KAAK;UACjBC,aAAa,EAAE;QACjB,CAAC;QAEDzD,OAAO,CAACC,GAAG,CAAC,sBAAsB,EAAEgB,OAAO,CAACL,EAAE,CAAC;QAC/CnB,SAAS,CAACiE,OAAO,CAACzC,OAAO,CAAC,CAAC,CAAC;QAC5B,OAAOA,OAAO;MAChB;MAEA,MAAMH,KAAK,GAAG,MAAMtB,SAAS,CAACmE,IAAI,CAAO,SAAS,EAAE3C,IAAI,CAAC;MACzD,IAAI,CAACjB,kBAAkB,GAAG,IAAI;MAC9B,OAAOe,KAAK;IACd,CAAC,CAAC,OAAOV,GAAQ,EAAE;MACjBJ,OAAO,CAACK,IAAI,CAAC,+DAA+D,CAAC;MAC7E,IAAI,CAACN,kBAAkB,GAAG,KAAK;MAC/B;MACA,OAAO,IAAI,CAACgB,UAAU,CAACC,IAAI,CAAC;IAC9B;EACF;EACA;AACF;AACA;EACE,aAAa4C,UAAUA,CAACpD,MAAc,EAAEQ,IAAmB,EAAiB;IAC1E,IAAI;MACF,IAAI,CAAC,IAAI,CAACjB,kBAAkB,EAAE;QAC5B,MAAM8D,SAAS,GAAGpE,SAAS,CAACqE,SAAS,CAAEnD,CAAO,IAAKA,CAAC,CAACC,EAAE,KAAKJ,MAAM,CAAC;QACnE,IAAIqD,SAAS,KAAK,CAAC,CAAC,EAAE,MAAM,IAAIhD,KAAK,CAAC,gBAAgB,CAAC;QAEvDpB,SAAS,CAACoE,SAAS,CAAC,GAAG;UAAE,GAAGpE,SAAS,CAACoE,SAAS,CAAC;UAAE,GAAG7C,IAAI;UAAE4B,UAAU,EAAE,IAAI1B,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC;QAAE,CAAC;QACjGnB,OAAO,CAACC,GAAG,CAAC,sBAAsB,EAAEO,MAAM,CAAC;QAC3C,OAAOf,SAAS,CAACoE,SAAS,CAAC;MAC7B;MAEA,MAAM/C,KAAK,GAAG,MAAMtB,SAAS,CAACuE,KAAK,CAAO,UAAUvD,MAAM,GAAG,EAAEQ,IAAI,CAAC;MACpE,IAAI,CAACjB,kBAAkB,GAAG,IAAI;MAC9B,OAAOe,KAAK;IACd,CAAC,CAAC,OAAOV,GAAQ,EAAE;MACjBJ,OAAO,CAACK,IAAI,CAAC,2DAA2D,CAAC;MACzE,IAAI,CAACN,kBAAkB,GAAG,KAAK;MAC/B,OAAO,IAAI,CAAC6D,UAAU,CAACpD,MAAM,EAAEQ,IAAI,CAAC;IACtC;EACF;;EAEA;AACF;AACA;EACE,aAAagD,UAAUA,CAACxD,MAAc,EAAiB;IACrD,IAAI;MACF,IAAI,CAAC,IAAI,CAACT,kBAAkB,EAAE;QAC5B,MAAM8D,SAAS,GAAGpE,SAAS,CAACqE,SAAS,CAAEnD,CAAO,IAAKA,CAAC,CAACC,EAAE,KAAKJ,MAAM,CAAC;QACnE,IAAIqD,SAAS,KAAK,CAAC,CAAC,EAAE,MAAM,IAAIhD,KAAK,CAAC,gBAAgB,CAAC;QAEvDpB,SAAS,CAACwE,MAAM,CAACJ,SAAS,EAAE,CAAC,CAAC;QAC9B7D,OAAO,CAACC,GAAG,CAAC,sBAAsB,EAAEO,MAAM,CAAC;QAC3C;MACF;MAEA,MAAMhB,SAAS,CAAC0E,MAAM,CAAC,UAAU1D,MAAM,GAAG,CAAC;MAC3C,IAAI,CAACT,kBAAkB,GAAG,IAAI;IAChC,CAAC,CAAC,OAAOK,GAAQ,EAAE;MACjBJ,OAAO,CAACK,IAAI,CAAC,+DAA+D,CAAC;MAC7E,IAAI,CAACN,kBAAkB,GAAG,KAAK;MAC/B,OAAO,IAAI,CAACiE,UAAU,CAACxD,MAAM,CAAC;IAChC;EACF;;EAEA;AACF;AACA;EACE,aAAa2D,WAAWA,CAAC3D,MAAc,EAAEQ,IAAwB,GAAG,CAAC,CAAC,EAAiB;IACrF,IAAI;MACF,IAAI,CAAC,IAAI,CAACjB,kBAAkB,EAAE;QAC5B,MAAM8D,SAAS,GAAGpE,SAAS,CAACqE,SAAS,CAAEnD,CAAO,IAAKA,CAAC,CAACC,EAAE,KAAKJ,MAAM,CAAC;QACnE,IAAIqD,SAAS,KAAK,CAAC,CAAC,EAAE,MAAM,IAAIhD,KAAK,CAAC,gBAAgB,CAAC;QAEvD,MAAMC,KAAK,GAAGrB,SAAS,CAACoE,SAAS,CAAC;QAClC,MAAMO,cAAc,GAAGtD,KAAK,CAACkB,mBAAmB,GAAG,CAAC;QAEpD,IAAIoC,cAAc,GAAGtD,KAAK,CAACmB,eAAe,CAACoC,MAAM,EAAE;UACjD,MAAMC,SAAS,GAAGxD,KAAK,CAACmB,eAAe,CAACmC,cAAc,CAAC;UACvDtD,KAAK,CAACkB,mBAAmB,GAAGoC,cAAc;UAC1CtD,KAAK,CAACiB,MAAM,GAAGuC,SAAS,CAACpC,IAAW;UACpCpB,KAAK,CAAC2C,aAAa,GAAGa,SAAS,CAACpC,IAAI;UACpCpB,KAAK,CAACyB,sBAAsB,GAAGC,SAAS,CAAC,CAAC;QAC5C,CAAC,MAAM;UACL;UACA1B,KAAK,CAACiB,MAAM,GAAG,IAAI;UACnBjB,KAAK,CAAC2C,aAAa,GAAG,iBAAiB;QACzC;QAEA3C,KAAK,CAAC8B,UAAU,GAAG,IAAI1B,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC;QAC3C,IAAIH,IAAI,CAAC6B,KAAK,EAAE;UACd/B,KAAK,CAAC+B,KAAK,CAAC0B,IAAI,CAAC,GAAG,IAAIrD,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,KAAKH,IAAI,CAAC6B,KAAK,EAAE,CAAC;QAChE;QAEA7C,OAAO,CAACC,GAAG,CAAC,uBAAuB,EAAEO,MAAM,EAAE,IAAI,EAAEM,KAAK,CAAC2C,aAAa,CAAC;QACvE,OAAO3C,KAAK;MACd;MAEA,MAAMA,KAAK,GAAG,MAAMtB,SAAS,CAACmE,IAAI,CAAO,UAAUnD,MAAM,WAAW,EAAEQ,IAAI,CAAC;MAC3E,IAAI,CAACjB,kBAAkB,GAAG,IAAI;MAC9B,OAAOe,KAAK;IACd,CAAC,CAAC,OAAOV,GAAQ,EAAE;MACjBJ,OAAO,CAACK,IAAI,CAAC,6DAA6D,CAAC;MAC3E,IAAI,CAACN,kBAAkB,GAAG,KAAK;MAC/B,OAAO,IAAI,CAACoE,WAAW,CAAC3D,MAAM,EAAEQ,IAAI,CAAC;IACvC;EACF;EACA;AACF;AACA;EACE,aAAawD,gBAAgBA,CAAChE,MAAc,EAAEQ,IAA6B,EAAiB;IAC1F,IAAI;MACF,IAAI,CAAC,IAAI,CAACjB,kBAAkB,EAAE;QAC5B,MAAM8D,SAAS,GAAGpE,SAAS,CAACqE,SAAS,CAAEnD,CAAO,IAAKA,CAAC,CAACC,EAAE,KAAKJ,MAAM,CAAC;QACnE,IAAIqD,SAAS,KAAK,CAAC,CAAC,EAAE,MAAM,IAAIhD,KAAK,CAAC,gBAAgB,CAAC;QAEvDpB,SAAS,CAACoE,SAAS,CAAC,CAACtB,sBAAsB,GAAGvB,IAAI,CAACyD,aAAa;QAChEhF,SAAS,CAACoE,SAAS,CAAC,CAACjB,UAAU,GAAG,IAAI1B,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC;QAE1DnB,OAAO,CAACC,GAAG,CAAC,6BAA6B,EAAEe,IAAI,CAACyD,aAAa,EAAE,UAAU,EAAEjE,MAAM,CAAC;QAClF,OAAOf,SAAS,CAACoE,SAAS,CAAC;MAC7B;MAEA,MAAM/C,KAAK,GAAG,MAAMtB,SAAS,CAACmE,IAAI,CAAO,UAAUnD,MAAM,qBAAqB,EAAEQ,IAAI,CAAC;MACrF,IAAI,CAACjB,kBAAkB,GAAG,IAAI;MAC9B,OAAOe,KAAK;IACd,CAAC,CAAC,OAAOV,GAAQ,EAAE;MACjBJ,OAAO,CAACK,IAAI,CAAC,yEAAyE,CAAC;MACvF,IAAI,CAACN,kBAAkB,GAAG,KAAK;MAC/B,OAAO,IAAI,CAACyE,gBAAgB,CAAChE,MAAM,EAAEQ,IAAI,CAAC;IAC5C;EACF;;EAEA;AACF;AACA;EACE,aAAa0D,eAAeA,CAAA,EAAoB;IAC9C,IAAI;MACF,IAAI,CAAC,IAAI,CAAC3E,kBAAkB,EAAE;QAC5B,OAAOH,mBAAmB,CAAC,CAAC;MAC9B;MAEA,MAAMM,KAAK,GAAG,MAAMV,SAAS,CAACW,GAAG,CAAS,iBAAiB,CAAC;MAC5D,IAAI,CAACJ,kBAAkB,GAAG,IAAI;MAC9B,OAAOG,KAAK;IACd,CAAC,CAAC,OAAOE,GAAQ,EAAE;MACjBJ,OAAO,CAACK,IAAI,CAAC,2DAA2D,CAAC;MACzE,IAAI,CAACN,kBAAkB,GAAG,KAAK;MAC/B,OAAOH,mBAAmB,CAAC,CAAC;IAC9B;EACF;;EAEA;AACF;AACA;EACE,aAAa+E,gBAAgBA,CAAC5C,MAAc,EAAmB;IAC7D,IAAI;MACF,IAAI,CAAC,IAAI,CAAChC,kBAAkB,EAAE;QAC5B,OAAOJ,oBAAoB,CAACoC,MAAM,CAAC;MACrC;MAEA,MAAM7B,KAAK,GAAG,MAAMV,SAAS,CAACW,GAAG,CAAS,4BAA4B4B,MAAM,EAAE,CAAC;MAC/E,IAAI,CAAChC,kBAAkB,GAAG,IAAI;MAC9B,OAAOG,KAAK;IACd,CAAC,CAAC,OAAOE,GAAQ,EAAE;MACjBJ,OAAO,CAACK,IAAI,CAAC,2DAA2D,CAAC;MACzE,IAAI,CAACN,kBAAkB,GAAG,KAAK;MAC/B,OAAOJ,oBAAoB,CAACoC,MAAM,CAAC;IACrC;EACF;;EAEA;AACF;AACA;EACE,aAAa6C,iBAAiBA,CAAA,EAAiB;IAC7C,IAAI;MACF,IAAI,CAAC,IAAI,CAAC7E,kBAAkB,EAAE;QAC5BC,OAAO,CAACC,GAAG,CAAC,+BAA+B,CAAC;QAC5C,OAAOP,kBAAkB;MAC3B;;MAEA;MACA,MAAMQ,KAAK,GAAG,MAAM,IAAI,CAACJ,QAAQ,CAAC,CAAC;MACnC,MAAM+E,KAAK,GAAG,IAAI3D,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;MAEpD,MAAM0D,KAAK,GAAG;QACZC,YAAY,EAAE7E,KAAK,CAAC8E,MAAM,CAACrE,CAAC,IAAI,CAAC,CAAC,WAAW,EAAE,WAAW,CAAC,CAACsE,QAAQ,CAACtE,CAAC,CAACoB,MAAM,CAAC,CAAC,CAACsC,MAAM;QACtFa,eAAe,EAAEhF,KAAK,CAAC8E,MAAM,CAACrE,CAAC,IAC7BA,CAAC,CAACoB,MAAM,KAAK,WAAW,IACxBpB,CAAC,CAACiC,UAAU,CAACuC,UAAU,CAACN,KAAK,CAC/B,CAAC,CAACR,MAAM;QACRe,UAAU,EAAElF,KAAK,CAAC8E,MAAM,CAACrE,CAAC,IAAIA,CAAC,CAACoB,MAAM,KAAK,IAAI,CAAC,CAACsC,MAAM;QACvDgB,aAAa,EAAE,IAAI;QAAE;QACrBC,aAAa,EAAEpF,KAAK,CAAC8E,MAAM,CAACrE,CAAC,IAAIA,CAAC,CAAC6C,UAAU,CAAC,CAACa;MACjD,CAAC;MAEDrE,OAAO,CAACC,GAAG,CAAC,oCAAoC,CAAC;MACjD,IAAI,CAACF,kBAAkB,GAAG,IAAI;MAC9B,OAAO+E,KAAK;IACd,CAAC,CAAC,OAAOS,KAAK,EAAE;MACdvF,OAAO,CAACK,IAAI,CAAC,6DAA6D,CAAC;MAC3E,IAAI,CAACN,kBAAkB,GAAG,KAAK;MAC/B,OAAOL,kBAAkB;IAC3B;EACF;;EAEA;AACF;AACA;EACE,aAAa8F,kBAAkBA,CAAA,EAAqB;IAClD,IAAI;MACF;MACA,MAAMC,QAAQ,GAAG,MAAMlG,KAAK,CAACY,GAAG,CAAC,+BAA+B,CAAC;MACjE,IAAI,CAACJ,kBAAkB,GAAG,IAAI;MAC9BC,OAAO,CAACC,GAAG,CAAC,+BAA+B,CAAC;MAC5C,OAAO,IAAI;IACb,CAAC,CAAC,OAAOsF,KAAK,EAAE;MACd,IAAI,CAACxF,kBAAkB,GAAG,KAAK;MAC/BC,OAAO,CAACC,GAAG,CAAC,+BAA+B,CAAC;MAC5C,OAAO,KAAK;IACd;EACF;;EAEA;AACF;AACA;EACE,OAAOyF,gBAAgBA,CAAA,EAAyC;IAC9D,OAAO;MACLC,SAAS,EAAE,IAAI,CAAC5F,kBAAkB;MAClC6F,IAAI,EAAE,IAAI,CAAC7F,kBAAkB,GAAG,MAAM,GAAG;IAC3C,CAAC;EACH;AACF;AA/TaF,YAAY,CACRE,kBAAkB,GAAG,IAAI", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}