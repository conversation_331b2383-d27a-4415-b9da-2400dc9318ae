#!/usr/bin/env python3
"""
Test environment variables and settings
"""

from decouple import config

print("Testing environment variables:")
print(f"DEBUG: {config('DEBUG', default=True, cast=bool)}")
print(f"SECRET_KEY: {config('SECRET_KEY', default='dev-secret-key-change-in-production')[:20]}...")
print(f"ALLOWED_HOSTS: {config('ALLOWED_HOSTS', default='localhost,127.0.0.1,*')}")

# Test Django settings import
try:
    import os
    os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'dentflow_project.settings')
    
    import django
    django.setup()
    
    from django.conf import settings
    print(f"\nDjango settings:")
    print(f"DEBUG: {settings.DEBUG}")
    print(f"ALLOWED_HOSTS: {settings.ALLOWED_HOSTS}")
    print("✅ Django settings loaded successfully!")
    
except Exception as e:
    print(f"❌ Django settings failed: {e}")
    import traceback
    traceback.print_exc()
