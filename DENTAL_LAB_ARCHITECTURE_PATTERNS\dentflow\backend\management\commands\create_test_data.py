#!/usr/bin/env python3
"""
Django management command to create comprehensive test data
Usage: python manage.py create_test_data
"""

import os
import sys
from decimal import Decimal
from datetime import datetime, timedelta
import random

# Add the project root to Python path
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(__file__))))

from django.core.management.base import BaseCommand
from django.contrib.auth.models import User
from apps.tenants.models import Tenant
from apps.cases.models import Case, CaseStatus
from apps.inventory.models import (
    MaterialCategory, Material, Supplier, MaterialStock, MaterialTransaction,
    PurchaseOrder, PurchaseOrderItem, ItemCategory, Item, ItemMaterialComposition
)
from apps.billing.models import (
    PriceList, ServicePrice, ItemPrice, Invoice, InvoiceItem, InvoiceItemMaterialUsage
)


class Command(BaseCommand):
    help = 'Create comprehensive test data for DentFlow system'

    def add_arguments(self, parser):
        parser.add_argument(
            '--large-dataset',
            action='store_true',
            help='Create large dataset for performance testing',
        )

    def handle(self, *args, **options):
        self.stdout.write(self.style.SUCCESS('🚀 Creating comprehensive test data...'))
        
        # Create basic data
        self.create_users()
        self.create_tenants()
        self.create_suppliers()
        self.create_material_categories()
        self.create_materials()
        self.create_item_categories()
        self.create_items()
        self.create_material_compositions()
        self.create_price_lists()
        self.create_cases()
        self.create_invoices()
        
        if options['large_dataset']:
            self.create_large_dataset()
        
        self.stdout.write(self.style.SUCCESS('✅ Test data creation completed!'))
        self.display_summary()

    def create_users(self):
        """Create test users"""
        self.stdout.write('Creating users...')
        
        # Admin user
        self.admin_user, created = User.objects.get_or_create(
            username='admin',
            defaults={
                'email': '<EMAIL>',
                'first_name': 'Admin',
                'last_name': 'User',
                'is_staff': True,
                'is_superuser': True
            }
        )
        if created or not self.admin_user.check_password('admin123'):
            self.admin_user.set_password('admin123')
            self.admin_user.save()
        
        # Lab technicians
        self.technicians = []
        for i in range(3):
            user, created = User.objects.get_or_create(
                username=f'tech{i+1}',
                defaults={
                    'email': f'tech{i+1}@demo-lab.com',
                    'first_name': f'Technician',
                    'last_name': f'{i+1}',
                    'is_staff': True
                }
            )
            if created:
                user.set_password('tech123')
                user.save()
            self.technicians.append(user)

    def create_tenants(self):
        """Create test tenants"""
        self.stdout.write('Creating tenants...')
        
        self.tenant, created = Tenant.objects.get_or_create(
            subdomain='demo-lab',
            defaults={
                'name': 'Demo Dental Laboratory',
                'email': '<EMAIL>',
                'phone': '******-0123',
                'address': '123 Dental Street, Lab City, LC 12345',
                'plan': 'professional',
                'max_users': 10,
                'max_cases_per_month': 100,
                'is_active': True
            }
        )

    def create_suppliers(self):
        """Create test suppliers"""
        self.stdout.write('Creating suppliers...')
        
        suppliers_data = [
            {
                'name': 'Dental Materials Inc.',
                'contact_person': 'John Smith',
                'email': '<EMAIL>',
                'phone': '******-1001',
                'address': '456 Supply Ave, Material City, MC 67890'
            },
            {
                'name': 'Ceramic Solutions Ltd.',
                'contact_person': 'Sarah Johnson',
                'email': '<EMAIL>',
                'phone': '******-1002',
                'address': '789 Ceramic Blvd, Porcelain Town, PT 54321'
            },
            {
                'name': 'Metal Works Pro',
                'contact_person': 'Mike Wilson',
                'email': '<EMAIL>',
                'phone': '******-1003',
                'address': '321 Alloy Street, Metal City, MC 98765'
            }
        ]
        
        self.suppliers = []
        for supplier_data in suppliers_data:
            supplier, created = Supplier.objects.get_or_create(
                name=supplier_data['name'],
                defaults=supplier_data
            )
            self.suppliers.append(supplier)

    def create_material_categories(self):
        """Create material categories"""
        self.stdout.write('Creating material categories...')
        
        categories_data = [
            {'name': 'Ceramics', 'description': 'Ceramic materials for dental restorations'},
            {'name': 'Metals', 'description': 'Metal alloys for dental work'},
            {'name': 'Composites', 'description': 'Composite resin materials'},
            {'name': 'Impression Materials', 'description': 'Materials for taking impressions'},
            {'name': 'Adhesives', 'description': 'Bonding and cementing agents'},
        ]
        
        self.material_categories = []
        for cat_data in categories_data:
            category, created = MaterialCategory.objects.get_or_create(
                name=cat_data['name'],
                defaults=cat_data
            )
            self.material_categories.append(category)

    def create_materials(self):
        """Create test materials"""
        self.stdout.write('Creating materials...')
        
        materials_data = [
            # Ceramics
            {
                'name': 'Zirconia Block - High Translucency',
                'category': self.material_categories[0],
                'unit': 'piece',
                'standard_cost': Decimal('52.00'),
                'current_stock': Decimal('30.0000'),
                'minimum_stock': Decimal('5.0000'),
                'description': 'High-strength, high-translucency zirconia block for anterior restorations'
            },
            {
                'name': 'Lithium Disilicate Block',
                'category': self.material_categories[0],
                'unit': 'piece',
                'standard_cost': Decimal('48.00'),
                'current_stock': Decimal('25.0000'),
                'minimum_stock': Decimal('5.0000'),
                'description': 'Premium lithium disilicate for high-aesthetic restorations'
            },
            {
                'name': 'Feldspathic Porcelain Powder',
                'category': self.material_categories[0],
                'unit': 'gram',
                'standard_cost': Decimal('0.85'),
                'current_stock': Decimal('2500.0000'),
                'minimum_stock': Decimal('500.0000'),
                'description': 'Traditional feldspathic porcelain for layering'
            },
            # Metals
            {
                'name': 'Titanium Grade 2 Alloy',
                'category': self.material_categories[1],
                'unit': 'gram',
                'standard_cost': Decimal('3.20'),
                'current_stock': Decimal('800.0000'),
                'minimum_stock': Decimal('100.0000'),
                'description': 'Biocompatible titanium alloy for implant components'
            },
            {
                'name': 'Cobalt-Chrome Alloy',
                'category': self.material_categories[1],
                'unit': 'gram',
                'standard_cost': Decimal('2.80'),
                'current_stock': Decimal('600.0000'),
                'minimum_stock': Decimal('100.0000'),
                'description': 'High-strength cobalt-chrome for RPD frameworks'
            },
            {
                'name': 'Gold Alloy Type III',
                'category': self.material_categories[1],
                'unit': 'gram',
                'standard_cost': Decimal('65.00'),
                'current_stock': Decimal('50.0000'),
                'minimum_stock': Decimal('10.0000'),
                'description': 'Premium gold alloy for high-end restorations'
            },
            # Composites
            {
                'name': 'Nano-Hybrid Composite Resin',
                'category': self.material_categories[2],
                'unit': 'ml',
                'standard_cost': Decimal('12.50'),
                'current_stock': Decimal('150.0000'),
                'minimum_stock': Decimal('20.0000'),
                'description': 'High-strength nano-hybrid composite for direct restorations'
            },
            # Impression Materials
            {
                'name': 'Polyvinyl Siloxane (PVS)',
                'category': self.material_categories[3],
                'unit': 'ml',
                'standard_cost': Decimal('8.75'),
                'current_stock': Decimal('200.0000'),
                'minimum_stock': Decimal('50.0000'),
                'description': 'High-precision impression material'
            },
            # Adhesives
            {
                'name': 'Resin Cement - Dual Cure',
                'category': self.material_categories[4],
                'unit': 'ml',
                'standard_cost': Decimal('15.25'),
                'current_stock': Decimal('80.0000'),
                'minimum_stock': Decimal('15.0000'),
                'description': 'Dual-cure resin cement for permanent cementation'
            }
        ]
        
        self.materials = []
        for mat_data in materials_data:
            material, created = Material.objects.get_or_create(
                name=mat_data['name'],
                defaults=mat_data
            )
            self.materials.append(material)

    def create_item_categories(self):
        """Create item categories"""
        self.stdout.write('Creating item categories...')
        
        categories_data = [
            {'name': 'Crowns', 'description': 'All types of dental crowns'},
            {'name': 'Bridges', 'description': 'Fixed dental bridges'},
            {'name': 'Veneers', 'description': 'Porcelain and composite veneers'},
            {'name': 'Inlays/Onlays', 'description': 'Indirect restorations'},
            {'name': 'Implant Components', 'description': 'Custom abutments and crowns'},
            {'name': 'Removable Prosthetics', 'description': 'Partial and complete dentures'},
        ]
        
        self.item_categories = []
        for cat_data in categories_data:
            category, created = ItemCategory.objects.get_or_create(
                name=cat_data['name'],
                defaults=cat_data
            )
            self.item_categories.append(category)

    def create_items(self):
        """Create test items"""
        self.stdout.write('Creating items...')
        
        items_data = [
            # Crowns
            {
                'name': 'Zirconia Crown - Anterior',
                'category': self.item_categories[0],
                'description': 'High-translucency zirconia crown for anterior teeth',
                'estimated_production_time_minutes': 180,
                'is_active': True
            },
            {
                'name': 'Lithium Disilicate Crown',
                'category': self.item_categories[0],
                'description': 'Premium aesthetic crown for high-visibility areas',
                'estimated_production_time_minutes': 210,
                'is_active': True
            },
            {
                'name': 'PFM Crown (Porcelain Fused to Metal)',
                'category': self.item_categories[0],
                'description': 'Traditional PFM crown with gold substructure',
                'estimated_production_time_minutes': 240,
                'is_active': True
            },
            # Bridges
            {
                'name': 'Zirconia Bridge (3-unit)',
                'category': self.item_categories[1],
                'description': '3-unit zirconia bridge',
                'estimated_production_time_minutes': 360,
                'is_active': True
            },
            {
                'name': 'Titanium Bridge (4-unit)',
                'category': self.item_categories[1],
                'description': '4-unit titanium bridge for posterior region',
                'estimated_production_time_minutes': 480,
                'is_active': True
            },
            # Veneers
            {
                'name': 'Porcelain Veneer',
                'category': self.item_categories[2],
                'description': 'Ultra-thin porcelain veneer',
                'estimated_production_time_minutes': 120,
                'is_active': True
            },
            # Implant Components
            {
                'name': 'Custom Titanium Abutment',
                'category': self.item_categories[4],
                'description': 'CAD/CAM custom titanium abutment',
                'estimated_production_time_minutes': 90,
                'is_active': True
            },
            {
                'name': 'Implant Crown - Screw Retained',
                'category': self.item_categories[4],
                'description': 'Screw-retained implant crown',
                'estimated_production_time_minutes': 150,
                'is_active': True
            }
        ]
        
        self.items = []
        for item_data in items_data:
            item, created = Item.objects.get_or_create(
                name=item_data['name'],
                defaults=item_data
            )
            self.items.append(item)

    def create_material_compositions(self):
        """Create material compositions for items"""
        self.stdout.write('Creating material compositions...')
        
        # Define compositions for each item
        compositions = [
            # Zirconia Crown - Anterior
            {'item': self.items[0], 'material': self.materials[0], 'quantity': Decimal('0.6000'), 'waste_factor': Decimal('12.00')},
            {'item': self.items[0], 'material': self.materials[2], 'quantity': Decimal('2.5000'), 'waste_factor': Decimal('8.00')},
            
            # Lithium Disilicate Crown
            {'item': self.items[1], 'material': self.materials[1], 'quantity': Decimal('0.7000'), 'waste_factor': Decimal('10.00')},
            
            # PFM Crown
            {'item': self.items[2], 'material': self.materials[5], 'quantity': Decimal('3.2000'), 'waste_factor': Decimal('5.00')},
            {'item': self.items[2], 'material': self.materials[2], 'quantity': Decimal('1.8000'), 'waste_factor': Decimal('8.00')},
            
            # Zirconia Bridge (3-unit)
            {'item': self.items[3], 'material': self.materials[0], 'quantity': Decimal('1.8000'), 'waste_factor': Decimal('15.00')},
            {'item': self.items[3], 'material': self.materials[2], 'quantity': Decimal('4.5000'), 'waste_factor': Decimal('10.00')},
            
            # Titanium Bridge (4-unit)
            {'item': self.items[4], 'material': self.materials[3], 'quantity': Decimal('25.0000'), 'waste_factor': Decimal('8.00')},
            
            # Porcelain Veneer
            {'item': self.items[5], 'material': self.materials[2], 'quantity': Decimal('1.2000'), 'waste_factor': Decimal('15.00')},
            
            # Custom Titanium Abutment
            {'item': self.items[6], 'material': self.materials[3], 'quantity': Decimal('4.5000'), 'waste_factor': Decimal('5.00')},
            
            # Implant Crown - Screw Retained
            {'item': self.items[7], 'material': self.materials[0], 'quantity': Decimal('0.5000'), 'waste_factor': Decimal('10.00')},
            {'item': self.items[7], 'material': self.materials[3], 'quantity': Decimal('2.0000'), 'waste_factor': Decimal('5.00')},
        ]
        
        for comp_data in compositions:
            composition, created = ItemMaterialComposition.objects.get_or_create(
                item=comp_data['item'],
                material=comp_data['material'],
                defaults={
                    'quantity': comp_data['quantity'],
                    'waste_factor': comp_data['waste_factor'],
                    'notes': f"Material composition for {comp_data['item'].name}"
                }
            )

    def create_price_lists(self):
        """Create price lists and item prices"""
        self.stdout.write('Creating price lists...')
        
        # Create main price list
        self.price_list, created = PriceList.objects.get_or_create(
            name='Standard Pricing 2024',
            defaults={
                'description': 'Standard laboratory pricing for 2024',
                'is_active': True,
                'effective_date': datetime.now().date()
            }
        )
        
        # Create item prices with realistic pricing
        item_prices_data = [
            {'item': self.items[0], 'base_price': Decimal('185.00'), 'labor_cost': Decimal('95.00'), 'markup_percentage': Decimal('35.00')},
            {'item': self.items[1], 'base_price': Decimal('220.00'), 'labor_cost': Decimal('110.00'), 'markup_percentage': Decimal('40.00')},
            {'item': self.items[2], 'base_price': Decimal('280.00'), 'labor_cost': Decimal('140.00'), 'markup_percentage': Decimal('30.00')},
            {'item': self.items[3], 'base_price': Decimal('520.00'), 'labor_cost': Decimal('280.00'), 'markup_percentage': Decimal('38.00')},
            {'item': self.items[4], 'base_price': Decimal('680.00'), 'labor_cost': Decimal('320.00'), 'markup_percentage': Decimal('42.00')},
            {'item': self.items[5], 'base_price': Decimal('145.00'), 'labor_cost': Decimal('85.00'), 'markup_percentage': Decimal('45.00')},
            {'item': self.items[6], 'base_price': Decimal('125.00'), 'labor_cost': Decimal('65.00'), 'markup_percentage': Decimal('35.00')},
            {'item': self.items[7], 'base_price': Decimal('195.00'), 'labor_cost': Decimal('105.00'), 'markup_percentage': Decimal('38.00')},
        ]
        
        for price_data in item_prices_data:
            item_price, created = ItemPrice.objects.get_or_create(
                price_list=self.price_list,
                item=price_data['item'],
                defaults=price_data
            )

    def create_cases(self):
        """Create test cases"""
        self.stdout.write('Creating cases...')
        
        case_statuses = ['received', 'in_progress', 'quality_check', 'completed', 'shipped']
        service_types = ['crown', 'bridge', 'veneer', 'implant', 'denture']
        priorities = ['low', 'normal', 'high', 'urgent']
        
        for i in range(15):
            case, created = Case.objects.get_or_create(
                case_number=f'LAB-2024-{str(i+1).zfill(4)}',
                defaults={
                    'tenant': self.tenant,
                    'patient_name': f'Patient {chr(65 + i % 26)} {i+1}',
                    'dentist_name': f'Dr. {["Smith", "Johnson", "Williams", "Brown", "Jones"][i % 5]}',
                    'service_type': random.choice(service_types),
                    'priority': random.choice(priorities),
                    'due_date': datetime.now().date() + timedelta(days=random.randint(3, 21)),
                    'notes': f'Test case {i+1} - {random.choice(["Anterior restoration", "Posterior restoration", "Full mouth rehabilitation", "Single tooth replacement", "Aesthetic enhancement"])}',
                    'created_by': random.choice([self.admin_user] + self.technicians)
                }
            )
            
            if created:
                # Create case status
                CaseStatus.objects.create(
                    case=case,
                    status=random.choice(case_statuses),
                    notes=f'Case {i+1} status update - {random.choice(["Initial assessment completed", "Work in progress", "Quality check passed", "Ready for delivery", "Shipped to dentist"])}',
                    created_by=random.choice([self.admin_user] + self.technicians)
                )

    def create_invoices(self):
        """Create test invoices"""
        self.stdout.write('Creating invoices...')
        
        cases = Case.objects.all()[:10]  # Use first 10 cases for invoices
        
        for i, case in enumerate(cases):
            invoice, created = Invoice.objects.get_or_create(
                invoice_number=f'INV-2024-{str(i+1).zfill(4)}',
                defaults={
                    'tenant': self.tenant,
                    'case': case,
                    'dentist_name': case.dentist_name,
                    'patient_name': case.patient_name,
                    'issue_date': datetime.now().date() - timedelta(days=random.randint(1, 30)),
                    'due_date': datetime.now().date() + timedelta(days=30),
                    'status': random.choice(['draft', 'sent', 'paid', 'overdue']),
                    'notes': f'Invoice for case {case.case_number}',
                    'created_by': self.admin_user
                }
            )
            
            if created:
                # Add invoice items
                selected_items = random.sample(self.items, random.randint(1, 3))
                for item in selected_items:
                    item_price = ItemPrice.objects.filter(item=item).first()
                    if item_price:
                        quantity = random.randint(1, 2)
                        
                        # Calculate costs
                        material_cost = sum([
                            comp.get_material_cost() for comp in item.material_compositions.all()
                        ])
                        
                        invoice_item = InvoiceItem.objects.create(
                            invoice=invoice,
                            item=item,
                            quantity=quantity,
                            unit_price=item_price.base_price,
                            material_cost_per_unit=material_cost,
                            labor_cost_per_unit=item_price.labor_cost,
                            total_material_cost=material_cost * quantity,
                            total_labor_cost=item_price.labor_cost * quantity,
                            description=f'{item.name} for {case.patient_name}'
                        )
                        
                        # Create material usage records
                        for composition in item.material_compositions.all():
                            InvoiceItemMaterialUsage.objects.create(
                                invoice_item=invoice_item,
                                material=composition.material,
                                planned_quantity=composition.get_total_quantity_needed() * quantity,
                                actual_quantity=composition.get_total_quantity_needed() * quantity * Decimal(str(random.uniform(0.95, 1.05))),
                                unit_cost=composition.material.standard_cost,
                                notes=f'Material usage for {item.name}'
                            )

    def create_large_dataset(self):
        """Create large dataset for performance testing"""
        self.stdout.write('Creating large dataset for performance testing...')
        
        # Create additional materials
        for i in range(50):
            Material.objects.get_or_create(
                name=f'Test Material {i+1}',
                defaults={
                    'category': random.choice(self.material_categories),
                    'unit': random.choice(['piece', 'gram', 'ml']),
                    'standard_cost': Decimal(str(random.uniform(1.0, 100.0))),
                    'current_stock': Decimal(str(random.uniform(10.0, 1000.0))),
                    'minimum_stock': Decimal(str(random.uniform(5.0, 50.0))),
                    'description': f'Test material {i+1} for performance testing'
                }
            )
        
        # Create additional cases
        for i in range(100):
            Case.objects.get_or_create(
                case_number=f'PERF-2024-{str(i+1).zfill(4)}',
                defaults={
                    'tenant': self.tenant,
                    'patient_name': f'Performance Test Patient {i+1}',
                    'dentist_name': f'Dr. Performance {i % 10}',
                    'service_type': random.choice(['crown', 'bridge', 'veneer']),
                    'priority': random.choice(['low', 'normal', 'high']),
                    'due_date': datetime.now().date() + timedelta(days=random.randint(1, 30)),
                    'notes': f'Performance test case {i+1}',
                    'created_by': self.admin_user
                }
            )

    def display_summary(self):
        """Display summary of created data"""
        self.stdout.write('\n' + '='*60)
        self.stdout.write(self.style.SUCCESS('📊 TEST DATA SUMMARY'))
        self.stdout.write('='*60)
        
        self.stdout.write(f'👥 Users: {User.objects.count()}')
        self.stdout.write(f'🏢 Tenants: {Tenant.objects.count()}')
        self.stdout.write(f'🏭 Suppliers: {Supplier.objects.count()}')
        self.stdout.write(f'📦 Material Categories: {MaterialCategory.objects.count()}')
        self.stdout.write(f'🧪 Materials: {Material.objects.count()}')
        self.stdout.write(f'📋 Item Categories: {ItemCategory.objects.count()}')
        self.stdout.write(f'🦷 Items: {Item.objects.count()}')
        self.stdout.write(f'🔗 Material Compositions: {ItemMaterialComposition.objects.count()}')
        self.stdout.write(f'💰 Price Lists: {PriceList.objects.count()}')
        self.stdout.write(f'💵 Item Prices: {ItemPrice.objects.count()}')
        self.stdout.write(f'📁 Cases: {Case.objects.count()}')
        self.stdout.write(f'📄 Invoices: {Invoice.objects.count()}')
        self.stdout.write(f'📝 Invoice Items: {InvoiceItem.objects.count()}')
        
        self.stdout.write('\n🌐 ACCESS INFORMATION:')
        self.stdout.write('• Frontend: http://localhost:3001')
        self.stdout.write('• Backend API: http://localhost:8001/api/v1')
        self.stdout.write('• Django Admin: http://localhost:8001/admin')
        
        self.stdout.write('\n🔐 LOGIN CREDENTIALS:')
        self.stdout.write('• Admin: admin / admin123')
        self.stdout.write('• Technician 1: tech1 / tech123')
        self.stdout.write('• Technician 2: tech2 / tech123')
        self.stdout.write('• Technician 3: tech3 / tech123')
        
        self.stdout.write('\n✅ Ready for comprehensive testing!')
