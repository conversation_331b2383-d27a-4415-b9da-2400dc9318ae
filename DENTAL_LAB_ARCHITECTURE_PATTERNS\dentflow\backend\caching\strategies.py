"""
DentFlow Advanced Caching System
Multi-level caching with intelligent invalidation and performance optimization
"""

import json
import hashlib
import time
from typing import Any, Dict, List, Optional, Union, Callable
from functools import wraps, lru_cache
from datetime import datetime, timedelta
from django.core.cache import cache
from django.core.cache.utils import make_template_fragment_key
from django.conf import settings
from django.db.models import QuerySet
import structlog

logger = structlog.get_logger(__name__)


class CacheLevel:
    """Cache level enumeration"""
    MEMORY = "memory"      # In-process memory cache
    REDIS = "redis"        # Redis cache
    DATABASE = "database"  # Database query cache
    CDN = "cdn"           # CDN cache


class CacheKeyGenerator:
    """
    Intelligent cache key generation with collision prevention
    """
    
    @staticmethod
    def generate_key(prefix: str, *args, **kwargs) -> str:
        """Generate cache key with collision prevention"""
        
        # Create base key from arguments
        key_parts = [prefix]
        
        # Add positional arguments
        for arg in args:
            if hasattr(arg, 'id'):
                key_parts.append(f"id_{arg.id}")
            else:
                key_parts.append(str(arg))
        
        # Add keyword arguments (sorted for consistency)
        for key, value in sorted(kwargs.items()):
            key_parts.append(f"{key}_{value}")
        
        # Create hash for long keys to prevent Redis key length issues
        base_key = ":".join(key_parts)
        if len(base_key) > 200:
            key_hash = hashlib.md5(base_key.encode()).hexdigest()
            return f"{prefix}:hash_{key_hash}"
        
        return base_key
    
    @staticmethod
    def generate_query_key(model_name: str, filters: Dict, ordering: List = None) -> str:
        """Generate cache key for database queries"""
        key_parts = [f"query_{model_name}"]
        
        # Add filters
        for key, value in sorted(filters.items()):
            key_parts.append(f"{key}_{value}")
        
        # Add ordering
        if ordering:
            key_parts.append(f"order_{'_'.join(ordering)}")
        
        return CacheKeyGenerator.generate_key(*key_parts)
    
    @staticmethod
    def generate_user_key(user_id: str, data_type: str, **kwargs) -> str:
        """Generate user-specific cache key"""
        return CacheKeyGenerator.generate_key(
            "user", user_id, data_type, **kwargs
        )
    
    @staticmethod
    def generate_tenant_key(tenant_id: str, data_type: str, **kwargs) -> str:
        """Generate tenant-specific cache key"""
        return CacheKeyGenerator.generate_key(
            "tenant", tenant_id, data_type, **kwargs
        )


class SmartCache:
    """
    Intelligent multi-level caching system with automatic invalidation
    """
    
    def __init__(self):
        self.cache_ttl = {
            'dashboard_stats': 300,      # 5 minutes
            'case_details': 600,         # 10 minutes
            'user_profile': 1800,        # 30 minutes
            'reports': 3600,             # 1 hour
            'static_data': 86400,        # 24 hours
            'workflow_templates': 43200,  # 12 hours
        }
        
        self.memory_cache = {}  # Simple in-memory cache
        self.cache_stats = {
            'hits': 0,
            'misses': 0,
            'invalidations': 0
        }
    
    def get(self, key: str, default: Any = None, 
           cache_level: str = CacheLevel.REDIS) -> Any:
        """Get value from cache with multi-level fallback"""
        
        # Try memory cache first
        if cache_level == CacheLevel.MEMORY and key in self.memory_cache:
            entry = self.memory_cache[key]
            if entry['expires'] > time.time():
                self.cache_stats['hits'] += 1
                return entry['value']
            else:
                del self.memory_cache[key]
        
        # Try Redis cache
        if cache_level in [CacheLevel.REDIS, CacheLevel.DATABASE]:
            try:
                value = cache.get(key, default)
                if value is not default:
                    self.cache_stats['hits'] += 1
                    return value
            except Exception as e:
                logger.error("Cache get error", key=key, error=str(e))
        
        self.cache_stats['misses'] += 1
        return default
    
    def set(self, key: str, value: Any, ttl: Optional[int] = None,
           cache_level: str = CacheLevel.REDIS) -> bool:
        """Set value in cache with automatic TTL determination"""
        
        if ttl is None:
            ttl = self._determine_ttl(key)
        
        try:
            # Set in memory cache if requested
            if cache_level == CacheLevel.MEMORY:
                self.memory_cache[key] = {
                    'value': value,
                    'expires': time.time() + ttl,
                    'created': time.time()
                }
            
            # Set in Redis cache
            if cache_level in [CacheLevel.REDIS, CacheLevel.DATABASE]:
                cache.set(key, value, ttl)
            
            return True
            
        except Exception as e:
            logger.error("Cache set error", key=key, error=str(e))
            return False
    
    def delete(self, key: str, cache_level: str = CacheLevel.REDIS) -> bool:
        """Delete key from cache"""
        
        try:
            # Delete from memory cache
            if key in self.memory_cache:
                del self.memory_cache[key]
            
            # Delete from Redis cache
            if cache_level in [CacheLevel.REDIS, CacheLevel.DATABASE]:
                cache.delete(key)
            
            self.cache_stats['invalidations'] += 1
            return True
            
        except Exception as e:
            logger.error("Cache delete error", key=key, error=str(e))
            return False
    
    def invalidate_pattern(self, pattern: str) -> int:
        """Invalidate all keys matching pattern"""
        
        try:
            # For Redis with pattern support
            from django.core.cache.backends.redis import RedisCache
            
            if isinstance(cache._cache, RedisCache):
                redis_client = cache._cache.get_client(1)
                keys = redis_client.keys(pattern)
                if keys:
                    redis_client.delete(*keys)
                    return len(keys)
            
            return 0
            
        except Exception as e:
            logger.error("Cache pattern invalidation error", pattern=pattern, error=str(e))
            return 0
    
    def _determine_ttl(self, key: str) -> int:
        """Determine TTL based on key patterns"""
        
        for pattern, ttl in self.cache_ttl.items():
            if pattern in key:
                return ttl
        
        # Default TTL
        return 600  # 10 minutes
    
    def get_stats(self) -> Dict[str, Any]:
        """Get cache performance statistics"""
        
        total_requests = self.cache_stats['hits'] + self.cache_stats['misses']
        hit_rate = (self.cache_stats['hits'] / total_requests * 100) if total_requests > 0 else 0
        
        return {
            'hits': self.cache_stats['hits'],
            'misses': self.cache_stats['misses'],
            'hit_rate': round(hit_rate, 2),
            'invalidations': self.cache_stats['invalidations'],
            'memory_cache_size': len(self.memory_cache)
        }


class QueryCache:
    """
    Intelligent database query caching with automatic invalidation
    """
    
    def __init__(self, smart_cache: SmartCache):
        self.cache = smart_cache
        self.invalidation_map = {}  # Maps models to cache keys
    
    def cached_query(self, model_class, filters: Dict, 
                    ordering: List = None, ttl: int = 600):
        """Cache database query results"""
        
        def decorator(func):
            @wraps(func)
            def wrapper(*args, **kwargs):
                # Generate cache key
                cache_key = CacheKeyGenerator.generate_query_key(
                    model_class.__name__, filters, ordering
                )
                
                # Try to get from cache
                result = self.cache.get(cache_key, cache_level=CacheLevel.DATABASE)
                if result is not None:
                    return result
                
                # Execute query
                result = func(*args, **kwargs)
                
                # Cache result
                self.cache.set(cache_key, result, ttl, CacheLevel.DATABASE)
                
                # Register for invalidation
                self._register_for_invalidation(model_class, cache_key)
                
                return result
            
            return wrapper
        return decorator
    
    def invalidate_model(self, model_class):
        """Invalidate all cached queries for a model"""
        
        model_name = model_class.__name__
        if model_name in self.invalidation_map:
            keys_to_invalidate = self.invalidation_map[model_name]
            
            for key in keys_to_invalidate:
                self.cache.delete(key, CacheLevel.DATABASE)
            
            # Clear invalidation map for this model
            self.invalidation_map[model_name] = set()
    
    def _register_for_invalidation(self, model_class, cache_key: str):
        """Register cache key for model-based invalidation"""
        
        model_name = model_class.__name__
        if model_name not in self.invalidation_map:
            self.invalidation_map[model_name] = set()
        
        self.invalidation_map[model_name].add(cache_key)


class DashboardCache:
    """
    Specialized caching for dashboard data with real-time invalidation
    """
    
    def __init__(self, smart_cache: SmartCache):
        self.cache = smart_cache
    
    def get_dashboard_stats(self, tenant_id: str, user_id: str) -> Optional[Dict]:
        """Get cached dashboard statistics"""
        
        cache_key = CacheKeyGenerator.generate_tenant_key(
            tenant_id, "dashboard_stats", user_id=user_id
        )
        
        return self.cache.get(cache_key)
    
    def set_dashboard_stats(self, tenant_id: str, user_id: str, 
                           stats: Dict, ttl: int = 300):
        """Cache dashboard statistics"""
        
        cache_key = CacheKeyGenerator.generate_tenant_key(
            tenant_id, "dashboard_stats", user_id=user_id
        )
        
        # Add metadata
        stats['cached_at'] = datetime.utcnow().isoformat()
        stats['cache_ttl'] = ttl
        
        self.cache.set(cache_key, stats, ttl)
    
    def invalidate_dashboard(self, tenant_id: str, user_id: str = None):
        """Invalidate dashboard cache"""
        
        if user_id:
            # Invalidate specific user's dashboard
            cache_key = CacheKeyGenerator.generate_tenant_key(
                tenant_id, "dashboard_stats", user_id=user_id
            )
            self.cache.delete(cache_key)
        else:
            # Invalidate all dashboards for tenant
            pattern = CacheKeyGenerator.generate_tenant_key(
                tenant_id, "dashboard_stats", user_id="*"
            )
            self.cache.invalidate_pattern(pattern)


class CacheDecorators:
    """
    Convenient decorators for caching function results
    """
    
    def __init__(self, smart_cache: SmartCache):
        self.cache = smart_cache
    
    def cached(self, key_prefix: str, ttl: int = 600, 
              cache_level: str = CacheLevel.REDIS):
        """Generic caching decorator"""
        
        def decorator(func):
            @wraps(func)
            def wrapper(*args, **kwargs):
                # Generate cache key
                cache_key = CacheKeyGenerator.generate_key(
                    key_prefix, *args, **kwargs
                )
                
                # Try cache first
                result = self.cache.get(cache_key, cache_level=cache_level)
                if result is not None:
                    return result
                
                # Execute function
                result = func(*args, **kwargs)
                
                # Cache result
                self.cache.set(cache_key, result, ttl, cache_level)
                
                return result
            
            return wrapper
        return decorator
    
    def cached_property(self, ttl: int = 300):
        """Cache property access"""
        
        def decorator(func):
            cache_name = f"_cached_{func.__name__}"
            
            @wraps(func)
            def wrapper(self):
                # Check if cached value exists and is fresh
                if hasattr(self, cache_name):
                    cached_data = getattr(self, cache_name)
                    if cached_data['expires'] > time.time():
                        return cached_data['value']
                
                # Compute and cache value
                result = func(self)
                setattr(self, cache_name, {
                    'value': result,
                    'expires': time.time() + ttl
                })
                
                return result
            
            return wrapper
        return decorator
    
    def invalidate_on_save(self, cache_keys: List[str]):
        """Invalidate cache keys when model is saved"""
        
        def decorator(model_class):
            original_save = model_class.save
            
            def new_save(self, *args, **kwargs):
                result = original_save(self, *args, **kwargs)
                
                # Invalidate specified cache keys
                for key_pattern in cache_keys:
                    # Replace placeholders with actual values
                    key = key_pattern.format(
                        id=self.id,
                        tenant_id=getattr(self, 'tenant_id', ''),
                        **kwargs
                    )
                    cache.delete(key)
                
                return result
            
            model_class.save = new_save
            return model_class
        
        return decorator


class CacheWarmer:
    """
    Proactive cache warming for improved performance
    """
    
    def __init__(self, smart_cache: SmartCache):
        self.cache = smart_cache
        self.warming_jobs = []
    
    def register_warming_job(self, name: str, cache_key: str, 
                           data_function: Callable, ttl: int = 600,
                           schedule_interval: int = 300):
        """Register a cache warming job"""
        
        self.warming_jobs.append({
            'name': name,
            'cache_key': cache_key,
            'data_function': data_function,
            'ttl': ttl,
            'schedule_interval': schedule_interval,
            'last_run': 0
        })
    
    def warm_cache(self, job_name: str = None):
        """Warm cache for specific job or all jobs"""
        
        jobs_to_run = self.warming_jobs
        if job_name:
            jobs_to_run = [job for job in self.warming_jobs if job['name'] == job_name]
        
        for job in jobs_to_run:
            try:
                # Check if job needs to run
                if time.time() - job['last_run'] < job['schedule_interval']:
                    continue
                
                # Execute data function
                data = job['data_function']()
                
                # Cache the data
                self.cache.set(job['cache_key'], data, job['ttl'])
                
                # Update last run time
                job['last_run'] = time.time()
                
                logger.info(
                    "Cache warming job completed",
                    job_name=job['name'],
                    cache_key=job['cache_key']
                )
                
            except Exception as e:
                logger.error(
                    "Cache warming job failed",
                    job_name=job['name'],
                    error=str(e)
                )
    
    def get_popular_dashboard_data(self, tenant_id: str):
        """Example warming job for dashboard data"""
        
        # This would be implemented based on your actual dashboard needs
        from backend.apps.cases.models import CaseModel
        
        stats = {
            'total_cases': CaseModel.objects.filter(tenant_id=tenant_id).count(),
            'active_cases': CaseModel.objects.filter(
                tenant_id=tenant_id
            ).exclude(status__in=['delivered', 'cancelled']).count(),
            'overdue_cases': CaseModel.objects.filter(
                tenant_id=tenant_id,
                due_date__lt=datetime.utcnow()
            ).exclude(status__in=['delivered', 'cancelled']).count()
        }
        
        return stats


# Global cache instances
smart_cache = SmartCache()
query_cache = QueryCache(smart_cache)
dashboard_cache = DashboardCache(smart_cache)
cache_decorators = CacheDecorators(smart_cache)
cache_warmer = CacheWarmer(smart_cache)


# Convenience decorators
cached = cache_decorators.cached
cached_property = cache_decorators.cached_property
invalidate_on_save = cache_decorators.invalidate_on_save


# Cache invalidation signals
from django.db.models.signals import post_save, post_delete
from django.dispatch import receiver


def setup_cache_invalidation():
    """Setup automatic cache invalidation for models"""
    
    @receiver(post_save)
    def invalidate_model_cache(sender, instance, **kwargs):
        """Invalidate cache when model is saved"""
        query_cache.invalidate_model(sender)
        
        # Invalidate related dashboard caches
        if hasattr(instance, 'tenant_id'):
            dashboard_cache.invalidate_dashboard(instance.tenant_id)
    
    @receiver(post_delete)
    def invalidate_model_cache_on_delete(sender, instance, **kwargs):
        """Invalidate cache when model is deleted"""
        query_cache.invalidate_model(sender)
        
        # Invalidate related dashboard caches
        if hasattr(instance, 'tenant_id'):
            dashboard_cache.invalidate_dashboard(instance.tenant_id)


# Initialize cache invalidation
setup_cache_invalidation()


class CacheMiddleware:
    """
    Middleware for automatic cache headers and optimization
    """
    
    def __init__(self, get_response):
        self.get_response = get_response
    
    def __call__(self, request):
        # Add cache-related headers to request
        request.cache_key = self._generate_request_cache_key(request)
        
        response = self.get_response(request)
        
        # Add cache control headers
        if request.path.startswith('/api/'):
            self._add_api_cache_headers(request, response)
        
        return response
    
    def _generate_request_cache_key(self, request):
        """Generate unique cache key for request"""
        return CacheKeyGenerator.generate_key(
            "request",
            request.method,
            request.path,
            request.GET.urlencode(),
            getattr(request.user, 'id', 'anonymous')
        )
    
    def _add_api_cache_headers(self, request, response):
        """Add appropriate cache headers for API responses"""
        
        # Default cache control
        if response.status_code == 200:
            if 'dashboard' in request.path:
                response['Cache-Control'] = 'private, max-age=300'  # 5 minutes
            elif 'reports' in request.path:
                response['Cache-Control'] = 'private, max-age=3600'  # 1 hour
            else:
                response['Cache-Control'] = 'private, max-age=600'  # 10 minutes
        else:
            response['Cache-Control'] = 'no-cache, no-store, must-revalidate'