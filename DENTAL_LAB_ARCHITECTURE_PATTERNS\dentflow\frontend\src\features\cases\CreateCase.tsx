/**
 * Create Case Component - Comprehensive Enhancement
 * Multi-step wizard for creating new dental laboratory cases
 */

import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import {
  Box,
  Typography,
  Paper,
  TextField,
  Button,
  Grid,
  MenuItem,
  <PERSON>per,
  Step,
  StepLabel,
  StepContent,
  Card,
  CardContent,
  Divider,
  Chip,
  Avatar,
  IconButton,
  FormControl,
  InputLabel,
  Select,
  FormControlLabel,
  Checkbox,
  RadioGroup,
  Radio,
  FormLabel,
  Autocomplete,
  Alert,
  LinearProgress,
  Breadcrumbs,
  Link,
  useTheme,
  alpha,
} from '@mui/material';
import {
  ArrowBack,
  ArrowForward,
  Person,
  Business,
  Assignment,
  AttachFile,
  CalendarToday,
  MonetizationOn,
  CheckCircle,
  Warning,
  Add,
  PhotoCamera,
  Description,
  Save,
  Cancel,
  Schedule,
  Build,
  Science,
  LocalShipping,
  Done,
  NavigateNext,
} from '@mui/icons-material';
import { useCreateCase } from '../../hooks/api';
import { useNotifications } from '../../context';

// Enhanced interfaces
interface PatientInfo {
  name: string;
  phone: string;
  email: string;
  age: string;
  gender: string;
}

interface CaseDetails {
  service_type: string;
  tooth_number: string;
  shade: string;
  material: string;
  priority: string;
  special_instructions: string;
}

interface ClinicInfo {
  clinic_id: string;
  doctor_name: string;
  contact_person: string;
}

interface Timeline {
  due_date: string;
  estimated_completion: string;
  rush_order: boolean;
}

interface CostEstimate {
  base_cost: number;
  material_cost: number;
  labor_cost: number;
  rush_fee: number;
  total_cost: number;
}

function CreateCase() {
  const navigate = useNavigate();
  const theme = useTheme();
  const { success, error } = useNotifications();
  const createCaseMutation = useCreateCase();

  // Enhanced state management
  const [activeStep, setActiveStep] = useState(0);
  const [completed, setCompleted] = useState<{ [k: number]: boolean }>({});

  const [patientInfo, setPatientInfo] = useState<PatientInfo>({
    name: '',
    phone: '',
    email: '',
    age: '',
    gender: '',
  });

  const [caseDetails, setCaseDetails] = useState<CaseDetails>({
    service_type: '',
    tooth_number: '',
    shade: '',
    material: '',
    priority: 'normal',
    special_instructions: '',
  });

  const [clinicInfo, setClinicInfo] = useState<ClinicInfo>({
    clinic_id: '',
    doctor_name: '',
    contact_person: '',
  });

  const [timeline, setTimeline] = useState<Timeline>({
    due_date: '',
    estimated_completion: '',
    rush_order: false,
  });

  const [costEstimate, setCostEstimate] = useState<CostEstimate>({
    base_cost: 0,
    material_cost: 0,
    labor_cost: 0,
    rush_fee: 0,
    total_cost: 0,
  });

  const [attachedFiles, setAttachedFiles] = useState<File[]>([]);
  const [errors, setErrors] = useState<{ [key: string]: string }>({});

  // Step configuration
  const steps = [
    {
      label: 'Patient Information',
      description: 'Enter patient details and contact information',
      icon: <Person />,
    },
    {
      label: 'Case Details',
      description: 'Specify service type, materials, and requirements',
      icon: <Assignment />,
    },
    {
      label: 'Clinic Information',
      description: 'Select clinic and doctor information',
      icon: <Business />,
    },
    {
      label: 'Timeline & Priority',
      description: 'Set due dates and priority level',
      icon: <Schedule />,
    },
    {
      label: 'Files & Photos',
      description: 'Upload impressions, photos, and documents',
      icon: <AttachFile />,
    },
    {
      label: 'Review & Submit',
      description: 'Review all information and submit the case',
      icon: <CheckCircle />,
    },
  ];

  // Service type options
  const serviceTypes = [
    { value: 'crown', label: 'Crown', basePrice: 250 },
    { value: 'bridge', label: 'Bridge', basePrice: 400 },
    { value: 'denture', label: 'Denture', basePrice: 800 },
    { value: 'implant', label: 'Implant Crown', basePrice: 300 },
    { value: 'veneer', label: 'Veneer', basePrice: 200 },
    { value: 'inlay', label: 'Inlay/Onlay', basePrice: 180 },
  ];

  // Material options
  const materials = [
    { value: 'zirconia', label: 'Zirconia', additionalCost: 50 },
    { value: 'porcelain', label: 'Porcelain', additionalCost: 25 },
    { value: 'metal', label: 'Metal', additionalCost: 0 },
    { value: 'composite', label: 'Composite', additionalCost: 15 },
    { value: 'gold', label: 'Gold', additionalCost: 200 },
  ];

  // Shade options
  const shades = [
    'A1', 'A2', 'A3', 'A3.5', 'A4',
    'B1', 'B2', 'B3', 'B4',
    'C1', 'C2', 'C3', 'C4',
    'D2', 'D3', 'D4'
  ];

  // Handler functions
  const handleNext = () => {
    if (validateCurrentStep()) {
      setActiveStep((prevActiveStep) => prevActiveStep + 1);
      setCompleted((prev) => ({ ...prev, [activeStep]: true }));
    }
  };

  const handleBack = () => {
    setActiveStep((prevActiveStep) => prevActiveStep - 1);
  };

  const handleStep = (step: number) => () => {
    setActiveStep(step);
  };

  const validateCurrentStep = (): boolean => {
    const newErrors: { [key: string]: string } = {};

    switch (activeStep) {
      case 0: // Patient Information
        if (!patientInfo.name.trim()) newErrors.name = 'Patient name is required';
        if (!patientInfo.phone.trim()) newErrors.phone = 'Phone number is required';
        break;
      case 1: // Case Details
        if (!caseDetails.service_type) newErrors.service_type = 'Service type is required';
        if (!caseDetails.tooth_number.trim()) newErrors.tooth_number = 'Tooth number is required';
        break;
      case 2: // Clinic Information
        if (!clinicInfo.clinic_id) newErrors.clinic_id = 'Clinic selection is required';
        if (!clinicInfo.doctor_name.trim()) newErrors.doctor_name = 'Doctor name is required';
        break;
      case 3: // Timeline
        if (!timeline.due_date) newErrors.due_date = 'Due date is required';
        break;
      default:
        break;
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const calculateCost = () => {
    const serviceType = serviceTypes.find(s => s.value === caseDetails.service_type);
    const material = materials.find(m => m.value === caseDetails.material);

    const baseCost = serviceType?.basePrice || 0;
    const materialCost = material?.additionalCost || 0;
    const laborCost = baseCost * 0.4; // 40% of base cost
    const rushFee = timeline.rush_order ? baseCost * 0.3 : 0; // 30% rush fee
    const totalCost = baseCost + materialCost + laborCost + rushFee;

    setCostEstimate({
      base_cost: baseCost,
      material_cost: materialCost,
      labor_cost: laborCost,
      rush_fee: rushFee,
      total_cost: totalCost,
    });
  };

  // Calculate cost when relevant fields change
  React.useEffect(() => {
    calculateCost();
  }, [caseDetails.service_type, caseDetails.material, timeline.rush_order]);

  const handleSubmit = async () => {
    if (!validateCurrentStep()) return;

    try {
      const formData = {
        // Patient info
        patient_name: patientInfo.name,
        patient_phone: patientInfo.phone,
        patient_email: patientInfo.email,
        patient_age: patientInfo.age,
        patient_gender: patientInfo.gender,

        // Case details
        service_type: caseDetails.service_type,
        tooth_number: caseDetails.tooth_number,
        shade: caseDetails.shade,
        material: caseDetails.material,
        priority: caseDetails.priority,
        special_instructions: caseDetails.special_instructions,

        // Clinic info
        clinic_id: clinicInfo.clinic_id,
        doctor_name: clinicInfo.doctor_name,
        contact_person: clinicInfo.contact_person,

        // Timeline
        due_date: timeline.due_date,
        estimated_completion: timeline.estimated_completion,
        rush_order: timeline.rush_order,

        // Cost
        estimated_cost: costEstimate.total_cost,
      };

      await createCaseMutation.mutateAsync(formData);
      success('Case created successfully!');
      navigate('/cases');
    } catch (err) {
      error('Failed to create case. Please try again.');
      console.error('Failed to create case:', err);
    }
  };

  const handleFileUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const files = Array.from(event.target.files || []);
    setAttachedFiles(prev => [...prev, ...files]);
  };

  const removeFile = (index: number) => {
    setAttachedFiles(prev => prev.filter((_, i) => i !== index));
  };

  return (
    <Box sx={{ p: 3, backgroundColor: '#f5f5f5', minHeight: '100vh' }}>
      {/* Enhanced Header */}
      <Box sx={{ mb: 4 }}>
        {/* Breadcrumbs */}
        <Breadcrumbs aria-label="breadcrumb" sx={{ mb: 2 }}>
          <Link
            underline="hover"
            color="inherit"
            href="#"
            onClick={() => navigate('/cases')}
            sx={{ display: 'flex', alignItems: 'center' }}
          >
            Cases
          </Link>
          <Typography color="text.primary">Create New Case</Typography>
        </Breadcrumbs>

        {/* Header with Back Button */}
        <Box display="flex" alignItems="center" gap={2} mb={3}>
          <IconButton
            onClick={() => navigate('/cases')}
            sx={{
              bgcolor: 'background.paper',
              boxShadow: 1,
              '&:hover': { boxShadow: 2 }
            }}
          >
            <ArrowBack />
          </IconButton>
          <Box>
            <Typography variant="h3" component="h1" sx={{
              color: 'primary.main',
              fontWeight: 'bold',
              display: 'flex',
              alignItems: 'center',
              gap: 1
            }}>
              ➕ Create New Case
            </Typography>
            <Typography variant="h6" color="text.secondary" sx={{ mt: 1 }}>
              Follow the steps below to create a comprehensive case
            </Typography>
          </Box>
        </Box>

        {/* Progress Indicator */}
        <Card sx={{ mb: 3 }}>
          <CardContent>
            <Box display="flex" justifyContent="space-between" alignItems="center" mb={2}>
              <Typography variant="h6" fontWeight="bold">
                Progress: Step {activeStep + 1} of {steps.length}
              </Typography>
              <Typography variant="body2" color="text.secondary">
                {Math.round(((activeStep + 1) / steps.length) * 100)}% Complete
              </Typography>
            </Box>
            <LinearProgress
              variant="determinate"
              value={((activeStep + 1) / steps.length) * 100}
              sx={{ height: 8, borderRadius: 4 }}
            />
          </CardContent>
        </Card>
      </Box>

      {/* Enhanced Multi-Step Form */}
      <Grid container spacing={3}>
        {/* Step Navigation */}
        <Grid item xs={12} md={4}>
          <Paper sx={{ p: 2, position: 'sticky', top: 20 }}>
            <Typography variant="h6" gutterBottom fontWeight="bold">
              Case Creation Steps
            </Typography>
            <Stepper activeStep={activeStep} orientation="vertical">
              {steps.map((step, index) => (
                <Step key={step.label} completed={completed[index]}>
                  <StepLabel
                    onClick={handleStep(index)}
                    sx={{ cursor: 'pointer' }}
                    StepIconComponent={() => (
                      <Avatar
                        sx={{
                          width: 32,
                          height: 32,
                          bgcolor: index <= activeStep ? 'primary.main' : 'grey.300',
                          color: 'white',
                          fontSize: '0.875rem'
                        }}
                      >
                        {completed[index] ? <CheckCircle /> : step.icon}
                      </Avatar>
                    )}
                  >
                    <Box>
                      <Typography variant="subtitle2" fontWeight="bold">
                        {step.label}
                      </Typography>
                      <Typography variant="caption" color="text.secondary">
                        {step.description}
                      </Typography>
                    </Box>
                  </StepLabel>
                </Step>
              ))}
            </Stepper>
          </Paper>
        </Grid>

        {/* Step Content */}
        <Grid item xs={12} md={8}>
          <Paper sx={{ p: 4 }}>
            {/* Step 1: Patient Information */}
            {activeStep === 0 && (
              <Box>
                <Typography variant="h5" gutterBottom sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                  <Person color="primary" />
                  Patient Information
                </Typography>
                <Typography variant="body2" color="text.secondary" gutterBottom sx={{ mb: 3 }}>
                  Enter the patient's personal and contact information
                </Typography>
                <Divider sx={{ mb: 3 }} />

                <Grid container spacing={3}>
                  <Grid item xs={12} md={6}>
                    <TextField
                      fullWidth
                      label="Patient Name"
                      value={patientInfo.name}
                      onChange={(e) => setPatientInfo(prev => ({ ...prev, name: e.target.value }))}
                      error={!!errors.name}
                      helperText={errors.name}
                      required
                    />
                  </Grid>
                  <Grid item xs={12} md={6}>
                    <TextField
                      fullWidth
                      label="Phone Number"
                      value={patientInfo.phone}
                      onChange={(e) => setPatientInfo(prev => ({ ...prev, phone: e.target.value }))}
                      error={!!errors.phone}
                      helperText={errors.phone}
                      required
                    />
                  </Grid>
                  <Grid item xs={12} md={6}>
                    <TextField
                      fullWidth
                      label="Email Address"
                      type="email"
                      value={patientInfo.email}
                      onChange={(e) => setPatientInfo(prev => ({ ...prev, email: e.target.value }))}
                    />
                  </Grid>
                  <Grid item xs={12} md={6}>
                    <TextField
                      fullWidth
                      label="Age"
                      type="number"
                      value={patientInfo.age}
                      onChange={(e) => setPatientInfo(prev => ({ ...prev, age: e.target.value }))}
                    />
                  </Grid>
                  <Grid item xs={12}>
                    <FormControl component="fieldset">
                      <FormLabel component="legend">Gender</FormLabel>
                      <RadioGroup
                        row
                        value={patientInfo.gender}
                        onChange={(e) => setPatientInfo(prev => ({ ...prev, gender: e.target.value }))}
                      >
                        <FormControlLabel value="male" control={<Radio />} label="Male" />
                        <FormControlLabel value="female" control={<Radio />} label="Female" />
                        <FormControlLabel value="other" control={<Radio />} label="Other" />
                      </RadioGroup>
                    </FormControl>
                  </Grid>
                </Grid>
              </Box>
            )}

            {/* Step 2: Case Details */}
            {activeStep === 1 && (
              <Box>
                <Typography variant="h5" gutterBottom sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                  <Assignment color="primary" />
                  Case Details
                </Typography>
                <Typography variant="body2" color="text.secondary" gutterBottom sx={{ mb: 3 }}>
                  Specify the service type, materials, and technical requirements
                </Typography>
                <Divider sx={{ mb: 3 }} />

                <Grid container spacing={3}>
                  <Grid item xs={12} md={6}>
                    <FormControl fullWidth error={!!errors.service_type}>
                      <InputLabel>Service Type *</InputLabel>
                      <Select
                        value={caseDetails.service_type}
                        label="Service Type *"
                        onChange={(e) => setCaseDetails(prev => ({ ...prev, service_type: e.target.value }))}
                      >
                        {serviceTypes.map((service) => (
                          <MenuItem key={service.value} value={service.value}>
                            <Box display="flex" justifyContent="space-between" width="100%">
                              <span>{service.label}</span>
                              <Chip label={`$${service.basePrice}`} size="small" />
                            </Box>
                          </MenuItem>
                        ))}
                      </Select>
                      {errors.service_type && (
                        <Typography variant="caption" color="error" sx={{ mt: 0.5 }}>
                          {errors.service_type}
                        </Typography>
                      )}
                    </FormControl>
                  </Grid>
                  <Grid item xs={12} md={6}>
                    <TextField
                      fullWidth
                      label="Tooth Number"
                      value={caseDetails.tooth_number}
                      onChange={(e) => setCaseDetails(prev => ({ ...prev, tooth_number: e.target.value }))}
                      error={!!errors.tooth_number}
                      helperText={errors.tooth_number}
                      placeholder="e.g., 11, 21, 36"
                      required
                    />
                  </Grid>
                  <Grid item xs={12} md={6}>
                    <Autocomplete
                      options={shades}
                      value={caseDetails.shade}
                      onChange={(_, newValue) => setCaseDetails(prev => ({ ...prev, shade: newValue || '' }))}
                      renderInput={(params) => (
                        <TextField {...params} label="Shade" placeholder="Select shade" />
                      )}
                    />
                  </Grid>
                  <Grid item xs={12} md={6}>
                    <FormControl fullWidth>
                      <InputLabel>Material</InputLabel>
                      <Select
                        value={caseDetails.material}
                        label="Material"
                        onChange={(e) => setCaseDetails(prev => ({ ...prev, material: e.target.value }))}
                      >
                        {materials.map((material) => (
                          <MenuItem key={material.value} value={material.value}>
                            <Box display="flex" justifyContent="space-between" width="100%">
                              <span>{material.label}</span>
                              <Chip
                                label={material.additionalCost > 0 ? `+$${material.additionalCost}` : 'Base'}
                                size="small"
                                color={material.additionalCost > 0 ? 'warning' : 'default'}
                              />
                            </Box>
                          </MenuItem>
                        ))}
                      </Select>
                    </FormControl>
                  </Grid>
                  <Grid item xs={12}>
                    <FormControl component="fieldset">
                      <FormLabel component="legend">Priority Level</FormLabel>
                      <RadioGroup
                        row
                        value={caseDetails.priority}
                        onChange={(e) => setCaseDetails(prev => ({ ...prev, priority: e.target.value }))}
                      >
                        <FormControlLabel value="low" control={<Radio />} label="Low" />
                        <FormControlLabel value="normal" control={<Radio />} label="Normal" />
                        <FormControlLabel value="urgent" control={<Radio />} label="Urgent" />
                        <FormControlLabel value="stat" control={<Radio />} label="STAT" />
                      </RadioGroup>
                    </FormControl>
                  </Grid>
                  <Grid item xs={12}>
                    <TextField
                      fullWidth
                      label="Special Instructions"
                      multiline
                      rows={4}
                      value={caseDetails.special_instructions}
                      onChange={(e) => setCaseDetails(prev => ({ ...prev, special_instructions: e.target.value }))}
                      placeholder="Enter any special instructions, notes, or requirements..."
                    />
                  </Grid>
                </Grid>
              </Box>
            )}

            {/* Navigation Buttons */}
            <Box sx={{ display: 'flex', justifyContent: 'space-between', mt: 4, pt: 3, borderTop: 1, borderColor: 'divider' }}>
              <Button
                onClick={handleBack}
                disabled={activeStep === 0}
                startIcon={<ArrowBack />}
              >
                Back
              </Button>

              <Box display="flex" gap={2}>
                <Button
                  variant="outlined"
                  onClick={() => navigate('/cases')}
                  startIcon={<Cancel />}
                >
                  Cancel
                </Button>

                {activeStep === steps.length - 1 ? (
                  <Button
                    variant="contained"
                    onClick={handleSubmit}
                    disabled={createCaseMutation.isPending}
                    startIcon={<Save />}
                  >
                    {createCaseMutation.isPending ? 'Creating...' : 'Create Case'}
                  </Button>
                ) : (
                  <Button
                    variant="contained"
                    onClick={handleNext}
                    endIcon={<ArrowForward />}
                  >
                    Next
                  </Button>
                )}
              </Box>
            </Box>
          </Paper>
        </Grid>
      </Grid>

      {/* Cost Estimate Card */}
      {(caseDetails.service_type || caseDetails.material) && (
        <Card sx={{ mt: 3, position: 'sticky', bottom: 20 }}>
          <CardContent>
            <Typography variant="h6" gutterBottom sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
              <MonetizationOn color="primary" />
              Cost Estimate
            </Typography>
            <Grid container spacing={2}>
              <Grid item xs={6} sm={3}>
                <Typography variant="body2" color="text.secondary">Base Cost</Typography>
                <Typography variant="h6">${costEstimate.base_cost}</Typography>
              </Grid>
              <Grid item xs={6} sm={3}>
                <Typography variant="body2" color="text.secondary">Material</Typography>
                <Typography variant="h6">${costEstimate.material_cost}</Typography>
              </Grid>
              <Grid item xs={6} sm={3}>
                <Typography variant="body2" color="text.secondary">Labor</Typography>
                <Typography variant="h6">${costEstimate.labor_cost}</Typography>
              </Grid>
              <Grid item xs={6} sm={3}>
                <Typography variant="body2" color="text.secondary">Total</Typography>
                <Typography variant="h5" color="primary.main" fontWeight="bold">
                  ${costEstimate.total_cost}
                </Typography>
              </Grid>
            </Grid>
          </CardContent>
        </Card>
      )}
    </Box>
  );
}

export default CreateCase;
