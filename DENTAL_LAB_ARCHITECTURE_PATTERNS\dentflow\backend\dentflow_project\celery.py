"""
Celery configuration for DentFlow project.
"""
import os
from celery import Celery
from django.conf import settings

# Set the default Django settings module for the 'celery' program.
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'dentflow_project.settings')

app = Celery('dentflow')

# Using a string here means the worker doesn't have to serialize
# the configuration object to child processes.
app.config_from_object('django.conf:settings', namespace='CELERY')

# Load task modules from all registered Django apps.
app.autodiscover_tasks()

# Celery Beat schedule for periodic tasks
app.conf.beat_schedule = {
    'advance-workflow-cases': {
        'task': 'apps.workflows.tasks.advance_workflow_cases',
        'schedule': 60.0,  # Run every minute
    },
    'check-sla-violations': {
        'task': 'apps.cases.tasks.check_sla_violations',
        'schedule': 300.0,  # Run every 5 minutes
    },
    'send-daily-reports': {
        'task': 'apps.reports.tasks.send_daily_reports',
        'schedule': {
            'hour': 8,
            'minute': 0,
        },
    },
    'cleanup-old-files': {
        'task': 'apps.files.tasks.cleanup_old_files',
        'schedule': {
            'hour': 2,
            'minute': 0,
        },
    },
}

# app.conf.timezone = settings.TIME_ZONE  # Commented out to avoid circular import

@app.task(bind=True)
def debug_task(self):
    print(f'Request: {self.request!r}')
