#!/usr/bin/env python
"""Debug script to check Django settings."""
import os
import sys

# Add the project directory to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# Set the Django settings module
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'dentflow_project.settings')

try:
    import django
    django.setup()
    
    from django.conf import settings
    
    print("=== Django Settings Debug ===")
    print(f"DEBUG: {settings.DEBUG}")
    print(f"ALLOWED_HOSTS: {settings.ALLOWED_HOSTS}")
    print(f"SECRET_KEY: {settings.SECRET_KEY[:20]}...")
    print(f"INSTALLED_APPS: {len(settings.INSTALLED_APPS)} apps")
    for app in settings.INSTALLED_APPS:
        print(f"  - {app}")
    
except Exception as e:
    print(f"Error loading settings: {e}")
    import traceback
    traceback.print_exc()
