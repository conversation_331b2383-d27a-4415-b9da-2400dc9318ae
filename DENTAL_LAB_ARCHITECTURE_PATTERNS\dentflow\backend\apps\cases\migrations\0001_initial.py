# Generated by Django 4.2.7 on 2025-07-25 19:26

from django.db import migrations, models
import django.db.models.deletion
import uuid


class Migration(migrations.Migration):

    initial = True

    dependencies = []

    operations = [
        migrations.CreateModel(
            name="Tenant",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4, primary_key=True, serialize=False
                    ),
                ),
                ("name", models.CharField(max_length=255)),
                ("subdomain", models.CharField(max_length=100, unique=True)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("is_active", models.BooleanField(default=True)),
            ],
        ),
        migrations.CreateModel(
            name="Clinic",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4, primary_key=True, serialize=False
                    ),
                ),
                ("name", models.CharField(max_length=255)),
                ("email", models.EmailField(max_length=254)),
                ("phone", models.CharField(max_length=20)),
                ("address", models.TextField()),
                (
                    "tenant",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE, to="cases.tenant"
                    ),
                ),
            ],
            options={
                "unique_together": {("tenant", "name")},
            },
        ),
        migrations.CreateModel(
            name="Case",
            fields=[
                (
                    "id",
                    models.CharField(max_length=50, primary_key=True, serialize=False),
                ),
                ("patient_name", models.CharField(max_length=255)),
                ("tooth_number", models.CharField(max_length=5)),
                ("service_type", models.CharField(max_length=100)),
                ("priority", models.CharField(default="normal", max_length=20)),
                ("status", models.CharField(default="received", max_length=20)),
                ("current_stage_index", models.IntegerField(default=0)),
                ("workflow_stages", models.JSONField(default=list)),
                (
                    "assigned_technician_id",
                    models.CharField(blank=True, max_length=50, null=True),
                ),
                ("due_date", models.DateTimeField(blank=True, null=True)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                ("deleted_at", models.DateTimeField(blank=True, null=True)),
                ("notes", models.JSONField(default=list)),
                ("files", models.JSONField(default=list)),
                (
                    "clinic",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE, to="cases.clinic"
                    ),
                ),
                (
                    "tenant",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE, to="cases.tenant"
                    ),
                ),
            ],
        ),
        migrations.CreateModel(
            name="CaseEvent",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4, primary_key=True, serialize=False
                    ),
                ),
                ("event_type", models.CharField(max_length=100)),
                ("event_data", models.JSONField()),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                (
                    "case",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="events",
                        to="cases.case",
                    ),
                ),
            ],
            options={
                "ordering": ["created_at"],
                "indexes": [
                    models.Index(
                        fields=["case", "created_at"],
                        name="cases_casee_case_id_354496_idx",
                    ),
                    models.Index(
                        fields=["event_type", "created_at"],
                        name="cases_casee_event_t_4431ae_idx",
                    ),
                ],
            },
        ),
        migrations.AddIndex(
            model_name="case",
            index=models.Index(
                fields=["tenant", "status", "created_at"],
                name="cases_case_tenant__e457b7_idx",
            ),
        ),
        migrations.AddIndex(
            model_name="case",
            index=models.Index(
                fields=["tenant", "due_date"], name="cases_case_tenant__7f9c9a_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="case",
            index=models.Index(
                fields=["clinic", "status"], name="cases_case_clinic__b2aa04_idx"
            ),
        ),
    ]
