# Generated by Django 4.2.7 on 2025-07-26 01:51

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion
import uuid


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ("tenants", "0001_initial"),
        ("cases", "0002_alter_case_tenant_alter_clinic_tenant_delete_tenant"),
    ]

    operations = [
        migrations.CreateModel(
            name="NotificationTemplate",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4, primary_key=True, serialize=False
                    ),
                ),
                ("name", models.CharField(max_length=255)),
                (
                    "notification_type",
                    models.CharField(
                        choices=[
                            ("case_created", "Case Created"),
                            ("case_updated", "Case Updated"),
                            ("case_completed", "Case Completed"),
                            ("case_overdue", "Case Overdue"),
                            ("payment_received", "Payment Received"),
                            ("payment_overdue", "Payment Overdue"),
                            ("appointment_reminder", "Appointment Reminder"),
                            ("quality_check_failed", "Quality Check Failed"),
                            ("system_maintenance", "System Maintenance"),
                        ],
                        max_length=50,
                    ),
                ),
                (
                    "channel",
                    models.Char<PERSON>ield(
                        choices=[
                            ("email", "Email"),
                            ("sms", "SMS"),
                            ("push", "Push Notification"),
                            ("in_app", "In-App Notification"),
                            ("slack", "Slack"),
                        ],
                        max_length=20,
                    ),
                ),
                ("subject", models.CharField(blank=True, max_length=255)),
                ("body_template", models.TextField()),
                ("html_template", models.TextField(blank=True)),
                ("variables", models.JSONField(default=list)),
                ("is_active", models.BooleanField(default=True)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                (
                    "tenant",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="notification_templates",
                        to="tenants.tenant",
                    ),
                ),
            ],
            options={
                "indexes": [
                    models.Index(
                        fields=["tenant", "notification_type", "channel"],
                        name="notificatio_tenant__a47fdc_idx",
                    ),
                    models.Index(
                        fields=["is_active"], name="notificatio_is_acti_b2a982_idx"
                    ),
                ],
                "unique_together": {("tenant", "notification_type", "channel")},
            },
        ),
        migrations.CreateModel(
            name="NotificationPreference",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4, primary_key=True, serialize=False
                    ),
                ),
                (
                    "notification_type",
                    models.CharField(
                        choices=[
                            ("case_created", "Case Created"),
                            ("case_updated", "Case Updated"),
                            ("case_completed", "Case Completed"),
                            ("case_overdue", "Case Overdue"),
                            ("payment_received", "Payment Received"),
                            ("payment_overdue", "Payment Overdue"),
                            ("appointment_reminder", "Appointment Reminder"),
                            ("quality_check_failed", "Quality Check Failed"),
                            ("system_maintenance", "System Maintenance"),
                        ],
                        max_length=50,
                    ),
                ),
                ("email_enabled", models.BooleanField(default=True)),
                ("sms_enabled", models.BooleanField(default=False)),
                ("push_enabled", models.BooleanField(default=True)),
                ("in_app_enabled", models.BooleanField(default=True)),
                ("quiet_hours", models.JSONField(blank=True, default=dict)),
                ("frequency_limit", models.PositiveIntegerField(default=0)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                (
                    "user",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="notification_preferences",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
            options={
                "indexes": [
                    models.Index(
                        fields=["user", "notification_type"],
                        name="notificatio_user_id_1eba11_idx",
                    )
                ],
                "unique_together": {("user", "notification_type")},
            },
        ),
        migrations.CreateModel(
            name="Notification",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4, primary_key=True, serialize=False
                    ),
                ),
                ("recipient_email", models.EmailField(blank=True, max_length=254)),
                ("title", models.CharField(max_length=255)),
                ("message", models.TextField()),
                (
                    "notification_type",
                    models.CharField(
                        choices=[
                            ("case_created", "Case Created"),
                            ("case_updated", "Case Updated"),
                            ("case_completed", "Case Completed"),
                            ("case_overdue", "Case Overdue"),
                            ("payment_received", "Payment Received"),
                            ("payment_overdue", "Payment Overdue"),
                            ("appointment_reminder", "Appointment Reminder"),
                            ("quality_check_failed", "Quality Check Failed"),
                            ("system_maintenance", "System Maintenance"),
                        ],
                        max_length=50,
                    ),
                ),
                (
                    "channel",
                    models.CharField(
                        choices=[
                            ("email", "Email"),
                            ("sms", "SMS"),
                            ("push", "Push Notification"),
                            ("in_app", "In-App Notification"),
                            ("slack", "Slack"),
                        ],
                        max_length=20,
                    ),
                ),
                (
                    "priority",
                    models.CharField(
                        choices=[
                            ("low", "Low"),
                            ("normal", "Normal"),
                            ("high", "High"),
                            ("urgent", "Urgent"),
                        ],
                        default="normal",
                        max_length=10,
                    ),
                ),
                ("scheduled_for", models.DateTimeField(blank=True, null=True)),
                (
                    "status",
                    models.CharField(
                        choices=[
                            ("pending", "Pending"),
                            ("sent", "Sent"),
                            ("delivered", "Delivered"),
                            ("failed", "Failed"),
                        ],
                        default="pending",
                        max_length=20,
                    ),
                ),
                ("is_read", models.BooleanField(default=False)),
                ("sent_at", models.DateTimeField(blank=True, null=True)),
                ("read_at", models.DateTimeField(blank=True, null=True)),
                ("delivery_attempts", models.PositiveIntegerField(default=0)),
                ("error_message", models.TextField(blank=True)),
                ("metadata", models.JSONField(blank=True, default=dict)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                (
                    "case",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        to="cases.case",
                    ),
                ),
                (
                    "recipient_user",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
            options={
                "ordering": ["-created_at"],
                "indexes": [
                    models.Index(
                        fields=["recipient_user", "is_read"],
                        name="notificatio_recipie_56404e_idx",
                    ),
                    models.Index(
                        fields=["status", "scheduled_for"],
                        name="notificatio_status_d8d933_idx",
                    ),
                    models.Index(
                        fields=["notification_type", "created_at"],
                        name="notificatio_notific_f2e0f7_idx",
                    ),
                    models.Index(
                        fields=["case"], name="notificatio_case_id_7833c4_idx"
                    ),
                ],
            },
        ),
    ]
