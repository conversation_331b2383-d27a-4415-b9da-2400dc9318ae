/**
 * DentFlow Dashboard Component - Enhanced Version
 * Comprehensive dashboard with advanced widgets, charts, and real-time data visualization
 */

import React, { useState, useEffect } from 'react';
import {
  Box,
  Grid,
  Card,
  CardContent,
  Typography,
  Avatar,
  Chip,
  LinearProgress,
  IconButton,
  Button,
  Alert,
  Paper,
  List,
  ListItem,
  ListItemText,
  ListItemAvatar,
  Divider,
  Badge,
  Tooltip,
  CircularProgress,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  useTheme,
  alpha,
} from '@mui/material';
import {
  TrendingUp,
  TrendingDown,
  Assignment,
  CheckCircle,
  Warning,
  Schedule,
  AttachMoney,
  People,
  Inventory,
  Analytics,
  Refresh,
  Notifications,
  CalendarToday,
  Build,
  LocalShipping,
  VerifiedUser,
} from '@mui/icons-material';
import { CasesService } from '../api/casesService';
import { Case } from '../api/types';
import { useAuth } from '../context/AuthContext';

interface DashboardStats {
  active_cases: number;
  completed_today: number;
  pending_qc: number;
  revenue_today: number;
  overdue_cases: number;
  total_cases_this_month: number;
  revenue_this_month: number;
  average_completion_time: number;
  customer_satisfaction: number;
  inventory_alerts: number;
  technician_utilization: number;
  pending_invoices: number;
}

interface ActivityItem {
  id: string;
  type: 'case_created' | 'case_completed' | 'payment_received' | 'inventory_low' | 'qc_failed';
  title: string;
  description: string;
  timestamp: string;
  user?: string;
  priority?: 'low' | 'medium' | 'high';
}

interface TechnicianStats {
  id: string;
  name: string;
  active_cases: number;
  completed_today: number;
  efficiency_score: number;
  specialization: string;
}

interface InventoryAlert {
  id: string;
  material_name: string;
  current_stock: number;
  minimum_stock: number;
  unit: string;
  urgency: 'low' | 'medium' | 'high';
}

const Dashboard: React.FC = () => {
  const { user } = useAuth();
  const theme = useTheme();

  const [stats, setStats] = useState<DashboardStats>({
    active_cases: 0,
    completed_today: 0,
    pending_qc: 0,
    revenue_today: 0,
    overdue_cases: 0,
    total_cases_this_month: 0,
    revenue_this_month: 0,
    average_completion_time: 0,
    customer_satisfaction: 0,
    inventory_alerts: 0,
    technician_utilization: 0,
    pending_invoices: 0,
  });

  const [recentCases, setRecentCases] = useState<Case[]>([]);
  const [recentActivities, setRecentActivities] = useState<ActivityItem[]>([]);
  const [technicianStats, setTechnicianStats] = useState<TechnicianStats[]>([]);
  const [inventoryAlerts, setInventoryAlerts] = useState<InventoryAlert[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string>('');
  const [backendStatus, setBackendStatus] = useState<{available: boolean; mode: string}>({
    available: false,
    mode: 'mock'
  });

  useEffect(() => {
    loadDashboardData();
  }, []);

  const loadDashboardData = async () => {
    try {
      setLoading(true);
      setError('');

      // Check backend health first
      await CasesService.checkBackendHealth();

      const [dashboardStats, cases] = await Promise.all([
        CasesService.getDashboardStats(),
        CasesService.getCases(),
      ]);

      // Enhanced stats with mock data for demo
      const enhancedStats = {
        ...dashboardStats,
        total_cases_this_month: dashboardStats.active_cases + dashboardStats.completed_today + 45,
        revenue_this_month: dashboardStats.revenue_today * 22,
        average_completion_time: 3.2,
        customer_satisfaction: 4.7,
        inventory_alerts: 3,
        technician_utilization: 87,
        pending_invoices: 12,
      };

      setStats(enhancedStats);
      setRecentCases(cases.slice(0, 8)); // Show more cases
      setBackendStatus(CasesService.getBackendStatus());

      // Load mock data for enhanced features
      loadMockEnhancedData();

    } catch (err: any) {
      console.error('Error loading dashboard:', err);
      setError('Failed to load some dashboard data');
      setBackendStatus(CasesService.getBackendStatus());
      loadMockEnhancedData(); // Load mock data even on error
    } finally {
      setLoading(false);
    }
  };

  const loadMockEnhancedData = () => {
    // Mock recent activities
    setRecentActivities([
      {
        id: '1',
        type: 'case_completed',
        title: 'Crown Case #1234 Completed',
        description: 'Zirconia crown for patient John Doe completed by Dr. Smith',
        timestamp: new Date(Date.now() - 1000 * 60 * 15).toISOString(),
        user: 'Dr. Smith',
        priority: 'medium'
      },
      {
        id: '2',
        type: 'inventory_low',
        title: 'Low Inventory Alert',
        description: 'Zirconia blocks running low (5 units remaining)',
        timestamp: new Date(Date.now() - 1000 * 60 * 45).toISOString(),
        priority: 'high'
      },
      {
        id: '3',
        type: 'payment_received',
        title: 'Payment Received',
        description: '$2,450 payment received from Dental Clinic ABC',
        timestamp: new Date(Date.now() - 1000 * 60 * 120).toISOString(),
        priority: 'low'
      },
      {
        id: '4',
        type: 'case_created',
        title: 'New Bridge Case',
        description: '3-unit bridge case received from Dr. Johnson',
        timestamp: new Date(Date.now() - 1000 * 60 * 180).toISOString(),
        user: 'Dr. Johnson',
        priority: 'medium'
      }
    ]);

    // Mock technician stats
    setTechnicianStats([
      {
        id: '1',
        name: 'Mike Rodriguez',
        active_cases: 8,
        completed_today: 3,
        efficiency_score: 94,
        specialization: 'Crowns & Bridges'
      },
      {
        id: '2',
        name: 'Sarah Chen',
        active_cases: 6,
        completed_today: 4,
        efficiency_score: 97,
        specialization: 'Implants'
      },
      {
        id: '3',
        name: 'David Kim',
        active_cases: 5,
        completed_today: 2,
        efficiency_score: 89,
        specialization: 'Orthodontics'
      }
    ]);

    // Mock inventory alerts
    setInventoryAlerts([
      {
        id: '1',
        material_name: 'Zirconia Blocks',
        current_stock: 5,
        minimum_stock: 10,
        unit: 'blocks',
        urgency: 'high'
      },
      {
        id: '2',
        material_name: 'Titanium Abutments',
        current_stock: 8,
        minimum_stock: 15,
        unit: 'pieces',
        urgency: 'medium'
      },
      {
        id: '3',
        material_name: 'Ceramic Stain',
        current_stock: 12,
        minimum_stock: 20,
        unit: 'ml',
        urgency: 'low'
      }
    ]);
  };
  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
    }).format(amount);
  };

  const getStatusColor = (status: string) => {
    const colors: Record<string, string> = {
      'received': '#2196f3',
      'design': '#ff9800',
      'milling': '#9c27b0',
      'sintering': '#e91e63',
      'qc': '#ff5722',
      'shipped': '#4caf50',
      'delivered': '#8bc34a',
      'cancelled': '#757575',
    };
    return colors[status] || '#666';
  };

  const getPriorityColor = (priority: string) => {
    const colors: Record<string, string> = {
      'low': '#4caf50',
      'normal': '#2196f3',
      'urgent': '#ff9800',
      'stat': '#f44336',
    };
    return colors[priority] || '#666';
  };

  if (loading) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" minHeight="60vh">
        <Box textAlign="center">
          <CircularProgress size={60} />
          <Typography variant="h6" sx={{ mt: 2, color: 'text.secondary' }}>
            Loading dashboard...
          </Typography>
        </Box>
      </Box>
    );
  }

  if (error) {
    return (
      <Box p={3}>
        <Alert
          severity="error"
          action={
            <Button color="inherit" size="small" onClick={loadDashboardData}>
              Retry
            </Button>
          }
        >
          {error}
        </Alert>
      </Box>
    );
  }
  return (
    <Box sx={{ p: 3, backgroundColor: '#f5f5f5', minHeight: '100vh' }}>
      {/* Header */}
      <Box sx={{ mb: 4 }}>
        <Box display="flex" justifyContent="space-between" alignItems="center" mb={2}>
          <Box>
            <Typography variant="h3" component="h1" sx={{
              color: 'primary.main',
              fontWeight: 'bold',
              display: 'flex',
              alignItems: 'center',
              gap: 1
            }}>
              🦷 DentFlow Dashboard
            </Typography>
            <Typography variant="h6" color="text.secondary" sx={{ mt: 1 }}>
              Welcome back, {user?.first_name} {user?.last_name}
              {user?.role && (
                <Chip
                  label={user.role}
                  size="small"
                  color="primary"
                  variant="outlined"
                  sx={{ ml: 1 }}
                />
              )}
            </Typography>
          </Box>
          <Box display="flex" alignItems="center" gap={2}>
            <Tooltip title="Refresh Dashboard">
              <IconButton onClick={loadDashboardData} color="primary">
                <Refresh />
              </IconButton>
            </Tooltip>
            <Chip
              icon={backendStatus.available ? <CheckCircle /> : <Warning />}
              label={backendStatus.available ? 'Live Data' : 'Demo Data'}
              color={backendStatus.available ? 'success' : 'warning'}
              variant="filled"
            />
          </Box>
        </Box>
      </Box>

      {/* Enhanced KPI Cards */}
      <Grid container spacing={3} sx={{ mb: 4 }}>
        {/* Active Cases */}
        <Grid item xs={12} sm={6} md={3}>
          <Card sx={{ height: '100%', background: 'linear-gradient(135deg, #1976d2 0%, #42a5f5 100%)' }}>
            <CardContent>
              <Box display="flex" alignItems="center" justifyContent="space-between">
                <Box>
                  <Typography variant="h4" sx={{ color: 'white', fontWeight: 'bold' }}>
                    {stats.active_cases}
                  </Typography>
                  <Typography variant="body2" sx={{ color: 'rgba(255,255,255,0.8)' }}>
                    Active Cases
                  </Typography>
                </Box>
                <Avatar sx={{ bgcolor: 'rgba(255,255,255,0.2)', width: 56, height: 56 }}>
                  <Assignment sx={{ color: 'white', fontSize: 30 }} />
                </Avatar>
              </Box>
              <Box mt={2}>
                <LinearProgress
                  variant="determinate"
                  value={75}
                  sx={{
                    backgroundColor: 'rgba(255,255,255,0.2)',
                    '& .MuiLinearProgress-bar': { backgroundColor: 'white' }
                  }}
                />
                <Typography variant="caption" sx={{ color: 'rgba(255,255,255,0.8)', mt: 1 }}>
                  75% capacity utilized
                </Typography>
              </Box>
            </CardContent>
          </Card>
        </Grid>

        {/* Completed Today */}
        <Grid item xs={12} sm={6} md={3}>
          <Card sx={{ height: '100%', background: 'linear-gradient(135deg, #4caf50 0%, #81c784 100%)' }}>
            <CardContent>
              <Box display="flex" alignItems="center" justifyContent="space-between">
                <Box>
                  <Typography variant="h4" sx={{ color: 'white', fontWeight: 'bold' }}>
                    {stats.completed_today}
                  </Typography>
                  <Typography variant="body2" sx={{ color: 'rgba(255,255,255,0.8)' }}>
                    Completed Today
                  </Typography>
                </Box>
                <Avatar sx={{ bgcolor: 'rgba(255,255,255,0.2)', width: 56, height: 56 }}>
                  <CheckCircle sx={{ color: 'white', fontSize: 30 }} />
                </Avatar>
              </Box>
              <Box mt={2} display="flex" alignItems="center">
                <TrendingUp sx={{ color: 'white', mr: 1 }} />
                <Typography variant="body2" sx={{ color: 'white' }}>
                  +12% from yesterday
                </Typography>
              </Box>
            </CardContent>
          </Card>
        </Grid>

        {/* Revenue Today */}
        <Grid item xs={12} sm={6} md={3}>
          <Card sx={{ height: '100%', background: 'linear-gradient(135deg, #ff9800 0%, #ffb74d 100%)' }}>
            <CardContent>
              <Box display="flex" alignItems="center" justifyContent="space-between">
                <Box>
                  <Typography variant="h4" sx={{ color: 'white', fontWeight: 'bold' }}>
                    {formatCurrency(stats.revenue_today)}
                  </Typography>
                  <Typography variant="body2" sx={{ color: 'rgba(255,255,255,0.8)' }}>
                    Revenue Today
                  </Typography>
                </Box>
                <Avatar sx={{ bgcolor: 'rgba(255,255,255,0.2)', width: 56, height: 56 }}>
                  <AttachMoney sx={{ color: 'white', fontSize: 30 }} />
                </Avatar>
              </Box>
              <Box mt={2} display="flex" alignItems="center">
                <TrendingUp sx={{ color: 'white', mr: 1 }} />
                <Typography variant="body2" sx={{ color: 'white' }}>
                  Target: {formatCurrency(stats.revenue_today * 1.2)}
                </Typography>
              </Box>
            </CardContent>
          </Card>
        </Grid>

        {/* Pending QC */}
        <Grid item xs={12} sm={6} md={3}>
          <Card sx={{
            height: '100%',
            background: stats.pending_qc > 5 ?
              'linear-gradient(135deg, #f44336 0%, #ef5350 100%)' :
              'linear-gradient(135deg, #9c27b0 0%, #ba68c8 100%)'
          }}>
            <CardContent>
              <Box display="flex" alignItems="center" justifyContent="space-between">
                <Box>
                  <Typography variant="h4" sx={{ color: 'white', fontWeight: 'bold' }}>
                    {stats.pending_qc}
                  </Typography>
                  <Typography variant="body2" sx={{ color: 'rgba(255,255,255,0.8)' }}>
                    Pending QC
                  </Typography>
                </Box>
                <Avatar sx={{ bgcolor: 'rgba(255,255,255,0.2)', width: 56, height: 56 }}>
                  <VerifiedUser sx={{ color: 'white', fontSize: 30 }} />
                </Avatar>
              </Box>
              <Box mt={2}>
                {stats.pending_qc > 5 && (
                  <Chip
                    label="High Priority"
                    size="small"
                    sx={{
                      backgroundColor: 'rgba(255,255,255,0.2)',
                      color: 'white',
                      fontWeight: 'bold'
                    }}
                  />
                )}
              </Box>
            </CardContent>
          </Card>
        </Grid>

        {/* Monthly Revenue */}
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box display="flex" alignItems="center" justifyContent="space-between">
                <Box>
                  <Typography variant="h5" sx={{ fontWeight: 'bold', color: 'primary.main' }}>
                    {formatCurrency(stats.revenue_this_month)}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    Monthly Revenue
                  </Typography>
                </Box>
                <Analytics color="primary" sx={{ fontSize: 40 }} />
              </Box>
              <Box mt={1}>
                <Typography variant="caption" color="success.main">
                  +18% vs last month
                </Typography>
              </Box>
            </CardContent>
          </Card>
        </Grid>

        {/* Customer Satisfaction */}
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box display="flex" alignItems="center" justifyContent="space-between">
                <Box>
                  <Typography variant="h5" sx={{ fontWeight: 'bold', color: 'success.main' }}>
                    {stats.customer_satisfaction}/5.0
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    Customer Rating
                  </Typography>
                </Box>
                <People color="success" sx={{ fontSize: 40 }} />
              </Box>
              <Box mt={1}>
                <LinearProgress
                  variant="determinate"
                  value={(stats.customer_satisfaction / 5) * 100}
                  color="success"
                />
              </Box>
            </CardContent>
          </Card>
        </Grid>

        {/* Inventory Alerts */}
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box display="flex" alignItems="center" justifyContent="space-between">
                <Box>
                  <Typography variant="h5" sx={{ fontWeight: 'bold', color: 'warning.main' }}>
                    {stats.inventory_alerts}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    Inventory Alerts
                  </Typography>
                </Box>
                <Badge badgeContent={stats.inventory_alerts} color="error">
                  <Inventory color="warning" sx={{ fontSize: 40 }} />
                </Badge>
              </Box>
              <Box mt={1}>
                <Typography variant="caption" color="warning.main">
                  Materials need restocking
                </Typography>
              </Box>
            </CardContent>
          </Card>
        </Grid>

        {/* Technician Utilization */}
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box display="flex" alignItems="center" justifyContent="space-between">
                <Box>
                  <Typography variant="h5" sx={{ fontWeight: 'bold', color: 'info.main' }}>
                    {stats.technician_utilization}%
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    Team Utilization
                  </Typography>
                </Box>
                <Build color="info" sx={{ fontSize: 40 }} />
              </Box>
              <Box mt={1}>
                <LinearProgress
                  variant="determinate"
                  value={stats.technician_utilization}
                  color="info"
                />
              </Box>
            </CardContent>
          </Card>
        </Grid>
      </Grid>
      {/* Main Content Grid */}
      <Grid container spacing={3}>
        {/* Recent Cases */}
        <Grid item xs={12} lg={8}>
          <Card>
            <CardContent>
              <Box display="flex" justifyContent="space-between" alignItems="center" mb={2}>
                <Typography variant="h6" sx={{ fontWeight: 'bold' }}>
                  Recent Cases
                </Typography>
                <Button variant="outlined" size="small">
                  View All
                </Button>
              </Box>

              {recentCases.length === 0 ? (
                <Box textAlign="center" py={4}>
                  <Typography color="text.secondary">
                    No cases found. Create your first case to get started!
                  </Typography>
                </Box>
              ) : (
                <TableContainer>
                  <Table>
                    <TableHead>
                      <TableRow>
                        <TableCell>Case ID</TableCell>
                        <TableCell>Patient</TableCell>
                        <TableCell>Type</TableCell>
                        <TableCell>Status</TableCell>
                        <TableCell>Priority</TableCell>
                        <TableCell>Due Date</TableCell>
                      </TableRow>
                    </TableHead>
                    <TableBody>
                      {recentCases.map((case_) => (
                        <TableRow key={case_.id} hover>
                          <TableCell>
                            <Typography variant="body2" fontWeight="bold">
                              #{case_.id}
                            </Typography>
                          </TableCell>
                          <TableCell>{case_.patient_name}</TableCell>
                          <TableCell>
                            <Chip
                              label={case_.service_type}
                              size="small"
                              variant="outlined"
                            />
                          </TableCell>
                          <TableCell>
                            <Chip
                              label={case_.current_stage}
                              size="small"
                              sx={{
                                backgroundColor: alpha(getStatusColor(case_.status), 0.1),
                                color: getStatusColor(case_.status),
                                border: `1px solid ${getStatusColor(case_.status)}`
                              }}
                            />
                          </TableCell>
                          <TableCell>
                            <Chip
                              label={case_.priority}
                              size="small"
                              color={
                                case_.priority === 'stat' ? 'error' :
                                case_.priority === 'urgent' ? 'warning' :
                                case_.priority === 'normal' ? 'primary' : 'default'
                              }
                            />
                          </TableCell>
                          <TableCell>
                            <Typography
                              variant="body2"
                              color={case_.is_overdue ? 'error.main' : 'text.primary'}
                            >
                              {case_.due_date ? (
                                case_.days_until_due !== null && case_.days_until_due !== undefined ? (
                                  case_.days_until_due >= 0 ?
                                    `${case_.days_until_due} days` :
                                    `${Math.abs(case_.days_until_due)} days overdue`
                                ) : 'Today'
                              ) : 'Not set'}
                            </Typography>
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </TableContainer>
              )}
            </CardContent>
          </Card>
        </Grid>

        {/* Right Sidebar */}
        <Grid item xs={12} lg={4}>
          {/* Recent Activities */}
          <Card sx={{ mb: 3 }}>
            <CardContent>
              <Box display="flex" justifyContent="space-between" alignItems="center" mb={2}>
                <Typography variant="h6" sx={{ fontWeight: 'bold' }}>
                  Recent Activities
                </Typography>
                <Badge badgeContent={recentActivities.length} color="primary">
                  <Notifications />
                </Badge>
              </Box>

              <List>
                {recentActivities.map((activity, index) => (
                  <React.Fragment key={activity.id}>
                    <ListItem alignItems="flex-start" sx={{ px: 0 }}>
                      <ListItemAvatar>
                        <Avatar sx={{
                          bgcolor:
                            activity.type === 'case_completed' ? 'success.main' :
                            activity.type === 'inventory_low' ? 'warning.main' :
                            activity.type === 'payment_received' ? 'info.main' :
                            'primary.main',
                          width: 32,
                          height: 32
                        }}>
                          {activity.type === 'case_completed' ? <CheckCircle sx={{ fontSize: 16 }} /> :
                           activity.type === 'inventory_low' ? <Warning sx={{ fontSize: 16 }} /> :
                           activity.type === 'payment_received' ? <AttachMoney sx={{ fontSize: 16 }} /> :
                           <Assignment sx={{ fontSize: 16 }} />}
                        </Avatar>
                      </ListItemAvatar>
                      <ListItemText
                        primary={
                          <Typography variant="body2" sx={{ fontWeight: 'bold' }}>
                            {activity.title}
                          </Typography>
                        }
                        secondary={
                          <Box>
                            <Typography variant="caption" color="text.secondary">
                              {activity.description}
                            </Typography>
                            <Typography variant="caption" display="block" color="text.secondary">
                              {new Date(activity.timestamp).toLocaleString()}
                            </Typography>
                          </Box>
                        }
                      />
                      {activity.priority === 'high' && (
                        <Chip
                          label="High"
                          size="small"
                          color="error"
                          sx={{ ml: 1 }}
                        />
                      )}
                    </ListItem>
                    {index < recentActivities.length - 1 && <Divider />}
                  </React.Fragment>
                ))}
              </List>
            </CardContent>
          </Card>

          {/* Inventory Alerts */}
          <Card sx={{ mb: 3 }}>
            <CardContent>
              <Typography variant="h6" sx={{ fontWeight: 'bold', mb: 2 }}>
                Inventory Alerts
              </Typography>

              <List>
                {inventoryAlerts.map((alert, index) => (
                  <React.Fragment key={alert.id}>
                    <ListItem sx={{ px: 0 }}>
                      <ListItemAvatar>
                        <Avatar sx={{
                          bgcolor:
                            alert.urgency === 'high' ? 'error.main' :
                            alert.urgency === 'medium' ? 'warning.main' :
                            'info.main',
                          width: 32,
                          height: 32
                        }}>
                          <Inventory sx={{ fontSize: 16 }} />
                        </Avatar>
                      </ListItemAvatar>
                      <ListItemText
                        primary={alert.material_name}
                        secondary={
                          <Box>
                            <Typography variant="caption" color="text.secondary">
                              Current: {alert.current_stock} {alert.unit}
                            </Typography>
                            <Typography variant="caption" display="block" color="text.secondary">
                              Minimum: {alert.minimum_stock} {alert.unit}
                            </Typography>
                          </Box>
                        }
                      />
                      <Chip
                        label={alert.urgency}
                        size="small"
                        color={
                          alert.urgency === 'high' ? 'error' :
                          alert.urgency === 'medium' ? 'warning' : 'info'
                        }
                      />
                    </ListItem>
                    {index < inventoryAlerts.length - 1 && <Divider />}
                  </React.Fragment>
                ))}
              </List>
            </CardContent>
          </Card>

          {/* Technician Performance */}
          <Card>
            <CardContent>
              <Typography variant="h6" sx={{ fontWeight: 'bold', mb: 2 }}>
                Team Performance
              </Typography>

              <List>
                {technicianStats.map((tech, index) => (
                  <React.Fragment key={tech.id}>
                    <ListItem sx={{ px: 0 }}>
                      <ListItemAvatar>
                        <Avatar sx={{ bgcolor: 'primary.main' }}>
                          {tech.name.split(' ').map(n => n[0]).join('')}
                        </Avatar>
                      </ListItemAvatar>
                      <ListItemText
                        primary={tech.name}
                        secondary={
                          <Box>
                            <Typography variant="caption" color="text.secondary">
                              {tech.specialization}
                            </Typography>
                            <Box display="flex" alignItems="center" mt={0.5}>
                              <Typography variant="caption" sx={{ mr: 1 }}>
                                Efficiency: {tech.efficiency_score}%
                              </Typography>
                              <LinearProgress
                                variant="determinate"
                                value={tech.efficiency_score}
                                sx={{ flexGrow: 1, height: 4 }}
                                color={
                                  tech.efficiency_score >= 95 ? 'success' :
                                  tech.efficiency_score >= 85 ? 'primary' : 'warning'
                                }
                              />
                            </Box>
                          </Box>
                        }
                      />
                      <Box textAlign="center">
                        <Typography variant="caption" color="text.secondary">
                          Active: {tech.active_cases}
                        </Typography>
                        <Typography variant="caption" display="block" color="text.secondary">
                          Done: {tech.completed_today}
                        </Typography>
                      </Box>
                    </ListItem>
                    {index < technicianStats.length - 1 && <Divider />}
                  </React.Fragment>
                ))}
              </List>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* System Status Footer */}
      <Box sx={{ mt: 4 }}>
        <Alert
          severity={backendStatus.available ? 'success' : 'warning'}
          action={
            !backendStatus.available && (
              <Button color="inherit" size="small" onClick={loadDashboardData}>
                🔄 Reconnect
              </Button>
            )
          }
        >
          <Typography variant="body2" sx={{ fontWeight: 'bold' }}>
            Backend Status: {backendStatus.mode === 'live' ? 'Connected' : 'Demo Mode'}
          </Typography>
          <Typography variant="caption">
            {backendStatus.available
              ? 'React frontend successfully connected to Django REST API backend. Real-time data loading from DentFlow case management system.'
              : 'Backend unavailable. Using demonstration data. All features remain functional for testing.'
            }
          </Typography>
        </Alert>
      </Box>
    </Box>
  );
};

export default Dashboard;