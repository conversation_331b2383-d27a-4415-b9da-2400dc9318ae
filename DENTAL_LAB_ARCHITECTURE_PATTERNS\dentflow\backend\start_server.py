#!/usr/bin/env python3
"""
Start Django server
"""

import os
import sys
import django
from django.core.management import execute_from_command_line

# Set Django settings module
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'dentflow_project.settings')

def main():
    print("🚀 Starting DentFlow Django Server...")
    
    try:
        # Setup Django
        django.setup()
        
        from django.conf import settings
        print(f"✅ Django configured - DEBUG: {settings.DEBUG}")
        print(f"✅ ALLOWED_HOSTS: {settings.ALLOWED_HOSTS}")
        
        # Run migrations first
        print("📝 Running migrations...")
        execute_from_command_line(['manage.py', 'migrate', '--run-syncdb'])
        
        # Create superuser if needed
        print("👤 Ensuring superuser exists...")
        from django.contrib.auth.models import User
        if not User.objects.filter(username='admin').exists():
            User.objects.create_superuser('admin', '<EMAIL>', 'admin123')
            print("✅ Superuser 'admin' created")
        else:
            print("✅ Superuser 'admin' already exists")
        
        # Start server
        print("🌐 Starting server on http://127.0.0.1:9000")
        execute_from_command_line(['manage.py', 'runserver', '127.0.0.1:9000'])
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)

if __name__ == "__main__":
    main()
