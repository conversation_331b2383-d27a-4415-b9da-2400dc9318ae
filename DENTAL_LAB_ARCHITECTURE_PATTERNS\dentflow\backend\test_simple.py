#!/usr/bin/env python3
"""
Simple Django test
"""

import os
import sys

# Set Django settings module
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'dentflow_project.settings')

try:
    import django
    print(f"Django version: {django.get_version()}")
    
    django.setup()
    print("Django setup successful")
    
    from django.conf import settings
    print(f"DEBUG: {settings.DEBUG}")
    print(f"ALLOWED_HOSTS: {settings.ALLOWED_HOSTS}")
    print(f"SECRET_KEY: {settings.SECRET_KEY[:20]}...")
    
    # Test database connection
    from django.db import connection
    cursor = connection.cursor()
    cursor.execute("SELECT 1")
    print("Database connection successful")
    
    print("✅ All tests passed!")
    
except Exception as e:
    print(f"❌ Error: {e}")
    import traceback
    traceback.print_exc()
    sys.exit(1)
