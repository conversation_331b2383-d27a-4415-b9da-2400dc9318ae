/**
 * API Service Layer for DentFlow
 * Centralized API communication with error handling and caching
 */

import axios, { AxiosInstance, AxiosRequestConfig, AxiosResponse } from 'axios';

// API Configuration
const API_BASE_URL = process.env.REACT_APP_API_URL || 'http://localhost:8001/api';
const API_TIMEOUT = 30000; // 30 seconds

// Create axios instance with default configuration
const apiClient: AxiosInstance = axios.create({
  baseURL: API_BASE_URL,
  timeout: API_TIMEOUT,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Request interceptor for authentication
apiClient.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem('authToken');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => Promise.reject(error)
);

// Response interceptor for error handling
apiClient.interceptors.response.use(
  (response: AxiosResponse) => response,
  (error) => {
    if (error.response?.status === 401) {
      // Handle unauthorized - redirect to login
      localStorage.removeItem('authToken');
      window.location.href = '/login';
    }
    return Promise.reject(error);
  }
);

// Base API service class
class BaseApiService {
  protected async get<T>(url: string, config?: AxiosRequestConfig): Promise<T> {
    const response = await apiClient.get<T>(url, config);
    return response.data;
  }

  protected async post<T>(url: string, data?: any, config?: AxiosRequestConfig): Promise<T> {
    const response = await apiClient.post<T>(url, data, config);
    return response.data;
  }

  protected async put<T>(url: string, data?: any, config?: AxiosRequestConfig): Promise<T> {
    const response = await apiClient.put<T>(url, data, config);
    return response.data;
  }

  protected async patch<T>(url: string, data?: any, config?: AxiosRequestConfig): Promise<T> {
    const response = await apiClient.patch<T>(url, data, config);
    return response.data;
  }

  protected async delete<T>(url: string, config?: AxiosRequestConfig): Promise<T> {
    const response = await apiClient.delete<T>(url, config);
    return response.data;
  }
}

// Authentication Service
export interface LoginCredentials {
  email: string;
  password: string;
}

export interface AuthUser {
  id: string;
  email: string;
  firstName: string;
  lastName: string;
  role: string;
  permissions: string[];
}

export interface AuthResponse {
  user: AuthUser;
  token: string;
  refreshToken: string;
}

class AuthService extends BaseApiService {
  async login(credentials: LoginCredentials): Promise<AuthResponse> {
    return this.post<AuthResponse>('/auth/login', credentials);
  }

  async logout(): Promise<void> {
    await this.post('/auth/logout');
    localStorage.removeItem('authToken');
  }

  async refreshToken(refreshToken: string): Promise<AuthResponse> {
    return this.post<AuthResponse>('/auth/refresh', { refreshToken });
  }

  async getCurrentUser(): Promise<AuthUser> {
    return this.get<AuthUser>('/auth/me');
  }

  async resetPassword(email: string): Promise<void> {
    return this.post('/auth/reset-password', { email });
  }

  isAuthenticated(): boolean {
    return !!localStorage.getItem('authToken');
  }
}

// Cases Service - Updated to match backend structure
export interface Clinic {
  id: string;
  name: string;
  email: string;
  phone: string;
  address: string;
}

export interface WorkflowStage {
  name: string;
  department: string;
  estimated_duration_minutes: number;
  auto_assign: boolean;
  requires_quality_check: boolean;
  machine_required?: string;
}

export interface Case {
  id: string;
  clinic: Clinic;
  patient_name: string;
  tooth_number: string;
  service_type: string;
  priority: 'low' | 'normal' | 'urgent' | 'stat';
  status: string;
  current_stage_index: number;
  workflow_stages: WorkflowStage[];
  assigned_technician_id?: string;
  due_date?: string;
  created_at: string;
  updated_at: string;
  notes: string[];
  files: string[];
  days_until_due?: number;
  is_overdue: boolean;
  current_stage?: WorkflowStage;
}

export interface CreateCaseRequest {
  clinic_id: string;
  patient_name: string;
  tooth_number: string;
  service_type: string; // Allow any string for flexibility
  priority?: string; // Allow any string for flexibility
  notes?: string;
  files?: string[];
}

export interface CasesListResponse {
  results: Case[];
  count: number;
  next?: string;
  previous?: string;
}

class CasesService extends BaseApiService {
  async getCases(params?: {
    page?: number;
    limit?: number;
    status?: string;
    priority?: string;
    search?: string;
  }): Promise<CasesListResponse> {
    return this.get<CasesListResponse>('/cases/', { params });
  }

  async getCaseById(id: string): Promise<Case> {
    return this.get<Case>(`/cases/${id}/`);
  }

  async createCase(data: CreateCaseRequest): Promise<Case> {
    return this.post<Case>('/cases/', data);
  }

  async updateCase(id: string, data: Partial<CreateCaseRequest>): Promise<Case> {
    return this.patch<Case>(`/cases/${id}/`, data);
  }

  async deleteCase(id: string): Promise<void> {
    return this.delete(`/cases/${id}/`);
  }

  // Advance case to next workflow stage
  async advanceCase(id: string, notes?: string, quality_check_passed?: boolean): Promise<Case> {
    return this.post<Case>(`/cases/${id}/advance/`, {
      notes: notes || '',
      quality_check_passed: quality_check_passed !== false
    });
  }

  // Assign technician to case
  async assignTechnician(id: string, technician_id: string, stage?: string): Promise<Case> {
    return this.post<Case>(`/cases/${id}/assign_technician/`, {
      technician_id,
      stage: stage || ''
    });
  }

  // Get overdue cases
  async getOverdueCases(): Promise<Case[]> {
    return this.get<Case[]>('/cases/overdue/');
  }

  // Get cases by status
  async getCasesByStatus(status: string): Promise<Case[]> {
    return this.get<Case[]>(`/cases/by_status/?status=${status}`);
  }

  async uploadCaseFiles(id: string, files: File[]): Promise<any> {
    const formData = new FormData();
    files.forEach(file => formData.append('files', file));

    return this.post(`/cases/${id}/files/`, formData, {
      headers: { 'Content-Type': 'multipart/form-data' }
    });
  }
}

// Clinics Service
class ClinicsService extends BaseApiService {
  async getClinics(): Promise<Clinic[]> {
    const response = await this.get<{ results: Clinic[] }>('/clinics/');
    return response.results || [];
  }

  async getClinicById(id: string): Promise<Clinic> {
    return this.get<Clinic>(`/clinics/${id}/`);
  }

  async createClinic(data: Omit<Clinic, 'id'>): Promise<Clinic> {
    return this.post<Clinic>('/clinics/', data);
  }

  async updateClinic(id: string, data: Partial<Clinic>): Promise<Clinic> {
    return this.patch<Clinic>(`/clinics/${id}/`, data);
  }

  async deleteClinic(id: string): Promise<void> {
    return this.delete(`/clinics/${id}/`);
  }
}

// Workflow Service
export interface WorkflowStageOld {
  id: string;
  name: string;
  description: string;
  order: number;
  estimatedTime: number;
  color: string;
}

export interface Task {
  id: string;
  caseId: string;
  stageId: string;
  assigneeId: string;
  status: 'pending' | 'in_progress' | 'completed' | 'blocked';
  priority: 'stat' | 'urgent' | 'normal' | 'low';
  startTime?: string;
  endTime?: string;
  notes?: string;
}

class WorkflowService extends BaseApiService {
  async getWorkflowStages(): Promise<WorkflowStage[]> {
    return this.get<WorkflowStage[]>('/workflow/stages');
  }

  async getTasks(params?: {
    status?: string;
    assigneeId?: string;
    stageId?: string;
  }): Promise<Task[]> {
    return this.get<Task[]>('/workflow/tasks', { params });
  }

  async createTask(data: Omit<Task, 'id'>): Promise<Task> {
    return this.post<Task>('/workflow/tasks', data);
  }

  async updateTask(id: string, data: Partial<Task>): Promise<Task> {
    return this.put<Task>(`/workflow/tasks/${id}`, data);
  }

  async assignTask(taskId: string, assigneeId: string): Promise<Task> {
    return this.patch<Task>(`/workflow/tasks/${taskId}/assign`, { assigneeId });
  }

  async startTask(taskId: string): Promise<Task> {
    return this.patch<Task>(`/workflow/tasks/${taskId}/start`);
  }

  async completeTask(taskId: string, notes?: string): Promise<Task> {
    return this.patch<Task>(`/workflow/tasks/${taskId}/complete`, { notes });
  }
}

// Billing Service
export interface Invoice {
  id: string;
  caseId: string;
  amount: number;
  status: 'draft' | 'sent' | 'paid' | 'overdue' | 'cancelled';
  dueDate: string;
  paidDate?: string;
  items: InvoiceItem[];
  patient: string;
  dentist: string;
  clinic: string;
}

export interface InvoiceItem {
  description: string;
  quantity: number;
  unitPrice: number;
  total: number;
}

export interface Payment {
  id: string;
  invoiceId: string;
  amount: number;
  method: 'bank_transfer' | 'credit_card' | 'check' | 'cash';
  reference: string;
  paidDate: string;
  notes?: string;
}

class BillingService extends BaseApiService {
  async getInvoices(params?: {
    status?: string;
    dateFrom?: string;
    dateTo?: string;
  }): Promise<Invoice[]> {
    return this.get<Invoice[]>('/billing/invoices', { params });
  }

  async getInvoiceById(id: string): Promise<Invoice> {
    return this.get<Invoice>(`/billing/invoices/${id}`);
  }

  async createInvoice(data: Omit<Invoice, 'id' | 'status'>): Promise<Invoice> {
    return this.post<Invoice>('/billing/invoices', data);
  }

  async updateInvoice(id: string, data: Partial<Invoice>): Promise<Invoice> {
    return this.put<Invoice>(`/billing/invoices/${id}`, data);
  }

  async sendInvoice(id: string): Promise<void> {
    return this.post(`/billing/invoices/${id}/send`);
  }

  async recordPayment(invoiceId: string, payment: Omit<Payment, 'id' | 'invoiceId'>): Promise<Payment> {
    return this.post<Payment>(`/billing/invoices/${invoiceId}/payments`, payment);
  }

  async getPayments(invoiceId?: string): Promise<Payment[]> {
    const url = invoiceId ? `/billing/invoices/${invoiceId}/payments` : '/billing/payments';
    return this.get<Payment[]>(url);
  }

  async getFinancialStats(period?: string): Promise<any> {
    return this.get('/billing/stats', { params: { period } });
  }
}

// Schedule Service
export interface Appointment {
  id: string;
  title: string;
  caseId?: string;
  patient: string;
  dentist: string;
  type: 'consultation' | 'delivery' | 'qc_review' | 'design_review' | 'pickup';
  date: string;
  startTime: string;
  endTime: string;
  status: 'scheduled' | 'confirmed' | 'completed' | 'cancelled';
  notes?: string;
}

class ScheduleService extends BaseApiService {
  async getAppointments(params?: {
    dateFrom?: string;
    dateTo?: string;
    type?: string;
    status?: string;
  }): Promise<Appointment[]> {
    return this.get<Appointment[]>('/schedule/appointments', { params });
  }

  async getAppointmentById(id: string): Promise<Appointment> {
    return this.get<Appointment>(`/schedule/appointments/${id}`);
  }

  async createAppointment(data: Omit<Appointment, 'id'>): Promise<Appointment> {
    return this.post<Appointment>('/schedule/appointments', data);
  }

  async updateAppointment(id: string, data: Partial<Appointment>): Promise<Appointment> {
    return this.put<Appointment>(`/schedule/appointments/${id}`, data);
  }

  async deleteAppointment(id: string): Promise<void> {
    return this.delete(`/schedule/appointments/${id}`);
  }

  async getAvailableSlots(date: string, duration: number): Promise<string[]> {
    return this.get<string[]>('/schedule/available-slots', { 
      params: { date, duration } 
    });
  }
}

// Reports Service
export interface ReportRequest {
  type: 'production' | 'financial' | 'quality' | 'customer' | 'performance';
  dateRange: {
    from: string;
    to: string;
  };
  filters?: Record<string, any>;
}

export interface ReportResponse {
  id: string;
  type: string;
  status: 'generating' | 'completed' | 'failed';
  url?: string;
  createdAt: string;
}

class ReportsService extends BaseApiService {
  async generateReport(request: ReportRequest): Promise<ReportResponse> {
    return this.post<ReportResponse>('/reports/generate', request);
  }

  async getReports(): Promise<ReportResponse[]> {
    return this.get<ReportResponse[]>('/reports');
  }

  async getReportById(id: string): Promise<ReportResponse> {
    return this.get<ReportResponse>(`/reports/${id}`);
  }

  async downloadReport(id: string): Promise<Blob> {
    const response = await apiClient.get(`/reports/${id}/download`, {
      responseType: 'blob'
    });
    return response.data;
  }

  async getDashboardStats(): Promise<any> {
    return this.get('/reports/dashboard-stats');
  }
}

// Export service instances
export const authService = new AuthService();
export const casesService = new CasesService();
export const clinicsService = new ClinicsService();
export const workflowService = new WorkflowService();
export const billingService = new BillingService();
export const scheduleService = new ScheduleService();
export const reportsService = new ReportsService();

// Export the configured axios instance for custom requests
export { apiClient };

// Utility functions for error handling
export const handleApiError = (error: any): string => {
  if (error.response?.data?.message) {
    return error.response.data.message;
  }
  if (error.message) {
    return error.message;
  }
  return 'An unexpected error occurred';
};

export const isNetworkError = (error: any): boolean => {
  return !error.response && error.request;
};

export default {
  authService,
  casesService,
  clinicsService,
  workflowService,
  billingService,
  scheduleService,
  reportsService,
  apiClient,
  handleApiError,
  isNetworkError,
};
