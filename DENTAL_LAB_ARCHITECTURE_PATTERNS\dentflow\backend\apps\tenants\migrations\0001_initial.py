# Generated by Django 4.2.7 on 2025-07-25 19:48

import django.core.validators
from django.db import migrations, models
import django.db.models.deletion
import uuid


class Migration(migrations.Migration):

    initial = True

    dependencies = []

    operations = [
        migrations.CreateModel(
            name="Tenant",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4, primary_key=True, serialize=False
                    ),
                ),
                ("name", models.CharField(max_length=255)),
                ("subdomain", models.CharField(max_length=100, unique=True)),
                ("contact_email", models.EmailField(max_length=254)),
                ("phone", models.CharField(blank=True, max_length=20)),
                ("address", models.TextField(blank=True)),
                ("website", models.URLField(blank=True)),
                (
                    "plan",
                    models.CharField(
                        choices=[
                            ("starter", "Starter Plan"),
                            ("professional", "Professional Plan"),
                            ("enterprise", "Enterprise Plan"),
                        ],
                        default="starter",
                        max_length=20,
                    ),
                ),
                ("max_users", models.PositiveIntegerField(default=5)),
                ("max_cases_per_month", models.PositiveIntegerField(default=100)),
                ("is_active", models.BooleanField(default=True)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
            ],
        ),
        migrations.CreateModel(
            name="TenantSubscription",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "plan",
                    models.CharField(
                        choices=[
                            ("starter", "Starter Plan - $29/month"),
                            ("professional", "Professional Plan - $79/month"),
                            ("enterprise", "Enterprise Plan - $199/month"),
                        ],
                        max_length=20,
                    ),
                ),
                ("is_active", models.BooleanField(default=True)),
                ("is_trial", models.BooleanField(default=False)),
                ("start_date", models.DateField()),
                ("end_date", models.DateField()),
                (
                    "monthly_price",
                    models.DecimalField(
                        decimal_places=2,
                        max_digits=10,
                        validators=[django.core.validators.MinValueValidator(0)],
                    ),
                ),
                (
                    "stripe_subscription_id",
                    models.CharField(blank=True, max_length=100),
                ),
                ("last_payment_date", models.DateField(blank=True, null=True)),
                ("next_billing_date", models.DateField(blank=True, null=True)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                (
                    "tenant",
                    models.OneToOneField(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="subscription",
                        to="tenants.tenant",
                    ),
                ),
            ],
        ),
        migrations.CreateModel(
            name="TenantSettings",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "timezone",
                    models.CharField(
                        choices=[
                            ("UTC", "UTC"),
                            ("US/Eastern", "Eastern Time"),
                            ("US/Central", "Central Time"),
                            ("US/Mountain", "Mountain Time"),
                            ("US/Pacific", "Pacific Time"),
                            ("Europe/London", "London"),
                            ("Europe/Berlin", "Berlin"),
                        ],
                        default="UTC",
                        max_length=50,
                    ),
                ),
                (
                    "currency",
                    models.CharField(
                        choices=[
                            ("USD", "US Dollar"),
                            ("EUR", "Euro"),
                            ("GBP", "British Pound"),
                            ("CAD", "Canadian Dollar"),
                        ],
                        default="USD",
                        max_length=3,
                    ),
                ),
                (
                    "language",
                    models.CharField(
                        choices=[
                            ("en", "English"),
                            ("es", "Spanish"),
                            ("fr", "French"),
                            ("de", "German"),
                        ],
                        default="en",
                        max_length=2,
                    ),
                ),
                ("date_format", models.CharField(default="MM/DD/YYYY", max_length=20)),
                ("business_hours", models.JSONField(default=dict)),
                ("default_workflow", models.JSONField(default=list)),
                ("email_notifications", models.BooleanField(default=True)),
                ("sms_notifications", models.BooleanField(default=False)),
                ("slack_webhook", models.URLField(blank=True)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                (
                    "tenant",
                    models.OneToOneField(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="settings",
                        to="tenants.tenant",
                    ),
                ),
            ],
        ),
        migrations.AddIndex(
            model_name="tenant",
            index=models.Index(
                fields=["subdomain"], name="tenants_ten_subdoma_6c6a7a_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="tenant",
            index=models.Index(
                fields=["is_active"], name="tenants_ten_is_acti_8f654a_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="tenantsubscription",
            index=models.Index(
                fields=["plan", "is_active"], name="tenants_ten_plan_19973b_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="tenantsubscription",
            index=models.Index(
                fields=["end_date"], name="tenants_ten_end_dat_94a3ef_idx"
            ),
        ),
    ]
